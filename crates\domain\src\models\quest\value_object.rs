use reforged_shared::{UuidId, Value};

use super::entity::Quest;

pub type QuestId = UuidId<Quest>;

#[derive(Debug, PartialEq, Eq, PartialOrd, Ord, Clone, Default)]

pub struct QuestName(String);

impl QuestName {
    pub fn new(name: impl Into<String>) -> Self {
        Self(name.into())
    }
}

impl Value<String> for QuestName {
    fn value(&self) -> String {
        self.0.clone()
    }
}

impl From<String> for QuestName {
    fn from(value: String) -> Self {
        Self(value)
    }
}

impl From<&str> for QuestName {
    fn from(value: &str) -> Self {
        Self(value.to_string())
    }
}

#[derive(Debug, PartialEq, Eq, PartialOrd, Ord, Clone, Default)]

pub struct QuestDescription(String);

impl QuestDescription {
    pub fn new(description: impl Into<String>) -> Self {
        Self(description.into())
    }
}

impl Value<String> for QuestDescription {
    fn value(&self) -> String {
        self.0.clone()
    }
}

impl From<String> for QuestDescription {
    fn from(value: String) -> Self {
        Self(value)
    }
}

impl From<&str> for QuestDescription {
    fn from(value: &str) -> Self {
        Self(value.to_string())
    }
}

#[derive(Debug, PartialEq, Eq, PartialOrd, Ord, Clone, Default)]

pub struct QuestEndText(String);

impl QuestEndText {
    pub fn new(end_text: impl Into<String>) -> Self {
        Self(end_text.into())
    }
}

impl Value<String> for QuestEndText {
    fn value(&self) -> String {
        self.0.clone()
    }
}

impl From<String> for QuestEndText {
    fn from(value: String) -> Self {
        Self(value)
    }
}

impl From<&str> for QuestEndText {
    fn from(value: &str) -> Self {
        Self(value.to_string())
    }
}

#[derive(Debug, PartialEq, Eq, PartialOrd, Ord, Clone, Default)]

pub struct QuestExperience(i64);

impl QuestExperience {
    pub fn new(experience: i64) -> Self {
        Self(experience)
    }
}

impl Value<i64> for QuestExperience {
    fn value(&self) -> i64 {
        self.0
    }
}

impl From<i64> for QuestExperience {
    fn from(value: i64) -> Self {
        Self(value)
    }
}

#[derive(Debug, PartialEq, Eq, PartialOrd, Ord, Clone, Default)]

pub struct QuestGExperience(i32);

impl QuestGExperience {
    pub fn new(g_experience: i32) -> Self {
        Self(g_experience)
    }
}

impl Value<i32> for QuestGExperience {
    fn value(&self) -> i32 {
        self.0
    }
}

impl From<i32> for QuestGExperience {
    fn from(value: i32) -> Self {
        Self(value)
    }
}

#[derive(Debug, PartialEq, Eq, PartialOrd, Ord, Clone, Default)]

pub struct QuestGold(i64);

impl QuestGold {
    pub fn new(gold: i64) -> Self {
        Self(gold)
    }
}

impl Value<i64> for QuestGold {
    fn value(&self) -> i64 {
        self.0
    }
}

impl From<i64> for QuestGold {
    fn from(value: i64) -> Self {
        Self(value)
    }
}

#[derive(Debug, PartialEq, Eq, PartialOrd, Ord, Clone, Default)]

pub struct QuestCoins(i32);

impl QuestCoins {
    pub fn new(coins: i32) -> Self {
        Self(coins)
    }
}

impl Value<i32> for QuestCoins {
    fn value(&self) -> i32 {
        self.0
    }
}

impl From<i32> for QuestCoins {
    fn from(value: i32) -> Self {
        Self(value)
    }
}

#[derive(Debug, PartialEq, Eq, PartialOrd, Ord, Clone, Default)]

pub struct QuestReputation(i32);

impl QuestReputation {
    pub fn new(reputation: i32) -> Self {
        Self(reputation)
    }
}

impl Value<i32> for QuestReputation {
    fn value(&self) -> i32 {
        self.0
    }
}

impl From<i32> for QuestReputation {
    fn from(value: i32) -> Self {
        Self(value)
    }
}

#[derive(Debug, PartialEq, Eq, PartialOrd, Ord, Clone, Default)]

pub struct QuestReqReputation(i32);

impl QuestReqReputation {
    pub fn new(req_reputation: i32) -> Self {
        Self(req_reputation)
    }
}

impl Value<i32> for QuestReqReputation {
    fn value(&self) -> i32 {
        self.0
    }
}

impl From<i32> for QuestReqReputation {
    fn from(value: i32) -> Self {
        Self(value)
    }
}

#[derive(Debug, PartialEq, Eq, PartialOrd, Ord, Clone, Default)]

pub struct QuestClassPoints(i32);

impl QuestClassPoints {
    pub fn new(class_points: i32) -> Self {
        Self(class_points)
    }
}

impl Value<i32> for QuestClassPoints {
    fn value(&self) -> i32 {
        self.0
    }
}

impl From<i32> for QuestClassPoints {
    fn from(value: i32) -> Self {
        Self(value)
    }
}

#[derive(Debug, PartialEq, Eq, PartialOrd, Ord, Clone, Default)]

pub struct QuestReqClassPoints(i32);

impl QuestReqClassPoints {
    pub fn new(req_class_points: i32) -> Self {
        Self(req_class_points)
    }
}

impl Value<i32> for QuestReqClassPoints {
    fn value(&self) -> i32 {
        self.0
    }
}

impl From<i32> for QuestReqClassPoints {
    fn from(value: i32) -> Self {
        Self(value)
    }
}

#[derive(Debug, PartialEq, Eq, PartialOrd, Ord, Clone, Default)]

pub enum QuestRewardType {
    #[default]
    R,
    S,
}

impl QuestRewardType {
    pub fn new(reward_type: impl Into<String>) -> Self {
        match reward_type.into().as_str() {
            "R" => Self::R,
            "S" => Self::S,
            _ => panic!("Invalid reward type"),
        }
    }
}

impl Value<String> for QuestRewardType {
    fn value(&self) -> String {
        match self {
            Self::R => "R".to_string(),
            Self::S => "S".to_string(),
        }
    }
}

impl From<String> for QuestRewardType {
    fn from(value: String) -> Self {
        match value.as_str() {
            "R" => Self::R,
            "S" => Self::S,
            _ => panic!("Invalid reward type"),
        }
    }
}

impl From<&str> for QuestRewardType {
    fn from(value: &str) -> Self {
        match value {
            "R" => Self::R,
            "S" => Self::S,
            _ => panic!("Invalid reward type"),
        }
    }
}

#[derive(Debug, PartialEq, Eq, PartialOrd, Ord, Clone, Default)]

pub struct QuestLevel(i16);

impl QuestLevel {
    pub fn new(level: i16) -> Self {
        Self(level)
    }
}

impl Value<i16> for QuestLevel {
    fn value(&self) -> i16 {
        self.0
    }
}

impl From<i16> for QuestLevel {
    fn from(value: i16) -> Self {
        Self(value)
    }
}

#[derive(Debug, PartialEq, Eq, PartialOrd, Ord, Clone, Default)]

pub struct QuestUpgrade(bool);

impl QuestUpgrade {
    pub fn new(upgrade: bool) -> Self {
        Self(upgrade)
    }
}

impl Value<bool> for QuestUpgrade {
    fn value(&self) -> bool {
        self.0
    }
}

impl From<bool> for QuestUpgrade {
    fn from(value: bool) -> Self {
        Self(value)
    }
}

#[derive(Debug, PartialEq, Eq, PartialOrd, Ord, Clone, Default)]

pub struct QuestOnce(bool);

impl QuestOnce {
    pub fn new(once: bool) -> Self {
        Self(once)
    }
}

impl Value<bool> for QuestOnce {
    fn value(&self) -> bool {
        self.0
    }
}

impl From<bool> for QuestOnce {
    fn from(value: bool) -> Self {
        Self(value)
    }
}

#[derive(Debug, PartialEq, Eq, PartialOrd, Ord, Clone, Default)]

pub struct QuestSlot(i32);

impl QuestSlot {
    pub fn new(slot: i32) -> Self {
        Self(slot)
    }
}

impl Value<i32> for QuestSlot {
    fn value(&self) -> i32 {
        self.0
    }
}

impl From<i32> for QuestSlot {
    fn from(value: i32) -> Self {
        Self(value)
    }
}

#[derive(Debug, PartialEq, Eq, PartialOrd, Ord, Clone, Default)]

pub struct QuestValue(i32);

impl QuestValue {
    pub fn new(value: i32) -> Self {
        Self(value)
    }
}

impl Value<i32> for QuestValue {
    fn value(&self) -> i32 {
        self.0
    }
}

impl From<i32> for QuestValue {
    fn from(value: i32) -> Self {
        Self(value)
    }
}

#[derive(Debug, PartialEq, Eq, PartialOrd, Ord, Clone, Default)]

pub struct QuestField(String);

impl QuestField {
    pub fn new(field: impl Into<String>) -> Self {
        Self(field.into())
    }
}

impl Value<String> for QuestField {
    fn value(&self) -> String {
        self.0.clone()
    }
}

impl From<String> for QuestField {
    fn from(value: String) -> Self {
        Self(value)
    }
}

impl From<&str> for QuestField {
    fn from(value: &str) -> Self {
        Self(value.to_string())
    }
}

#[derive(Debug, PartialEq, Eq, PartialOrd, Ord, Clone, Default)]

pub struct QuestIndex(i32);

impl QuestIndex {
    pub fn new(index: i32) -> Self {
        Self(index)
    }
}

impl Value<i32> for QuestIndex {
    fn value(&self) -> i32 {
        self.0
    }
}

impl From<i32> for QuestIndex {
    fn from(value: i32) -> Self {
        Self(value)
    }
}

#[derive(Debug, PartialEq, Eq, PartialOrd, Ord, Clone, Default)]

pub struct QuestBadges(String);

impl QuestBadges {
    pub fn new(badges: impl Into<String>) -> Self {
        Self(badges.into())
    }
}

impl Value<String> for QuestBadges {
    fn value(&self) -> String {
        self.0.clone()
    }
}

impl From<String> for QuestBadges {
    fn from(value: String) -> Self {
        Self(value)
    }
}

impl From<&str> for QuestBadges {
    fn from(value: &str) -> Self {
        Self(value.to_string())
    }
}

#[derive(Debug, PartialEq, Eq, PartialOrd, Ord, Clone, Default)]

pub struct QuestGiveMembership(bool);

impl QuestGiveMembership {
    pub fn new(give_membership: bool) -> Self {
        Self(give_membership)
    }
}

impl Value<bool> for QuestGiveMembership {
    fn value(&self) -> bool {
        self.0
    }
}

impl From<bool> for QuestGiveMembership {
    fn from(value: bool) -> Self {
        Self(value)
    }
}

impl From<i32> for QuestGiveMembership {
    fn from(value: i32) -> Self {
        Self(value != 0)
    }
}

#[derive(Debug, PartialEq, Eq, PartialOrd, Ord, Clone, Default)]

pub struct QuestWarMega(bool);

impl QuestWarMega {
    pub fn new(war_mega: bool) -> Self {
        Self(war_mega)
    }
}

impl Value<bool> for QuestWarMega {
    fn value(&self) -> bool {
        self.0
    }
}

impl From<bool> for QuestWarMega {
    fn from(value: bool) -> Self {
        Self(value)
    }
}

#[derive(Debug, PartialEq, Eq, PartialOrd, Ord, Clone, Default)]

pub struct QuestReqGuildLevel(i32);

impl QuestReqGuildLevel {
    pub fn new(req_guild_level: i32) -> Self {
        Self(req_guild_level)
    }
}

impl Value<i32> for QuestReqGuildLevel {
    fn value(&self) -> i32 {
        self.0
    }
}

impl From<i32> for QuestReqGuildLevel {
    fn from(value: i32) -> Self {
        Self(value)
    }
}

#[derive(Debug, PartialEq, Eq, PartialOrd, Ord, Clone, Default)]

pub struct QuestStaff(bool);

impl QuestStaff {
    pub fn new(staff: bool) -> Self {
        Self(staff)
    }
}

impl Value<bool> for QuestStaff {
    fn value(&self) -> bool {
        self.0
    }
}

impl From<bool> for QuestStaff {
    fn from(value: bool) -> Self {
        Self(value)
    }
}

#[derive(Debug, PartialEq, Eq, PartialOrd, Ord, Clone, Default)]

pub struct QuestCheck(bool);

impl QuestCheck {
    pub fn new(check: bool) -> Self {
        Self(check)
    }
}

impl Value<bool> for QuestCheck {
    fn value(&self) -> bool {
        self.0
    }
}

impl From<bool> for QuestCheck {
    fn from(value: bool) -> Self {
        Self(value)
    }
}
