use super::value_object::{<PERSON>actionId, FactionName};

use getset::{<PERSON><PERSON>, <PERSON><PERSON>};

#[derive(<PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, bon::<PERSON><PERSON><PERSON>, <PERSON><PERSON>, Set<PERSON>)]
#[getset(get = "pub", set = "pub")]
pub struct Faction {
    id: FactionId,
    name: FactionName,
}

impl Faction {
    pub fn new(id: FactionId, name: FactionName) -> Self {
        Self { id, name }
    }
}

#[cfg(test)]
mod tests {
    use reforged_shared::{IdTrait, Value};

    use super::*;

    #[test]
    fn test_faction_new() {
        let id = FactionId::new(uuid::Uuid::now_v7());
        let name = FactionName::new("Alliance");

        let faction = Faction::new(id.clone(), name.clone());

        assert_eq!(faction.id().get_id(), id.get_id());
        assert_eq!(faction.name().value(), name.value());
    }

    #[test]
    fn test_faction_builder() {
        let id = FactionId::new(uuid::Uuid::now_v7());
        let name = FactionName::new("Alliance");

        let faction = Faction::builder().id(id.clone()).name(name.clone()).build();

        assert_eq!(faction.id().get_id(), id.get_id());
        assert_eq!(faction.name().value(), name.value());
    }

    #[test]
    fn test_getters_and_setters() {
        let mut faction = Faction::default();

        let id = FactionId::new(uuid::Uuid::now_v7());
        let name = FactionName::new("Alliance");

        faction.set_id(id.clone());
        faction.set_name(name.clone());

        assert_eq!(faction.id().get_id(), id.get_id());
        assert_eq!(faction.name().value(), name.value());
    }
}
