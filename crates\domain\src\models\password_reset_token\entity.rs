use getset::{<PERSON><PERSON>, <PERSON><PERSON>};

use crate::models::user::value_object::UserId;

use super::value_object::{
    PasswordResetTokenCreatedAt, PasswordResetTokenExpiresAt, PasswordResetTokenId, Token,
};

#[allow(unused)]
#[derive(<PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, bon::<PERSON><PERSON><PERSON>, <PERSON><PERSON>, Set<PERSON>)]
#[getset(get = "pub", set = "pub")]
pub struct PasswordResetToken {
    id: PasswordResetTokenId,
    user_id: UserId,
    token: Token,
    created_at: PasswordResetTokenCreatedAt,
    expires_at: PasswordResetTokenExpiresAt,
}

impl PasswordResetToken {
    pub fn new(
        id: PasswordResetTokenId,
        user_id: UserId,
        token: Token,
        created_at: PasswordResetTokenCreatedAt,
        expires_at: PasswordResetTokenExpiresAt,
    ) -> Self {
        Self {
            id,
            user_id,
            token,
            created_at,
            expires_at,
        }
    }
}

#[cfg(test)]
mod tests {
    use reforged_shared::{IdTrait, Value};

    use crate::models::{
        password_reset_token::{entity::PasswordResetToken, value_object::*},
        user::value_object::UserId,
    };

    #[test]
    fn test_new() {
        let token = PasswordResetToken::new(
            PasswordResetTokenId::new(uuid::Uuid::now_v7()),
            UserId::new(uuid::Uuid::now_v7()),
            Token::new("test_token".to_string()),
            PasswordResetTokenCreatedAt::default(),
            PasswordResetTokenExpiresAt::default(),
        );

        assert!(token.id().get_id().to_string().len() > 0);
        assert!(token.user_id().get_id().to_string().len() > 0);
        assert_eq!(token.token().value(), "test_token");
    }
}
