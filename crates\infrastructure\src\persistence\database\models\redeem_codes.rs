//! `SeaORM` Entity, @generated by sea-orm-codegen 1.1.0

use sea_orm::entity::prelude::*;
use serde::Deserialize;

#[derive(Clone, Debug, PartialEq, DeriveEntityModel, Eq, Deserialize)]
#[sea_orm(table_name = "redeem_codes")]
pub struct Model {
    #[sea_orm(primary_key, auto_increment = false)]
    pub id: Uuid,
    pub code: String,
    pub coins: i32,
    pub gold: i32,
    pub exp: i32,
    pub class_points: i32,
    pub item_id: Option<Uuid>,
    pub upgrade_days: i32,
    pub date_expiry: DateTime,
    pub limit: i32,
}

#[derive(Co<PERSON>, Clone, Debug, EnumIter, DeriveRelation)]
pub enum Relation {
    #[sea_orm(
        belongs_to = "super::items::Entity",
        from = "Column::ItemId",
        to = "super::items::Column::Id",
        on_update = "Cascade",
        on_delete = "SetNull"
    )]
    Items,
    #[sea_orm(has_many = "super::user_redeems::Entity")]
    UserRedeems,
}

impl Related<super::items::Entity> for Entity {
    fn to() -> RelationDef {
        Relation::Items.def()
    }
}

impl Related<super::user_redeems::Entity> for Entity {
    fn to() -> RelationDef {
        Relation::UserRedeems.def()
    }
}

impl ActiveModelBehavior for ActiveModel {}
