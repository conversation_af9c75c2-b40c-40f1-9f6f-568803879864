//! `SeaORM` Entity, @generated by sea-orm-codegen 1.1.0

use sea_orm::entity::prelude::*;
use serde::Deserialize;

#[derive(<PERSON><PERSON>, Debug, PartialEq, DeriveEntityModel, Eq, Deserialize)]
#[sea_orm(table_name = "enhancements")]
pub struct Model {
    #[sea_orm(primary_key, auto_increment = false)]
    pub id: Uuid,
    pub name: String,
    pub pattern_id: Uuid,
    pub rarity: Uuid,
    pub dps: i16,
    pub level: i16,
}

#[derive(<PERSON><PERSON>, <PERSON><PERSON>, Debug, EnumIter, DeriveRelation)]
pub enum Relation {
    #[sea_orm(
        belongs_to = "super::enhancement_patterns::Entity",
        from = "Column::PatternId",
        to = "super::enhancement_patterns::Column::Id",
        on_update = "Cascade",
        on_delete = "Cascade"
    )]
    EnhancementPatterns,
    #[sea_orm(has_many = "super::items::Entity")]
    Items,
    #[sea_orm(has_many = "super::user_items::Entity")]
    UserItems,
}

impl Related<super::enhancement_patterns::Entity> for Entity {
    fn to() -> RelationDef {
        Relation::EnhancementPatterns.def()
    }
}

impl Related<super::items::Entity> for Entity {
    fn to() -> RelationDef {
        Relation::Items.def()
    }
}

impl Related<super::user_items::Entity> for Entity {
    fn to() -> RelationDef {
        Relation::UserItems.def()
    }
}

impl ActiveModelBehavior for ActiveModel {}
