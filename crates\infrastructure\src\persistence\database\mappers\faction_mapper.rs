use reforged_domain::models::faction::{entity::Faction, value_object::FactionId};

#[derive(Debug)]
pub struct FactionDbModelMapper(super::super::models::factions::Model);

impl FactionDbModelMapper {
    pub fn new(faction: super::super::models::factions::Model) -> Self {
        Self(faction)
    }
}

impl From<FactionDbModelMapper> for Faction {
    fn from(value: FactionDbModelMapper) -> Self {
        let faction_model = value.0;

        let id = FactionId::new(faction_model.id);

        Faction::builder()
            .id(id)
            .name(faction_model.name.into())
            .build()
    }
}
