use reforged_shared::{UuidId, Value};

use super::entity::MapCell;

pub type MapCellId = UuidId<MapCell>;

#[derive(Debug, PartialEq, Eq, PartialOrd, Ord, Clone, <PERSON><PERSON>ult)]

pub struct MapCellFrame(String);

impl MapCellFrame {
    pub fn new(frame: impl Into<String>) -> Self {
        Self(frame.into())
    }
}

impl Value<String> for MapCellFrame {
    fn value(&self) -> String {
        self.0.clone()
    }
}

impl From<String> for MapCellFrame {
    fn from(value: String) -> Self {
        Self(value)
    }
}

impl From<&str> for MapCellFrame {
    fn from(value: &str) -> Self {
        Self(value.to_string())
    }
}

#[derive(Debug, PartialEq, Eq, PartialOrd, Ord, Clone, Default)]

pub struct MapCellPad(String);

impl MapCellPad {
    pub fn new(pad: impl Into<String>) -> Self {
        Self(pad.into())
    }
}

impl Value<String> for MapCellPad {
    fn value(&self) -> String {
        self.0.clone()
    }
}

impl From<String> for MapCellPad {
    fn from(value: String) -> Self {
        Self(value)
    }
}

impl From<&str> for MapCellPad {
    fn from(value: &str) -> Self {
        Self(value.to_string())
    }
}
