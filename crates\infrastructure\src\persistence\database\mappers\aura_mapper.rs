use num_traits::ToPrimitive;
use reforged_domain::models::aura::{
    entity::Aura,
    value_object::{
        Aura<PERSON><PERSON><PERSON><PERSON>, <PERSON>ra<PERSON><PERSON><PERSON>, AuraDamageIncrease, AuraDamageTakenDecrease, AuraDuration,
        AuraId, AuraName, AuraSelfTarget,
    },
};

#[derive(Debug)]
pub struct AuraDbModelMapper(super::super::models::auras::Model);

impl AuraDbModelMapper {
    pub fn new(aura: super::super::models::auras::Model) -> Self {
        Self(aura)
    }
}

impl From<AuraDbModelMapper> for Aura {
    fn from(value: AuraDbModelMapper) -> Self {
        let aura_model = value.0;

        let id = AuraId::new(aura_model.id);

        Aura::builder()
            .id(id)
            .name(AuraName::new(aura_model.name))
            .duration(AuraDuration::new(aura_model.duration))
            .category(AuraCategory::new(aura_model.category))
            .damage_increase(AuraDamageIncrease::new(
                aura_model.damage_increase.to_f64().unwrap_or(0.0),
            ))
            .damage_taken_decrease(AuraDamageTakenDecrease::new(
                aura_model.damage_taken_decrease.to_f64().unwrap_or(0.0),
            ))
            .chance(AuraChance::new(aura_model.chance.to_f64().unwrap_or(0.0)))
            .self_target(AuraSelfTarget::new(aura_model.self_))
            .build()
    }
}
