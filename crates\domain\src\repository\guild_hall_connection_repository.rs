use async_trait::async_trait;
use mockall::automock;

use crate::error::RepositoryError;
use crate::models::guild_hall_connection::entity::GuildHallConnection;
use crate::models::guild_hall_connection::value_object::GuildHallConnectionId;

#[automock]
#[async_trait]
pub trait GuildHallConnectionRepository: Send + Sync {
    async fn save(&self, entity: &GuildHallConnection) -> Result<(), RepositoryError>;
    async fn update(&self, entity: &GuildHallConnection) -> Result<(), RepositoryError>;
    async fn delete(&self, id: &GuildHallConnectionId) -> Result<(), RepositoryError>;
}

#[automock]
#[async_trait]
pub trait GuildHallConnectionReadRepository: Send + Sync {
    async fn find_by_id(
        &self,
        id: &GuildHallConnectionId,
    ) -> Result<Option<GuildHallConnection>, RepositoryError>;
    async fn find_all(&self) -> Result<Vec<GuildHallConnection>, RepositoryError>;
}
