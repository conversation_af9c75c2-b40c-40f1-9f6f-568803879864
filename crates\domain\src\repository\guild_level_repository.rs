use async_trait::async_trait;
use mockall::automock;

use crate::error::RepositoryError;
use crate::models::guild_level::entity::GuildLevel;
use crate::models::guild_level::value_object::GuildLevelId;

#[automock]
#[async_trait]
pub trait GuildLevelRepository: Send + Sync {
    async fn save(&self, entity: &GuildLevel) -> Result<(), RepositoryError>;
    async fn update(&self, entity: &GuildLevel) -> Result<(), RepositoryError>;
    async fn delete(&self, id: &GuildLevelId) -> Result<(), RepositoryError>;
}

#[automock]
#[async_trait]
pub trait GuildLevelReadRepository: Send + Sync {
    async fn find_by_id(&self, id: &GuildLevelId) -> Result<Option<GuildLevel>, RepositoryError>;
    async fn find_all(&self) -> Result<Vec<GuildLevel>, RepositoryError>;
}
