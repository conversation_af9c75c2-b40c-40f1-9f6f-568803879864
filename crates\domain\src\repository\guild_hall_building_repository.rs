use async_trait::async_trait;
use mockall::automock;

use crate::error::RepositoryError;
use crate::models::guild_hall_building::entity::GuildHallBuilding;
use crate::models::guild_hall_building::value_object::GuildHallBuildingId;

#[automock]
#[async_trait]
pub trait GuildHallBuildingRepository: Send + Sync {
    async fn save(&self, entity: &GuildHallBuilding) -> Result<(), RepositoryError>;
    async fn update(&self, entity: &GuildHallBuilding) -> Result<(), RepositoryError>;
    async fn delete(&self, id: &GuildHallBuildingId) -> Result<(), RepositoryError>;
}

#[automock]
#[async_trait]
pub trait GuildHallBuildingReadRepository: Send + Sync {
    async fn find_by_id(
        &self,
        id: &GuildHallBuildingId,
    ) -> Result<Option<GuildHallBuilding>, RepositoryError>;
    async fn find_all(&self) -> Result<Vec<GuildHallBuilding>, RepositoryError>;
}
