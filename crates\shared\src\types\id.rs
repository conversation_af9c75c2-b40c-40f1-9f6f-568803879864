use std::{fmt::Display, marker::PhantomData};

use crate::uuid_generator::uuid_from_secs;

pub trait IdTrait<T> {
    fn get_id(&self) -> T;

    /// Unique string with a format of: PhantomData<crate_path::T>_<T::value>
    /// sample: PhantomData<reforged_domain::models::user::entity::User>_1
    fn get_key(&self) -> String;
}

#[derive(<PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>)]
pub struct Id<T>(i32, PhantomData<T>);

#[derive(Debug, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>ult)]
pub struct UuidId<T>(uuid::Uuid, PhantomData<T>);

impl<T> Display for Id<T> {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        write!(f, "{}", self.0)
    }
}

impl<T> IdTrait<i32> for Id<T> {
    fn get_id(&self) -> i32 {
        self.0
    }

    fn get_key(&self) -> String {
        let marker = self.1;
        let marker_str = format!("{:?}", marker);
        let replaced = marker_str.replace("PhantomData<", "").replace(">", "");
        let split = replaced.split("::");
        let data_type = split.last().unwrap_or("Unknown");

        format!("{}_{}", data_type, self.get_id())
    }
}

impl<T> Id<T> {
    pub fn new(id: i32) -> Self {
        Self(id, PhantomData::<T>)
    }
}

impl<T> Display for UuidId<T> {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        write!(f, "{}", self.0)
    }
}

impl<T> IdTrait<uuid::Uuid> for UuidId<T> {
    fn get_id(&self) -> uuid::Uuid {
        self.0
    }

    fn get_key(&self) -> String {
        let marker = self.1;
        let marker_str = format!("{:?}", marker);
        let replaced = marker_str.replace("PhantomData<", "").replace(">", "");
        let split = replaced.split("::");
        let data_type = split.last().unwrap_or("Unknown");

        format!("{}_{}", data_type, self.get_id())
    }
}

impl<T> UuidId<T> {
    pub fn new(id: uuid::Uuid) -> Self {
        Self(id, PhantomData::<T>)
    }
}

impl<T> From<u8> for UuidId<T> {
    fn from(value: u8) -> Self {
        let id = uuid_from_secs(value as u64);
        Self(id, PhantomData::<T>)
    }
}

impl<T> From<u16> for UuidId<T> {
    fn from(value: u16) -> Self {
        let id = uuid_from_secs(value as u64);
        Self(id, PhantomData::<T>)
    }
}

impl<T> From<u32> for UuidId<T> {
    fn from(value: u32) -> Self {
        let id = uuid_from_secs(value as u64);
        Self(id, PhantomData::<T>)
    }
}

impl<T> From<u64> for UuidId<T> {
    fn from(value: u64) -> Self {
        let id = uuid_from_secs(value);
        Self(id, PhantomData::<T>)
    }
}

impl<T> PartialEq for UuidId<T> {
    fn eq(&self, other: &Self) -> bool {
        self.get_id().eq(&other.get_id())
    }

    fn ne(&self, other: &Self) -> bool {
        !self.get_id().eq(&other.get_id())
    }
}

#[cfg(test)]
mod tests {
    use uuid::Uuid;

    use super::*;

    #[test]
    fn test_id() {
        let id = Id::<String>::new(1);
        assert_eq!(id.get_id(), 1);
        assert_eq!(id.get_key(), "String_1");
    }

    #[test]
    fn test_uuid_id() {
        let id = UuidId::<String>::new(Uuid::now_v7());
        let expected_key = format!("String_{}", id.get_id().to_string());
        assert_eq!(id.get_key(), expected_key);
    }
}
