use std::sync::Arc;

use chrono::{DateTime, Utc};
use serde::Serialize;

use crate::{
    error::ApplicationError,
    traits::{PasetoClaimPurpose, TokenService},
};

#[derive(Serialize)]
pub struct EmailVerificationResponse {
    verified_at: DateTime<Utc>,
}

pub struct EmailVerificationUsecase {
    token_service: Arc<dyn TokenService>,
    get_email_verification_token_handler: (),
}

impl EmailVerificationUsecase {
    pub fn new(token_service: Arc<dyn TokenService>) -> Self {
        Self {
            token_service,
            get_email_verification_token_handler: (),
        }
    }
}

impl EmailVerificationUsecase {
    pub async fn execute(
        &self,
        token: String,
    ) -> Result<EmailVerificationResponse, ApplicationError> {
        let token_service = self.token_service.clone();

        // TODO: check if user is already verified
        // TODO: CHANGE Authenticated to EmailVerification
        let verify = token_service.validate_token(token, PasetoClaimPurpose::EmailVerification)?;
        let user_id = verify.id;

        // TODO: check if the token in the database is the same as the token in the request
        _ = user_id;
        _ = self.get_email_verification_token_handler;

        let now = Utc::now();
        let response = EmailVerificationResponse { verified_at: now };

        Ok(response)
    }
}
