use reforged_domain::models::market_type::entity::MarketType;

#[derive(Debug)]
pub struct MarketTypeDbModelMapper(super::super::models::sea_orm_active_enums::MarketType);

impl MarketTypeDbModelMapper {
    pub fn new(market_type: super::super::models::sea_orm_active_enums::MarketType) -> Self {
        Self(market_type)
    }
}

impl From<MarketTypeDbModelMapper> for MarketType {
    fn from(value: MarketTypeDbModelMapper) -> Self {
        match value.0 {
            super::super::models::sea_orm_active_enums::MarketType::Auction => MarketType::Auction,
            super::super::models::sea_orm_active_enums::MarketType::Vending => MarketType::Vending,
        }
    }
}
