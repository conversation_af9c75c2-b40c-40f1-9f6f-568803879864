use reforged_domain::models::user_currency::entity::{UserCurrency, UserCurrencyId};
use reforged_domain::models::user::value_object::UserId;

#[derive(Debug)]
pub struct UserCurrencyDbModelMapper(super::super::models::user_currencies::Model);

impl UserCurrencyDbModelMapper {
    pub fn new(model: super::super::models::user_currencies::Model) -> Self {
        Self(model)
    }
}

impl From<UserCurrencyDbModelMapper> for UserCurrency {
    fn from(value: UserCurrencyDbModelMapper) -> Self {
        let model = value.0;
        
        UserCurrency::builder()
            .id(UserCurrencyId::new(model.id))
            .user_id(UserId::new(model.user_id))
            .gold(model.gold.into())
            .coins(model.coins.into())
            .diamonds(model.diamonds.into())
            .crystal(model.crystal.into())
            .build()
    }
}
