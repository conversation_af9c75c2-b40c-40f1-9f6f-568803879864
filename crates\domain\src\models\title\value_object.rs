use reforged_shared::{UuidId, Value};

use super::entity::Title;

pub type TitleId = UuidId<Title>;

#[derive(Debug, PartialEq, Eq, PartialOrd, Ord, Clone, Default)]

pub struct TitleName(String);

impl TitleName {
    pub fn new(name: impl Into<String>) -> Self {
        Self(name.into())
    }
}

impl Value<String> for TitleName {
    fn value(&self) -> String {
        self.0.clone()
    }
}

impl From<String> for TitleName {
    fn from(value: String) -> Self {
        Self(value)
    }
}

impl From<&str> for TitleName {
    fn from(value: &str) -> Self {
        Self(value.to_string())
    }
}

#[derive(Debug, PartialEq, Eq, PartialOrd, Ord, Clone, Default)]

pub struct TitleDescription(String);

impl TitleDescription {
    pub fn new(description: impl Into<String>) -> Self {
        Self(description.into())
    }
}

impl Value<String> for TitleDescription {
    fn value(&self) -> String {
        self.0.clone()
    }
}

impl From<String> for TitleDescription {
    fn from(value: String) -> Self {
        Self(value)
    }
}

impl From<&str> for TitleDescription {
    fn from(value: &str) -> Self {
        Self(value.to_string())
    }
}

#[derive(Debug, PartialEq, Eq, PartialOrd, Ord, Clone, Default)]

pub struct TitleColor(String);

impl TitleColor {
    pub fn new(color: impl Into<String>) -> Self {
        Self(color.into())
    }
}

impl Value<String> for TitleColor {
    fn value(&self) -> String {
        self.0.clone()
    }
}

impl From<String> for TitleColor {
    fn from(value: String) -> Self {
        Self(value)
    }
}

impl From<&str> for TitleColor {
    fn from(value: &str) -> Self {
        Self(value.to_string())
    }
}

#[derive(Debug, PartialEq, Eq, PartialOrd, Ord, Clone, Default)]

pub struct TitleStrength(i32);

impl TitleStrength {
    pub fn new(strength: i32) -> Self {
        Self(strength)
    }
}

impl Value<i32> for TitleStrength {
    fn value(&self) -> i32 {
        self.0
    }
}

impl From<i32> for TitleStrength {
    fn from(value: i32) -> Self {
        Self(value)
    }
}

#[derive(Debug, PartialEq, Eq, PartialOrd, Ord, Clone, Default)]

pub struct TitleIntellect(i32);

impl TitleIntellect {
    pub fn new(intellect: i32) -> Self {
        Self(intellect)
    }
}

impl Value<i32> for TitleIntellect {
    fn value(&self) -> i32 {
        self.0
    }
}

impl From<i32> for TitleIntellect {
    fn from(value: i32) -> Self {
        Self(value)
    }
}

#[derive(Debug, PartialEq, Eq, PartialOrd, Ord, Clone, Default)]

pub struct TitleEndurance(i32);

impl TitleEndurance {
    pub fn new(endurance: i32) -> Self {
        Self(endurance)
    }
}

impl Value<i32> for TitleEndurance {
    fn value(&self) -> i32 {
        self.0
    }
}

impl From<i32> for TitleEndurance {
    fn from(value: i32) -> Self {
        Self(value)
    }
}

#[derive(Debug, PartialEq, Eq, PartialOrd, Ord, Clone, Default)]

pub struct TitleDexterity(i32);

impl TitleDexterity {
    pub fn new(dexterity: i32) -> Self {
        Self(dexterity)
    }
}

impl Value<i32> for TitleDexterity {
    fn value(&self) -> i32 {
        self.0
    }
}

impl From<i32> for TitleDexterity {
    fn from(value: i32) -> Self {
        Self(value)
    }
}

#[derive(Debug, PartialEq, Eq, PartialOrd, Ord, Clone, Default)]

pub struct TitleWisdom(i32);

impl TitleWisdom {
    pub fn new(wisdom: i32) -> Self {
        Self(wisdom)
    }
}

impl Value<i32> for TitleWisdom {
    fn value(&self) -> i32 {
        self.0
    }
}

impl From<i32> for TitleWisdom {
    fn from(value: i32) -> Self {
        Self(value)
    }
}

#[derive(Debug, PartialEq, Eq, PartialOrd, Ord, Clone, Default)]

pub struct TitleLuck(i32);

impl TitleLuck {
    pub fn new(luck: i32) -> Self {
        Self(luck)
    }
}

impl Value<i32> for TitleLuck {
    fn value(&self) -> i32 {
        self.0
    }
}

impl From<i32> for TitleLuck {
    fn from(value: i32) -> Self {
        Self(value)
    }
}
