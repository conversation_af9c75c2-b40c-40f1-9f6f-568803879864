use std::sync::Arc;

use crate::traits::BrokerServicePublisher;
use async_trait::async_trait;
use chrono::Utc;
use esrs::{
    Aggregate,
    bus::EventBus,
    manager::AggregateManager,
    store::{StoreEvent, postgres::PgStore},
};
use reforged_domain::{
    commands::user_commands::UserCommands,
    error::UserError,
    events::user_events::{ResetPasswordRequested, UserCreated, UserEvents, UserPasswordChanged},
    models::user::entity::User,
};
use tracing::error;
use uuid::Uuid;

#[derive(Debug, <PERSON><PERSON>, Default)]
pub struct UserAggregateState(pub User);

pub type UserStoreManager = AggregateManager<PgStore<UserAggregate, UserEvents>>;

#[derive(Clone)]
pub struct UserAggregate;

impl Aggregate for UserAggregate {
    const NAME: &'static str = "user";
    type State = UserAggregateState;
    type Error = UserError;
    type Event = UserEvents;
    type Command = UserCommands;

    fn handle_command(
        _state: &Self::State,
        command: Self::Command,
    ) -> Result<Vec<Self::Event>, Self::Error> {
        match command {
            UserCommands::CreateUser(cmd) => {
                println!("Creating user with ID {}", cmd.id);

                let user_created_event = UserCreated::builder()
                    .id(cmd.id)
                    .email(cmd.email)
                    .username(cmd.username)
                    .role(cmd.role)
                    .gender(cmd.gender)
                    .password(cmd.password)
                    .build();
                let ev = UserEvents::UserCreated(user_created_event);

                Ok(vec![ev])
            }
            UserCommands::RequestPasswordReset(cmd) => {
                println!("Requesting password reset for user with ID {}", cmd.id);

                let event = ResetPasswordRequested::builder()
                    .id(Uuid::now_v7()) // New unique ID for the token/event record
                    .user_id(cmd.id) // User's ID from the command
                    .email(cmd.email)
                    .reset_token(cmd.reset_token)
                    .created_at(cmd.created_at)
                    .expires_at(cmd.expires_at)
                    .build();
                let ev = UserEvents::ResetPasswordRequested(event);

                Ok(vec![ev])
            }
            UserCommands::ChangeUserPassword(cmd) => {
                println!("Changing password for user with ID {}", cmd.user_id);

                let event = UserPasswordChanged::builder()
                    .id(cmd.user_id)
                    .email(cmd.email)
                    .new_password(cmd.new_password)
                    .password_changed_at(Utc::now())
                    .reset_token_id(cmd.token_id)
                    .build();
                let ev = UserEvents::UserPasswordChanged(event);
                Ok(vec![ev])
            }
        }
    }

    fn apply_event(state: Self::State, _payload: Self::Event) -> Self::State {
        state
    }
}

pub struct UserAggregateEventBus {
    broker_service: Arc<dyn BrokerServicePublisher>,
}

impl UserAggregateEventBus {
    pub fn new(broker_service: Arc<dyn BrokerServicePublisher>) -> Self {
        Self { broker_service }
    }

    async fn publish_event_entry<E: serde::Serialize + std::fmt::Debug>(
        &self,
        event_name: &str,
        event_data: &E,
    ) {
        match serde_json::to_value(event_data) {
            Ok(json_payload) => {
                if let Err(e) = self.broker_service.publish(event_name, json_payload).await {
                    error!(
                        "Failed to publish {} event to broker: {:?}. Event data: {:?}",
                        event_name, e, event_data
                    );
                }
            }
            Err(e) => {
                error!(
                    "Failed to serialize {} event: {:?}. Event data: {:?}",
                    event_name, e, event_data
                );
            }
        }
    }
}

#[async_trait]
impl EventBus<UserAggregate> for UserAggregateEventBus {
    async fn publish(&self, store_event: &StoreEvent<UserEvents>) {
        match store_event.payload() {
            UserEvents::UserCreated(ev_data) => {
                println!("Publishing UserCreated event");
                self.publish_event_entry("UserCreated", ev_data).await;
            }
            UserEvents::UserPasswordChanged(ev_data) => {
                println!("Publishing UserPasswordChanged event");
                self.publish_event_entry("UserPasswordChanged", ev_data)
                    .await;
            }
            UserEvents::ResetPasswordRequested(ev_data) => {
                println!("Publishing ResetPasswordRequested event");
                self.publish_event_entry("ResetPasswordRequested", ev_data)
                    .await;
            }
        }
    }
}
