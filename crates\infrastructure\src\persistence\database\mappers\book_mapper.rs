use reforged_domain::models::book::entity::Book;
use reforged_domain::models::book::value_object::{
    BookDesc, BookField, BookFile, BookHide, BookId, BookIndex, BookLabel, BookLinkage, BookLock,
    BookMap, BookName, BookType, BookValue,
};
use reforged_domain::models::shop::value_object::ShopId;

#[derive(Debug)]
pub struct BookDbModelMapper(super::super::models::books::Model);

impl BookDbModelMapper {
    pub fn new(book: super::super::models::books::Model) -> Self {
        Self(book)
    }
}

impl From<BookDbModelMapper> for Book {
    fn from(value: BookDbModelMapper) -> Self {
        let book_model = value.0;

        let id = BookId::new(book_model.id);
        let file = BookFile::from(book_model.file);
        let name = BookName::from(book_model.name);
        let linkage = BookLinkage::from(book_model.linkage);
        let lock = BookLock::from(book_model.lock);
        let desc = BookDesc::from(book_model.desc);
        let map = BookMap::from(book_model.map);
        let book_type = BookType::from(book_model.r#type);
        let hide = BookHide::new(book_model.hide);
        let label = BookLabel::from(book_model.label);
        let shop = ShopId::new(book_model.shop_id);
        let field = BookField::from(book_model.field);
        let index = BookIndex::new(book_model.index);
        let value = BookValue::new(book_model.value);

        Book::builder()
            .id(id)
            .file(file)
            .name(name)
            .linkage(linkage)
            .lock(lock)
            .desc(desc)
            .map(map)
            .book_type(book_type)
            .hide(hide)
            .label(label)
            .shop_id(shop)
            .field(field)
            .index(index)
            .value(value)
            .build()
    }
}
