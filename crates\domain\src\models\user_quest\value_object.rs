use reforged_shared::{UuidId, Value};

use super::entity::UserQuest;

pub type UserQuestId = UuidId<UserQuest>;

#[derive(Debug, PartialEq, Eq, PartialOrd, Ord, Clone, Default)]
pub struct UserQuestQuests1(String);

impl UserQuestQuests1 {
    pub fn new(quests1: impl Into<String>) -> Self {
        Self(quests1.into())
    }
}

impl Value<String> for UserQuestQuests1 {
    fn value(&self) -> String {
        self.0.clone()
    }
}

impl From<String> for UserQuestQuests1 {
    fn from(value: String) -> Self {
        Self(value)
    }
}

impl From<&str> for UserQuestQuests1 {
    fn from(value: &str) -> Self {
        Self(value.to_string())
    }
}

#[derive(Debug, PartialEq, Eq, PartialOrd, Ord, Clone, Default)]

pub struct UserQuestQuests2(String);

impl UserQuestQuests2 {
    pub fn new(quests2: impl Into<String>) -> Self {
        Self(quests2.into())
    }
}

impl Value<String> for UserQuestQuests2 {
    fn value(&self) -> String {
        self.0.clone()
    }
}

impl From<String> for UserQuestQuests2 {
    fn from(value: String) -> Self {
        Self(value)
    }
}

impl From<&str> for UserQuestQuests2 {
    fn from(value: &str) -> Self {
        Self(value.to_string())
    }
}

#[derive(Debug, PartialEq, Eq, PartialOrd, Ord, Clone, Default)]

pub struct UserQuestQuests3(String);

impl UserQuestQuests3 {
    pub fn new(quests3: impl Into<String>) -> Self {
        Self(quests3.into())
    }
}

impl Value<String> for UserQuestQuests3 {
    fn value(&self) -> String {
        self.0.clone()
    }
}

impl From<String> for UserQuestQuests3 {
    fn from(value: String) -> Self {
        Self(value)
    }
}

impl From<&str> for UserQuestQuests3 {
    fn from(value: &str) -> Self {
        Self(value.to_string())
    }
}

#[derive(Debug, PartialEq, Eq, PartialOrd, Ord, Clone, Default)]

pub struct UserQuestQuests4(String);

impl UserQuestQuests4 {
    pub fn new(quests4: impl Into<String>) -> Self {
        Self(quests4.into())
    }
}

impl Value<String> for UserQuestQuests4 {
    fn value(&self) -> String {
        self.0.clone()
    }
}

impl From<String> for UserQuestQuests4 {
    fn from(value: String) -> Self {
        Self(value)
    }
}

impl From<&str> for UserQuestQuests4 {
    fn from(value: &str) -> Self {
        Self(value.to_string())
    }
}

#[derive(Debug, PartialEq, Eq, PartialOrd, Ord, Clone, Default)]

pub struct UserQuestQuests5(String);

impl UserQuestQuests5 {
    pub fn new(quests5: impl Into<String>) -> Self {
        Self(quests5.into())
    }
}

impl Value<String> for UserQuestQuests5 {
    fn value(&self) -> String {
        self.0.clone()
    }
}

impl From<String> for UserQuestQuests5 {
    fn from(value: String) -> Self {
        Self(value)
    }
}

impl From<&str> for UserQuestQuests5 {
    fn from(value: &str) -> Self {
        Self(value.to_string())
    }
}

#[derive(Debug, PartialEq, Eq, PartialOrd, Ord, Clone, Default)]

pub struct UserQuestDailyQuests0(i32);

impl UserQuestDailyQuests0 {
    pub fn new(daily_quests0: i32) -> Self {
        Self(daily_quests0)
    }
}

impl Value<i32> for UserQuestDailyQuests0 {
    fn value(&self) -> i32 {
        self.0
    }
}

impl From<i32> for UserQuestDailyQuests0 {
    fn from(value: i32) -> Self {
        Self(value)
    }
}

#[derive(Debug, PartialEq, Eq, PartialOrd, Ord, Clone, Default)]

pub struct UserQuestDailyQuests1(i32);

impl UserQuestDailyQuests1 {
    pub fn new(daily_quests1: i32) -> Self {
        Self(daily_quests1)
    }
}

impl Value<i32> for UserQuestDailyQuests1 {
    fn value(&self) -> i32 {
        self.0
    }
}

impl From<i32> for UserQuestDailyQuests1 {
    fn from(value: i32) -> Self {
        Self(value)
    }
}

#[derive(Debug, PartialEq, Eq, PartialOrd, Ord, Clone, Default)]

pub struct UserQuestDailyQuests2(i32);

impl UserQuestDailyQuests2 {
    pub fn new(daily_quests2: i32) -> Self {
        Self(daily_quests2)
    }
}

impl Value<i32> for UserQuestDailyQuests2 {
    fn value(&self) -> i32 {
        self.0
    }
}

impl From<i32> for UserQuestDailyQuests2 {
    fn from(value: i32) -> Self {
        Self(value)
    }
}

#[derive(Debug, PartialEq, Eq, PartialOrd, Ord, Clone, Default)]

pub struct UserQuestMonthlyQuests0(i32);

impl UserQuestMonthlyQuests0 {
    pub fn new(monthly_quests0: i32) -> Self {
        Self(monthly_quests0)
    }
}

impl Value<i32> for UserQuestMonthlyQuests0 {
    fn value(&self) -> i32 {
        self.0
    }
}

impl From<i32> for UserQuestMonthlyQuests0 {
    fn from(value: i32) -> Self {
        Self(value)
    }
}

#[derive(Debug, PartialEq, Eq, PartialOrd, Ord, Clone, Default)]

pub struct UserQuestDailyAds(i32);

impl UserQuestDailyAds {
    pub fn new(daily_ads: i32) -> Self {
        Self(daily_ads)
    }
}

impl Value<i32> for UserQuestDailyAds {
    fn value(&self) -> i32 {
        self.0
    }
}

impl From<i32> for UserQuestDailyAds {
    fn from(value: i32) -> Self {
        Self(value)
    }
}
