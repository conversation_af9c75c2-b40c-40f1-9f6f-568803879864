use crate::models::item::value_object::ItemId;
use crate::models::item_effect::value_object::*;
use getset::{<PERSON><PERSON>, Set<PERSON>};

#[derive(Debug, <PERSON><PERSON>, Default, bon::<PERSON><PERSON><PERSON>, <PERSON><PERSON>, Setters)]
#[getset(get = "pub", set = "pub")]
pub struct ItemEffect {
    pub id: ItemEffectId,
    pub item_id: ItemId,
    pub damage_increase: ItemEffectDamageIncrease,
    pub damage_taken: ItemEffectDamageTaken,
    pub exp: ItemEffectExp,
    pub gold: ItemEffectGold,
    pub coins: ItemEffectCoins,
    pub class_point: ItemEffectClassPoint,
    pub reputation: ItemEffectReputation,
}

impl ItemEffect {
    pub fn new(
        id: ItemEffectId,
        item_id: ItemId,
        damage_increase: ItemEffectDamageIncrease,
        damage_taken: ItemEffectDamageTaken,
        exp: ItemEffectExp,
        gold: ItemEffectGold,
        coins: ItemEffectCoins,
        class_point: ItemEffectClassPoint,
        reputation: ItemEffectReputation,
    ) -> Self {
        Self {
            id,
            item_id,
            damage_increase,
            damage_taken,
            exp,
            gold,
            coins,
            class_point,
            reputation,
        }
    }
}

#[cfg(test)]
mod tests {
    use reforged_shared::{IdTrait, Value};

    use super::*;

    #[test]
    fn test_item_effect_new() {
        let id = ItemEffectId::new(uuid::Uuid::now_v7());
        let item_id = ItemId::new(uuid::Uuid::now_v7());
        let damage_increase = ItemEffectDamageIncrease::new(10.5);
        let damage_taken = ItemEffectDamageTaken::new(-5.0);
        let exp = ItemEffectExp::new(20.0);
        let gold = ItemEffectGold::new(15.0);
        let coins = ItemEffectCoins::new(5.0);
        let class_point = ItemEffectClassPoint::new(10.0);
        let reputation = ItemEffectReputation::new(25.0);

        let effect = ItemEffect::new(
            id.clone(),
            item_id.clone(),
            damage_increase.clone(),
            damage_taken.clone(),
            exp.clone(),
            gold.clone(),
            coins.clone(),
            class_point.clone(),
            reputation.clone(),
        );

        assert_eq!(effect.id.get_id(), id.get_id());
        assert_eq!(effect.item_id.get_id(), item_id.get_id());
        assert_eq!(effect.damage_increase.value(), damage_increase.value());
        assert_eq!(effect.damage_taken.value(), damage_taken.value());
        assert_eq!(effect.exp.value(), exp.value());
        assert_eq!(effect.gold.value(), gold.value());
        assert_eq!(effect.coins.value(), coins.value());
        assert_eq!(effect.class_point.value(), class_point.value());
        assert_eq!(effect.reputation.value(), reputation.value());
    }

    #[test]
    fn test_item_effect_builder() {
        let id = ItemEffectId::new(uuid::Uuid::now_v7());
        let item_id = ItemId::new(uuid::Uuid::now_v7());
        let damage_increase = ItemEffectDamageIncrease::new(10.5);
        let damage_taken = ItemEffectDamageTaken::new(-5.0);
        let exp = ItemEffectExp::new(20.0);
        let gold = ItemEffectGold::new(15.0);
        let coins = ItemEffectCoins::new(5.0);
        let class_point = ItemEffectClassPoint::new(10.0);
        let reputation = ItemEffectReputation::new(25.0);

        let effect = ItemEffect::builder()
            .id(id.clone())
            .item_id(item_id.clone())
            .damage_increase(damage_increase.clone())
            .damage_taken(damage_taken.clone())
            .exp(exp.clone())
            .gold(gold.clone())
            .coins(coins.clone())
            .class_point(class_point.clone())
            .reputation(reputation.clone())
            .build();

        assert_eq!(effect.id.get_id(), id.get_id());
        assert_eq!(effect.item_id.get_id(), item_id.get_id());
        assert_eq!(effect.damage_increase.value(), damage_increase.value());
        assert_eq!(effect.damage_taken.value(), damage_taken.value());
        assert_eq!(effect.exp.value(), exp.value());
        assert_eq!(effect.gold.value(), gold.value());
        assert_eq!(effect.coins.value(), coins.value());
        assert_eq!(effect.class_point.value(), class_point.value());
        assert_eq!(effect.reputation.value(), reputation.value());
    }

    #[test]
    fn test_getters_and_setters() {
        let mut effect = ItemEffect::default();

        let id = ItemEffectId::new(uuid::Uuid::now_v7());
        let item_id = ItemId::new(uuid::Uuid::now_v7());
        let damage_increase = ItemEffectDamageIncrease::new(10.5);
        let damage_taken = ItemEffectDamageTaken::new(-5.0);
        let exp = ItemEffectExp::new(20.0);
        let gold = ItemEffectGold::new(15.0);
        let coins = ItemEffectCoins::new(5.0);
        let class_point = ItemEffectClassPoint::new(10.0);
        let reputation = ItemEffectReputation::new(25.0);

        effect.set_id(id.clone());
        effect.set_item_id(item_id.clone());
        effect.set_damage_increase(damage_increase.clone());
        effect.set_damage_taken(damage_taken.clone());
        effect.set_exp(exp.clone());
        effect.set_gold(gold.clone());
        effect.set_coins(coins.clone());
        effect.set_class_point(class_point.clone());
        effect.set_reputation(reputation.clone());

        assert_eq!(effect.id().get_id(), id.get_id());
        assert_eq!(effect.item_id().get_id(), item_id.get_id());
        assert_eq!(effect.damage_increase().value(), damage_increase.value());
        assert_eq!(effect.damage_taken().value(), damage_taken.value());
        assert_eq!(effect.exp().value(), exp.value());
        assert_eq!(effect.gold().value(), gold.value());
        assert_eq!(effect.coins().value(), coins.value());
        assert_eq!(effect.class_point().value(), class_point.value());
        assert_eq!(effect.reputation().value(), reputation.value());
    }
}
