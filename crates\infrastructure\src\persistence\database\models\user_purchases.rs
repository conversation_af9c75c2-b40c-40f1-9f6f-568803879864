//! `SeaORM` Entity, @generated by sea-orm-codegen 1.1.0

use sea_orm::entity::prelude::*;
use serde::Deserialize;

#[derive(Clone, Debug, PartialEq, DeriveEntityModel, Eq, Deserialize)]
#[sea_orm(table_name = "user_purchases")]
pub struct Model {
    #[sea_orm(primary_key, auto_increment = false)]
    pub id: Uuid,
    pub user_id: Uuid,
    pub payment_id: String,
    pub transaction_id: Option<String>,
    pub email: Option<String>,
    pub hash: String,
    pub item: String,
    pub item_id: Uuid,
    pub price: String,
    pub method: String,
    pub currency: String,
    pub purchased: i16,
    pub date: DateTime,
    pub broadcast: bool,
}

#[derive(Co<PERSON>, <PERSON><PERSON>, Debug, EnumIter, DeriveRelation)]
pub enum Relation {
    #[sea_orm(
        belongs_to = "super::stores::Entity",
        from = "Column::ItemId",
        to = "super::stores::Column::Id",
        on_update = "Cascade",
        on_delete = "Cascade"
    )]
    Stores,
    #[sea_orm(
        belongs_to = "super::users::Entity",
        from = "Column::UserId",
        to = "super::users::Column::Id",
        on_update = "Cascade",
        on_delete = "Cascade"
    )]
    Users,
}

impl Related<super::stores::Entity> for Entity {
    fn to() -> RelationDef {
        Relation::Stores.def()
    }
}

impl Related<super::users::Entity> for Entity {
    fn to() -> RelationDef {
        Relation::Users.def()
    }
}

impl ActiveModelBehavior for ActiveModel {}
