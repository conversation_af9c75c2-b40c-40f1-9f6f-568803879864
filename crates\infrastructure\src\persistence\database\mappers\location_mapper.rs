use super::super::models::sea_orm_active_enums::LoginLocation;
use reforged_domain::models::game_setting::value_object::Location;

#[derive(Debug)]
pub struct LoginLocationMapper(LoginLocation);

impl LoginLocationMapper {
    pub fn new(location: LoginLocation) -> Self {
        LoginLocationMapper(location)
    }
}

impl From<LoginLocationMapper> for Location {
    fn from(mapper: LoginLocationMapper) -> Self {
        match mapper.0 {
            LoginLocation::Game => Location::Game,
            LoginLocation::Wiki => Location::Wiki,
            LoginLocation::Loader => Location::Loader,
        }
    }
}
