use reforged_shared::{UuidId, Value};

use super::entity::DiscordCommand;

pub type DiscordCommandId = UuidId<DiscordCommand>;

#[derive(Debug, PartialEq, Eq, PartialOrd, Ord, Clone, Default)]

pub struct Command(String);

impl Command {
    pub fn new(command: impl Into<String>) -> Self {
        Self(command.into())
    }
}

impl Value<String> for Command {
    fn value(&self) -> String {
        self.0.clone()
    }
}

impl From<String> for Command {
    fn from(value: String) -> Self {
        Self(value)
    }
}

impl From<&str> for Command {
    fn from(value: &str) -> Self {
        Self(value.to_string())
    }
}
