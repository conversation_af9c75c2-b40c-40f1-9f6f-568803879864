use reforged_shared::{UuidId, Value};

use super::entity::UserCurrency;

pub type UserCurrencyId = UuidId<UserCurrency>;

#[derive(Debug, PartialEq, Eq, PartialOrd, Ord, Clone, Default)]
pub struct Gold(i64);

impl Gold {
    pub fn new(value: i64) -> Self {
        Self(value)
    }
}

impl Value<i64> for Gold {
    fn value(&self) -> i64 {
        self.0
    }
}

impl From<i64> for Gold {
    fn from(value: i64) -> Self {
        Self(value)
    }
}

#[derive(Debug, PartialEq, Eq, PartialOrd, Ord, Clone, Default)]
pub struct Coins(i64);

impl Coins {
    pub fn new(value: i64) -> Self {
        Self(value)
    }
}

impl Value<i64> for Coins {
    fn value(&self) -> i64 {
        self.0
    }
}

impl From<i64> for Coins {
    fn from(value: i64) -> Self {
        Self(value)
    }
}

#[derive(Debug, PartialEq, Eq, <PERSON>ialO<PERSON>, Or<PERSON>, <PERSON><PERSON>, De<PERSON>ult)]
pub struct Diamonds(i64);

impl Diamonds {
    pub fn new(value: i64) -> Self {
        Self(value)
    }
}

impl Value<i64> for Diamonds {
    fn value(&self) -> i64 {
        self.0
    }
}

impl From<i64> for Diamonds {
    fn from(value: i64) -> Self {
        Self(value)
    }
}

#[derive(Debug, PartialEq, Eq, PartialOrd, Ord, Clone, Default)]
pub struct Crystal(i64);

impl Crystal {
    pub fn new(value: i64) -> Self {
        Self(value)
    }
}

impl Value<i64> for Crystal {
    fn value(&self) -> i64 {
        self.0
    }
}

impl From<i64> for Crystal {
    fn from(value: i64) -> Self {
        Self(value)
    }
}