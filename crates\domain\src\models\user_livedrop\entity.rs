use getset::{<PERSON><PERSON>, <PERSON><PERSON>};

use crate::models::{
    achievement::value_object::AchievementId, item::value_object::ItemId,
    title::value_object::TitleId, user::value_object::UserId,
};

use super::value_object::{
    LivedropBagSlots, LivedropBankSlots, LivedropCoins, LivedropCrystal, LivedropDate,
    LivedropExperience, LivedropGold, LivedropHouseSlots, LivedropQuantity, Message, Sent,
    UpgradeDays, UserLivedropId,
};

#[derive(Debug, <PERSON><PERSON>, Default, bon::<PERSON>uild<PERSON>, Get<PERSON>, Setters)]
#[getset(get = "pub", set = "pub")]
pub struct UserLivedrop {
    id: UserLivedropId,
    user_id: Option<UserId>,
    item_id: Option<ItemId>,
    quantity: LivedropQuantity,
    sent: Sent,
    date: LivedropDate,
    message: Message,
    achievement_id: Option<AchievementId>,
    title_id: Option<TitleId>,
    experience: LivedropExperience,
    gold: LivedropGold,
    coins: LivedropCoins,
    crystal: LivedropCrystal,
    upgrade_days: UpgradeDays,
    bag_slots: LivedropBagSlots,
    bank_slots: LivedropBankSlots,
    house_slots: LivedropHouseSlots,
}

impl UserLivedrop {
    #[allow(clippy::too_many_arguments)]
    pub fn new(
        id: UserLivedropId,
        user_id: Option<UserId>,
        item_id: Option<ItemId>,
        quantity: LivedropQuantity,
        sent: Sent,
        date: LivedropDate,
        message: Message,
        achievement_id: Option<AchievementId>,
        title_id: Option<TitleId>,
        experience: LivedropExperience,
        gold: LivedropGold,
        coins: LivedropCoins,
        crystal: LivedropCrystal,
        upgrade_days: UpgradeDays,
        bag_slots: LivedropBagSlots,
        bank_slots: LivedropBankSlots,
        house_slots: LivedropHouseSlots,
    ) -> Self {
        Self {
            id,
            user_id,
            item_id,
            quantity,
            sent,
            date,
            message,
            achievement_id,
            title_id,
            experience,
            gold,
            coins,
            crystal,
            upgrade_days,
            bag_slots,
            bank_slots,
            house_slots,
        }
    }
}

#[cfg(test)]
mod tests {
    use chrono::Utc;
    use reforged_shared::{IdTrait, Value};

    use super::*;

    #[test]
    fn test_user_livedrop_new() {
        let id = UserLivedropId::new(uuid::Uuid::now_v7());
        let user_id = Some(UserId::new(uuid::Uuid::now_v7()));
        let item_id = Some(ItemId::new(uuid::Uuid::now_v7()));
        let quantity = LivedropQuantity::new(Some(5));
        let sent = Sent::new(1);
        let date = LivedropDate::new(Utc::now());
        let message = Message::new(Some("Congratulations!".to_string()));
        let achievement_id = None;
        let title_id = None;
        let experience = LivedropExperience::new(1000);
        let gold = LivedropGold::new(500);
        let coins = LivedropCoins::new(100);
        let crystal = LivedropCrystal::new(50);
        let upgrade_days = UpgradeDays::new(7);
        let bag_slots = LivedropBagSlots::new(5);
        let bank_slots = LivedropBankSlots::new(10);
        let house_slots = LivedropHouseSlots::new(3);

        let user_livedrop = UserLivedrop::new(
            id.clone(),
            user_id.clone(),
            item_id.clone(),
            quantity.clone(),
            sent.clone(),
            date.clone(),
            message.clone(),
            achievement_id.clone(),
            title_id.clone(),
            experience.clone(),
            gold.clone(),
            coins.clone(),
            crystal.clone(),
            upgrade_days.clone(),
            bag_slots.clone(),
            bank_slots.clone(),
            house_slots.clone(),
        );

        assert_eq!(user_livedrop.id().get_id(), id.get_id());
        assert_eq!(user_livedrop.quantity().value(), Some(5));
        assert_eq!(user_livedrop.sent().value(), 1);
        assert_eq!(user_livedrop.experience().value(), 1000);
        assert_eq!(user_livedrop.gold().value(), 500);
        assert_eq!(user_livedrop.coins().value(), 100);
        assert_eq!(user_livedrop.crystal().value(), 50);
        assert_eq!(user_livedrop.upgrade_days().value(), 7);
        assert_eq!(user_livedrop.bag_slots().value(), 5);
        assert_eq!(user_livedrop.bank_slots().value(), 10);
        assert_eq!(user_livedrop.house_slots().value(), 3);
    }
}