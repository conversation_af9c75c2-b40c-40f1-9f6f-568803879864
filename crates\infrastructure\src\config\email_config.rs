use super::{Config, ConfigError};

pub struct EmailConfig {
    pub host: String,
    pub port: u16,
    pub username: String,
    pub password: String,
}

impl Config for EmailConfig {
    fn from_env() -> Result<Self, ConfigError> {
        let host = std::env::var("EMAIL_HOST")
            .map_err(|_| ConfigError::EnvVarNotFound("EMAIL_HOST".to_string()))?;
        let port = std::env::var("EMAIL_PORT")
            .map_err(|_| ConfigError::EnvVarNotFound("EMAIL_PORT".to_string()))?
            .parse::<u16>()
            .map_err(|_| ConfigError::EnvVarNotValid("EMAIL_PORT".to_string()))?;

        let username = std::env::var("EMAIL_USERNAME")
            .map_err(|_| ConfigError::EnvVarNotFound("EMAIL_USERNAME".to_string()))?;
        let password = std::env::var("EMAIL_PASSWORD")
            .map_err(|_| ConfigError::EnvVarNotFound("EMAIL_PASSWORD".to_string()))?;

        Ok(Self {
            host,
            port,
            username,
            password,
        })
    }
}
