use async_trait::async_trait;

use crate::models::user::value_object::HashedPassword;

#[derive(Debug)]
pub enum HashError {
    HashingFailed,
    PasswordMismatch,
}

#[async_trait]
pub trait PasswordHasher: Send + Sync {
    async fn hash(&self, plain_password: &str) -> Result<HashedPassword, HashError>;
    async fn verify(&self, plain_password: &str, hashed_password: &str) -> Result<(), HashError>;
}
