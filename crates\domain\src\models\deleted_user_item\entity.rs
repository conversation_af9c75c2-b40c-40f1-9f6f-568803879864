use crate::models::{item::value_object::ItemId, user::value_object::UserId};

use super::value_object::{DeletedDate, DeletedUserItemId, Quantity};
use getset::{<PERSON><PERSON>, Set<PERSON>};

#[derive(<PERSON>bu<PERSON>, <PERSON><PERSON>, De<PERSON>ult, bon::<PERSON><PERSON><PERSON>, <PERSON><PERSON>, Setters)]
#[getset(get = "pub", set = "pub")]
pub struct DeletedUserItem {
    id: DeletedUserItemId,
    user_id: UserId,
    item_id: ItemId,
    quantity: Quantity,
    date: DeletedDate,
}

impl DeletedUserItem {
    pub fn new(
        id: DeletedUserItemId,
        user_id: UserId,
        item_id: ItemId,
        quantity: Quantity,
        date: DeletedDate,
    ) -> Self {
        Self {
            id,
            user_id,
            item_id,
            quantity,
            date,
        }
    }
}

#[cfg(test)]
mod tests {
    use chrono::Utc;
    use reforged_shared::{IdTrait, Value};

    use super::*;

    #[test]
    fn test_deleted_user_item_new() {
        let id = DeletedUserItemId::new(uuid::Uuid::now_v7());
        let user_id = UserId::new(uuid::Uuid::now_v7());
        let item_id = ItemId::new(uuid::Uuid::now_v7());
        let quantity = Quantity::new(5);
        let date = DeletedDate::new(Utc::now());

        let deleted_user_item = DeletedUserItem::new(
            id.clone(),
            user_id.clone(),
            item_id.clone(),
            quantity.clone(),
            date.clone(),
        );

        assert_eq!(deleted_user_item.id().get_id(), id.get_id());
        assert_eq!(deleted_user_item.user_id().get_id(), user_id.get_id());
        assert_eq!(deleted_user_item.item_id().get_id(), item_id.get_id());
        assert_eq!(deleted_user_item.quantity().value(), quantity.value());
        assert_eq!(deleted_user_item.date().value(), date.value());
    }

    #[test]
    fn test_deleted_user_item_builder() {
        let id = DeletedUserItemId::new(uuid::Uuid::now_v7());
        let user_id = UserId::new(uuid::Uuid::now_v7());
        let item_id = ItemId::new(uuid::Uuid::now_v7());
        let quantity = Quantity::new(5);
        let date = DeletedDate::new(Utc::now());

        let deleted_user_item = DeletedUserItem::builder()
            .id(id.clone())
            .user_id(user_id.clone())
            .item_id(item_id.clone())
            .quantity(quantity.clone())
            .date(date.clone())
            .build();

        assert_eq!(deleted_user_item.id().get_id(), id.get_id());
        assert_eq!(deleted_user_item.user_id().get_id(), user_id.get_id());
        assert_eq!(deleted_user_item.item_id().get_id(), item_id.get_id());
        assert_eq!(deleted_user_item.quantity().value(), quantity.value());
        assert_eq!(deleted_user_item.date().value(), date.value());
    }

    #[test]
    fn test_getters_and_setters() {
        let mut deleted_user_item = DeletedUserItem::default();

        let id = DeletedUserItemId::new(uuid::Uuid::now_v7());
        let user_id = UserId::new(uuid::Uuid::now_v7());
        let item_id = ItemId::new(uuid::Uuid::now_v7());
        let quantity = Quantity::new(5);
        let date = DeletedDate::new(Utc::now());

        deleted_user_item.set_id(id.clone());
        deleted_user_item.set_user_id(user_id.clone());
        deleted_user_item.set_item_id(item_id.clone());
        deleted_user_item.set_quantity(quantity.clone());
        deleted_user_item.set_date(date.clone());

        assert_eq!(deleted_user_item.id().get_id(), id.get_id());
        assert_eq!(deleted_user_item.user_id().get_id(), user_id.get_id());
        assert_eq!(deleted_user_item.item_id().get_id(), item_id.get_id());
        assert_eq!(deleted_user_item.quantity().value(), quantity.value());
        assert_eq!(deleted_user_item.date().value(), date.value());
    }
}
