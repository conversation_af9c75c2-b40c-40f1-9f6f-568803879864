use reforged_shared::{UuidId, Value};

use super::entity::Hair;

pub type HairId = UuidId<Hair>;

#[derive(Debug, PartialEq, Eq, PartialOrd, Ord, <PERSON>lone, De<PERSON>ult)]

pub struct HairName(String);

impl HairName {
    pub fn new(name: impl Into<String>) -> Self {
        Self(name.into())
    }
}

impl Value<String> for HairName {
    fn value(&self) -> String {
        self.0.clone()
    }
}

impl From<String> for HairName {
    fn from(value: String) -> Self {
        Self(value)
    }
}

impl From<&str> for HairName {
    fn from(value: &str) -> Self {
        Self(value.to_string())
    }
}
