use reforged_domain::models::profile::value_object::Gender;

impl From<super::super::models::sea_orm_active_enums::Gender> for Gender {
    fn from(value: super::super::models::sea_orm_active_enums::Gender) -> Self {
        match value {
            super::super::models::sea_orm_active_enums::Gender::M => Gender::Male,
            super::super::models::sea_orm_active_enums::Gender::F => Gender::Female,
        }
    }
}
