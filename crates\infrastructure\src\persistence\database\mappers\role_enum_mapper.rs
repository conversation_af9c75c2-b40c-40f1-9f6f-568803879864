use reforged_domain::models::role::value_object::UserRoleTag;

use crate::models::sea_orm_active_enums::UserRole;

pub struct UserRoleMapper(UserRole);

impl UserRoleMapper {
    pub fn new(role: UserRole) -> Self {
        UserRoleMapper(role)
    }
}

impl From<UserRoleMapper> for UserRoleTag {
    fn from(value: UserRoleMapper) -> Self {
        match value.0 {
            UserRole::Banned => UserRoleTag::Banned,
            UserRole::Player => UserRoleTag::Player,
            UserRole::Founder => UserRoleTag::Founder,
            UserRole::Support => UserRoleTag::Support,
            UserRole::Vip => UserRoleTag::VIP,
            UserRole::Moderator => UserRoleTag::Moderator,
            UserRole::Trainee => UserRoleTag::Trainee,
            UserRole::Administrator => UserRoleTag::Administrator,
        }
    }
}
