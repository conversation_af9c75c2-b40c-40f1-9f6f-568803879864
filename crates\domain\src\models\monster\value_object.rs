use reforged_shared::{UuidId, Value};

use super::entity::Monster;

pub type MonsterId = UuidId<Monster>;

#[derive(Debug, PartialEq, Eq, PartialOrd, Ord, Clone, Default)]

pub struct MonsterName(String);

impl MonsterName {
    pub fn new(name: impl Into<String>) -> Self {
        Self(name.into())
    }
}

impl Value<String> for MonsterName {
    fn value(&self) -> String {
        self.0.clone()
    }
}

impl From<String> for MonsterName {
    fn from(value: String) -> Self {
        Self(value)
    }
}

impl From<&str> for MonsterName {
    fn from(value: &str) -> Self {
        Self(value.to_string())
    }
}

#[derive(Debug, PartialEq, Eq, PartialOrd, Ord, Clone, Default)]

pub struct MonsterRace(String);

impl MonsterRace {
    pub fn new(race: impl Into<String>) -> Self {
        Self(race.into())
    }
}

impl Value<String> for MonsterRace {
    fn value(&self) -> String {
        self.0.clone()
    }
}

impl From<String> for MonsterRace {
    fn from(value: String) -> Self {
        Self(value)
    }
}

impl From<&str> for MonsterRace {
    fn from(value: &str) -> Self {
        Self(value.to_string())
    }
}

#[derive(Debug, PartialEq, Eq, PartialOrd, Ord, Clone, Default)]

pub struct MonsterFile(String);

impl MonsterFile {
    pub fn new(file: impl Into<String>) -> Self {
        Self(file.into())
    }
}

impl Value<String> for MonsterFile {
    fn value(&self) -> String {
        self.0.clone()
    }
}

impl From<String> for MonsterFile {
    fn from(value: String) -> Self {
        Self(value)
    }
}

impl From<&str> for MonsterFile {
    fn from(value: &str) -> Self {
        Self(value.to_string())
    }
}

#[derive(Debug, PartialEq, Eq, PartialOrd, Ord, Clone, Default)]

pub struct MonsterHealth(i32);

impl MonsterHealth {
    pub fn new(health: i32) -> Self {
        Self(health)
    }
}

impl Value<i32> for MonsterHealth {
    fn value(&self) -> i32 {
        self.0
    }
}

impl From<i32> for MonsterHealth {
    fn from(value: i32) -> Self {
        Self(value)
    }
}

#[derive(Debug, PartialEq, Eq, PartialOrd, Ord, Clone, Default)]

pub struct MonsterMana(i32);

impl MonsterMana {
    pub fn new(mana: i32) -> Self {
        Self(mana)
    }
}

impl Value<i32> for MonsterMana {
    fn value(&self) -> i32 {
        self.0
    }
}

impl From<i32> for MonsterMana {
    fn from(value: i32) -> Self {
        Self(value)
    }
}

#[derive(Debug, PartialEq, Eq, PartialOrd, Ord, Clone, Default)]

pub struct MonsterLevel(i16);

impl MonsterLevel {
    pub fn new(level: i16) -> Self {
        Self(level)
    }
}

impl Value<i16> for MonsterLevel {
    fn value(&self) -> i16 {
        self.0
    }
}

impl From<i16> for MonsterLevel {
    fn from(value: i16) -> Self {
        Self(value)
    }
}

#[derive(Debug, PartialEq, Eq, PartialOrd, Ord, Clone, Default)]

pub struct MonsterGold(i32);

impl MonsterGold {
    pub fn new(gold: i32) -> Self {
        Self(gold)
    }
}

impl Value<i32> for MonsterGold {
    fn value(&self) -> i32 {
        self.0
    }
}

impl From<i32> for MonsterGold {
    fn from(value: i32) -> Self {
        Self(value)
    }
}

#[derive(Debug, PartialEq, Eq, PartialOrd, Ord, Clone, Default)]

pub struct MonsterCoins(i32);

impl MonsterCoins {
    pub fn new(coins: i32) -> Self {
        Self(coins)
    }
}

impl Value<i32> for MonsterCoins {
    fn value(&self) -> i32 {
        self.0
    }
}

impl From<i32> for MonsterCoins {
    fn from(value: i32) -> Self {
        Self(value)
    }
}

#[derive(Debug, PartialEq, Eq, PartialOrd, Ord, Clone, Default)]

pub struct MonsterExperience(i32);

impl MonsterExperience {
    pub fn new(experience: i32) -> Self {
        Self(experience)
    }
}

impl Value<i32> for MonsterExperience {
    fn value(&self) -> i32 {
        self.0
    }
}

impl From<i32> for MonsterExperience {
    fn from(value: i32) -> Self {
        Self(value)
    }
}

#[derive(Debug, PartialEq, Eq, PartialOrd, Ord, Clone, Default)]

pub struct MonsterReputation(i32);

impl MonsterReputation {
    pub fn new(reputation: i32) -> Self {
        Self(reputation)
    }
}

impl Value<i32> for MonsterReputation {
    fn value(&self) -> i32 {
        self.0
    }
}

impl From<i32> for MonsterReputation {
    fn from(value: i32) -> Self {
        Self(value)
    }
}

#[derive(Debug, PartialEq, Eq, PartialOrd, Ord, Clone, Default)]

pub struct MonsterDPS(i32);

impl MonsterDPS {
    pub fn new(dps: i32) -> Self {
        Self(dps)
    }
}

impl Value<i32> for MonsterDPS {
    fn value(&self) -> i32 {
        self.0
    }
}

impl From<i32> for MonsterDPS {
    fn from(value: i32) -> Self {
        Self(value)
    }
}

#[derive(Debug, PartialEq, Eq, PartialOrd, Ord, Clone, Default)]

pub struct MonsterSpeed(i32);

impl MonsterSpeed {
    pub fn new(speed: i32) -> Self {
        Self(speed)
    }
}

impl Value<i32> for MonsterSpeed {
    fn value(&self) -> i32 {
        self.0
    }
}

impl From<i32> for MonsterSpeed {
    fn from(value: i32) -> Self {
        Self(value)
    }
}

#[derive(Debug, PartialEq, Eq, PartialOrd, Ord, Clone, Default)]

pub struct MonsterElement(String);

impl MonsterElement {
    pub fn new(element: impl Into<String>) -> Self {
        Self(element.into())
    }
}

impl Value<String> for MonsterElement {
    fn value(&self) -> String {
        self.0.clone()
    }
}

impl From<String> for MonsterElement {
    fn from(value: String) -> Self {
        Self(value)
    }
}

impl From<&str> for MonsterElement {
    fn from(value: &str) -> Self {
        Self(value.to_string())
    }
}

#[derive(Debug, PartialEq, Eq, PartialOrd, Ord, Clone, Default)]

pub struct MonsterLinkage(String);

impl MonsterLinkage {
    pub fn new(linkage: impl Into<String>) -> Self {
        Self(linkage.into())
    }
}

impl Value<String> for MonsterLinkage {
    fn value(&self) -> String {
        self.0.clone()
    }
}

impl From<String> for MonsterLinkage {
    fn from(value: String) -> Self {
        Self(value)
    }
}

impl From<&str> for MonsterLinkage {
    fn from(value: &str) -> Self {
        Self(value.to_string())
    }
}

#[derive(Debug, PartialEq, Eq, PartialOrd, Ord, Clone, Default)]

pub struct MonsterTeamId(i32);

impl MonsterTeamId {
    pub fn new(team_id: i32) -> Self {
        Self(team_id)
    }
}

impl Value<i32> for MonsterTeamId {
    fn value(&self) -> i32 {
        self.0
    }
}

impl From<i32> for MonsterTeamId {
    fn from(value: i32) -> Self {
        Self(value)
    }
}

#[derive(Debug, PartialEq, Eq, PartialOrd, Ord, Clone, Default)]

pub struct MonsterBoss(bool);

impl MonsterBoss {
    pub fn new(boss: bool) -> Self {
        Self(boss)
    }
}

impl Value<bool> for MonsterBoss {
    fn value(&self) -> bool {
        self.0
    }
}

impl From<bool> for MonsterBoss {
    fn from(value: bool) -> Self {
        Self(value)
    }
}

#[derive(Debug, PartialEq, Eq, PartialOrd, Ord, Clone, Default)]

pub struct MonsterBossMinRes(i32);

impl MonsterBossMinRes {
    pub fn new(boss_min_res: i32) -> Self {
        Self(boss_min_res)
    }
}

impl Value<i32> for MonsterBossMinRes {
    fn value(&self) -> i32 {
        self.0
    }
}

impl From<i32> for MonsterBossMinRes {
    fn from(value: i32) -> Self {
        Self(value)
    }
}

#[derive(Debug, PartialEq, Eq, PartialOrd, Ord, Clone, Default)]

pub struct MonsterRespawnTime(i32);

impl MonsterRespawnTime {
    pub fn new(respawn_time: i32) -> Self {
        Self(respawn_time)
    }
}

impl Value<i32> for MonsterRespawnTime {
    fn value(&self) -> i32 {
        self.0
    }
}

impl From<i32> for MonsterRespawnTime {
    fn from(value: i32) -> Self {
        Self(value)
    }
}

#[derive(Debug, PartialEq, Eq, PartialOrd, Ord, Clone, Default)]

pub struct MonsterHide(bool);

impl MonsterHide {
    pub fn new(hide: bool) -> Self {
        Self(hide)
    }
}

impl Value<bool> for MonsterHide {
    fn value(&self) -> bool {
        self.0
    }
}

impl From<bool> for MonsterHide {
    fn from(value: bool) -> Self {
        Self(value)
    }
}

#[derive(Debug, PartialEq, Eq, PartialOrd, Ord, Clone, Default)]

pub struct MonsterWorldBoss(bool);

impl MonsterWorldBoss {
    pub fn new(world_boss: bool) -> Self {
        Self(world_boss)
    }
}

impl Value<bool> for MonsterWorldBoss {
    fn value(&self) -> bool {
        self.0
    }
}

impl From<bool> for MonsterWorldBoss {
    fn from(value: bool) -> Self {
        Self(value)
    }
}
