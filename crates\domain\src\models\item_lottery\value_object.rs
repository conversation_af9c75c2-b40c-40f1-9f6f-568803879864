use reforged_shared::{UuidId, Value};

use super::entity::ItemLottery;

pub type ItemLotteryId = UuidId<ItemLottery>;

#[derive(Debug, PartialEq, PartialOrd, <PERSON><PERSON>, <PERSON><PERSON>ult)]
pub struct ItemLotteryQuantity(i32);

impl ItemLotteryQuantity {
    pub fn new(quantity: i32) -> Self {
        Self(quantity)
    }
}

impl Value<i32> for ItemLotteryQuantity {
    fn value(&self) -> i32 {
        self.0
    }
}

impl From<i32> for ItemLotteryQuantity {
    fn from(value: i32) -> Self {
        Self(value)
    }
}

#[derive(Debug, PartialEq, PartialOrd, <PERSON><PERSON>, <PERSON><PERSON>ult)]

pub struct ItemLotteryChance(f64);

impl ItemLotteryChance {
    pub fn new(chance: f64) -> Self {
        Self(chance)
    }
}

impl Value<f64> for ItemLotteryChance {
    fn value(&self) -> f64 {
        self.0
    }
}

impl From<f64> for ItemLotteryChance {
    fn from(value: f64) -> Self {
        Self(value)
    }
}
