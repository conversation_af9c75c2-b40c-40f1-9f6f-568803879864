use reforged_shared::Value;
use std::sync::Arc;

use async_trait::async_trait;
use chrono::{DateTime, Utc};
use reforged_domain::{
    models::password_reset_token::entity::PasswordResetToken,
    repository::password_reset_token_repository::PasswordResetTokensReadRepository,
};
use serde::Serialize;

use crate::{error::ApplicationError, traits::QueryH<PERSON>ler};

use super::password_reset_token_queries::GetPasswordResetToken;

#[derive(Clone)]
pub struct PasswordResetTokenQueryHandler {
    password_reset_token_read_repository: Arc<dyn PasswordResetTokensReadRepository>,
}

impl PasswordResetTokenQueryHandler {
    pub fn new(
        password_reset_token_read_repository: Arc<dyn PasswordResetTokensReadRepository>,
    ) -> Self {
        Self {
            password_reset_token_read_repository,
        }
    }
}

#[async_trait]
impl QueryHandler<GetPasswordResetToken, Option<PasswordResetTokenResponse>>
    for PasswordResetTokenQueryHandler
{
    async fn handle(
        &self,
        query: GetPasswordResetToken,
    ) -> Result<Option<PasswordResetTokenResponse>, ApplicationError> {
        let password_reset_token = self
            .password_reset_token_read_repository
            .find_by_token(&query.token)
            .await?;

        let password_reset_token = password_reset_token.map(PasswordResetTokenResponse::from);
        Ok(password_reset_token)
    }
}

#[derive(Serialize)]
pub struct PasswordResetTokenResponse {
    pub id: String,
    pub user_id: String,
    pub token: String,
    pub created_at: DateTime<Utc>,
    pub expires_at: DateTime<Utc>,
}

impl From<PasswordResetToken> for PasswordResetTokenResponse {
    fn from(value: PasswordResetToken) -> Self {
        Self {
            id: value.id().to_string(),
            user_id: value.user_id().to_string(),
            token: value.token().value(),
            created_at: value.created_at().value(),
            expires_at: value.expires_at().value(),
        }
    }
}
