use async_trait::async_trait;
use mockall::automock;

use crate::error::RepositoryError;
use crate::models::item_skill::entity::ItemSkill;
use crate::models::item_skill::value_object::ItemSkillId;

#[automock]
#[async_trait]
pub trait ItemSkillRepository: Send + Sync {
    async fn save(&self, entity: &ItemSkill) -> Result<(), RepositoryError>;
    async fn update(&self, entity: &ItemSkill) -> Result<(), RepositoryError>;
    async fn delete(&self, id: &ItemSkillId) -> Result<(), RepositoryError>;
}

#[automock]
#[async_trait]
pub trait ItemSkillReadRepository: Send + Sync {
    async fn find_by_id(&self, id: &ItemSkillId) -> Result<Option<ItemSkill>, RepositoryError>;
    async fn find_all(&self) -> Result<Vec<ItemSkill>, RepositoryError>;
}
