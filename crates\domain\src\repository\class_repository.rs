use async_trait::async_trait;
use mockall::automock;

use crate::error::RepositoryError;
use crate::models::class::entity::Class;
use crate::models::class::value_object::ClassId;

#[automock]
#[async_trait]
pub trait ClassRepository: Send + Sync {
    async fn save(&self, entity: &Class) -> Result<(), RepositoryError>;
    async fn update(&self, entity: &Class) -> Result<(), RepositoryError>;
    async fn delete(&self, id: &ClassId) -> Result<(), RepositoryError>;
}

#[automock]
#[async_trait]
pub trait ClassReadRepository: Send + Sync {
    async fn find_by_id(&self, id: &ClassId) -> Result<Option<Class>, RepositoryError>;
    async fn find_all(&self) -> Result<Vec<Class>, RepositoryError>;
}
