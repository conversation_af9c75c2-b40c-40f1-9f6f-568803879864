use crate::{SeaORMErr, mappers::password_reset_token_mapper::PasswordResetDbModelTokenMapper};

use crate::models::password_reset_tokens::Column as PasswordResetTokenColumn;
use crate::models::password_reset_tokens::Entity as PasswordResetTokenModel;

use async_trait::async_trait;
use reforged_domain::models::password_reset_token::value_object::Token;
use reforged_domain::{
    error::RepositoryError,
    models::password_reset_token::{
        entity::PasswordResetToken, value_object::PasswordResetTokenId,
    },
    repository::password_reset_token_repository::PasswordResetTokensReadRepository,
};
use reforged_shared::{IdTrait, Value};
use sea_orm::ColumnTrait;
use sea_orm::QueryFilter;
use sea_orm::{DatabaseConnection, EntityTrait};

pub struct PostgresPasswordResetTokenReadRepository {
    pool: DatabaseConnection,
}

impl PostgresPasswordResetTokenReadRepository {
    pub fn new(pool: DatabaseConnection) -> Self {
        Self { pool }
    }
}

#[async_trait]
impl PasswordResetTokensReadRepository for PostgresPasswordResetTokenReadRepository {
    async fn find_by_id(
        &self,
        id: &PasswordResetTokenId,
    ) -> Result<Option<PasswordResetToken>, RepositoryError> {
        let token = PasswordResetTokenModel::find_by_id(id.get_id())
            .one(&self.pool)
            .await
            .map_err(SeaORMErr::from)?;

        token
            .ok_or(RepositoryError::NotFound(format!(
                "with id: {}",
                id.get_id()
            )))
            .and_then(|token| {
                let mapped_token = PasswordResetDbModelTokenMapper::new(token);
                let token = PasswordResetToken::from(mapped_token);

                Ok(Some(token))
            })
    }

    async fn find_by_token(
        &self,
        token: &Token,
    ) -> Result<Option<PasswordResetToken>, RepositoryError> {
        let model = PasswordResetTokenModel::find()
            .filter(PasswordResetTokenColumn::Token.eq(token.value()))
            .one(&self.pool)
            .await
            .map_err(SeaORMErr::from)?;

        model
            .ok_or(RepositoryError::NotFound(format!(
                "with token: {}",
                token.value()
            )))
            .and_then(|token| {
                let mapped_token = PasswordResetDbModelTokenMapper::new(token);
                let token = PasswordResetToken::from(mapped_token);

                Ok(Some(token))
            })
    }
}
