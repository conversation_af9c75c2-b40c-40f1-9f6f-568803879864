use chrono::{DateTime, Utc};
use reforged_shared::{UuidId, Value};

use super::entity::Article;

pub type ArticlePostId = UuidId<Article>;

#[derive(Debug, PartialEq, Eq, PartialOrd, Ord, Clone, Default)]

pub struct ArticleTitle(String);

impl ArticleTitle {
    pub fn new(title: String) -> Self {
        Self(title)
    }
}

impl Value<String> for ArticleTitle {
    fn value(&self) -> String {
        self.0.clone()
    }
}

impl From<String> for ArticleTitle {
    fn from(value: String) -> Self {
        Self(value)
    }
}

impl From<&str> for ArticleTitle {
    fn from(value: &str) -> Self {
        Self(value.to_string())
    }
}

#[derive(Debug, PartialEq, Eq, PartialOrd, Ord, Clone, Default)]

pub struct ArticleContent(String);

impl ArticleContent {
    pub fn new(content: String) -> Self {
        Self(content)
    }
}

impl Value<String> for ArticleContent {
    fn value(&self) -> String {
        self.0.clone()
    }
}

impl From<String> for ArticleContent {
    fn from(value: String) -> Self {
        Self(value)
    }
}

impl From<&str> for ArticleContent {
    fn from(value: &str) -> Self {
        Self(value.to_string())
    }
}

#[derive(Debug, PartialEq, Eq, PartialOrd, Ord, Clone, Default)]

pub struct ArticleSubject(String);

impl ArticleSubject {
    pub fn new(subject: String) -> Self {
        Self(subject)
    }
}

impl Value<String> for ArticleSubject {
    fn value(&self) -> String {
        self.0.clone()
    }
}

impl From<String> for ArticleSubject {
    fn from(value: String) -> Self {
        Self(value)
    }
}

impl From<&str> for ArticleSubject {
    fn from(value: &str) -> Self {
        Self(value.to_string())
    }
}

#[derive(Debug, PartialEq, Eq, PartialOrd, Ord, Clone, Default)]

pub struct ArticleImage(String);

impl ArticleImage {
    pub fn new(image: String) -> Self {
        Self(image)
    }
}

impl Value<String> for ArticleImage {
    fn value(&self) -> String {
        self.0.clone()
    }
}

impl From<String> for ArticleImage {
    fn from(value: String) -> Self {
        Self(value)
    }
}

impl From<&str> for ArticleImage {
    fn from(value: &str) -> Self {
        Self(value.to_string())
    }
}

#[derive(Debug, PartialEq, Eq, PartialOrd, Ord, Clone, Default)]

pub struct ArticleTags(String);

impl ArticleTags {
    pub fn new(tags: String) -> Self {
        Self(tags)
    }
}

impl Value<String> for ArticleTags {
    fn value(&self) -> String {
        self.0.clone()
    }
}

impl From<String> for ArticleTags {
    fn from(value: String) -> Self {
        Self(value)
    }
}

impl From<&str> for ArticleTags {
    fn from(value: &str) -> Self {
        Self(value.to_string())
    }
}

#[derive(Debug, PartialEq, Eq, PartialOrd, Ord, Clone, Default)]

pub struct ArticleDate(DateTime<Utc>);

impl ArticleDate {
    pub fn new(date: DateTime<Utc>) -> Self {
        Self(date)
    }
}

impl Value<DateTime<Utc>> for ArticleDate {
    fn value(&self) -> DateTime<Utc> {
        self.0.clone()
    }
}

impl From<DateTime<Utc>> for ArticleDate {
    fn from(value: DateTime<Utc>) -> Self {
        Self(value)
    }
}
