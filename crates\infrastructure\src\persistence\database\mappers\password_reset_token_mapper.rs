use reforged_domain::models::{
    password_reset_token::{
        entity::PasswordResetToken,
        value_object::{
            PasswordResetTokenCreatedAt, PasswordResetTokenExpiresAt, PasswordResetTokenId, Token,
        },
    },
    user::value_object::UserId,
};

#[derive(Debug)]
pub struct PasswordResetDbModelTokenMapper(
    super::super::super::models::password_reset_tokens::Model,
);

impl PasswordResetDbModelTokenMapper {
    pub fn new(token: super::super::super::models::password_reset_tokens::Model) -> Self {
        Self(token)
    }
}

impl From<PasswordResetDbModelTokenMapper> for PasswordResetToken {
    fn from(value: PasswordResetDbModelTokenMapper) -> Self {
        PasswordResetToken::builder()
            .id(PasswordResetTokenId::new(value.0.id))
            .user_id(UserId::new(value.0.user_id))
            .token(Token::new(value.0.token))
            .created_at(PasswordResetTokenCreatedAt::new(
                value.0.created_at.and_utc(),
            ))
            .expires_at(PasswordResetTokenExpiresAt::new(
                value.0.expires_at.and_utc(),
            ))
            .build()
    }
}
