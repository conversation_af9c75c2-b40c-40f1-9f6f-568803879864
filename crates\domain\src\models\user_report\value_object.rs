use chrono::{DateTime, Utc};
use reforged_shared::{UuidId, Value};

use super::entity::UserReport;

pub type UserReportId = UuidId<UserReport>;

#[derive(Debug, PartialEq, Eq, PartialOrd, Ord, Clone, Default)]

pub struct TargetName(String);

impl TargetName {
    pub fn new(name: String) -> Self {
        Self(name)
    }
}

impl Value<String> for TargetName {
    fn value(&self) -> String {
        self.0.clone()
    }
}

impl From<String> for TargetName {
    fn from(name: String) -> Self {
        Self(name)
    }
}

impl From<&str> for TargetName {
    fn from(name: &str) -> Self {
        Self(name.to_string())
    }
}

#[derive(Debug, PartialEq, Eq, PartialOrd, Ord, Clone, Default)]

pub struct ReportCategory(String);

impl ReportCategory {
    pub fn new(category: String) -> Self {
        Self(category)
    }
}

impl Value<String> for ReportCategory {
    fn value(&self) -> String {
        self.0.clone()
    }
}

impl From<String> for ReportCategory {
    fn from(category: String) -> Self {
        Self(category)
    }
}

impl From<&str> for ReportCategory {
    fn from(category: &str) -> Self {
        Self(category.to_string())
    }
}

#[derive(Debug, PartialEq, Eq, PartialOrd, Ord, Clone, Default)]

pub struct ReportDescription(String);

impl ReportDescription {
    pub fn new(description: String) -> Self {
        Self(description)
    }
}

impl Value<String> for ReportDescription {
    fn value(&self) -> String {
        self.0.clone()
    }
}

impl From<String> for ReportDescription {
    fn from(description: String) -> Self {
        Self(description)
    }
}

impl From<&str> for ReportDescription {
    fn from(description: &str) -> Self {
        Self(description.to_string())
    }
}

#[derive(Debug, PartialEq, Eq, PartialOrd, Ord, Clone, Default)]

pub struct ReportDate(DateTime<Utc>);

impl ReportDate {
    pub fn new(date: DateTime<Utc>) -> Self {
        Self(date)
    }
}

impl Value<DateTime<Utc>> for ReportDate {
    fn value(&self) -> DateTime<Utc> {
        self.0.clone()
    }
}

impl From<DateTime<Utc>> for ReportDate {
    fn from(date: DateTime<Utc>) -> Self {
        Self(date)
    }
}
