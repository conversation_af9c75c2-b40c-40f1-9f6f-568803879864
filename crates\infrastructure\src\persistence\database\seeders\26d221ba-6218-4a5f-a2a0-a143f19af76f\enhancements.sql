INSERT INTO "enhancements" ("id", "name", "pattern_id", "rarity", "dps", "level") VALUES (1, 'Adventurer Enhancement', 1, 10, 10, 1);
INSERT INTO "enhancements" ("id", "name", "pattern_id", "rarity", "dps", "level") VALUES (2, 'Fighter Enhancement +5', 2, 10, 20, 5);
INSERT INTO "enhancements" ("id", "name", "pattern_id", "rarity", "dps", "level") VALUES (3, 'Fighter Enhancement +10', 2, 10, 25, 10);
INSERT INTO "enhancements" ("id", "name", "pattern_id", "rarity", "dps", "level") VALUES (4, 'Fighter Enhancement +15', 2, 10, 30, 15);
INSERT INTO "enhancements" ("id", "name", "pattern_id", "rarity", "dps", "level") VALUES (5, 'Fighter Enhancement +20', 2, 10, 40, 20);
INSERT INTO "enhancements" ("id", "name", "pattern_id", "rarity", "dps", "level") VALUES (6, 'Fighter Enhancement +25', 2, 10, 45, 25);
INSERT INTO "enhancements" ("id", "name", "pattern_id", "rarity", "dps", "level") VALUES (7, 'Fighter Enhancement  +30', 2, 10, 50, 30);
INSERT INTO "enhancements" ("id", "name", "pattern_id", "rarity", "dps", "level") VALUES (8, 'Fighter Enhancement +35', 2, 10, 55, 35);
INSERT INTO "enhancements" ("id", "name", "pattern_id", "rarity", "dps", "level") VALUES (9, 'Wizard Enhancement +5', 6, 10, 18, 5);
INSERT INTO "enhancements" ("id", "name", "pattern_id", "rarity", "dps", "level") VALUES (10, 'Wizard Enhancement +10', 6, 10, 24, 10);
INSERT INTO "enhancements" ("id", "name", "pattern_id", "rarity", "dps", "level") VALUES (11, 'Wizard Enhancement +15', 6, 10, 29, 15);
INSERT INTO "enhancements" ("id", "name", "pattern_id", "rarity", "dps", "level") VALUES (12, 'Wizard Enhancement +20', 6, 10, 33, 20);
INSERT INTO "enhancements" ("id", "name", "pattern_id", "rarity", "dps", "level") VALUES (13, 'Wizard Enhancement +25', 6, 10, 38, 25);
INSERT INTO "enhancements" ("id", "name", "pattern_id", "rarity", "dps", "level") VALUES (14, 'Wizard Enhancement +30', 6, 10, 48, 30);
INSERT INTO "enhancements" ("id", "name", "pattern_id", "rarity", "dps", "level") VALUES (15, 'Wizard Enhancement +35', 6, 10, 59, 35);
INSERT INTO "enhancements" ("id", "name", "pattern_id", "rarity", "dps", "level") VALUES (16, 'Lucky Enhancement +5', 9, 10, 18, 5);
INSERT INTO "enhancements" ("id", "name", "pattern_id", "rarity", "dps", "level") VALUES (17, 'Lucky Enhancement +10', 9, 10, 28, 10);
INSERT INTO "enhancements" ("id", "name", "pattern_id", "rarity", "dps", "level") VALUES (18, 'Lucky Enhancement +15', 9, 10, 36, 15);
INSERT INTO "enhancements" ("id", "name", "pattern_id", "rarity", "dps", "level") VALUES (19, 'Lucky Enhancement +20', 9, 10, 39, 20);
INSERT INTO "enhancements" ("id", "name", "pattern_id", "rarity", "dps", "level") VALUES (20, 'Lucky Enhancement +25', 9, 10, 48, 25);
INSERT INTO "enhancements" ("id", "name", "pattern_id", "rarity", "dps", "level") VALUES (21, 'Lucky Enhancement +30', 9, 10, 50, 30);
INSERT INTO "enhancements" ("id", "name", "pattern_id", "rarity", "dps", "level") VALUES (22, 'Lucky Enhancement +35', 9, 10, 60, 35);
INSERT INTO "enhancements" ("id", "name", "pattern_id", "rarity", "dps", "level") VALUES (23, 'Healer Enhancement +5', 7, 10, 16, 5);
INSERT INTO "enhancements" ("id", "name", "pattern_id", "rarity", "dps", "level") VALUES (24, 'Healer Enhancement +10', 7, 10, 25, 10);
INSERT INTO "enhancements" ("id", "name", "pattern_id", "rarity", "dps", "level") VALUES (25, 'Healer Enhancement +15', 7, 10, 34, 15);
INSERT INTO "enhancements" ("id", "name", "pattern_id", "rarity", "dps", "level") VALUES (26, 'Healer Enhancement +20', 7, 10, 39, 20);
INSERT INTO "enhancements" ("id", "name", "pattern_id", "rarity", "dps", "level") VALUES (27, 'Healer Enhancement +25', 7, 10, 45, 25);
INSERT INTO "enhancements" ("id", "name", "pattern_id", "rarity", "dps", "level") VALUES (28, 'Healer Enhancement +30', 7, 10, 50, 30);
INSERT INTO "enhancements" ("id", "name", "pattern_id", "rarity", "dps", "level") VALUES (29, 'Healer Enhancement +35', 7, 10, 55, 35);
INSERT INTO "enhancements" ("id", "name", "pattern_id", "rarity", "dps", "level") VALUES (30, 'Thief Enhancement +5', 3, 10, 20, 5);
INSERT INTO "enhancements" ("id", "name", "pattern_id", "rarity", "dps", "level") VALUES (31, 'Thief Enhancement +10', 3, 10, 27, 10);
INSERT INTO "enhancements" ("id", "name", "pattern_id", "rarity", "dps", "level") VALUES (32, 'Thief Enhancement +15', 3, 10, 35, 15);
INSERT INTO "enhancements" ("id", "name", "pattern_id", "rarity", "dps", "level") VALUES (33, 'Thief Enhancement +20', 3, 10, 43, 20);
INSERT INTO "enhancements" ("id", "name", "pattern_id", "rarity", "dps", "level") VALUES (34, 'Thief Enhancement +25', 3, 10, 49, 25);
INSERT INTO "enhancements" ("id", "name", "pattern_id", "rarity", "dps", "level") VALUES (35, 'Thief Enhancement +30', 3, 10, 58, 30);
INSERT INTO "enhancements" ("id", "name", "pattern_id", "rarity", "dps", "level") VALUES (36, 'Thief Enhancement +35', 3, 10, 63, 35);
INSERT INTO "enhancements" ("id", "name", "pattern_id", "rarity", "dps", "level") VALUES (37, 'Hybrid Enhancement +5', 5, 10, 21, 5);
INSERT INTO "enhancements" ("id", "name", "pattern_id", "rarity", "dps", "level") VALUES (38, 'Hybrid Enhancement +10', 5, 10, 23, 10);
INSERT INTO "enhancements" ("id", "name", "pattern_id", "rarity", "dps", "level") VALUES (39, 'Hybrid Enhancement +15', 5, 10, 45, 15);
INSERT INTO "enhancements" ("id", "name", "pattern_id", "rarity", "dps", "level") VALUES (40, 'Hybrid Enhancement +20', 5, 10, 2, 20);
INSERT INTO "enhancements" ("id", "name", "pattern_id", "rarity", "dps", "level") VALUES (41, 'Hybrid Enhancement +25', 5, 10, 32, 25);
INSERT INTO "enhancements" ("id", "name", "pattern_id", "rarity", "dps", "level") VALUES (42, 'Hybrid Enhancement +30', 5, 10, 0, 30);
INSERT INTO "enhancements" ("id", "name", "pattern_id", "rarity", "dps", "level") VALUES (43, 'Hybrid Enhancement +35', 5, 10, 1, 35);
INSERT INTO "enhancements" ("id", "name", "pattern_id", "rarity", "dps", "level") VALUES (44, 'Armsman Enhancement +5', 10, 10, 40, 5);
INSERT INTO "enhancements" ("id", "name", "pattern_id", "rarity", "dps", "level") VALUES (45, 'SpellBreaker Enhancement +5', 11, 10, 40, 5);
INSERT INTO "enhancements" ("id", "name", "pattern_id", "rarity", "dps", "level") VALUES (46, 'Fighter Enhancement +40', 2, 10, 60, 40);
INSERT INTO "enhancements" ("id", "name", "pattern_id", "rarity", "dps", "level") VALUES (47, 'Fighter Enhancement +45', 2, 10, 65, 45);
INSERT INTO "enhancements" ("id", "name", "pattern_id", "rarity", "dps", "level") VALUES (48, 'Fighter Enhancement +50', 2, 10, 70, 50);
INSERT INTO "enhancements" ("id", "name", "pattern_id", "rarity", "dps", "level") VALUES (49, 'Thief Enhancement +40', 3, 10, 69, 40);
INSERT INTO "enhancements" ("id", "name", "pattern_id", "rarity", "dps", "level") VALUES (50, 'Thief Enhancement +45', 3, 10, 74, 45);
INSERT INTO "enhancements" ("id", "name", "pattern_id", "rarity", "dps", "level") VALUES (51, 'Thief Enhancement +50', 3, 10, 79, 50);
INSERT INTO "enhancements" ("id", "name", "pattern_id", "rarity", "dps", "level") VALUES (52, 'Wizard Enhancement +40', 6, 10, 63, 40);
INSERT INTO "enhancements" ("id", "name", "pattern_id", "rarity", "dps", "level") VALUES (53, 'Wizard Enhancement +45', 6, 10, 68, 45);
INSERT INTO "enhancements" ("id", "name", "pattern_id", "rarity", "dps", "level") VALUES (54, 'Wizard Enhancement +50', 6, 10, 73, 50);
INSERT INTO "enhancements" ("id", "name", "pattern_id", "rarity", "dps", "level") VALUES (55, 'Healer Enhancement +40', 7, 10, 60, 40);
INSERT INTO "enhancements" ("id", "name", "pattern_id", "rarity", "dps", "level") VALUES (56, 'Healer Enhancement +45', 7, 10, 63, 45);
INSERT INTO "enhancements" ("id", "name", "pattern_id", "rarity", "dps", "level") VALUES (57, 'Healer Enhancement +50', 7, 10, 67, 50);
INSERT INTO "enhancements" ("id", "name", "pattern_id", "rarity", "dps", "level") VALUES (58, 'Lucky Enhancement +40', 9, 10, 65, 40);
INSERT INTO "enhancements" ("id", "name", "pattern_id", "rarity", "dps", "level") VALUES (59, 'Lucky Enhancement +45', 9, 10, 70, 45);
INSERT INTO "enhancements" ("id", "name", "pattern_id", "rarity", "dps", "level") VALUES (60, 'Lucky Enhancement +50', 9, 10, 73, 50);
