//! `SeaORM` Entity, @generated by sea-orm-codegen 1.1.0

use sea_orm::entity::prelude::*;
use serde::Deserialize;

#[derive(<PERSON>lone, Debug, PartialEq, DeriveEntityModel, Eq, Deserialize)]
#[sea_orm(table_name = "guild_halls")]
pub struct Model {
    #[sea_orm(primary_key, auto_increment = false)]
    pub id: Uuid,
    pub guild_id: Uuid,
    pub linkage: String,
    pub cell: String,
    pub x: Decimal,
    pub y: Decimal,
    #[sea_orm(column_type = "Text")]
    pub interior: String,
}

#[derive(Co<PERSON>, Clone, Debug, EnumIter, DeriveRelation)]
pub enum Relation {
    #[sea_orm(has_many = "super::guild_hall_buildings::Entity")]
    GuildHallBuildings,
    #[sea_orm(has_many = "super::guild_hall_connections::Entity")]
    GuildHallConnections,
    #[sea_orm(
        belongs_to = "super::guilds::Entity",
        from = "Column::GuildId",
        to = "super::guilds::Column::Id",
        on_update = "Cascade",
        on_delete = "Cascade"
    )]
    Guilds,
}

impl Related<super::guild_hall_buildings::Entity> for Entity {
    fn to() -> RelationDef {
        Relation::GuildHallBuildings.def()
    }
}

impl Related<super::guild_hall_connections::Entity> for Entity {
    fn to() -> RelationDef {
        Relation::GuildHallConnections.def()
    }
}

impl Related<super::guilds::Entity> for Entity {
    fn to() -> RelationDef {
        Relation::Guilds.def()
    }
}

impl ActiveModelBehavior for ActiveModel {}
