use getset::{<PERSON><PERSON>, <PERSON><PERSON>};

use crate::models::user::value_object::UserId;

use super::value_object::{Coins, Crystal, Diamonds, Gold, UserCurrencyId};

#[derive(<PERSON>bu<PERSON>, <PERSON><PERSON>, Default, bon::<PERSON><PERSON><PERSON>, <PERSON><PERSON>, Set<PERSON>)]
#[getset(get = "pub", set = "pub")]
pub struct UserCurrency {
    id: UserCurrencyId,
    user_id: UserId,
    gold: Gold,
    coins: Coins,
    diamonds: Diamonds,
    crystal: Crystal,
}

impl UserCurrency {
    pub fn new(
        id: UserCurrencyId,
        user_id: UserId,
        gold: Gold,
        coins: Coins,
        diamonds: Diamonds,
        crystal: Crystal,
    ) -> Self {
        Self {
            id,
            user_id,
            gold,
            coins,
            diamonds,
            crystal,
        }
    }
}

#[cfg(test)]
mod tests {
    use reforged_shared::{IdTrait, Value};

    use super::*;

    #[test]
    fn test_user_currency_new() {
        let id = UserCurrencyId::new(uuid::Uuid::now_v7());
        let user_id = UserId::new(uuid::Uuid::now_v7());
        let gold = Gold::new(1000);
        let coins = Coins::new(500);
        let diamonds = Diamonds::new(100);
        let crystal = Crystal::new(50);

        let user_currency = UserCurrency::new(
            id.clone(),
            user_id.clone(),
            gold.clone(),
            coins.clone(),
            diamonds.clone(),
            crystal.clone(),
        );

        assert_eq!(user_currency.id().get_id(), id.get_id());
        assert_eq!(user_currency.user_id().get_id(), user_id.get_id());
        assert_eq!(user_currency.gold().value(), 1000);
        assert_eq!(user_currency.coins().value(), 500);
        assert_eq!(user_currency.diamonds().value(), 100);
        assert_eq!(user_currency.crystal().value(), 50);
    }

    #[test]
    fn test_user_currency_builder() {
        let id = UserCurrencyId::new(uuid::Uuid::now_v7());
        let user_id = UserId::new(uuid::Uuid::now_v7());

        let user_currency = UserCurrency::builder()
            .id(id.clone())
            .user_id(user_id.clone())
            .gold(Gold::new(2000))
            .coins(Coins::new(1000))
            .diamonds(Diamonds::new(200))
            .crystal(Crystal::new(100))
            .build();

        assert_eq!(user_currency.id().get_id(), id.get_id());
        assert_eq!(user_currency.user_id().get_id(), user_id.get_id());
        assert_eq!(user_currency.gold().value(), 2000);
        assert_eq!(user_currency.coins().value(), 1000);
        assert_eq!(user_currency.diamonds().value(), 200);
        assert_eq!(user_currency.crystal().value(), 100);
    }
}