//! `SeaORM` Entity, @generated by sea-orm-codegen 1.1.0

use sea_orm::entity::prelude::*;
use serde::Deserialize;

#[derive(<PERSON>lone, Debug, PartialEq, DeriveEntityModel, Eq, Deserialize)]
#[sea_orm(table_name = "books")]
pub struct Model {
    #[sea_orm(primary_key, auto_increment = false)]
    pub id: Uuid,
    pub file: String,
    pub name: String,
    pub linkage: String,
    pub lock: String,
    pub desc: String,
    pub map: String,
    pub r#type: String,
    pub hide: i32,
    pub label: String,
    pub shop_id: Uuid,
    pub field: String,
    pub index: i32,
    pub value: i32,
}

#[derive(Co<PERSON>, <PERSON><PERSON>, Debug, EnumIter, DeriveRelation)]
pub enum Relation {
    #[sea_orm(has_one = "super::book_quests::Entity")]
    BookQuests,
}

impl Related<super::book_quests::Entity> for Entity {
    fn to() -> RelationDef {
        Relation::BookQuests.def()
    }
}

impl ActiveModelBehavior for ActiveModel {}
