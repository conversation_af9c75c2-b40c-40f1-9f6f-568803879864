use reforged_domain::models::store::entity::Store;
use reforged_domain::models::store::value_object::{StoreId, StoreType};
use reforged_domain::models::role::value_object::RoleId;
use reforged_domain::models::title::value_object::TitleId;

#[derive(Debug)]
pub struct StoreDbModelMapper(super::super::models::stores::Model);

impl StoreDbModelMapper {
    pub fn new(model: super::super::models::stores::Model) -> Self {
        Self(model)
    }
}

impl From<StoreDbModelMapper> for Store {
    fn from(value: StoreDbModelMapper) -> Self {
        let model = value.0;
        
        let store_type = model.store_type.map(|t| match t.as_str() {
            "Package" => StoreType::Package,
            "VIP" => StoreType::VIP,
            "FOUNDER" => StoreType::FOUNDER,
            _ => StoreType::Coin,
        });
        
        Store::builder()
            .id(StoreId::new(model.id))
            .available(model.available.into())
            .name(model.name.into())
            .price(model.price.into())
            .item(model.item.into())
            .achievement(model.achievement.into())
            .gold(model.gold.into())
            .coins(model.coins.into())
            .crystal(model.crystal.into())
            .diamonds(model.diamonds.into())
            .upgrade(model.upgrade.into())
            .maybe_role_id(model.role_id.map(RoleId::new))
            .maybe_title_id(model.title_id.map(TitleId::new))
            .bag_slots(model.bag_slots.into())
            .bank_slots(model.bank_slots.into())
            .house_slots(model.house_slots.into())
            .maybe_store_type(store_type)
            .quantity(model.quantity.into())
            .img(model.img.into())
            .build()
    }
}
