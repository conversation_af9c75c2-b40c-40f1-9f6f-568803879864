use reforged_domain::models::store::entity::Store;
use reforged_domain::models::store::value_object::*;
use reforged_domain::models::role::value_object::RoleId;
use reforged_domain::models::title::value_object::TitleId;
use num_traits::ToPrimitive;

#[derive(Debug)]
pub struct StoreDbModelMapper(super::super::models::stores::Model);

impl StoreDbModelMapper {
    pub fn new(model: super::super::models::stores::Model) -> Self {
        Self(model)
    }
}

impl From<StoreDbModelMapper> for Store {
    fn from(value: StoreDbModelMapper) -> Self {
        let model = value.0;

        let store_type = model.r#type.map(|t| match t {
            super::super::models::sea_orm_active_enums::StoreType::Package => StoreType::Package,
            super::super::models::sea_orm_active_enums::StoreType::Vip => StoreType::VIP,
            super::super::models::sea_orm_active_enums::StoreType::Founder => StoreType::FOUNDER,
            super::super::models::sea_orm_active_enums::StoreType::Coin => StoreType::Coin,
        });

        Store::builder()
            .id(StoreId::new(model.id))
            .available(Available::new(model.available.try_into().unwrap_or(0)))
            .name(StoreName::new(model.name))
            .price(Price::new(model.price.to_f64().unwrap_or(0.0)))
            .item(Item::new(model.item.try_into().unwrap_or(0)))
            .achievement(Achievement::new(model.achievement))
            .gold(StoreGold::new(model.gold.try_into().unwrap_or(0)))
            .coins(StoreCoins::new(model.coins.try_into().unwrap_or(0)))
            .crystal(StoreCrystal::new(model.crystal.try_into().unwrap_or(0)))
            .diamonds(StoreDiamonds::new(model.diamonds.try_into().unwrap_or(0)))
            .upgrade(Upgrade::new(model.upgrade.try_into().unwrap_or(0)))
            .maybe_role_id(model.role_id.map(RoleId::new))
            .maybe_title_id(model.title_id.map(TitleId::new))
            .bag_slots(BagSlots::new(model.bag_slots.try_into().unwrap_or(0)))
            .bank_slots(BankSlots::new(model.bank_slots.try_into().unwrap_or(0)))
            .house_slots(HouseSlots::new(model.house_slots.try_into().unwrap_or(0)))
            .maybe_store_type(store_type)
            .quantity(Quantity::new(model.quantity.try_into().unwrap_or(0)))
            .img(Img::new(model.img))
            .build()
    }
}
