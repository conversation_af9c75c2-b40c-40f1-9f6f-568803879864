use reforged_domain::models::user_color::entity::UserColor;
use reforged_domain::models::user_color::value_object::UserColorId;
use reforged_domain::models::user::value_object::UserId;

#[derive(Debug)]
pub struct UserColorDbModelMapper(super::super::models::user_colors::Model);

impl UserColorDbModelMapper {
    pub fn new(model: super::super::models::user_colors::Model) -> Self {
        Self(model)
    }
}

impl From<UserColorDbModelMapper> for UserColor {
    fn from(value: UserColorDbModelMapper) -> Self {
        let model = value.0;
        
        UserColor::builder()
            .id(UserColorId::new(model.id))
            .user_id(UserId::new(model.user_id))
            .color_eye(model.color_eye.into())
            .color_name(model.color_name.into())
            .color_chat(model.color_chat.into())
            .color_hair(model.color_hair.into())
            .color_skin(model.color_skin.into())
            .color_base(model.color_base.into())
            .color_trim(model.color_trim.into())
            .color_accessory(model.color_accessory.into())
            .build()
    }
}
