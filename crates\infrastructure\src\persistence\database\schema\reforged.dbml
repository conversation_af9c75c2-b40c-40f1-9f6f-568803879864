Enum "PlayerAccess" {
  "Banned"
  "Player"
  "Founder"
  "Support"
  "VIP"
  "Moderator"
  "Trainee"
  "Admistrator" 
  "Admin"
}

Enum "Gender" {
  "M"
  "F"
}

Enum "LoginLocation" {
  "Loader"
  "Game"
  "Wiki"
}

Enum "StoreType" {
  "Package"
  "VIP"
  "FOUNDER"
  "Coin"
}

Enum "MarketType" {
  "Auction"
  "Vending"
}

Enum "MarketLogType" {
  "Buy"
  "Sell"
  "Retrieve"
}

Enum "MarketLogMarketType" {
  "Auction"
}

Table "access" {
  "id" UUID [unique, pk, not null]
  "tag" PlayerAccess [not null, default: "Player"]
  "user_color" varchar(8) [not null]
}

Table "achievements" {
  "id" UUID [unique, pk, not null]
  "name" varchar(50) [not null]
  "description" text [not null]
  "file" varchar(25) [not null]
  "category" varchar(50) [not null, default: "''"]
  "show" BOOLEAN [not null, default: FALSE]
}

Table "admin_uploads" {
  "id" UUID [unique, pk, not null]
  "user_id" UUID [not null]
  "file_name" varchar(255) [not null]
  "type" varchar(2555) [not null]
  "date" date [not null, default: `now()`]
}

Table "articles" {
  "post_id" UUID [pk, not null]
  "author" UUID [not null]
  "subject" varchar(50) [not null]
  "content" text [not null]
  "image" varchar(255) [default: null]
  "tags" varchar(50) [default: null]
  "date" timestamp [not null, default: `now()`]

  Indexes {
    author
  }
}

Table "auras" {
  "id" UUID [unique, pk, not null]
  "name" varchar(32) [not null]
  "duration" smallint [not null, default: 6]
  "category" varchar(8) [not null]
  "damage_increase" decimal(7,2) [not null, default: 0]
  "damage_taken_decrease" decimal(7,2) [not null, default: 0]
  "chance" decimal(7,2) [not null, default: 1]
  "self" BOOLEAN [not null, default: FALSE]
}

Table "aura_effects" {
  "id" UUID [unique, pk, not null]
  "aura_id" UUID [not null]
  "stat" "char (3)" [not null]
  "value" decimal(7,2) [not null, default: 0]
  "type" "char (1)" [not null, default: "+"]

  Indexes {
    aura_id
  }
}

Table "books" {
  "id" UUID [unique, pk, not null]
  "file" varchar(60) [not null]
  "name" varchar(60) [not null]
  "linkage" varchar(60) [not null]
  "lock" varchar(60) [not null]
  "desc" varchar(60) [not null]
  "map" varchar(60) [not null]
  "type" varchar(60) [not null]
  "hide" int [not null]
  "label" varchar(60) [not null]
  "shop" int [not null]
  "field" varchar(60) [not null]
  "index" int [not null]
  "value" int [not null]
}

Table "book_quests" {
  "id" UUID [unique, pk, not null]
  "name" varchar(60) [not null]
  "field" varchar(60) [not null]
  "lock" varchar(60) [not null]
  "map" varchar(60) [not null]
  "type" varchar(60) [not null]
  "hide" int [not null]
  "index" int [not null]
  "value" int [not null]
}

Table "classes" {
  "id" UUID [unique, pk, not null]
  "name" varchar(30) [not null]
  "category" "char (2)" [not null]
  "description" text [not null]
  "mana_regeneration_methods" text [not null]
  "stats_description" text [not null]
}

Table "class_categories" {
  "id" UUID [unique, pk, not null]
  "name" "char (20)" [not null]
  "category" "char (2)" [not null]
  "strength" decimal(7,2) [not null, default: 0]
  "endurance" decimal(7,2) [not null, default: 0]
  "dexterity" decimal(7,2) [not null, default: 0]
  "intellect" decimal(7,2) [not null, default: 0]
  "wisdom" decimal(7,2) [not null, default: 0]
  "luck" decimal(7,2) [not null, default: 0]

  Indexes {
    (id, category)
  }
}

Table "class_skills" {
  "id" UUID [unique, pk, not null]
  "class_id" UUID [not null]
  "skill_id" UUID [not null]

  Indexes {
    (class_id, skill_id)
  }
}

Table "enhancements" {
  "id" UUID [unique, pk, not null]
  "name" varchar(32) [not null]
  "pattern_id" UUID [not null, default: 1]
  "rarity" smallint [not null]
  "dps" smallint [not null]
  "level" smallint [not null, default: 1]
}

Table "enhancement_patterns" {
  "id" UUID [unique, pk, not null]
  "name" varchar(32) [not null]
  "desc" varchar(4) [not null]
  "wisdom" smallint [not null]
  "strength" smallint [not null]
  "luck" smallint [not null]
  "dexterity" smallint [not null]
  "endurance" smallint [not null]
  "intelligence" smallint [not null]
}

Table "factions" {
  "id" UUID [unique, pk, not null]
  "name" varchar(20) [not null]
}

Table "guilds" {
  "id" UUID [unique, pk, not null]
  "name" varchar(64) [not null]
  "message_of_the_day" varchar(512) [not null]
  "max_members" smallint [not null, default: 15]
  "hall_size" smallint [not null, default: 1]
  "last_updated" timestamp [not null, default: `now()`]
  "wins" int [not null, default: 0]
  "loss" int [not null, default: 0]
  "total_kills" int [not null, default: 0]
  "level" int [not null, default: 1]
  "experience" bigint [not null, default: 0]
  "guild_color" varchar(255) [not null, default: "16777215"]
  "staff_g" int [not null, default: 0]
  "color" int [default: 0]
}

Table "guild_halls" {
  "id" UUID [unique, pk, not null]
  "guild_id" UUID [not null]
  "linkage" varchar(64) [not null]
  "cell" varchar(16) [not null]
  "x" decimal(7,2) [not null, default: 0.0]
  "y" decimal(7,2) [not null, default: 0.0]
  "interior" text [not null]
}

Table "guild_hall_buildings" {
  "id" UUID [unique, pk, not null]
  "hall_id" UUID [not null]
  "item_id" UUID [not null]
  "slot" smallint [not null, default: 1]
  "size" smallint [not null, default: 1]
}

Table "guild_hall_connections" {
  "hall_id" UUID [not null]
  "pad" varchar(16) [not null]
  "cell" varchar(16) [not null]
  "pad_position" varchar(16) [not null]
}

Table "guild_inventories" {
  "guild_id" UUID [not null]
  "item_id" UUID [not null]
  "user_id" UUID [not null]

  Indexes {
    (guild_id, item_id, user_id) [pk]
    (guild_id, item_id, user_id)
  }
}

Table "guild_levels" {
  "level" int [pk, not null]
  "exp" int [not null]
}

Table "hairs" {
  "id" UUID [unique, pk, not null]
  "gender" Gender [not null, default: "M"]
  "name" varchar(16) [not null]
  "file" varchar(64) [not null]
}

Table "hair_shops" {
  "id" UUID [unique, pk, not null]
  "name" varchar(32) [not null, default: ""]
}

Table "hair_shop_items" {
  "id" UUID [unique, pk, not null]
  "gender" Gender [not null, default: "M"]
  "shop_id" UUID [not null]
  "hair_id" uuid [not null]
}

Table "items" {
  "id" UUID [unique, pk, not null]
  "class_id" UUID [default: null]
  "name" varchar(60) [not null]
  "description" text [not null]
  "type" varchar(16) [not null]
  "element" varchar(16) [not null, default: "None"]
  "file" varchar(64) [not null]
  "link" varchar(64) [not null]
  "icon" varchar(16) [not null]
  "equipment" varchar(6) [not null]
  "level" smallint [not null, default: 1]
  "dps" smallint [not null, default: 100]
  "range" smallint [not null, default: 100]
  "rarity" smallint [not null, default: 10]
  "quantity" smallint [not null, default: 1]
  "stack" smallint [not null, default: 1]
  "cost" int [not null, default: 0]
  "coins" smallint [not null, default: 0]
  "diamonds" smallint [not null, default: 0]
  "crystal" bigint [not null, default: 0]
  "sell" BOOLEAN [not null, default: TRUE]
  "market" BOOLEAN [not null, default: TRUE]
  "temporary" BOOLEAN [not null, default: FALSE]
  "upgrade" BOOLEAN [not null, default: FALSE]
  "staff" BOOLEAN [not null, default: FALSE]
  "enh_id" UUID [not null, default: 1]
  "faction_id" UUID [default: null]
  "req_reputation" bigint [not null, default: 0]
  "req_class_id" UUID [default: null]
  "req_class_points" bigint [not null, default: 0]
  "req_quests" varchar(64) [not null, default: ""]
  "quest_string_index" smallint [not null, default: `-1`]
  "quest_string_value" smallint [not null, default: 0]
  "meta" varchar(32) [default: null]
  "color" varchar(6) [not null, default: "FFFFFF"]
}

Table "item_bundles" {
  "id" UUID [unique, pk, not null]
  "item_id" UUID [not null]
  "reward_id" UUID [not null]
  "quantity" int [default: 0]
}

Table "item_effects" {
  "id" UUID [unique, pk, not null]
  "item_id" UUID [not null]
  "damage_increase" decimal(7,2) [not null]
  "damage_taken" decimal(7,2) [not null]
  "exp" decimal(7,2) [not null]
  "gold" decimal(7,2) [not null]
  "coins" decimal(7,2) [not null]
  "class_point" decimal(7,2) [not null, default: 0]
  "reputation" decimal(7,2) [not null, default: 0]
}

Table "item_lottery" {
  "item_id" UUID [not null]
  "reward_id" UUID [not null]
  "quantity" int [not null]
  "chance" decimal(7,2) [not null, default: 0]
}

Table "item_rarities" {
  "id" UUID [unique, pk, not null]
  "name" varchar(255) [not null]
}

Table "item_requirements" {
  "id" UUID [unique, pk, not null]
  "item_id" UUID [not null]
  "req_item_id" UUID [not null]
  "quantity" smallint [not null]

  Indexes {
    (item_id, req_item_id, id)
  }
}

Table "item_skills" {
  "id" INT [unique, not null, increment]
  "item_id" UUID [not null]
  "skill_id" UUID [not null]

  Indexes {
    (id, item_id) [pk]
  }
}

Table "maps" {
  "id" UUID [unique, pk, not null]
  "name" varchar(32) [not null]
  "file" varchar(128) [not null]
  "max_players" smallint [not null, default: 6]
  "req_level" smallint [not null, default: 0]
  "upgrade" smallint [not null, default: 0]
  "staff" smallint [not null, default: 0]
  "pvp" smallint [not null, default: 0]
  "world_boss" smallint [not null, default: 0]
}

Table "map_cells" {
  "id" UUID [unique, pk, not null]
  "map_id" UUID [not null]
  "frame" varchar(16) [not null]
  "pad" varchar(16) [not null]
}

Table "map_items" {
  "id" UUID [unique, pk, not null]
  "map_id" UUID [not null]
  "item_id" UUID [not null]

  Indexes {
    (map_id, item_id)
  }
}

Table "map_monsters" {
  "id" UUID [unique, pk, not null]
  "map_id" uuid [not null]
  "monster_id" UUID [not null]
  "mon_map_id" UUID [not null]
  "frame" varchar(16) [not null]
}

Table "monsters" {
  "id" UUID [unique, pk, not null]
  "name" varchar(30) [not null]
  "race" varchar(16) [not null, default: "None"]
  "file" varchar(128) [not null]
  "health" int [not null, default: 1000]
  "mana" int [not null, default: 100]
  "level" smallint [not null, default: 1]
  "gold" int [not null, default: 100]
  "coins" int [not null, default: 0]
  "experience" int [not null, default: 100]
  "reputation" int [not null, default: 100]
  "dps" int [not null, default: 100]
  "speed" int [not null, default: 2000]
  "element" varchar(8) [not null, default: "None"]
  "linkage" varchar(32) [not null]
  "team_id" UUID [not null]
  "boss" int [not null, default: 0]
  "boss_min_res" int [not null, default: 0]
  "respawn_time" int [not null, default: 3]
  "hide" smallint [not null, default: 0]
  "world_boss" BOOLEAN [not null, default: FALSE]
}

Table "monster_bosses" {
  "id" UUID [unique, pk, not null]
  "monster_id" UUID [not null]
  "map_id" UUID [not null]
  "spawn_interval" bigint [not null]
  "time_limit" int [not null]
  "kills" int [default: 0]
  "deaths" int [default: 0]
  "death_time" timestamp [not null, default: `now()`]
  "spawn_time" timestamp [not null, default: `now()`]
  "description" text [default: null]
}

Table "monster_drops" {
  "id" UUID [unique, pk, not null]
  "monster_id" UUID [not null]
  "item_id" UUID [not null]
  "chance" decimal(7,2) [not null, default: 1]
  "quantity" int [not null, default: 1]

  Indexes {
    (id, monster_id, item_id)
  }
}

Table "monster_skills" {
  "id" UUID [unique, pk, not null]
  "monster_id" UUID [not null]
  "skills" int [not null]
  "skill_id" UUID [not null]
}

Table "quests" {
  "id" UUID [unique, pk, not null]
  "faction_id" UUID [not null, default: 1]
  "req_achievement" int [default: null]
  "req_reputation" int [not null, default: 0]
  "req_class_id" UUID [not null, default: `-1`]
  "req_class_points" int [not null, default: 0]
  "name" varchar(64) [not null]
  "description" text [not null]
  "end_text" text [default: null]
  "experience" bigint [not null, default: 0]
  "g_experience" int [not null, default: 0]
  "gold" bigint [not null, default: 0]
  "coins" int [not null, default: 0]
  "reputation" int [not null, default: 0]
  "class_points" int [not null, default: 0]
  "reward_type" "char (1)" [not null, default: "S"]
  "level" smallint [not null, default: 1]
  "upgrade" BOOLEAN [not null, default: FALSE]
  "once" BOOLEAN [not null, default: FALSE]
  "slot" int [not null, default: `-1`]
  "value" int [not null, default: 0]
  "field" "char (3)" [not null, default: ""]
  "index" int [not null, default: `-1`]
  "badges" "char (3)" [default: null]
  "give_membership" int [default: null]
  "war_id" UUID [not null, default: `-1`]
  "achievement_id" UUID [not null, default: `-1`]
  "war_mega" BOOLEAN [not null, default: FALSE]
  "req_guild_level" int [default: 0]
  "staff" BOOLEAN [not null, default: FALSE]
  "check" BOOLEAN [not null, default: TRUE]
}

Table "quest_locations" {
  "id" UUID [unique, pk, not null]
  "quest_id" UUID [not null]
  "map_id" UUID [not null]

  Indexes {
    (quest_id, map_id)
  }
}

Table "quest_reqditems" {
  "id" UUID [unique, pk, not null]
  "quest_id" UUID [not null]
  "item_id" UUID [not null]
  "quantity" int [not null]
}

Table "quest_requirements" {
  "id" UUID [unique, pk, not null]
  "quest_id" UUID [not null]
  "item_id" UUID [not null]
  "quantity" int [default: 1]

  Indexes {
    (quest_id, item_id, id)
  }
}

Table "quest_rewards" {
  "id" UUID [unique, pk, not null]
  "quest_id" UUID [not null]
  "item_id" UUID [not null]
  "quantity" int [not null, default: 1]
  "rate" decimal(7,2) [not null, default: 1]
  "reward_type" varchar(60) [not null, default: "S"]

  Indexes {
    (id, item_id)
  }
}

Table "redeem_codes" {
  "id" UUID [unique, pk, not null]
  "code" varchar(60) [not null]
  "coins" int [not null]
  "gold" int [not null]
  "exp" int [not null]
  "class_points" int [not null]
  "item_id" UUID [not null, default: `-1`]
  "upgrade_days" int [not null]
  "date_expiry" TIMESTAMP [not null, default: "2000-01-01 00:00:00"]
  "limit" int [not null, default: 1000]
}

Table "servers" {
  "id" UUID [unique, pk, not null]
  "name" varchar(64) [not null, default: "Server"]
  "ip" "char (18)" [not null, default: "0.0.0.0"]
  "port" int [not null, default: 5588]
  "online" BOOLEAN [not null, default: FALSE]
  "upgrade" BOOLEAN [not null, default: FALSE]
  "chat" smallint [not null, default: 2]
  "count" bigint [not null, default: 0]
  "max" bigint [not null, default: 500]
  "motd" text [not null]
  "maintenance" BOOLEAN [not null, default: FALSE]
}

Table "filter_settings" {
  "swear" varchar(60) [not null]
  "time_to_mute" int [not null]
}

Table "login_settings" {
  "name" varchar(50) [pk, not null, default: ""]
  "value" varchar(50) [not null, default: ""]
  "location" LoginLocation [not null]
}

Table "rate_settings" {
  "name" varchar(50) [pk, not null, default: ""]
  "value" varchar(50) [not null, default: ""]
}

Table "shops" {
  "id" UUID [unique, pk, not null]
  "name" varchar(32) [not null]
  "house" BOOLEAN [not null, default: FALSE]
  "upgrade" BOOLEAN [not null, default: FALSE]
  "staff" BOOLEAN [not null, default: FALSE]
  "limited" BOOLEAN [not null, default: FALSE]
  "field" varchar(8) [not null, default: ""]
  "achievement_id" UUID [default: null]
  "item_id" UUID [default: null]
}

Table "shop_items" {
  "id" UUID [unique, pk, not null]
  "shop_id" UUID [not null]
  "item_id" UUID [not null]
  "quantity_remain" int [not null, default: 0]
}

Table "shop_locations" {
  "id" UUID [unique, pk, not null]
  "shop_id" UUID [not null]
  "map_id" uniqueidentifier [not null]

  Indexes {
    (shop_id, map_id)
  }
}

Table "skills" {
  "id" UUID [unique, pk, not null]
  "name" varchar(32) [not null]
  "animation" varchar(64) [not null]
  "description" text [not null]
  "damage" decimal(7,2) [not null, default: 1]
  "mana" smallint [not null]
  "mana_back" smallint [not null]
  "life_steal" decimal(7,2) [not null, default: 0]
  "icon" varchar(32) [not null]
  "range" smallint [not null, default: 808]
  "dsrc" varchar(16) [not null]
  "reference" "char (2)" [not null]
  "target" "char (1)" [not null]
  "effects" "char (1)" [not null]
  "type" varchar(7) [not null]
  "strl" varchar(32) [not null]
  "cooldown" int [not null]
  "hit_targets" smallint [not null, default: 1]
  "pet" int [not null, default: 0]
  "chance" decimal(7,2) [default: 1]
  "show_damage" BOOLEAN [not null, default: FALSE]
}

Table "skill_auras" {
  "id" UUID [unique, pk, not null]
  "skill_id" UUID [not null]
  "aura_id" UUID [not null]

  Indexes {
    (skill_id, aura_id)
  }
}

Table "stores" {
  "id" UUID [unique, pk, not null]
  "available" smallint [not null, default: 1]
  "name" varchar(255) [not null]
  "price" decimal(7,2) [not null, default: 0]
  "item" int [default: null]
  "achievement" int [default: null]
  "gold" bigint [not null, default: 0]
  "coins" int [not null, default: 0]
  "crystal" int [not null]
  "diamonds" int [not null, default: 0]
  "upgrade" int [not null, default: 0]
  "access" int [default: null]
  "title" int [default: null]
  "bag_slots" int [not null, default: 0]
  "bank_slots" int [not null, default: 0]
  "house_slots" int [not null, default: 0]
  "type" StoreType [default: null]
  "quantity" int [not null, default: `-1`]
  "img" varchar(255) [default: null]
}

Table "titles" {
  "id" UUID [unique, pk, not null]
  "name" varchar(30) [not null]
  "description" text [not null]
  "color" varchar(8) [not null]
  "strength" int [not null]
  "intellect" int [not null]
  "endurance" int [not null]
  "dexterity" int [not null]
  "wisdom" int [not null]
  "luck" int [not null]
  "access" int [not null]
}

Table "users" {
  "id" UUID [unique, pk, not null]
  "name" varchar(32) [not null]
  "hash" "char (17)" [not null]
  "hair_id" UUID [not null]
  "access" int [not null, default: 1]
  "title" int [not null]
  "activation_flag" smallint [not null, default: 5]
  "permamute_flag" smallint [not null, default: 0]
  "country" "char (2)" [not null, default: "xx"]
  "age" smallint [not null, default: 13]
  "gender" Gender [not null, default: "M"]
  "email" varchar(64) [not null]
  "level" smallint [not null, default: 1]
  "date_created" timestamp [not null, default: `now()`]
  "last_login" date [not null]
  "achievement" int [not null, default: 0]
  "settings" int [not null, default: 0]
  "rebirth" int [not null, default: 0]
  "web_login" int [not null, default: 0]
  "user_color" varchar(11) [not null, default: "0xFFFFFF"]
  "register_address" varchar(255) [default: "0.0.0.0"]
  "socket_address" varchar(255) [default: "0.0.0.0"]
  "web_address" varchar(255) [default: "0.0.0.0"]
  "address" varchar(255) [not null, default: "0.0.0.0"]
  "token" varchar(17) [default: null]
  "avatar" "char (255)" [not null, default: "https://data.whicdn.com/images/358929274/original.gif"]

  Indexes {
    (id, name, hash)
  }
}

Table "user_stats" {
  "user_id" UUID [unique, not null]
  "last_area" varchar(64) [not null, default: "faroff-1|Enter|Spawn"]
  "current_server" varchar(16) [not null, default: "Offline"]
  "house_info" text [not null]
  "kill_count" bigint [not null, default: 0]
  "death_count" bigint [not null, default: 0]
  "pvp_ratio" bigint [default: 0]

  Indexes {
    (user_id)
  }
}

Ref: user_stats.user_id > users.id [delete: cascade]

Table "user_quests" {
  "user_id" UUID [unique, not null]
  "quests1" "char (100)" [not null, default: "0000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000"]
  "quests2" "char (100)" [not null, default: "0000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000"]
  "quests3" "char (100)" [not null, default: "0000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000"]
  "quests4" "char (100)" [not null, default: "0000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000"]
  "quests5" "char (100)" [not null, default: "0000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000"]
  "daily_quests0" int [not null, default: 0]
  "daily_quests1" int [not null, default: 0]
  "daily_quests2" int [not null, default: 0]
  "monthly_quests0" int [not null, default: 0]
  "daily_ads" int [not null, default: 0]

  Indexes {
    (user_id)
  }
}

Ref: user_quests.user_id > users.id [delete: cascade]

Table "user_colors" {
  "user_id" UUID [unique, not null]
  "color_chat"  "char (6)" [not null, default: "9CCAFD"]
  "color_username" "char (8)" [not null, default: "0xFFFFFF"]
  "color_hair" "char (6)" [not null, default: "000000"]
  "color_skin" "char (6)" [not null, default: "FFCC99"]
  "color_eye" "char (6)" [not null, default: "000000"]
  "color_base" "char (6)" [not null, default: "000000"]
  "color_trim" "char (6)" [not null, default: "000000"]
  "color_accessory" "char (6)" [not null, default: "000000"]

  Indexes {
    (user_id)
  }
}

Ref: user_colors.user_id - users.id [delete: cascade]

Table "user_currencies" {
  "user_id" UUID [unique, not null]
  "gold" bigint [not null, default: 0]
  "coins" bigint [not null, default: 0]
  "diamonds" bigint [not null, default: 0]
  "crystal" bigint [not null, default: 0]

  Indexes {
    (user_id)
  }
}

Ref: user_currencies.user_id - users.id [delete: cascade]

Table "user_exps" {
  "user_id" UUID [unique, not null]
  "exp" bigint [not null, default: 0]

  Indexes {
    (user_id)
  }

}

Ref: user_exps.user_id - users.id [delete: cascade]

Table "user_slots" {
  "user_id" UUID [unique, not null]
  "slots_bag" smallint [not null, default: 150]
  "slots_bank" smallint [not null, default: 50]
  "slots_house" smallint [not null, default: 20]
  "slots_auction" smallint [not null, default: 5] 

  Indexes {
    (user_id)
  }
}

Ref: user_slots.user_id - users.id [delete: cascade]

Table "user_boosts" {
  "user_id" UUID [unique, not null]
  "cp_boost_expire" date [not null, default: "2000-01-01 00:00:00"]
  "rep_boost_expire" date [not null, default: "2000-01-01 00:00:00"]
  "gold_boost_expire" date [not null, default: "2000-01-01 00:00:00"]
  "exp_boost_expire" date [not null, default: "2000-01-01 00:00:00"]
  "upgrade_expire" date [not null, default: "2000-01-01 00:00:00"]
  "upgrade_days" smallint [not null, default: `-1`]
  "upgraded" BOOLEAN [not null, default: FALSE]
}

Ref: user_boosts.user_id - users.id [delete: cascade]

Table "user_achievements" {
  "user_id" UUID [not null]
  "achievement_id" UUID [not null]
  "date" timestamp [not null, default: `now()`]

  Indexes {
    (user_id, achievement_id) [pk]
    (user_id, achievement_id)
  }
}

Table "user_browsers" {
  "id" UUID [unique, pk, not null]
  "user_id" UUID [not null]
  "referer" text [not null]
  "engine" varchar(50) [not null]
  "platform" varchar(50) [not null]
  "browser" varchar(50) [not null]

  Indexes {
    (id, user_id)
  }
}

Ref: user_browsers.user_id > users.id [delete: cascade]

Table "user_factions" {
  "id" UUID [unique, pk, not null]
  "user_id" UUID [not null]
  "faction_id" UUID [not null]
  "reputation" bigint [not null, default: 0]
}

Table "user_friends" {
  "id" UUID [unique, pk, not null]
  "user_id" UUID [not null]
  "friend_id" uniqueidentifier [not null]

  Indexes {
    friend_id
  }
}

Table "user_guilds" {
  "id" UUID [unique, pk, not null]
  "guild_id" UUID [not null]
  "user_id" UUID [not null]
  "rank" smallint [not null, default: 1]

  Indexes {
    (guild_id, user_id)
  }
}

Table "user_items" {
  "id" UUID [unique, pk, not null]
  "user_id" UUID [not null]
  "item_id" UUID [not null]
  "enh_id" UUID [not null]
  "equipped" smallint [not null]
  "quantity" bigint [not null]
  "bank" smallint [not null]
  "date_purchased" timestamp [not null, default: `now()`]
  "bind" smallint [default: 0]

  Indexes {
    (item_id, user_id, enh_id)
  }
}

Table "user_deleted_items" {
  "id" UUID [unique, pk, not null]
  "user_id" UUID [not null]
  "item_id" UUID [not null]
  "quantity" int [not null]
  "date" timestamp [not null, default: `now()`]

  Indexes {
    (user_id, item_id)
  }
}

Table "user_livedrops" {
  "id" UUID [unique, pk, not null]
  "user_id" UUID [default: null]
  "item_id" UUID [default: null]
  "quantity" int [default: null]
  "sent" smallint [not null, default: 0]
  "date" timestamp [not null, default: `now()`]
  "message" text [default: null]
  "achievement_id" UUID [default: null]
  "title_id" UUID [default: null]
  "experience" bigint [not null, default: 0]
  "gold" bigint [not null, default: 0]
  "coins" int [not null, default: 0]
  "crystal" bigint [not null, default: 0]
  "upgrade_days" int [not null, default: 0]
  "bag_slots" int [not null, default: 0]
  "bank_slots" int [not null, default: 0]
  "house_slots" int [not null, default: 0]

  Indexes {
    (user_id, item_id, title_id, achievement_id)
  }
}

Table "user_logins" {
  "user_id" UUID [not null]
  "location" varchar(15) [not null]
  "status" varchar(15) [not null]
  "address" varchar(255) [not null]
  "date" timestamp [not null, default: `now()`]

  Indexes {
    user_id
  }
}

Table "user_logs" {
  "id" UUID [unique, pk, not null]
  "user_id" UUID [not null]
  "map_id" UUID [not null]
  "violation" varchar(64) [not null]
  "details" text [not null]
  "date" timestamp [not null, default: `now()`]
}

Table "user_ballyhoo_logs" {
  "user_id" UUID [not null]
  "gold" UUID [not null]
  "coins" int [not null]
  "date" timestamp [not null, default: `now()`]

  Indexes {
    user_id
  }
}

Table "user_bank_logs" {
  "char_item_id" int [not null]
  "user_id" UUID [not null]
  "item_id" UUID [not null]
  "quantity" int [not null]
  "bank" smallint [not null, default: 0]
  "date" timestamp [not null, default: `now()`]
}

Table "user_duel_logs" {
  "winner_id" UUID [not null]
  "loser_id" UUID [not null]
  "date" timestamp [not null, default: `now()`]
}

Table "user_bought_item_logs" {
  "id" UUID [unique, pk, not null]
  "user_id" UUID [not null]
  "item_id" UUID [not null]
  "shop_id" UUID [not null]
  "map_id" UUID [not null]
  "date" timestamp [not null, default: `now()`]
}

Table "user_leech_logs" {
  "user_id" int [default: null]
  "directory" text [not null]
  "address" varchar(255) [not null]
}

Table "user_lottery_logs" {
  "user_id" UUID [not null]
  "item_id" UUID [not null]
  "chance" decimal(7,2) [not null]
}

Table "user_panel_logs" {
  "user_id" UUID [not null]
  "action" varchar(255) [not null]
  "details" text [not null]
  "category" varchar(255) [not null]
  "ip" varchar(55) [not null]
  "date" timestamp [not null, default: `now()`]
}

Table "user_trade_logs" {
  "id" UUID [unique, pk, not null]
  "from_user_id" UUID [not null]
  "to_user_id" UUID [not null]
  "gold" int [not null]
  "coins" int [not null]
  "date" timestamp [default: `now()`]
}

Table "user_trade_log_items" {
  "id" UUID [unique, pk, not null]
  "from_user_id" UUID [not null]
  "to_user_id" UUID [not null]
  "item_id" UUID [not null]
  "enh_id" UUID [not null]
  "quantity" int [not null]
  "date" timestamp [default: `now()`]
}

Table "user_upload_logs" {
  "id" UUID [unique, pk, not null]
  "user_id" UUID [not null]
  "file_name" varchar(100) [not null]
  "type" varchar(13) [not null]
  "date" date [not null, default: `now()`]

  Indexes {
    (id, user_id)
  }
}

Table "user_markets" {
  "id" UUID [pk, unique, not null]
  "user_id" UUID [not null]
  "item_id" UUID [not null]
  "enh_id" UUID [not null]
  "datetime" timestamp [not null, default: `now()`]
  "buyer_id" UUID [default: null]
  "coins" int [not null]
  "gold" int [not null]
  "quantity" int [not null]
  "status" smallint [not null]
  "type" MarketType [not null]
}

Table "user_market_logs" {
  "id" UUID [unique, pk, not null]
  "owner_id" UUID [not null]
  "buyer_id" UUID [default: null]
  "gold" int [not null]
  "coins" int [not null]
  "item_id" UUID [not null]
  "enh_id" UUID [not null]
  "quantity" int [not null]
  "type" MarketLogType [not null]
  "market" MarketLogMarketType [not null]
  "date" timestamp [not null, default: `now()`]
}

Table "user_purchases" {
  "id" UUID [unique, pk, not null]
  "user_id" UUID [not null]
  "payment_id" varchar(255) [not null]
  "transaction_id" varchar(255) [default: null]
  "email" varchar(255) [default: null]
  "hash" varchar(255) [not null]
  "item" varchar(255) [not null]
  "item_id" uniqueidentifier [not null]
  "price" varchar(10) [not null, default: "0.00"]
  "method" varchar(10) [not null, default: "PayPal"]
  "currency" varchar(3) [not null, default: "USD"]
  "purchased" smallint [not null, default: 0]
  "date" timestamp [not null, default: `now()`]
  "broadcast" BOOLEAN [not null, default: FALSE]

  Indexes {
    (id, user_id, payment_id)
  }
}

Table "user_redeems" {
  "id" UUID [pk, unique, not null]
  "user_id" UUID [not null]
  "redeem_id" uniqueidentifier [not null]
  "date" timestamp [not null, default: "2000-01-01 00:00:00"]
}

Table "user_reports" {
  "id" UUID [pk, unique, not null]
  "user_id" UUID [not null]
  "target_name" varchar(60) [not null]
  "category" varchar(60) [not null]
  "description" text [not null]
  "date_submitted" timestamp [not null, default: "1974-01-01 00:00:00"]
}

Table "user_resets" {
  "id" UUID [pk, unique, not null]
  "user_id" UUID [not null]
  "email" varchar(255) [not null]
  "token" varchar(255) [not null]
  "success" BOOLEAN [not null, default: FALSE]
  "date" timestamp [not null, default: `now()`]
}

Table "user_staff_logs" {
  "user_id" UUID [not null]
  "command" varchar(64) [default: null]
  "date" timestamp [not null, default: "1974-01-01 00:00:00"]
  "country" "char (3)" [not null, default: "xx"]
}

Table "user_titles" {
  "user_id" UUID [not null]
  "title_id" UUID [not null]
  "date" timestamp [not null, default: `now()`]
}

Table "wars" {
  "id" UUID [pk, unique, not null]
  "name" varchar(202) [not null]
  "points" int [not null]
  "max_points" int [not null]
}

Table "wheel_rewards" {
  "item_id" UUID [pk, not null]
  "chance" decimal(7,5) [not null, default: 1]
}

Ref "enhancements_ibfk_1":"enhancement_patterns"."id" < "enhancements"."pattern_id" [update: cascade, delete: cascade]

Ref "articles_ibfk_1":"users"."id" < "articles"."author" [update: cascade, delete: cascade]

Ref "items_ibfk_1":"classes"."id" < "items"."class_id" [update: cascade, delete: cascade]

Ref "item_bundles_ibfk_1":"items"."id" < "item_bundles"."item_id" [update: cascade, delete: cascade]

Ref "item_bundles_ibfk_2":"items"."id" < "item_bundles"."reward_id" [update: cascade, delete: cascade]

Ref "item_effects_ibfk_1":"items"."id" < "item_effects"."item_id" [update: cascade, delete: cascade]

Ref "quests_ibfk_1":"achievements"."id" < "quests"."req_achievement" [update: cascade, delete: cascade]

Ref "quests_ibfk_2":"wars"."id" < "quests"."war_id" [update: cascade, delete: cascade]

Ref "quests_ibfk_3":"achievements"."id" < "quests"."achievement_id" [update: cascade, delete: cascade]

Ref "redeem_codes_ibfk_1":"items"."id" < "redeem_codes"."item_id" [update: cascade, delete: set null]

Ref "shops_ibfk_1":"items"."id" < "shops"."item_id" [update: cascade, delete: cascade]

Ref "shop_items_ibfk_1":"items"."id" < "shop_items"."item_id" [update: cascade, delete: cascade]

Ref "shop_items_ibfk_2":"shops"."id" < "shop_items"."shop_id" [update: cascade, delete: cascade]

Ref "skill_auras_ibfk_2":"auras"."id" < "skill_auras"."aura_id" [update: cascade, delete: cascade]

Ref "skill_auras_ibfk_3":"skills"."id" < "skill_auras"."skill_id" [update: cascade, delete: cascade]

Ref "users_ibfk_1":"titles"."id" < "users"."title" [update: cascade, delete: set null]

Ref "users_ibfk_4":"access"."id" < "users"."access" [update: cascade, delete: set null]

Ref "user_achievements_ibfk_1":"users"."id" < "user_achievements"."user_id" [update: cascade, delete: cascade]

Ref "user_achievements_ibfk_2":"achievements"."id" < "user_achievements"."achievement_id" [update: cascade, delete: cascade]

Ref "user_items_ibfk_1":"items"."id" < "user_items"."item_id" [update: cascade, delete: cascade]

Ref "user_items_ibfk_2":"users"."id" < "user_items"."user_id" [update: cascade, delete: cascade]

Ref "user_deleted_items_ibfk_1":"users"."id" < "user_deleted_items"."user_id" [update: cascade, delete: cascade]

Ref "user_deleted_items_ibfk_2":"items"."id" < "user_deleted_items"."item_id" [update: cascade, delete: cascade]

Ref "user_livedrops_ibfk_1":"users"."id" < "user_livedrops"."user_id" [update: cascade, delete: set null]

Ref "user_livedrops_ibfk_2":"items"."id" < "user_livedrops"."item_id" [update: cascade, delete: set null]

Ref "user_livedrops_ibfk_5":"titles"."id" < "user_livedrops"."title_id" [update: cascade, delete: set null]

Ref "user_livedrops_ibfk_6":"achievements"."id" < "user_livedrops"."achievement_id" [update: cascade, delete: set null]

Ref "user_logins_ibfk_1":"users"."id" < "user_logins"."user_id" [update: cascade, delete: cascade]

Ref "user_ballyhoo_logs_ibfk_1":"users"."id" < "user_ballyhoo_logs"."user_id" [update: cascade, delete: cascade]

Ref "user_bank_logs_ibfk_1":"users"."id" < "user_bank_logs"."user_id" [update: cascade, delete: cascade]

Ref "user_bank_logs_ibfk_2":"items"."id" < "user_bank_logs"."item_id" [update: cascade, delete: cascade]

Ref "user_duel_logs_ibfk_1":"users"."id" < "user_duel_logs"."winner_id" [update: cascade, delete: cascade]

Ref "user_duel_logs_ibfk_2":"users"."id" < "user_duel_logs"."loser_id" [update: cascade, delete: cascade]

Ref "user_bought_item_logs_ibfk_1":"users"."id" < "user_bought_item_logs"."user_id" [update: cascade, delete: cascade]

Ref "user_bought_item_logs_ibfk_2":"shops"."id" < "user_bought_item_logs"."shop_id" [update: cascade, delete: cascade]

Ref "user_bought_item_logs_ibfk_3":"items"."id" < "user_bought_item_logs"."item_id" [update: cascade, delete: cascade]

Ref "user_bought_item_logs_ibfk_4":"maps"."id" < "user_bought_item_logs"."map_id" [update: cascade, delete: cascade]

Ref "user_lottery_logs_ibfk_1":"users"."id" < "user_lottery_logs"."user_id" [update: cascade, delete: cascade]

Ref "user_lottery_logs_ibfk_2":"items"."id" < "user_lottery_logs"."item_id" [update: cascade, delete: cascade]

Ref "user_panel_logs_ibfk_1":"users"."id" < "user_panel_logs"."user_id" [update: cascade, delete: cascade]

Ref "user_trade_log_items_ibfk_1":"users"."id" < "user_trade_log_items"."from_user_id" [update: cascade, delete: cascade]

Ref "user_trade_log_items_ibfk_2":"users"."id" < "user_trade_log_items"."to_user_id" [update: cascade, delete: cascade]

Ref "user_trade_log_items_ibfk_3":"items"."id" < "user_trade_log_items"."item_id" [update: cascade, delete: cascade]

Ref "user_upload_logs_ibfk_1":"users"."id" < "user_upload_logs"."user_id" [update: cascade, delete: cascade]

Ref "user_markets_ibfk_1":"users"."id" < "user_markets"."user_id" [update: cascade, delete: cascade]

Ref "user_markets_ibfk_2":"items"."id" < "user_markets"."item_id" [update: cascade, delete: cascade]

Ref "user_markets_ibfk_4":"users"."id" < "user_markets"."buyer_id" [update: cascade, delete: cascade]

Ref "user_market_logs_ibfk_1":"users"."id" < "user_market_logs"."owner_id" [update: cascade, delete: cascade]

Ref "user_market_logs_ibfk_2":"users"."id" < "user_market_logs"."buyer_id" [update: cascade, delete: cascade]

Ref "user_market_logs_ibfk_3":"items"."id" < "user_market_logs"."item_id" [update: cascade, delete: no action]

Ref "user_purchases_ibfk_1":"users"."id" < "user_purchases"."user_id" [update: cascade, delete: no action]

Ref "user_purchases_ibfk_3":"stores"."id" < "user_purchases"."item_id" [update: cascade, delete: no action]

Ref "user_titles_ibfk_1":"users"."id" < "user_titles"."user_id" [update: cascade, delete: cascade]

Ref "user_titles_ibfk_2":"titles"."id" < "user_titles"."title_id" [update: cascade, delete: cascade]

Ref "wheel_rewards_ibfk_1":"items"."id" < "wheel_rewards"."item_id" [update: cascade, delete: cascade]