ALTER TABLE IF EXISTS "public"."quests"
DROP CONSTRAINT "fk-quests-req_achievement";

ALTER TABLE IF EXISTS "public"."class_skills"
DROP CONSTRAINT "fk-class_skills-class_id";

ALTER TABLE IF EXISTS "public"."items"
DROP CONSTRAINT "fk-items-class_id";

ALTER TABLE IF EXISTS "public"."class_skills"
DROP CONSTRAINT "fk-class_skills-skill_id";

ALTER TABLE IF EXISTS "public"."item_skills"
DROP CONSTRAINT "fk-item_skills-skill_id";

ALTER TABLE IF EXISTS "public"."skill_auras"
DROP CONSTRAINT "fk-skill_auras-skill_id";

ALTER TABLE IF EXISTS "public"."monster_skills"
DROP CONSTRAINT "fk-monster_skills-skill_id";

ALTER TABLE IF EXISTS "public"."wheel_rewards"
DROP CONSTRAINT "fk-wheel_rewards-item_id";

ALTER TABLE IF EXISTS "public"."item_bundles"
DROP CONSTRAINT "fk-item_bundles-item_id";

ALTER TABLE IF EXISTS "public"."item_bundles"
DROP CONSTRAINT "fk-item_bundles-reward_id";

ALTER TABLE IF EXISTS "public"."item_effects"
DROP CONSTRAINT "fk-item_effects-item_id";

ALTER TABLE IF EXISTS "public"."item_lotteries"
DROP CONSTRAINT "fk-item_lotteries-item_id";

ALTER TABLE IF EXISTS "public"."item_lotteries"
DROP CONSTRAINT "fk-item_lotteries-reward_id";

ALTER TABLE IF EXISTS "public"."item_requirements"
DROP CONSTRAINT "fk-item_requirements-item_id";

ALTER TABLE IF EXISTS "public"."item_requirements"
DROP CONSTRAINT "fk-item_requirements-req_item_id";

ALTER TABLE IF EXISTS "public"."item_skills"
DROP CONSTRAINT "fk-item_skills-item_id";

ALTER TABLE IF EXISTS "public"."monster_drops"
DROP CONSTRAINT "fk-monster_drops-item_id";

ALTER TABLE IF EXISTS "public"."map_items"
DROP CONSTRAINT "fk-map_items-item_id";

ALTER TABLE IF EXISTS "public"."shops"
DROP CONSTRAINT "fk-shops-item_id";

ALTER TABLE IF EXISTS "public"."shop_items"
DROP CONSTRAINT "fk-shop_items-item_id";

ALTER TABLE IF EXISTS "public"."redeem_codes"
DROP CONSTRAINT "fk-redeem_codes-item_id";

ALTER TABLE IF EXISTS "public"."deleted_user_items"
DROP CONSTRAINT "fk-deleted_user_items-item_id";

ALTER TABLE IF EXISTS "public"."user_items"
DROP CONSTRAINT "fk-user_items-item_id";

ALTER TABLE IF EXISTS "public"."user_livedrops"
DROP CONSTRAINT "fk-user_livedrops-item_id";

ALTER TABLE IF EXISTS "public"."user_markets"
DROP CONSTRAINT "fk-user_markets-item_id";

ALTER TABLE IF EXISTS "public"."guild_hall_buildings"
DROP CONSTRAINT "fk-guild_hall_buildings-item_id";

ALTER TABLE IF EXISTS "public"."guild_items"
DROP CONSTRAINT "fk-guild_items-item_id";

ALTER TABLE IF EXISTS "public"."quest_requirements"
DROP CONSTRAINT "fk-quest_requirements-item_id";

ALTER TABLE IF EXISTS "public"."quest_rewards"
DROP CONSTRAINT "fk-quest_rewards-item_id";

ALTER TABLE IF EXISTS "public"."quest_reqditems"
DROP CONSTRAINT "fk-quest_reqditems-item_id";

ALTER TABLE IF EXISTS "public"."items"
DROP CONSTRAINT "fk-items-rarity";

ALTER TABLE IF EXISTS "public"."skill_auras"
DROP CONSTRAINT "fk-skill_auras-aura_id";

ALTER TABLE IF EXISTS "public"."aura_effects"
DROP CONSTRAINT "fk-aura_effects-aura_id";

ALTER TABLE IF EXISTS "public"."enhancements"
DROP CONSTRAINT "fk-enhancements-pattern_id";

ALTER TABLE IF EXISTS "public"."user_items"
DROP CONSTRAINT "fk-user_items-enh_id";

ALTER TABLE IF EXISTS "public"."monster_skills"
DROP CONSTRAINT "fk-monster_skills-monster_id";

ALTER TABLE IF EXISTS "public"."monster_drops"
DROP CONSTRAINT "fk-monster_drops-monster_id";

ALTER TABLE IF EXISTS "public"."map_monsters"
DROP CONSTRAINT "fk-map_monsters-monster_id";

ALTER TABLE IF EXISTS "public"."monster_bosses"
DROP CONSTRAINT "fk-monster_bosses-monster_id";

ALTER TABLE IF EXISTS "public"."map_cells"
DROP CONSTRAINT "fk-map_cells-map_id";

ALTER TABLE IF EXISTS "public"."map_items"
DROP CONSTRAINT "fk-map_items-map_id";

ALTER TABLE IF EXISTS "public"."map_monsters"
DROP CONSTRAINT "fk-map_monsters-map_id";

ALTER TABLE IF EXISTS "public"."monster_bosses"
DROP CONSTRAINT "fk-monster_bosses-map_id";

ALTER TABLE IF EXISTS "public"."shop_locations"
DROP CONSTRAINT "fk-shop_locations-map_id";

ALTER TABLE IF EXISTS "public"."quest_locations"
DROP CONSTRAINT "fk-quest_locations-map_id";

ALTER TABLE IF EXISTS "public"."shop_items"
DROP CONSTRAINT "fk-shop_items-shop_id";

ALTER TABLE IF EXISTS "public"."shop_locations"
DROP CONSTRAINT "fk-shop_locations-shop_id";

ALTER TABLE IF EXISTS "public"."sessions"
DROP CONSTRAINT "fk-sessions-user_id";

ALTER TABLE IF EXISTS "public"."user_achievements"
DROP CONSTRAINT "fk-user_achievements-user_id";

ALTER TABLE IF EXISTS "public"."user_boosts"
DROP CONSTRAINT "fk-user_boosts-user_id";

ALTER TABLE IF EXISTS "public"."user_browsers"
DROP CONSTRAINT "fk-user_browsers-user_id";

ALTER TABLE IF EXISTS "public"."user_colors"
DROP CONSTRAINT "fk-user_colors-user_id";

ALTER TABLE IF EXISTS "public"."user_currencies"
DROP CONSTRAINT "fk-user_currencies-user_id";

ALTER TABLE IF EXISTS "public"."user_exps"
DROP CONSTRAINT "fk-user_exps-user_id";

ALTER TABLE IF EXISTS "public"."user_factions"
DROP CONSTRAINT "fk-user_factions-user_id";

ALTER TABLE IF EXISTS "public"."user_friends"
DROP CONSTRAINT "fk-user_friends-user_id";

ALTER TABLE IF EXISTS "public"."user_friends"
DROP CONSTRAINT "fk-user_friends-friend_id";

ALTER TABLE IF EXISTS "public"."deleted_user_items"
DROP CONSTRAINT "fk-deleted_user_items-user_id";

ALTER TABLE IF EXISTS "public"."user_items"
DROP CONSTRAINT "fk-user_items-user_id";

ALTER TABLE IF EXISTS "public"."user_livedrops"
DROP CONSTRAINT "fk-user_livedrops-user_id";

ALTER TABLE IF EXISTS "public"."user_logins"
DROP CONSTRAINT "fk-user_logins-user_id";

ALTER TABLE IF EXISTS "public"."user_markets"
DROP CONSTRAINT "fk-user_markets-user_id";

ALTER TABLE IF EXISTS "public"."user_markets"
DROP CONSTRAINT "fk-user_markets-buyer_id";

ALTER TABLE IF EXISTS "public"."user_purchases"
DROP CONSTRAINT "fk-user_purchases-user_id";

ALTER TABLE IF EXISTS "public"."user_redeems"
DROP CONSTRAINT "fk-user_redeems-user_id";

ALTER TABLE IF EXISTS "public"."user_reports"
DROP CONSTRAINT "fk-user_reports-user_id";

ALTER TABLE IF EXISTS "public"."user_slots"
DROP CONSTRAINT "fk-user_slots-user_id";

ALTER TABLE IF EXISTS "public"."user_stats"
DROP CONSTRAINT "fk-user_stats-user_id";

ALTER TABLE IF EXISTS "public"."user_titles"
DROP CONSTRAINT "fk-user_titles-user_id";

ALTER TABLE IF EXISTS "public"."guilds"
DROP CONSTRAINT "fk-guilds-staff_g";

ALTER TABLE IF EXISTS "public"."user_guilds"
DROP CONSTRAINT "fk-user_guilds-user_id";

ALTER TABLE IF EXISTS "public"."guild_items"
DROP CONSTRAINT "fk-guild_items-user_id";

ALTER TABLE IF EXISTS "public"."articles"
DROP CONSTRAINT "fk-articles-author_id";

ALTER TABLE IF EXISTS "public"."user_quests"
DROP CONSTRAINT "fk-user_quests-user_id";

ALTER TABLE IF EXISTS "public"."users"
DROP CONSTRAINT "fk-users-title_id";

ALTER TABLE IF EXISTS "public"."user_livedrops"
DROP CONSTRAINT "fk-user_livedrops-title_id";

ALTER TABLE IF EXISTS "public"."user_titles"
DROP CONSTRAINT "fk-user_titles-title_id";

ALTER TABLE IF EXISTS "public"."user_redeems"
DROP CONSTRAINT "fk-user_redeems-redeem_id";

ALTER TABLE IF EXISTS "public"."user_achievements"
DROP CONSTRAINT "fk-user_achievements-achievement_id";

ALTER TABLE IF EXISTS "public"."user_livedrops"
DROP CONSTRAINT "fk-user_livedrops-achievement_id";

ALTER TABLE IF EXISTS "public"."quests"
DROP CONSTRAINT "fk-quests-achievement_id";

ALTER TABLE IF EXISTS "public"."user_factions"
DROP CONSTRAINT "fk-user_factions-faction_id";

ALTER TABLE IF EXISTS "public"."user_purchases"
DROP CONSTRAINT "fk-user_purchases-item_id";

ALTER TABLE IF EXISTS "public"."user_guilds"
DROP CONSTRAINT "fk-user_guilds-guild_id";

ALTER TABLE IF EXISTS "public"."guild_halls"
DROP CONSTRAINT "fk-guild_halls-guild_id";

ALTER TABLE IF EXISTS "public"."guild_levels"
DROP CONSTRAINT "fk-guild_levels-guild_id";

ALTER TABLE IF EXISTS "public"."guild_items"
DROP CONSTRAINT "fk-guild_items-guild_id";

ALTER TABLE IF EXISTS "public"."guild_hall_buildings"
DROP CONSTRAINT "fk-guild_hall_buildings-hall_id";

ALTER TABLE IF EXISTS "public"."guild_hall_connections"
DROP CONSTRAINT "fk-guild_hall_connections-hall_id";

ALTER TABLE IF EXISTS "public"."book_quests"
DROP CONSTRAINT "fk-book_quests-id";

ALTER TABLE IF EXISTS "public"."hair_shop_items"
DROP CONSTRAINT "fk-hair_shop_items-hair_id";

ALTER TABLE IF EXISTS "public"."hair_shop_items"
DROP CONSTRAINT "fk-hair_shop_items-shop_id";

ALTER TABLE IF EXISTS "public"."quest_locations"
DROP CONSTRAINT "fk-quest_locations-quest_id";

ALTER TABLE IF EXISTS "public"."quest_requirements"
DROP CONSTRAINT "fk-quest_requirements-quest_id";

ALTER TABLE IF EXISTS "public"."quest_rewards"
DROP CONSTRAINT "fk-quest_rewards-quest_id";

ALTER TABLE IF EXISTS "public"."quest_reqditems"
DROP CONSTRAINT "fk-quest_reqditems-quest_id";

ALTER TABLE IF EXISTS "public"."quests"
DROP CONSTRAINT "fk-quests-war_id";

ALTER TABLE IF EXISTS "public"."profiles"
DROP CONSTRAINT "fk-users-profile";

ALTER TABLE IF EXISTS "public"."users"
DROP CONSTRAINT "fk-users-role";

ALTER TABLE IF EXISTS "public"."password_reset_tokens"
DROP CONSTRAINT "fk-password_reset_tokens-user_id";

DROP TABLE IF EXISTS "public"."password_reset_tokens";

DROP TABLE IF EXISTS "public"."class_categories";

DROP TABLE IF EXISTS "public"."classes";

DROP TABLE IF EXISTS "public"."skills";

DROP TABLE IF EXISTS "public"."class_skills";

DROP TABLE IF EXISTS "public"."items";

DROP TABLE IF EXISTS "public"."item_rarities";

DROP TABLE IF EXISTS "public"."wheel_rewards";

DROP TABLE IF EXISTS "public"."item_bundles";

DROP TABLE IF EXISTS "public"."item_effects";

DROP TABLE IF EXISTS "public"."item_lotteries";

DROP TABLE IF EXISTS "public"."item_requirements";

DROP TABLE IF EXISTS "public"."item_skills";

DROP TABLE IF EXISTS "public"."auras";

DROP TABLE IF EXISTS "public"."skill_auras";

DROP TABLE IF EXISTS "public"."aura_effects";

DROP TABLE IF EXISTS "public"."enhancement_patterns";

DROP TABLE IF EXISTS "public"."enhancements";

DROP TABLE IF EXISTS "public"."monsters";

DROP TABLE IF EXISTS "public"."monster_skills";

DROP TABLE IF EXISTS "public"."monster_drops";

DROP TABLE IF EXISTS "public"."maps";

DROP TABLE IF EXISTS "public"."map_cells";

DROP TABLE IF EXISTS "public"."map_items";

DROP TABLE IF EXISTS "public"."map_monsters";

DROP TABLE IF EXISTS "public"."monster_bosses";

DROP TABLE IF EXISTS "public"."shops";

DROP TABLE IF EXISTS "public"."shop_items";

DROP TABLE IF EXISTS "public"."shop_locations";

DROP TABLE IF EXISTS "public"."users";

DROP TABLE IF EXISTS "public"."profiles";

DROP TABLE IF EXISTS "public"."titles";

DROP TABLE IF EXISTS "public"."redeem_codes";

DROP TABLE IF EXISTS "public"."sessions";

DROP TABLE IF EXISTS "public"."achievements";

DROP TABLE IF EXISTS "public"."user_achievements";

DROP TABLE IF EXISTS "public"."user_boosts";

DROP TABLE IF EXISTS "public"."user_browsers";

DROP TABLE IF EXISTS "public"."user_colors";

DROP TABLE IF EXISTS "public"."user_currencies";

DROP TABLE IF EXISTS "public"."user_exps";

DROP TABLE IF EXISTS "public"."user_factions";

DROP TABLE IF EXISTS "public"."factions";

DROP TABLE IF EXISTS "public"."user_friends";

DROP TABLE IF EXISTS "public"."deleted_user_items";

DROP TABLE IF EXISTS "public"."user_items";

DROP TABLE IF EXISTS "public"."user_livedrops";

DROP TABLE IF EXISTS "public"."user_logins";

DROP TABLE IF EXISTS "public"."user_markets";

DROP TABLE IF EXISTS "public"."stores";

DROP TABLE IF EXISTS "public"."user_purchases";

DROP TABLE IF EXISTS "public"."user_redeems";

DROP TABLE IF EXISTS "public"."user_reports";

DROP TABLE IF EXISTS "public"."user_slots";

DROP TABLE IF EXISTS "public"."user_stats";

DROP TABLE IF EXISTS "public"."user_titles";

DROP TABLE IF EXISTS "public"."guilds";

DROP TABLE IF EXISTS "public"."user_guilds";

DROP TABLE IF EXISTS "public"."guild_halls";

DROP TABLE IF EXISTS "public"."guild_hall_buildings";

DROP TABLE IF EXISTS "public"."guild_hall_connections";

DROP TABLE IF EXISTS "public"."guild_levels";

DROP TABLE IF EXISTS "public"."guild_items";

DROP TABLE IF EXISTS "public"."articles";

DROP TABLE IF EXISTS "public"."cms_articles";

DROP TABLE IF EXISTS "public"."books";

DROP TABLE IF EXISTS "public"."book_quests";

DROP TABLE IF EXISTS "public"."hairs";

DROP TABLE IF EXISTS "public"."hair_shop_items";

DROP TABLE IF EXISTS "public"."hair_shops";

DROP TABLE IF EXISTS "public"."quests";

DROP TABLE IF EXISTS "public"."wars";

DROP TABLE IF EXISTS "public"."user_quests";

DROP TABLE IF EXISTS "public"."quest_locations";

DROP TABLE IF EXISTS "public"."quest_requirements";

DROP TABLE IF EXISTS "public"."quest_rewards";

DROP TABLE IF EXISTS "public"."quest_reqditems";

DROP TABLE IF EXISTS "public"."admin_uploads";

DROP TABLE IF EXISTS "public"."servers";

DROP TABLE IF EXISTS "public"."discord_commands";

DROP TABLE IF EXISTS "public"."profanity_filter_settings";

DROP TABLE IF EXISTS "public"."game_settings";

DROP TABLE IF EXISTS "public"."game_rates_settings";

DROP TABLE IF EXISTS "public"."roles";

DROP TYPE IF EXISTS "public"."user_role";

DROP TYPE IF EXISTS "public"."gender";

DROP TYPE IF EXISTS "public"."login_location";

DROP TYPE IF EXISTS "public"."store_type";

DROP TYPE IF EXISTS "public"."market_type";
