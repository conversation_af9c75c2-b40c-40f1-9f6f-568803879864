//! `SeaORM` Entity, @generated by sea-orm-codegen 1.1.0

use sea_orm::entity::prelude::*;
use serde::Deserialize;

#[derive(Clone, Debug, PartialEq, DeriveEntityModel, Eq, Deserialize)]
#[sea_orm(table_name = "items")]
pub struct Model {
    #[sea_orm(primary_key, auto_increment = false, unique)]
    pub id: Uuid,
    pub class_id: Option<Uuid>,
    pub name: String,
    #[sea_orm(column_type = "Text")]
    pub description: String,
    pub r#type: String,
    pub element: String,
    pub file: String,
    pub link: String,
    pub icon: String,
    pub equipment: String,
    pub level: i16,
    pub dps: i16,
    pub range: i16,
    #[sea_orm(primary_key, auto_increment = false)]
    pub rarity: Uuid,
    pub quantity: i16,
    pub stack: i32,
    pub cost: i32,
    pub coins: i16,
    pub diamonds: i16,
    pub crystal: i64,
    pub sell: bool,
    pub market: bool,
    pub temporary: bool,
    pub upgrade: bool,
    pub staff: bool,
    pub enh_id: Option<Uuid>,
    pub faction_id: Option<Uuid>,
    pub req_reputation: i64,
    pub req_class_id: Option<Uuid>,
    pub req_class_points: i64,
    pub req_quests: String,
    pub quest_string_index: i16,
    pub quest_string_value: i16,
    pub meta: Option<String>,
    pub color: String,
}

#[derive(Copy, Clone, Debug, EnumIter, DeriveRelation)]
pub enum Relation {
    #[sea_orm(
        belongs_to = "super::classes::Entity",
        from = "Column::ClassId",
        to = "super::classes::Column::Id",
        on_update = "Cascade",
        on_delete = "SetNull"
    )]
    Classes,
    #[sea_orm(has_many = "super::deleted_user_items::Entity")]
    DeletedUserItems,
    #[sea_orm(
        belongs_to = "super::enhancements::Entity",
        from = "Column::EnhId",
        to = "super::enhancements::Column::Id",
        on_update = "Cascade",
        on_delete = "SetNull"
    )]
    Enhancements,
    #[sea_orm(has_many = "super::guild_hall_buildings::Entity")]
    GuildHallBuildings,
    #[sea_orm(has_many = "super::guild_items::Entity")]
    GuildItems,
    #[sea_orm(has_many = "super::item_effects::Entity")]
    ItemEffects,
    #[sea_orm(
        belongs_to = "super::item_rarities::Entity",
        from = "Column::Rarity",
        to = "super::item_rarities::Column::Id",
        on_update = "Cascade",
        on_delete = "Cascade"
    )]
    ItemRarities,
    #[sea_orm(has_many = "super::item_skills::Entity")]
    ItemSkills,
    #[sea_orm(has_many = "super::map_items::Entity")]
    MapItems,
    #[sea_orm(has_many = "super::monster_drops::Entity")]
    MonsterDrops,
    #[sea_orm(has_many = "super::quest_reqditems::Entity")]
    QuestReqditems,
    #[sea_orm(has_many = "super::quest_requirements::Entity")]
    QuestRequirements,
    #[sea_orm(has_many = "super::quest_rewards::Entity")]
    QuestRewards,
    #[sea_orm(has_many = "super::redeem_codes::Entity")]
    RedeemCodes,
    #[sea_orm(has_many = "super::shop_items::Entity")]
    ShopItems,
    #[sea_orm(has_many = "super::shops::Entity")]
    Shops,
    #[sea_orm(has_many = "super::user_items::Entity")]
    UserItems,
    #[sea_orm(has_many = "super::user_livedrops::Entity")]
    UserLivedrops,
    #[sea_orm(has_many = "super::user_markets::Entity")]
    UserMarkets,
    #[sea_orm(has_many = "super::wheel_rewards::Entity")]
    WheelRewards,
}

impl Related<super::classes::Entity> for Entity {
    fn to() -> RelationDef {
        Relation::Classes.def()
    }
}

impl Related<super::deleted_user_items::Entity> for Entity {
    fn to() -> RelationDef {
        Relation::DeletedUserItems.def()
    }
}

impl Related<super::enhancements::Entity> for Entity {
    fn to() -> RelationDef {
        Relation::Enhancements.def()
    }
}

impl Related<super::guild_hall_buildings::Entity> for Entity {
    fn to() -> RelationDef {
        Relation::GuildHallBuildings.def()
    }
}

impl Related<super::guild_items::Entity> for Entity {
    fn to() -> RelationDef {
        Relation::GuildItems.def()
    }
}

impl Related<super::item_effects::Entity> for Entity {
    fn to() -> RelationDef {
        Relation::ItemEffects.def()
    }
}

impl Related<super::item_rarities::Entity> for Entity {
    fn to() -> RelationDef {
        Relation::ItemRarities.def()
    }
}

impl Related<super::item_skills::Entity> for Entity {
    fn to() -> RelationDef {
        Relation::ItemSkills.def()
    }
}

impl Related<super::map_items::Entity> for Entity {
    fn to() -> RelationDef {
        Relation::MapItems.def()
    }
}

impl Related<super::monster_drops::Entity> for Entity {
    fn to() -> RelationDef {
        Relation::MonsterDrops.def()
    }
}

impl Related<super::quest_reqditems::Entity> for Entity {
    fn to() -> RelationDef {
        Relation::QuestReqditems.def()
    }
}

impl Related<super::quest_requirements::Entity> for Entity {
    fn to() -> RelationDef {
        Relation::QuestRequirements.def()
    }
}

impl Related<super::quest_rewards::Entity> for Entity {
    fn to() -> RelationDef {
        Relation::QuestRewards.def()
    }
}

impl Related<super::redeem_codes::Entity> for Entity {
    fn to() -> RelationDef {
        Relation::RedeemCodes.def()
    }
}

impl Related<super::shop_items::Entity> for Entity {
    fn to() -> RelationDef {
        Relation::ShopItems.def()
    }
}

impl Related<super::shops::Entity> for Entity {
    fn to() -> RelationDef {
        Relation::Shops.def()
    }
}

impl Related<super::user_items::Entity> for Entity {
    fn to() -> RelationDef {
        Relation::UserItems.def()
    }
}

impl Related<super::user_livedrops::Entity> for Entity {
    fn to() -> RelationDef {
        Relation::UserLivedrops.def()
    }
}

impl Related<super::user_markets::Entity> for Entity {
    fn to() -> RelationDef {
        Relation::UserMarkets.def()
    }
}

impl Related<super::wheel_rewards::Entity> for Entity {
    fn to() -> RelationDef {
        Relation::WheelRewards.def()
    }
}

impl Related<super::class_skills::Entity> for Entity {
    fn to() -> RelationDef {
        super::classes::Relation::ClassSkills.def()
    }
    fn via() -> Option<RelationDef> {
        Some(super::classes::Relation::Items.def().rev())
    }
}

impl ActiveModelBehavior for ActiveModel {}
