use reforged_domain::models::quest_reward::entity::Quest<PERSON>ew<PERSON>;
use reforged_domain::models::quest_reward::value_object::*;
use reforged_domain::models::quest::value_object::QuestId;
use reforged_domain::models::item::value_object::ItemId;
use num_traits::ToPrimitive;

#[derive(Debug)]
pub struct QuestRewardDbModelMapper(super::super::models::quest_rewards::Model);

impl QuestRewardDbModelMapper {
    pub fn new(model: super::super::models::quest_rewards::Model) -> Self {
        Self(model)
    }
}

impl From<QuestRewardDbModelMapper> for QuestReward {
    fn from(value: QuestRewardDbModelMapper) -> Self {
        let model = value.0;

        QuestReward::builder()
            .id(QuestRewardId::new(model.id))
            .quest_id(QuestId::new(model.quest_id))
            .item_id(ItemId::new(model.item_id))
            .quantity(QuestRewardQuantity::new(model.quantity.try_into().unwrap_or(0)))
            .rate(QuestRewardRate::new(model.rate.to_f64().unwrap_or(0.0)))
            .reward_type(QuestRewardType::new(model.reward_type))
            .build()
    }
}
