use reforged_domain::models::quest_reward::entity::QuestReward;
use reforged_domain::models::quest_reward::value_object::QuestRewardId;
use reforged_domain::models::quest::value_object::QuestId;
use reforged_domain::models::item::value_object::ItemId;

#[derive(Debug)]
pub struct QuestRewardDbModelMapper(super::super::models::quest_rewards::Model);

impl QuestRewardDbModelMapper {
    pub fn new(model: super::super::models::quest_rewards::Model) -> Self {
        Self(model)
    }
}

impl From<QuestRewardDbModelMapper> for QuestReward {
    fn from(value: QuestRewardDbModelMapper) -> Self {
        let model = value.0;
        
        QuestReward::builder()
            .id(QuestRewardId::new(model.id))
            .quest_id(QuestId::new(model.quest_id))
            .maybe_item_id(model.item_id.map(ItemId::new))
            .experience(model.experience.into())
            .gold(model.gold.into())
            .coins(model.coins.into())
            .quantity(model.quantity.into())
            .build()
    }
}
