use async_trait::async_trait;
use mockall::automock;

use crate::error::RepositoryError;
use crate::models::hair::entity::Hair;
use crate::models::hair::value_object::HairId;

#[automock]
#[async_trait]
pub trait HairRepository: Send + Sync {
    async fn save(&self, entity: &Hair) -> Result<(), RepositoryError>;
    async fn update(&self, entity: &Hair) -> Result<(), RepositoryError>;
    async fn delete(&self, id: &HairId) -> Result<(), RepositoryError>;
}

#[automock]
#[async_trait]
pub trait HairReadRepository: Send + Sync {
    async fn find_by_id(&self, id: &HairId) -> Result<Option<Hair>, RepositoryError>;
    async fn find_all(&self) -> Result<Vec<Hair>, RepositoryError>;
}
