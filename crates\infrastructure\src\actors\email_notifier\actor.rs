use std::sync::Arc;

use actix::prelude::*;
use actix_broker::BrokerSubscribe;
use tracing::info;

use crate::actors::message_broker::message::Publish;
use reforged_application::services::email_service::EmailService;
use reforged_domain::events::user_events::{
    ResetPasswordRequested, UserCreated, UserPasswordChanged,
};

pub type NotificationActorAddr = Addr<NotificationActor>;

pub struct NotificationActor {
    client_url: String,
    email_service: Arc<dyn EmailService>,
}

impl NotificationActor {
    pub fn new(client_url: String, email_service: Arc<dyn EmailService>) -> Self {
        Self {
            client_url,
            email_service,
        }
    }

    pub fn email_service(&self) -> Arc<dyn EmailService> {
        self.email_service.clone()
    }

    pub fn client_url(&self) -> &str {
        &self.client_url
    }
}

impl Actor for NotificationActor {
    type Context = actix::Context<Self>;

    fn started(&mut self, ctx: &mut Self::Context) {
        info!("Notification actor started");
        self.subscribe_system_async::<Publish<UserCreated>>(ctx);
        self.subscribe_system_async::<Publish<ResetPasswordRequested>>(ctx);
        self.subscribe_system_async::<Publish<UserPasswordChanged>>(ctx);
    }

    fn stopped(&mut self, _ctx: &mut Self::Context) {
        info!("Notification actor stopped");
    }
}
