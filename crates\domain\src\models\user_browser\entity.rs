use super::value_object::{<PERSON><PERSON><PERSON>, <PERSON>, Platform, Referer, UserBrowserId};
use crate::models::user::value_object::UserId;
use getset::{<PERSON><PERSON>, Set<PERSON>};

#[derive(<PERSON>bu<PERSON>, <PERSON><PERSON>, De<PERSON>ult, bon::<PERSON><PERSON><PERSON>, <PERSON><PERSON>, Set<PERSON>)]
#[getset(get = "pub", set = "pub")]
pub struct UserBrowser {
    id: UserBrowserId,
    user_id: UserId,
    referer: Referer,
    engine: Engine,
    platform: Platform,
    browser: Browser,
}

impl UserBrowser {
    pub fn new(
        id: UserBrowserId,
        user_id: UserId,
        referer: Referer,
        engine: Engine,
        platform: Platform,
        browser: Browser,
    ) -> Self {
        Self {
            id,
            user_id,
            referer,
            engine,
            platform,
            browser,
        }
    }
}

#[cfg(test)]
mod tests {
    use reforged_shared::{IdTrait, Value};

    use super::*;

    #[test]
    fn test_user_browser_new() {
        let id = UserBrowserId::new(uuid::Uuid::now_v7());
        let user_id = UserId::new(uuid::Uuid::now_v7());
        let referer = Referer::new("test-referer");
        let engine = Engine::new("test-engine");
        let platform = Platform::new("test-platform");
        let browser = Browser::new("test-browser");

        let user_browser = UserBrowser::new(
            id.clone(),
            user_id.clone(),
            referer.clone(),
            engine.clone(),
            platform.clone(),
            browser.clone(),
        );

        assert_eq!(user_browser.id.get_id(), id.get_id());
        assert_eq!(user_browser.user_id.get_id(), user_id.get_id());
        assert_eq!(user_browser.referer.value(), referer.value());
        assert_eq!(user_browser.engine.value(), engine.value());
        assert_eq!(user_browser.platform.value(), platform.value());
        assert_eq!(user_browser.browser.value(), browser.value());
    }

    #[test]
    fn test_user_browser_builder() {
        let id = UserBrowserId::new(uuid::Uuid::now_v7());
        let user_id = UserId::new(uuid::Uuid::now_v7());
        let referer = Referer::new("test-referer");
        let engine = Engine::new("test-engine");
        let platform = Platform::new("test-platform");
        let browser = Browser::new("test-browser");

        let user_browser = UserBrowser::builder()
            .id(id.clone())
            .user_id(user_id.clone())
            .referer(referer.clone())
            .engine(engine.clone())
            .platform(platform.clone())
            .browser(browser.clone())
            .build();

        assert_eq!(user_browser.id.get_id(), id.get_id());
        assert_eq!(user_browser.user_id.get_id(), user_id.get_id());
        assert_eq!(user_browser.referer.value(), referer.value());
        assert_eq!(user_browser.engine.value(), engine.value());
        assert_eq!(user_browser.platform.value(), platform.value());
        assert_eq!(user_browser.browser.value(), browser.value());
    }

    #[test]
    fn test_getters_and_setters() {
        let mut user_browser = UserBrowser::default();

        let id = UserBrowserId::new(uuid::Uuid::now_v7());
        let user_id = UserId::new(uuid::Uuid::now_v7());
        let referer = Referer::new("test-referer");
        let engine = Engine::new("test-engine");
        let platform = Platform::new("test-platform");
        let browser = Browser::new("test-browser");

        user_browser.set_id(id.clone());
        user_browser.set_user_id(user_id.clone());
        user_browser.set_referer(referer.clone());
        user_browser.set_engine(engine.clone());
        user_browser.set_platform(platform.clone());
        user_browser.set_browser(browser.clone());

        assert_eq!(user_browser.id().get_id(), id.get_id());
        assert_eq!(user_browser.user_id().get_id(), user_id.get_id());
        assert_eq!(user_browser.referer().value(), referer.value());
        assert_eq!(user_browser.engine().value(), engine.value());
        assert_eq!(user_browser.platform().value(), platform.value());
        assert_eq!(user_browser.browser().value(), browser.value());
    }
}
