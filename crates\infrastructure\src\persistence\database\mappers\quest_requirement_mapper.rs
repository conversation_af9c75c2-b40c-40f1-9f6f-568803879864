use reforged_domain::models::quest_requirement::entity::QuestRequirement;
use reforged_domain::models::quest_requirement::value_object::QuestRequirementId;
use reforged_domain::models::quest::value_object::QuestId;
use reforged_domain::models::item::value_object::ItemId;

#[derive(Debug)]
pub struct QuestRequirementDbModelMapper(super::super::models::quest_requirements::Model);

impl QuestRequirementDbModelMapper {
    pub fn new(model: super::super::models::quest_requirements::Model) -> Self {
        Self(model)
    }
}

impl From<QuestRequirementDbModelMapper> for QuestRequirement {
    fn from(value: QuestRequirementDbModelMapper) -> Self {
        let model = value.0;
        
        QuestRequirement::builder()
            .id(QuestRequirementId::new(model.id))
            .quest_id(QuestId::new(model.quest_id))
            .maybe_item_id(model.item_id.map(ItemId::new))
            .maybe_class_id(model.class_id.map(|id| id.into()))
            .maybe_faction_id(model.faction_id.map(|id| id.into()))
            .level(model.level.into())
            .quantity(model.quantity.into())
            .build()
    }
}
