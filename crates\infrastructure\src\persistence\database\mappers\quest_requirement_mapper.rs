use reforged_domain::models::quest_requirement::entity::QuestRequirement;
use reforged_domain::models::quest_requirement::value_object::QuestRequirementId;
use reforged_domain::models::quest::value_object::QuestId;
use reforged_domain::models::item::value_object::ItemId;

#[derive(Debug)]
pub struct QuestRequirementDbModelMapper(super::super::models::quest_requirements::Model);

impl QuestRequirementDbModelMapper {
    pub fn new(model: super::super::models::quest_requirements::Model) -> Self {
        Self(model)
    }
}

impl From<QuestRequirementDbModelMapper> for QuestRequirement {
    fn from(value: QuestRequirementDbModelMapper) -> Self {
        let model = value.0;

        QuestRequirement::builder()
            .id(QuestRequirementId::new(model.id))
            .quest_id(QuestId::new(model.quest_id))
            .item_id(ItemId::new(model.item_id))
            .quantity(model.quantity.into())
            .build()
    }
}
