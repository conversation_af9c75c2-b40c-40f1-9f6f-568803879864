use async_trait::async_trait;
use mockall::automock;

use crate::error::RepositoryError;
use crate::models::discord_command::entity::DiscordCommand;
use crate::models::discord_command::value_object::DiscordCommandId;

#[automock]
#[async_trait]
pub trait DiscordCommandRepository: Send + Sync {
    async fn save(&self, entity: &DiscordCommand) -> Result<(), RepositoryError>;
    async fn update(&self, entity: &DiscordCommand) -> Result<(), RepositoryError>;
    async fn delete(&self, id: &DiscordCommandId) -> Result<(), RepositoryError>;
}

#[automock]
#[async_trait]
pub trait DiscordCommandReadRepository: Send + Sync {
    async fn find_by_id(
        &self,
        id: &DiscordCommandId,
    ) -> Result<Option<DiscordCommand>, RepositoryError>;
    async fn find_all(&self) -> Result<Vec<DiscordCommand>, RepositoryError>;
}
