use std::sync::Arc;

use reforged_domain::{
    models::class_category::value_object::ClassCategoryId,
    repository::class_category_repository::ClassCategoryRepository,
};

use crate::error::ApplicationError;

pub struct DeleteClassCategoryUsecase {
    class_category_repository: Arc<dyn ClassCategoryRepository>,
}

impl DeleteClassCategoryUsecase {
    pub fn new(class_category_repository: Arc<dyn ClassCategoryRepository>) -> Self {
        Self {
            class_category_repository,
        }
    }
}

impl DeleteClassCategoryUsecase {
    pub async fn execute(&self, id: uuid::Uuid) -> Result<(), ApplicationError> {
        let id = ClassCategoryId::new(id);
        self.class_category_repository.delete(&id).await?;

        Ok(())
    }
}
