use reforged_domain::models::user_quest::entity::UserQuest;
use reforged_domain::models::user_quest::value_object::UserQuestId;
use reforged_domain::models::user::value_object::UserId;
use reforged_domain::models::quest::value_object::QuestId;
use chrono::{DateTime, Utc};

#[derive(Debug)]
pub struct UserQuestDbModelMapper(super::super::models::user_quests::Model);

impl UserQuestDbModelMapper {
    pub fn new(model: super::super::models::user_quests::Model) -> Self {
        Self(model)
    }
}

impl From<UserQuestDbModelMapper> for UserQuest {
    fn from(value: UserQuestDbModelMapper) -> Self {
        let model = value.0;
        
        UserQuest::builder()
            .id(UserQuestId::new(model.id))
            .user_id(UserId::new(model.user_id))
            .quest_id(QuestId::new(model.quest_id))
            .progress(model.progress.into())
            .completed(model.completed.into())
            .maybe_completed_at(model.completed_at.map(|dt| DateTime::<Utc>::from_naive_utc_and_offset(dt, Utc).into()))
            .build()
    }
}
