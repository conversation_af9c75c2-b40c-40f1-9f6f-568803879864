use reforged_domain::models::user_quest::entity::UserQuest;
use reforged_domain::models::user_quest::value_object::*;
use reforged_domain::models::user::value_object::UserId;

#[derive(Debug)]
pub struct UserQuestDbModelMapper(super::super::models::user_quests::Model);

impl UserQuestDbModelMapper {
    pub fn new(model: super::super::models::user_quests::Model) -> Self {
        Self(model)
    }
}

impl From<UserQuestDbModelMapper> for UserQuest {
    fn from(value: UserQuestDbModelMapper) -> Self {
        let model = value.0;

        UserQuest::builder()
            .id(UserQuestId::new(model.id))
            .user_id(UserId::new(model.user_id))
            .quests1(UserQuestQuests1::new(model.quests1))
            .quests2(UserQuestQuests2::new(model.quests2))
            .quests3(UserQuestQuests3::new(model.quests3))
            .quests4(UserQuestQuests4::new(model.quests4))
            .quests5(UserQuestQuests5::new(model.quests5))
            .daily_quests0(UserQuestDailyQuests0::new(model.daily_quests0.try_into().unwrap_or(0)))
            .daily_quests1(UserQuestDailyQuests1::new(model.daily_quests1.try_into().unwrap_or(0)))
            .daily_quests2(UserQuestDailyQuests2::new(model.daily_quests2.try_into().unwrap_or(0)))
            .monthly_quests0(UserQuestMonthlyQuests0::new(model.monthly_quests0.try_into().unwrap_or(0)))
            .daily_ads(UserQuestDailyAds::new(model.daily_ads.try_into().unwrap_or(0)))
            .build()
    }
}
