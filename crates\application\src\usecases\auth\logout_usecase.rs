use chrono::{DateTime, Utc};
use serde::Serialize;

use crate::error::ApplicationError;

#[derive(Serialize)]
pub struct LogoutResponse {
    logged_out_at: DateTime<Utc>,
}

pub struct LogoutUsecase;

impl LogoutUsecase {
    pub fn new() -> Self {
        Self
    }
}

impl LogoutUsecase {
    pub async fn execute(
        &self,
        _refresh_token: String,
    ) -> Result<LogoutResponse, ApplicationError> {
        // TODO: Implement logout logic here
        // TODO: remove the session in the database using the session id from refresh token

        let now = Utc::now();
        let response = LogoutResponse { logged_out_at: now };

        Ok(response)
    }
}
