use async_trait::async_trait;
use mockall::automock;

use crate::error::RepositoryError;
use crate::models::guild_hall::entity::GuildHall;
use crate::models::guild_hall::value_object::GuildHallId;

#[automock]
#[async_trait]
pub trait GuildHallRepository: Send + Sync {
    async fn save(&self, entity: &GuildHall) -> Result<(), RepositoryError>;
    async fn update(&self, entity: &GuildHall) -> Result<(), RepositoryError>;
    async fn delete(&self, id: &GuildHallId) -> Result<(), RepositoryError>;
}

#[automock]
#[async_trait]
pub trait GuildHallReadRepository: Send + Sync {
    async fn find_by_id(&self, id: &GuildHallId) -> Result<Option<GuildHall>, RepositoryError>;
    async fn find_all(&self) -> Result<Vec<GuildHall>, RepositoryError>;
}
