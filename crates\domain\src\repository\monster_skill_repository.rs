use async_trait::async_trait;
use mockall::automock;

use crate::error::RepositoryError;
use crate::models::monster_skill::entity::MonsterSkill;
use crate::models::monster_skill::value_object::MonsterSkillId;

#[automock]
#[async_trait]
pub trait MonsterSkillRepository: Send + Sync {
    async fn save(&self, entity: &MonsterSkill) -> Result<(), RepositoryError>;
    async fn update(&self, entity: &MonsterSkill) -> Result<(), RepositoryError>;
    async fn delete(&self, id: &MonsterSkillId) -> Result<(), RepositoryError>;
}

#[automock]
#[async_trait]
pub trait MonsterSkillReadRepository: Send + Sync {
    async fn find_by_id(
        &self,
        id: &MonsterSkillId,
    ) -> Result<Option<MonsterSkill>, RepositoryError>;
    async fn find_all(&self) -> Result<Vec<MonsterSkill>, RepositoryError>;
}
