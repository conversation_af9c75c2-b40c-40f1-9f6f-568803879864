use super::value_object::{
    ClassCategoryEnum, ClassCategoryId, ClassCategoryName, Dexterity, Endurance, Intellect, Luck,
    Strength, Wisdom,
};
use getset::{Get<PERSON>, Setters};

#[derive(Debug, <PERSON>lone, Default, bon::<PERSON><PERSON><PERSON>, Get<PERSON>, Setters)]
#[getset(get = "pub", set = "pub")]
pub struct ClassCategory {
    id: ClassCategoryId,
    name: ClassCategoryName,
    category: ClassCategoryEnum,
    strength: Strength,
    endurance: Endurance,
    dexterity: Dexterity,
    intellect: Intellect,
    wisdom: Wisdom,
    luck: Luck,
}

impl ClassCategory {
    pub fn new(
        id: ClassCategoryId,
        name: ClassCategoryName,
        category: ClassCategoryEnum,
        strength: Strength,
        endurance: Endurance,
        dexterity: Dexterity,
        intellect: Intellect,
        wisdom: Wisdom,
        luck: Luck,
    ) -> Self {
        Self {
            id,
            name,
            category,
            strength,
            endurance,
            dexterity,
            intellect,
            wisdom,
            luck,
        }
    }
}

#[cfg(test)]
mod tests {
    use reforged_shared::{IdTrait, Value};

    use super::*;

    #[test]
    fn test_class_category_new() {
        let id = ClassCategoryId::new(uuid::Uuid::now_v7());
        let name = ClassCategoryName::new("Fighter Category");
        let category = ClassCategoryEnum::Fighter;
        let strength = Strength::new(50.0);
        let endurance = Endurance::new(40.0);
        let dexterity = Dexterity::new(30.0);
        let intellect = Intellect::new(20.0);
        let wisdom = Wisdom::new(15.0);
        let luck = Luck::new(10.0);

        let class_category = ClassCategory::new(
            id.clone(),
            name.clone(),
            category.clone(),
            strength.clone(),
            endurance.clone(),
            dexterity.clone(),
            intellect.clone(),
            wisdom.clone(),
            luck.clone(),
        );

        assert_eq!(class_category.id().get_id(), id.get_id());
        assert_eq!(class_category.name().value(), name.value());
        assert_eq!(class_category.category(), &category);
        assert_eq!(class_category.strength().value(), strength.value());
        assert_eq!(class_category.endurance().value(), endurance.value());
        assert_eq!(class_category.dexterity().value(), dexterity.value());
        assert_eq!(class_category.intellect().value(), intellect.value());
        assert_eq!(class_category.wisdom().value(), wisdom.value());
        assert_eq!(class_category.luck().value(), luck.value());
    }

    #[test]
    fn test_class_category_builder() {
        let id = ClassCategoryId::new(uuid::Uuid::now_v7());
        let name = ClassCategoryName::new("Fighter Category");
        let category = ClassCategoryEnum::Fighter;
        let strength = Strength::new(50.0);
        let endurance = Endurance::new(40.0);
        let dexterity = Dexterity::new(30.0);
        let intellect = Intellect::new(20.0);
        let wisdom = Wisdom::new(15.0);
        let luck = Luck::new(10.0);

        let class_category = ClassCategory::builder()
            .id(id.clone())
            .name(name.clone())
            .category(category.clone())
            .strength(strength.clone())
            .endurance(endurance.clone())
            .dexterity(dexterity.clone())
            .intellect(intellect.clone())
            .wisdom(wisdom.clone())
            .luck(luck.clone())
            .build();

        assert_eq!(class_category.id().get_id(), id.get_id());
        assert_eq!(class_category.name().value(), name.value());
        assert_eq!(class_category.category(), &category);
        assert_eq!(class_category.strength().value(), strength.value());
        assert_eq!(class_category.endurance().value(), endurance.value());
        assert_eq!(class_category.dexterity().value(), dexterity.value());
        assert_eq!(class_category.intellect().value(), intellect.value());
        assert_eq!(class_category.wisdom().value(), wisdom.value());
        assert_eq!(class_category.luck().value(), luck.value());
    }

    #[test]
    fn test_getters_and_setters() {
        let mut class_category = ClassCategory::default();

        let id = ClassCategoryId::new(uuid::Uuid::now_v7());
        let name = ClassCategoryName::new("Fighter Category");
        let category = ClassCategoryEnum::Fighter;
        let strength = Strength::new(50.0);
        let endurance = Endurance::new(40.0);
        let dexterity = Dexterity::new(30.0);
        let intellect = Intellect::new(20.0);
        let wisdom = Wisdom::new(15.0);
        let luck = Luck::new(10.0);

        class_category.set_id(id.clone());
        class_category.set_name(name.clone());
        class_category.set_category(category.clone());
        class_category.set_strength(strength.clone());
        class_category.set_endurance(endurance.clone());
        class_category.set_dexterity(dexterity.clone());
        class_category.set_intellect(intellect.clone());
        class_category.set_wisdom(wisdom.clone());
        class_category.set_luck(luck.clone());

        assert_eq!(class_category.id().get_id(), id.get_id());
        assert_eq!(class_category.name().value(), name.value());
        assert_eq!(class_category.category(), &category);
        assert_eq!(class_category.strength().value(), strength.value());
        assert_eq!(class_category.endurance().value(), endurance.value());
        assert_eq!(class_category.dexterity().value(), dexterity.value());
        assert_eq!(class_category.intellect().value(), intellect.value());
        assert_eq!(class_category.wisdom().value(), wisdom.value());
        assert_eq!(class_category.luck().value(), luck.value());
    }
}
