use super::value_object::{<PERSON><PERSON><PERSON>Id, HairShopName};
use getset::{<PERSON><PERSON>, <PERSON><PERSON>};

#[derive(<PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, bon::<PERSON><PERSON><PERSON>, <PERSON><PERSON>, Setters)]
#[getset(get = "pub", set = "pub")]
pub struct HairShop {
    id: Hair<PERSON>hopId,
    name: Hair<PERSON>hopName,
}

impl HairShop {
    pub fn new(id: HairShopId, name: Hair<PERSON>hopName) -> Self {
        Self { id, name }
    }
}

#[cfg(test)]
mod tests {
    use reforged_shared::{IdTrait, Value};

    use super::*;

    #[test]
    fn test_hair_shop_new() {
        let id = HairShopId::new(uuid::Uuid::now_v7());
        let name = HairShopName::new("Stylish Cuts".to_string());

        let shop = HairShop::new(id.clone(), name.clone());

        assert_eq!(shop.id().get_id(), id.get_id());
        assert_eq!(shop.name().value(), name.value());
    }

    #[test]
    fn test_hair_shop_builder() {
        let id = HairShopId::new(uuid::Uuid::now_v7());
        let name = HairShopName::new("Fancy Salon".to_string());

        let shop = HairShop::builder()
            .id(id.clone())
            .name(name.clone())
            .build();

        assert_eq!(shop.id().get_id(), id.get_id());
        assert_eq!(shop.name().value(), name.value());
    }

    #[test]
    fn test_getters_and_setters() {
        let mut shop = HairShop::default();

        let id = HairShopId::new(uuid::Uuid::now_v7());
        let name = HairShopName::new("Scissors & Style".to_string());

        shop.set_id(id.clone());
        shop.set_name(name.clone());

        assert_eq!(shop.id().get_id(), id.get_id());
        assert_eq!(shop.name().value(), name.value());
    }
}
