use std::sync::Arc;

use chrono::{DateTime, Duration, Utc};
use esrs::AggregateState; // Added
use reforged_domain::commands::user_commands::{RequestPasswordResetCommand, UserCommands};
use serde::Serialize;
use uuid::Uuid;

use crate::{
    error::ApplicationError,
    // events::user::ResetPasswordRequested, // Removed
    eventsource::user::{UserAggregateState, UserStoreManager}, // Added
    queries::{user_queries::GetUserByEmailQuery, user_query_handlers::User<PERSON><PERSON>yHand<PERSON>},
    services::captcha_service::Captcha,
    traits::{PasetoClaimPurpose, PasetoClaims, QueryHandler, TokenService}, // Removed BrokerServicePublisher
};

#[derive(Serialize)]
pub struct ForgotPasswordResponse {
    reset_requested_at: DateTime<Utc>,
}

pub struct ForgotPasswordUsecase {
    captcha_service: Arc<dyn Captcha>,
    user_query_handler: User<PERSON><PERSON><PERSON>Handler,
    token_service: Arc<dyn TokenService>,
    user_store_manager: Arc<UserStoreManager>, // Added
}

impl ForgotPasswordUsecase {
    pub fn new(
        captcha_service: Arc<dyn Captcha>,
        user_query_handler: UserQueryHandler,
        token_service: Arc<dyn TokenService>,
        user_store_manager: Arc<UserStoreManager>, // Added
    ) -> Self {
        Self {
            captcha_service,
            user_query_handler,
            token_service,
            user_store_manager, // Added
        }
    }
}

impl ForgotPasswordUsecase {
    pub async fn execute(
        &self,
        email: String,
        captcha: String,
    ) -> Result<ForgotPasswordResponse, ApplicationError> {
        self.captcha_service.validate(captcha).await?;

        let user = self
            .user_query_handler
            .handle(GetUserByEmailQuery { email })
            .await?;

        if user.is_none() {
            return Err(ApplicationError::UserNotFound);
        }

        let user = user.unwrap();
        let token_service = self.token_service.clone();

        let reset_token_claims = PasetoClaims::new(
            user.id,
            user.username.to_owned(),
            user.email.to_owned(),
            Duration::minutes(30),
            PasetoClaimPurpose::PasswordReset,
        );

        let now = Utc::now();

        let (reset_token, reset_token_expiration) =
            token_service.generate_token(reset_token_claims, Duration::minutes(30))?;

        // Create and dispatch RequestPasswordResetCommand
        let cmd = RequestPasswordResetCommand {
            id: user.id,
            email: user.email.clone(),
            reset_token: reset_token.clone(),
            created_at: now,
            expires_at: reset_token_expiration,
        };

        let id = Uuid::now_v7();

        let state: AggregateState<UserAggregateState> = AggregateState::with_id(id);
        if let Err(e) = self
            .user_store_manager
            .handle_command(state, UserCommands::RequestPasswordReset(cmd))
            .await
        {
            println!(
                "Failed to request password reset for user {}: {}",
                user.id, e
            );
        }

        let now = Utc::now();
        let response = ForgotPasswordResponse {
            reset_requested_at: now,
        };

        Ok(response)
    }
}
