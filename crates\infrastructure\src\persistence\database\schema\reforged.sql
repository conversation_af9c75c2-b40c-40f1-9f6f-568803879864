CREATE TYPE "user_role" AS ENUM (
    'Banned',
    /* 0 */
    'Player',
    /* 1 */
    'Founder',
    /* 2 */
    'Support',
    /* 3 */
    'VIP',
    /* 64 */
    'Moderator',
    /* 128 */
    'Trainee',
    /* 192 */
    'Administrator'
    /* 255 */
);

CREATE TYPE "gender" AS ENUM ('M', 'F');

CREATE TYPE "login_location" AS ENUM ('Loader', 'Game', 'Wiki');

CREATE TYPE "store_type" AS ENUM ('Package', 'VIP', 'FOUNDER', 'Coin');

CREATE TYPE "market_type" AS ENUM ('Auction', 'Vending');

CREATE TABLE "public"."class_categories" (
    "id" UUID NOT NULL,
    "name" VARCHAR NOT NULL,
    "category" VARCHAR NOT NULL,
    "strength" NUMERIC NOT NULL,
    "endurance" NUMERIC NOT NULL,
    "dexterity" NUMERIC NOT NULL,
    "intellect" NUMERIC NOT NULL,
    "wisdom" NUMERIC NOT NULL,
    "luck" NUMERIC NOT NULL,
    CONSTRAINT "pk-class_categories" PRIMARY KEY ("id"),
    CONSTRAINT "class_categories_name_key" UNIQUE ("name")
);

CREATE TABLE "public"."password_reset_tokens" (
    "id" UUID PRIMARY KEY NOT NULL,
    "user_id" UUID NOT NULL,
    "token" VARCHAR NOT NULL,
    "created_at" TIMESTAMP NOT NULL,
    "expires_at" TIMESTAMP NOT NULL
);

CREATE TABLE "public"."classes" (
    "id" UUID NOT NULL,
    "name" VARCHAR NOT NULL,
    "category" VARCHAR NOT NULL,
    "description" TEXT NOT NULL,
    "mana_regeneration_methods" TEXT NOT NULL,
    "stats_description" TEXT NOT NULL,
    CONSTRAINT "pk-classes" PRIMARY KEY ("id", "category"),
    CONSTRAINT "classes_id_key" UNIQUE ("id")
);

CREATE TABLE "public"."skills" (
    "id" UUID NOT NULL,
    "name" VARCHAR NOT NULL,
    "animation" VARCHAR NOT NULL,
    "description" TEXT NOT NULL,
    "damage" NUMERIC NOT NULL,
    "mana" SMALLINT NOT NULL,
    "mana_back" SMALLINT NOT NULL,
    "life_steal" NUMERIC NOT NULL,
    "icon" VARCHAR NOT NULL,
    "range" INTEGER NOT NULL,
    "dsrc" VARCHAR NOT NULL,
    "reference" VARCHAR NOT NULL,
    "target" VARCHAR NOT NULL,
    "effects" VARCHAR NOT NULL,
    "type" VARCHAR NOT NULL,
    "strl" VARCHAR NOT NULL,
    "cooldown" INTEGER NOT NULL,
    "hit_targets" SMALLINT NOT NULL,
    "pet" INTEGER NOT NULL,
    "chance" NUMERIC NULL,
    "show_damage" BOOLEAN NOT NULL,
    CONSTRAINT "skills_pkey" PRIMARY KEY ("id")
);

CREATE TABLE "public"."class_skills" (
    "id" UUID NOT NULL,
    "class_id" UUID NOT NULL,
    "skill_id" UUID NOT NULL,
    CONSTRAINT "class_skills_pkey" PRIMARY KEY ("id")
);

CREATE TABLE "public"."items" (
    "id" UUID NOT NULL,
    "class_id" UUID NULL,
    "name" VARCHAR NOT NULL,
    "description" TEXT NOT NULL,
    "type" VARCHAR NOT NULL,
    "element" VARCHAR NOT NULL,
    "file" VARCHAR NOT NULL,
    "link" VARCHAR NOT NULL,
    "icon" VARCHAR NOT NULL,
    "equipment" VARCHAR NOT NULL,
    "level" SMALLINT NOT NULL,
    "dps" SMALLINT NOT NULL,
    "range" SMALLINT NOT NULL,
    "rarity" UUID NOT NULL,
    "quantity" SMALLINT NOT NULL,
    "stack" INTEGER NOT NULL,
    "cost" INTEGER NOT NULL,
    "coins" SMALLINT NOT NULL,
    "diamonds" SMALLINT NOT NULL,
    "crystal" BIGINT NOT NULL,
    "sell" BOOLEAN NOT NULL,
    "market" BOOLEAN NOT NULL,
    "temporary" BOOLEAN NOT NULL,
    "upgrade" BOOLEAN NOT NULL,
    "staff" BOOLEAN NOT NULL,
    "enh_id" UUID NULL,
    "faction_id" UUID NULL,
    "req_reputation" BIGINT NOT NULL,
    "req_class_id" UUID NULL,
    "req_class_points" BIGINT NOT NULL,
    "req_quests" VARCHAR NOT NULL,
    "quest_string_index" SMALLINT NOT NULL,
    "quest_string_value" SMALLINT NOT NULL,
    "meta" VARCHAR NULL,
    "color" VARCHAR NOT NULL,
    CONSTRAINT "pk-items" PRIMARY KEY ("id", "rarity"),
    CONSTRAINT "items_id_key" UNIQUE ("id")
);

CREATE TABLE "public"."item_rarities" (
    "id" UUID NOT NULL,
    "name" VARCHAR NOT NULL,
    CONSTRAINT "item_rarities_pkey" PRIMARY KEY ("id")
);

CREATE TABLE "public"."wheel_rewards" (
    "id" UUID NOT NULL,
    "item_id" UUID NOT NULL,
    "chance" NUMERIC NOT NULL,
    CONSTRAINT "wheel_rewards_pkey" PRIMARY KEY ("id")
);

CREATE TABLE "public"."item_bundles" (
    "id" UUID NOT NULL,
    "item_id" UUID NOT NULL,
    "reward_id" UUID NOT NULL,
    "quantity" INTEGER NOT NULL,
    CONSTRAINT "item_bundles_pkey" PRIMARY KEY ("id")
);

CREATE TABLE "public"."item_effects" (
    "id" UUID NOT NULL,
    "item_id" UUID NOT NULL,
    "damage_increase" NUMERIC NOT NULL,
    "damage_taken" NUMERIC NOT NULL,
    "exp" NUMERIC NOT NULL,
    "gold" NUMERIC NOT NULL,
    "coins" NUMERIC NOT NULL,
    "class_point" NUMERIC NOT NULL,
    "reputation" NUMERIC NOT NULL,
    CONSTRAINT "item_effects_pkey" PRIMARY KEY ("id")
);

CREATE TABLE "public"."item_lotteries" (
    "id" UUID NOT NULL,
    "item_id" UUID NOT NULL,
    "reward_id" UUID NOT NULL,
    "quantity" INTEGER NOT NULL,
    "chance" NUMERIC NOT NULL,
    CONSTRAINT "item_lotteries_pkey" PRIMARY KEY ("id", "item_id")
);

CREATE TABLE "public"."item_requirements" (
    "id" UUID NOT NULL,
    "item_id" UUID NOT NULL,
    "req_item_id" UUID NOT NULL,
    "quantity" SMALLINT NOT NULL,
    CONSTRAINT "item_requirements_pkey" PRIMARY KEY ("id")
);

CREATE TABLE "public"."item_skills" (
    "id" UUID NOT NULL,
    "item_id" UUID NOT NULL,
    "skill_id" UUID NOT NULL,
    CONSTRAINT "item_skills_pkey" PRIMARY KEY ("id")
);

CREATE TABLE "public"."auras" (
    "id" UUID NOT NULL,
    "name" VARCHAR NOT NULL,
    "duration" SMALLINT NOT NULL,
    "category" VARCHAR NOT NULL,
    "damage_increase" NUMERIC NOT NULL,
    "damage_taken_decrease" NUMERIC NOT NULL,
    "chance" NUMERIC NOT NULL,
    "self" BOOLEAN NOT NULL,
    CONSTRAINT "auras_pkey" PRIMARY KEY ("id")
);

CREATE TABLE "public"."skill_auras" (
    "id" UUID NOT NULL,
    "skill_id" UUID NOT NULL,
    "aura_id" UUID NOT NULL,
    CONSTRAINT "skill_auras_pkey" PRIMARY KEY ("id")
);

CREATE TABLE "public"."aura_effects" (
    "id" UUID NOT NULL,
    "aura_id" UUID NOT NULL,
    "stat" VARCHAR NOT NULL,
    "value" NUMERIC NOT NULL,
    "type" VARCHAR NOT NULL,
    CONSTRAINT "aura_effects_pkey" PRIMARY KEY ("id")
);

CREATE TABLE "public"."enhancement_patterns" (
    "id" UUID NOT NULL,
    "name" VARCHAR NOT NULL,
    "desc" VARCHAR NOT NULL,
    "wisdom" SMALLINT NOT NULL,
    "strength" SMALLINT NOT NULL,
    "luck" SMALLINT NOT NULL,
    "dexterity" SMALLINT NOT NULL,
    "endurance" SMALLINT NOT NULL,
    "intelligence" SMALLINT NOT NULL,
    CONSTRAINT "enhancement_patterns_pkey" PRIMARY KEY ("id")
);

CREATE TABLE "public"."enhancements" (
    "id" UUID NOT NULL,
    "name" VARCHAR NOT NULL,
    "pattern_id" UUID NOT NULL,
    "rarity" UUID NOT NULL,
    "dps" SMALLINT NOT NULL,
    "level" SMALLINT NOT NULL,
    CONSTRAINT "enhancements_pkey" PRIMARY KEY ("id")
);

CREATE TABLE "public"."monsters" (
    "id" UUID NOT NULL,
    "name" VARCHAR NOT NULL,
    "race" VARCHAR NOT NULL,
    "file" VARCHAR NOT NULL,
    "health" INTEGER NOT NULL,
    "mana" INTEGER NOT NULL,
    "level" SMALLINT NOT NULL,
    "gold" INTEGER NOT NULL,
    "coins" INTEGER NOT NULL,
    "experience" INTEGER NOT NULL,
    "reputation" INTEGER NOT NULL,
    "dps" INTEGER NOT NULL,
    "speed" INTEGER NOT NULL,
    "element" VARCHAR NOT NULL,
    "linkage" VARCHAR NOT NULL,
    "team_id" INTEGER NOT NULL,
    "boss" BOOLEAN NOT NULL,
    "boss_min_res" INTEGER NOT NULL,
    "respawn_time" INTEGER NOT NULL,
    "hide" BOOLEAN NOT NULL,
    "world_boss" BOOLEAN NOT NULL,
    CONSTRAINT "monsters_pkey" PRIMARY KEY ("id")
);

CREATE TABLE "public"."monster_skills" (
    "id" UUID NOT NULL,
    "monster_id" UUID NOT NULL,
    "skills" INTEGER NOT NULL,
    "skill_id" UUID NOT NULL,
    CONSTRAINT "monster_skills_pkey" PRIMARY KEY ("id")
);

CREATE TABLE "public"."monster_drops" (
    "id" UUID NOT NULL,
    "monster_id" UUID NOT NULL,
    "item_id" UUID NOT NULL,
    "chance" NUMERIC NOT NULL,
    "quantity" INTEGER NOT NULL,
    CONSTRAINT "monster_drops_pkey" PRIMARY KEY ("id")
);

CREATE TABLE "public"."maps" (
    "id" UUID NOT NULL,
    "name" VARCHAR NOT NULL,
    "file" VARCHAR NOT NULL,
    "max_players" SMALLINT NOT NULL,
    "req_level" SMALLINT NOT NULL,
    "upgrade" BOOLEAN NOT NULL,
    "staff" BOOLEAN NOT NULL,
    "pvp" BOOLEAN NOT NULL,
    "world_boss" BOOLEAN NOT NULL,
    CONSTRAINT "maps_pkey" PRIMARY KEY ("id")
);

CREATE TABLE "public"."map_cells" (
    "id" UUID NOT NULL,
    "map_id" UUID NOT NULL,
    "frame" VARCHAR NOT NULL,
    "pad" VARCHAR NOT NULL,
    CONSTRAINT "map_cells_pkey" PRIMARY KEY ("id")
);

CREATE TABLE "public"."map_items" (
    "id" UUID NOT NULL,
    "map_id" UUID NOT NULL,
    "item_id" UUID NOT NULL,
    CONSTRAINT "map_items_pkey" PRIMARY KEY ("id")
);

CREATE TABLE "public"."map_monsters" (
    "id" UUID NOT NULL,
    "map_id" UUID NOT NULL,
    "monster_id" UUID NOT NULL,
    "mon_map_id" INTEGER NOT NULL, -- monster location inside the map
    "frame" VARCHAR NOT NULL,
    CONSTRAINT "map_monsters_pkey" PRIMARY KEY ("id")
);

CREATE TABLE "public"."monster_bosses" (
    "id" UUID NOT NULL,
    "monster_id" UUID NOT NULL,
    "map_id" UUID NOT NULL,
    "spawn_interval" BIGINT NOT NULL,
    "time_limit" INTEGER NOT NULL,
    "kills" INTEGER NULL,
    "deaths" INTEGER NULL,
    "death_time" TIMESTAMP NOT NULL,
    "spawn_time" TIMESTAMP NOT NULL,
    "description" TEXT NULL,
    CONSTRAINT "monster_bosses_pkey" PRIMARY KEY ("id")
);

CREATE TABLE "public"."shops" (
    "id" UUID NOT NULL,
    "name" VARCHAR NOT NULL,
    "house" BOOLEAN NOT NULL,
    "upgrade" BOOLEAN NOT NULL,
    "staff" BOOLEAN NOT NULL,
    "limited" BOOLEAN NOT NULL,
    "field" VARCHAR NOT NULL,
    "achievement_id" UUID NULL,
    "item_id" UUID NULL,
    CONSTRAINT "shops_pkey" PRIMARY KEY ("id")
);

CREATE TABLE "public"."shop_items" (
    "id" UUID NOT NULL,
    "shop_id" UUID NOT NULL,
    "item_id" UUID NOT NULL,
    "quantity_remain" INTEGER NOT NULL,
    CONSTRAINT "shop_items_pkey" PRIMARY KEY ("id")
);

CREATE TABLE "public"."shop_locations" (
    "id" UUID NOT NULL,
    "shop_id" UUID NOT NULL,
    "map_id" UUID NOT NULL,
    CONSTRAINT "shop_locations_pkey" PRIMARY KEY ("id")
);

CREATE TABLE "public"."roles" (
    "id" UUID NOT NULL,
    "tag" "user_role" NOT NULL,
    "user_color" VARCHAR NOT NULL,
    CONSTRAINT "roles_pkey" PRIMARY KEY ("id")
);

CREATE TABLE "public"."users" (
    "id" UUID NOT NULL,
    "username" VARCHAR NOT NULL,
    "hash" VARCHAR NOT NULL,
    "salt" VARCHAR NOT NULL,
    "role_id" UUID NOT NULL,
    "title_id" UUID NOT NULL,
    "email" VARCHAR NOT NULL,
    "date_created" TIMESTAMP NOT NULL,
    "last_login" TIMESTAMP NOT NULL,
    CONSTRAINT "users_pkey" PRIMARY KEY ("id"),
    CONSTRAINT "users_email_key" UNIQUE ("email"),
    CONSTRAINT "users_username_key" UNIQUE ("username")
);

CREATE TABLE "public"."profiles" (
    "id" UUID NOT NULL,
    "user_id" UUID NOT NULL,
    "avatar" VARCHAR NOT NULL,
    "age" SMALLINT NOT NULL,
    "gender" "gender" NOT NULL,
    "country" VARCHAR NOT NULL,
    CONSTRAINT "profiles_pkey" PRIMARY KEY ("id")
);

CREATE TABLE "public"."titles" (
    "id" UUID NOT NULL,
    "name" VARCHAR NOT NULL,
    "description" TEXT NOT NULL,
    "color" VARCHAR NOT NULL,
    "strength" INTEGER NOT NULL,
    "intellect" INTEGER NOT NULL,
    "endurance" INTEGER NOT NULL,
    "dexterity" INTEGER NOT NULL,
    "wisdom" INTEGER NOT NULL,
    "luck" INTEGER NOT NULL,
    "role_id" UUID NOT NULL,
    CONSTRAINT "titles_pkey" PRIMARY KEY ("id")
);

CREATE TABLE "public"."redeem_codes" (
    "id" UUID NOT NULL,
    "code" VARCHAR NOT NULL,
    "coins" INTEGER NOT NULL,
    "gold" INTEGER NOT NULL,
    "exp" INTEGER NOT NULL,
    "class_points" INTEGER NOT NULL,
    "item_id" UUID NULL,
    "upgrade_days" INTEGER NOT NULL,
    "date_expiry" TIMESTAMP NOT NULL,
    "limit" INTEGER NOT NULL,
    CONSTRAINT "redeem_codes_pkey" PRIMARY KEY ("id")
);

CREATE TABLE "public"."sessions" (
    "id" UUID NOT NULL,
    "user_id" UUID NOT NULL,
    "token" VARCHAR NOT NULL,
    "user_agent" VARCHAR NOT NULL,
    "client_ip" VARCHAR NOT NULL,
    "is_blocked" BOOLEAN NOT NULL,
    "created_at" TIMESTAMP NOT NULL,
    "expires_at" TIMESTAMP NULL,
    CONSTRAINT "sessions_pkey" PRIMARY KEY ("id")
);

CREATE TABLE "public"."achievements" (
    "id" UUID NOT NULL,
    "name" VARCHAR NOT NULL,
    "description" TEXT NOT NULL,
    "file" VARCHAR NOT NULL,
    "category" VARCHAR NOT NULL,
    "show" BOOLEAN NOT NULL,
    CONSTRAINT "achievements_pkey" PRIMARY KEY ("id")
);

CREATE TABLE "public"."user_achievements" (
    "id" UUID NOT NULL,
    "user_id" UUID NOT NULL,
    "achievement_id" UUID NOT NULL,
    "date" TIMESTAMP NOT NULL,
    CONSTRAINT "pk-user_achievements" PRIMARY KEY ("id", "user_id", "achievement_id")
);

CREATE TABLE "public"."user_boosts" (
    "id" UUID NOT NULL,
    "user_id" UUID NOT NULL,
    "cp_boost_expire" DATE NOT NULL,
    "rep_boost_expire" DATE NOT NULL,
    "gold_boost_expire" DATE NOT NULL,
    "exp_boost_expire" DATE NOT NULL,
    "upgrade_expire" DATE NOT NULL,
    "upgrade_days" SMALLINT NOT NULL,
    "upgraded" BOOLEAN NOT NULL,
    CONSTRAINT "user_boosts_pkey" PRIMARY KEY ("id", "user_id")
);

CREATE TABLE "public"."user_browsers" (
    "id" UUID NOT NULL,
    "user_id" UUID NOT NULL,
    "referer" TEXT NOT NULL,
    "engine" VARCHAR NOT NULL,
    "platform" VARCHAR NOT NULL,
    "browser" VARCHAR NOT NULL,
    CONSTRAINT "user_browsers_pkey" PRIMARY KEY ("id")
);

CREATE TABLE "public"."user_colors" (
    "id" UUID NOT NULL,
    "user_id" UUID NOT NULL,
    "color_chat" VARCHAR NOT NULL,
    "color_name" VARCHAR NOT NULL,
    "color_hair" VARCHAR NOT NULL,
    "color_skin" VARCHAR NOT NULL,
    "color_eye" VARCHAR NOT NULL,
    "color_base" VARCHAR NOT NULL,
    "color_trim" VARCHAR NOT NULL,
    "color_accessory" VARCHAR NOT NULL,
    CONSTRAINT "user_colors_pkey" PRIMARY KEY ("id")
);

CREATE TABLE "public"."user_currencies" (
    "id" UUID NOT NULL,
    "user_id" UUID NOT NULL,
    "gold" BIGINT NOT NULL,
    "coins" BIGINT NOT NULL,
    "diamonds" BIGINT NOT NULL,
    "crystal" BIGINT NOT NULL,
    CONSTRAINT "user_currencies_pkey" PRIMARY KEY ("id")
);

CREATE TABLE "public"."user_exps" (
    "id" UUID NOT NULL,
    "user_id" UUID NOT NULL,
    "level" SMALLINT NOT NULL,
    "exp" BIGINT NOT NULL,
    CONSTRAINT "user_exps_pkey" PRIMARY KEY ("id")
);

CREATE TABLE "public"."user_factions" (
    "id" UUID NOT NULL,
    "user_id" UUID NOT NULL,
    "faction_id" UUID NOT NULL,
    "reputation" BIGINT NOT NULL,
    CONSTRAINT "user_factions_pkey" PRIMARY KEY ("id")
);

CREATE TABLE "public"."factions" (
    "id" UUID NOT NULL,
    "name" VARCHAR NOT NULL,
    CONSTRAINT "factions_pkey" PRIMARY KEY ("id")
);

CREATE TABLE "public"."user_friends" (
    "id" UUID NOT NULL,
    "user_id" UUID NOT NULL,
    "friend_id" UUID NOT NULL,
    CONSTRAINT "user_friends_pkey" PRIMARY KEY ("id")
);

CREATE TABLE "public"."deleted_user_items" (
    "id" UUID NOT NULL,
    "user_id" UUID NOT NULL,
    "item_id" UUID NOT NULL,
    "quantity" INTEGER NOT NULL,
    "date" TIMESTAMP NOT NULL,
    CONSTRAINT "deleted_user_items_pkey" PRIMARY KEY ("id")
);

CREATE TABLE "public"."user_items" (
    "id" UUID NOT NULL,
    "user_id" UUID NOT NULL,
    "item_id" UUID NOT NULL,
    "enh_id" UUID NOT NULL,
    "equipped" SMALLINT NOT NULL,
    "quantity" BIGINT NOT NULL,
    "bank" SMALLINT NOT NULL,
    "date_purchased" TIMESTAMP NOT NULL,
    "bind" SMALLINT NULL,
    CONSTRAINT "user_items_pkey" PRIMARY KEY ("id")
);

CREATE TABLE "public"."user_livedrops" (
    "id" UUID NOT NULL,
    "user_id" UUID NULL,
    "item_id" UUID NULL,
    "quantity" INTEGER NULL,
    "sent" SMALLINT NOT NULL,
    "date" TIMESTAMP NOT NULL,
    "message" TEXT NULL,
    "achievement_id" UUID NULL,
    "title_id" UUID NULL,
    "experience" BIGINT NOT NULL,
    "gold" BIGINT NOT NULL,
    "coins" INTEGER NOT NULL,
    "crystal" BIGINT NOT NULL,
    "upgrade_days" INTEGER NOT NULL,
    "bag_slots" INTEGER NOT NULL,
    "bank_slots" INTEGER NOT NULL,
    "house_slots" INTEGER NOT NULL,
    CONSTRAINT "user_livedrops_pkey" PRIMARY KEY ("id")
);

CREATE TABLE "public"."user_logins" (
    "id" UUID NOT NULL,
    "user_id" UUID NOT NULL,
    "location" VARCHAR NOT NULL,
    "status" VARCHAR NOT NULL,
    "address" VARCHAR NOT NULL,
    "date" TIMESTAMP NOT NULL,
    CONSTRAINT "user_logins_pkey" PRIMARY KEY ("id")
);

CREATE TABLE "public"."user_markets" (
    "id" UUID NOT NULL,
    "user_id" UUID NOT NULL,
    "item_id" UUID NOT NULL,
    "enh_id" UUID NOT NULL,
    "datetime" TIMESTAMP NOT NULL,
    "buyer_id" UUID NULL,
    "coins" INTEGER NOT NULL,
    "gold" INTEGER NOT NULL,
    "quantity" INTEGER NOT NULL,
    "status" SMALLINT NOT NULL,
    "type" "market_type" NOT NULL,
    CONSTRAINT "user_markets_pkey" PRIMARY KEY ("id")
);

CREATE TABLE "public"."stores" (
    "id" UUID NOT NULL,
    "available" SMALLINT NOT NULL,
    "name" VARCHAR NOT NULL,
    "price" NUMERIC NOT NULL,
    "item" INTEGER NOT NULL,
    "achievement" INTEGER NULL,
    "gold" BIGINT NOT NULL,
    "coins" INTEGER NOT NULL,
    "crystal" INTEGER NOT NULL,
    "diamonds" INTEGER NOT NULL,
    "upgrade" INTEGER NOT NULL,
    "role_id" UUID NULL,
    "title_id" UUID NULL,
    "bag_slots" INTEGER NOT NULL,
    "bank_slots" INTEGER NOT NULL,
    "house_slots" INTEGER NOT NULL,
    "type" "store_type" NULL,
    "quantity" INTEGER NOT NULL,
    "img" VARCHAR NULL,
    CONSTRAINT "stores_pkey" PRIMARY KEY ("id")
);

CREATE TABLE "public"."user_purchases" (
    "id" UUID NOT NULL,
    "user_id" UUID NOT NULL,
    "payment_id" VARCHAR NOT NULL,
    "transaction_id" VARCHAR NULL,
    "email" VARCHAR NULL,
    "hash" VARCHAR NOT NULL,
    "item" VARCHAR NOT NULL,
    "item_id" UUID NOT NULL,
    "price" VARCHAR NOT NULL,
    "method" VARCHAR NOT NULL,
    "currency" VARCHAR NOT NULL,
    "purchased" SMALLINT NOT NULL,
    "date" TIMESTAMP NOT NULL,
    "broadcast" BOOLEAN NOT NULL,
    CONSTRAINT "user_purchases_pkey" PRIMARY KEY ("id")
);

CREATE TABLE "public"."user_redeems" (
    "id" UUID NOT NULL,
    "user_id" UUID NOT NULL,
    "redeem_id" UUID NOT NULL,
    "date" TIMESTAMP NOT NULL,
    CONSTRAINT "user_redeems_pkey" PRIMARY KEY ("id")
);

CREATE TABLE "public"."user_reports" (
    "id" UUID NOT NULL,
    "user_id" UUID NOT NULL,
    "target_name" VARCHAR NOT NULL,
    "category" VARCHAR NOT NULL,
    "description" TEXT NOT NULL,
    "date_submitted" TIMESTAMP NOT NULL,
    CONSTRAINT "user_reports_pkey" PRIMARY KEY ("id")
);

CREATE TABLE "public"."user_slots" (
    "id" UUID NOT NULL,
    "user_id" UUID NOT NULL,
    "slots_bag" SMALLINT NOT NULL,
    "slots_bank" SMALLINT NOT NULL,
    "slots_house" SMALLINT NOT NULL,
    "slots_auction" SMALLINT NOT NULL,
    CONSTRAINT "user_slots_pkey" PRIMARY KEY ("id")
);

CREATE TABLE "public"."user_stats" (
    "id" UUID NOT NULL,
    "user_id" UUID NOT NULL,
    "last_area" VARCHAR NOT NULL,
    "current_server" VARCHAR NOT NULL,
    "house_info" TEXT NOT NULL,
    "kill_count" BIGINT NOT NULL,
    "death_count" BIGINT NOT NULL,
    "pvp_ratio" BIGINT NULL,
    CONSTRAINT "user_stats_pkey" PRIMARY KEY ("id")
);

CREATE TABLE "public"."user_titles" (
    "id" UUID NOT NULL,
    "user_id" UUID NOT NULL,
    "title_id" UUID NOT NULL,
    "date" TIMESTAMP NOT NULL,
    CONSTRAINT "user_titles_pkey" PRIMARY KEY ("id")
);

CREATE TABLE "public"."guilds" (
    "id" UUID NOT NULL,
    "name" VARCHAR NOT NULL,
    "message_of_the_day" VARCHAR NOT NULL,
    "max_members" SMALLINT NOT NULL,
    "hall_size" SMALLINT NOT NULL,
    "last_updated" TIMESTAMP NOT NULL,
    "wins" INTEGER NOT NULL,
    "loss" INTEGER NOT NULL,
    "total_kills" INTEGER NOT NULL,
    "level" INTEGER NOT NULL,
    "experience" BIGINT NOT NULL,
    "guild_color" VARCHAR NOT NULL,
    "staff_g" UUID NOT NULL,
    "color" INTEGER NULL,
    CONSTRAINT "guilds_pkey" PRIMARY KEY ("id")
);

CREATE TABLE "public"."user_guilds" (
    "id" UUID NOT NULL,
    "guild_id" UUID NOT NULL,
    "user_id" UUID NOT NULL,
    "rank" SMALLINT NOT NULL,
    CONSTRAINT "user_guilds_pkey" PRIMARY KEY ("id")
);

CREATE TABLE "public"."guild_halls" (
    "id" UUID NOT NULL,
    "guild_id" UUID NOT NULL,
    "linkage" VARCHAR NOT NULL,
    "cell" VARCHAR NOT NULL,
    "x" NUMERIC NOT NULL,
    "y" NUMERIC NOT NULL,
    "interior" TEXT NOT NULL,
    CONSTRAINT "guild_halls_pkey" PRIMARY KEY ("id")
);

CREATE TABLE "public"."guild_hall_buildings" (
    "id" UUID NOT NULL,
    "hall_id" UUID NOT NULL,
    "item_id" UUID NOT NULL,
    "slot" SMALLINT NOT NULL,
    "size" SMALLINT NOT NULL,
    CONSTRAINT "guild_hall_buildings_pkey" PRIMARY KEY ("id")
);

CREATE TABLE "public"."guild_hall_connections" (
    "id" UUID NOT NULL,
    "hall_id" UUID NOT NULL,
    "pad" VARCHAR NOT NULL,
    "cell" VARCHAR NOT NULL,
    "pad_position" VARCHAR NOT NULL,
    CONSTRAINT "guild_hall_connections_pkey" PRIMARY KEY ("id")
);

CREATE TABLE "public"."guild_levels" (
    "id" UUID NOT NULL,
    "guild_id" UUID NOT NULL,
    "level" INTEGER NOT NULL,
    "exp" INTEGER NOT NULL,
    CONSTRAINT "guild_levels_pkey" PRIMARY KEY ("id")
);

CREATE TABLE "public"."guild_items" (
    "id" UUID NOT NULL,
    "guild_id" UUID NOT NULL,
    "item_id" UUID NOT NULL,
    "user_id" UUID NOT NULL,
    CONSTRAINT "guild_items_pkey" PRIMARY KEY ("id")
);

CREATE TABLE "public"."articles" (
    "id" UUID NOT NULL,
    "author_id" UUID NOT NULL,
    "subject" VARCHAR NOT NULL,
    "content" TEXT NOT NULL,
    "image" VARCHAR NULL,
    "tags" VARCHAR NULL,
    "date" TIMESTAMP NOT NULL,
    CONSTRAINT "articles_pkey" PRIMARY KEY ("id")
);

CREATE TABLE "public"."cms_articles" (
    "id" UUID NOT NULL,
    "author_id" UUID NOT NULL,
    "title" VARCHAR NOT NULL,
    "content" TEXT NOT NULL,
    "created_at" DATE NOT NULL,
    "Image" VARCHAR NULL,
    CONSTRAINT "cms_articles_pkey" PRIMARY KEY ("id")
);

CREATE TABLE "public"."books" (
    "id" UUID NOT NULL,
    "file" VARCHAR NOT NULL,
    "name" VARCHAR NOT NULL,
    "linkage" VARCHAR NOT NULL,
    "lock" VARCHAR NOT NULL,
    "desc" VARCHAR NOT NULL,
    "map" VARCHAR NOT NULL,
    "type" VARCHAR NOT NULL,
    "hide" INTEGER NOT NULL,
    "label" VARCHAR NOT NULL,
    "shop_id" UUID NOT NULL,
    "field" VARCHAR NOT NULL,
    "index" INTEGER NOT NULL,
    "value" INTEGER NOT NULL,
    CONSTRAINT "books_pkey" PRIMARY KEY ("id")
);

CREATE TABLE "public"."book_quests" (
    "id" UUID NOT NULL,
    "name" VARCHAR NOT NULL,
    "field" VARCHAR NOT NULL,
    "lock" VARCHAR NOT NULL,
    "map" VARCHAR NOT NULL,
    "type" VARCHAR NOT NULL,
    "hide" INTEGER NOT NULL,
    "index" INTEGER NOT NULL,
    "value" INTEGER NOT NULL,
    CONSTRAINT "book_quests_pkey" PRIMARY KEY ("id")
);

CREATE TABLE "public"."hairs" (
    "id" UUID NOT NULL,
    "gender" "gender" NOT NULL,
    "name" VARCHAR NOT NULL,
    "file" VARCHAR NOT NULL,
    CONSTRAINT "hairs_pkey" PRIMARY KEY ("id")
);

CREATE TABLE "public"."hair_shop_items" (
    "id" UUID NOT NULL,
    "gender" "gender" NOT NULL,
    "shop_id" UUID NOT NULL,
    "hair_id" UUID NOT NULL,
    CONSTRAINT "hair_shop_items_pkey" PRIMARY KEY ("id")
);

CREATE TABLE "public"."hair_shops" (
    "id" UUID NOT NULL,
    "name" VARCHAR NOT NULL,
    CONSTRAINT "hair_shops_pkey" PRIMARY KEY ("id")
);

CREATE TABLE "public"."quests" (
    "id" UUID NOT NULL,
    "faction_id" UUID NOT NULL,
    "req_achievement" UUID NULL,
    "req_reputation" INTEGER NOT NULL,
    "req_class_id" UUID NULL,
    "req_class_points" INTEGER NOT NULL,
    "name" VARCHAR NOT NULL,
    "description" TEXT NOT NULL,
    "end_text" TEXT NULL,
    "experience" BIGINT NOT NULL,
    "g_experience" INTEGER NOT NULL,
    "gold" BIGINT NOT NULL,
    "coins" INTEGER NOT NULL,
    "reputation" INTEGER NOT NULL,
    "class_points" INTEGER NOT NULL,
    "reward_type" VARCHAR NOT NULL,
    "level" SMALLINT NOT NULL,
    "upgrade" BOOLEAN NOT NULL,
    "once" BOOLEAN NOT NULL,
    "slot" INTEGER NOT NULL,
    "value" INTEGER NOT NULL,
    "field" VARCHAR NOT NULL,
    "index" INTEGER NOT NULL,
    "badges" VARCHAR NULL,
    "give_membership" INTEGER NULL,
    "war_id" UUID NULL,
    "achievement_id" UUID NULL,
    "war_mega" BOOLEAN NOT NULL,
    "req_guild_level" INTEGER NULL,
    "staff" BOOLEAN NOT NULL,
    "check" BOOLEAN NOT NULL,
    CONSTRAINT "quests_pkey" PRIMARY KEY ("id")
);

CREATE TABLE "public"."wars" (
    "id" UUID NOT NULL,
    "name" VARCHAR NOT NULL,
    "points" INTEGER NOT NULL,
    "max_points" INTEGER NOT NULL,
    CONSTRAINT "wars_pkey" PRIMARY KEY ("id")
);

CREATE TABLE "public"."user_quests" (
    "id" UUID NOT NULL,
    "user_id" UUID NOT NULL,
    "quests1" VARCHAR NOT NULL,
    "quests2" VARCHAR NOT NULL,
    "quests3" VARCHAR NOT NULL,
    "quests4" VARCHAR NOT NULL,
    "quests5" VARCHAR NOT NULL,
    "daily_quests0" INTEGER NOT NULL,
    "daily_quests1" INTEGER NOT NULL,
    "daily_quests2" INTEGER NOT NULL,
    "monthly_quests0" INTEGER NOT NULL,
    "daily_ads" INTEGER NOT NULL,
    CONSTRAINT "user_quests_pkey" PRIMARY KEY ("id")
);

CREATE TABLE "public"."quest_locations" (
    "id" UUID NOT NULL,
    "quest_id" UUID NOT NULL,
    "map_id" UUID NOT NULL,
    CONSTRAINT "quest_locations_pkey" PRIMARY KEY ("id")
);

CREATE TABLE "public"."quest_requirements" (
    "id" UUID NOT NULL,
    "quest_id" UUID NOT NULL,
    "item_id" UUID NOT NULL,
    "quantity" INTEGER NOT NULL,
    CONSTRAINT "quest_requirements_pkey" PRIMARY KEY ("id")
);

CREATE TABLE "public"."quest_rewards" (
    "id" UUID NOT NULL,
    "quest_id" UUID NOT NULL,
    "item_id" UUID NOT NULL,
    "quantity" INTEGER NOT NULL,
    "rate" NUMERIC NOT NULL,
    "reward_type" VARCHAR NOT NULL,
    CONSTRAINT "quest_rewards_pkey" PRIMARY KEY ("id")
);

CREATE TABLE "public"."quest_reqditems" (
    "id" UUID NOT NULL,
    "quest_id" UUID NOT NULL,
    "item_id" UUID NOT NULL,
    "quantity" INTEGER NOT NULL,
    CONSTRAINT "quest_reqditems_pkey" PRIMARY KEY ("id")
);

CREATE TABLE "public"."admin_uploads" (
    "id" UUID NOT NULL,
    "user_id" UUID NOT NULL,
    "file_name" VARCHAR NOT NULL,
    "type" VARCHAR NOT NULL,
    "date" DATE NOT NULL,
    CONSTRAINT "admin_uploads_pkey" PRIMARY KEY ("id")
);

CREATE TABLE "public"."servers" (
    "id" UUID NOT NULL,
    "name" VARCHAR NOT NULL,
    "ip" VARCHAR NOT NULL,
    "port" INTEGER NOT NULL,
    "online" BOOLEAN NOT NULL,
    "upgrade" BOOLEAN NOT NULL,
    "chat" SMALLINT NOT NULL,
    "count" BIGINT NOT NULL,
    "max" BIGINT NOT NULL,
    "motd" TEXT NOT NULL,
    "maintenance" BOOLEAN NOT NULL,
    CONSTRAINT "servers_pkey" PRIMARY KEY ("id")
);

CREATE TABLE "public"."discord_commands" (
    "id" UUID NOT NULL,
    "role_id" UUID,
    "command" VARCHAR NOT NULL,
    "file" VARCHAR NOT NULL,
    CONSTRAINT "discord_commands_pkey" PRIMARY KEY ("id")
);

CREATE TABLE "public"."profanity_filter_settings" (
    "id" UUID NOT NULL,
    "swear" VARCHAR NOT NULL,
    "time_to_mute" INTEGER NOT NULL,
    CONSTRAINT "profanity_filter_settings_pkey" PRIMARY KEY ("id")
);

CREATE TABLE "public"."game_settings" (
    "id" UUID NOT NULL,
    "name" VARCHAR NOT NULL,
    "value" VARCHAR NOT NULL,
    "location" "login_location" NOT NULL,
    CONSTRAINT "game_settings_pkey" PRIMARY KEY ("id")
);

CREATE TABLE "public"."game_rates_settings" (
    "id" UUID NOT NULL,
    "name" VARCHAR NOT NULL,
    "value" NUMERIC NOT NULL,
    CONSTRAINT "game_rates_settings_pkey" PRIMARY KEY ("id")
);

ALTER TABLE "public"."quests" ADD CONSTRAINT "fk-quests-req_achievement" FOREIGN KEY ("req_achievement") REFERENCES "public"."achievements" ("id") ON DELETE SET NULL ON UPDATE CASCADE;

ALTER TABLE "public"."password_reset_tokens" ADD CONSTRAINT "fk-password_reset_tokens-user_id" FOREIGN KEY ("user_id") REFERENCES "public"."users" ("id") ON DELETE CASCADE ON UPDATE CASCADE;

ALTER TABLE "public"."class_skills" ADD CONSTRAINT "fk-class_skills-skill_id" FOREIGN KEY ("skill_id") REFERENCES "public"."skills" ("id") ON DELETE CASCADE ON UPDATE CASCADE;

ALTER TABLE "public"."class_skills" ADD CONSTRAINT "fk-class_skills-class_id" FOREIGN KEY ("class_id") REFERENCES "public"."classes" ("id") ON DELETE CASCADE ON UPDATE CASCADE;

ALTER TABLE "public"."items" ADD CONSTRAINT "fk-items-class_id" FOREIGN KEY ("class_id") REFERENCES "public"."classes" ("id") ON DELETE SET NULL ON UPDATE CASCADE;

ALTER TABLE "public"."items" ADD CONSTRAINT "fk-items-rarity" FOREIGN KEY ("rarity") REFERENCES "public"."item_rarities" ("id") ON DELETE CASCADE ON UPDATE CASCADE;

ALTER TABLE "public"."items" ADD CONSTRAINT "fk-items-enh_id" FOREIGN KEY ("enh_id") REFERENCES "public"."enhancements" ("id") ON DELETE SET NULL ON UPDATE CASCADE;

ALTER TABLE "public"."wheel_rewards" ADD CONSTRAINT "fk-wheel_rewards-item_id" FOREIGN KEY ("item_id") REFERENCES "public"."items" ("id") ON DELETE CASCADE ON UPDATE CASCADE;

ALTER TABLE "public"."item_bundles" ADD CONSTRAINT "fk-item_bundles-item_id" FOREIGN KEY ("item_id") REFERENCES "public"."items" ("id") ON DELETE CASCADE ON UPDATE CASCADE;

ALTER TABLE "public"."item_bundles" ADD CONSTRAINT "fk-item_bundles-reward_id" FOREIGN KEY ("reward_id") REFERENCES "public"."items" ("id") ON DELETE CASCADE ON UPDATE CASCADE;

ALTER TABLE "public"."item_effects" ADD CONSTRAINT "fk-item_effects-item_id" FOREIGN KEY ("item_id") REFERENCES "public"."items" ("id") ON DELETE CASCADE ON UPDATE CASCADE;

ALTER TABLE "public"."item_lotteries" ADD CONSTRAINT "fk-item_lotteries-item_id" FOREIGN KEY ("item_id") REFERENCES "public"."items" ("id") ON DELETE CASCADE ON UPDATE CASCADE;

ALTER TABLE "public"."item_lotteries" ADD CONSTRAINT "fk-item_lotteries-reward_id" FOREIGN KEY ("reward_id") REFERENCES "public"."items" ("id") ON DELETE CASCADE ON UPDATE CASCADE;

ALTER TABLE "public"."item_requirements" ADD CONSTRAINT "fk-item_requirements-item_id" FOREIGN KEY ("item_id") REFERENCES "public"."items" ("id") ON DELETE CASCADE ON UPDATE CASCADE;

ALTER TABLE "public"."item_requirements" ADD CONSTRAINT "fk-item_requirements-req_item_id" FOREIGN KEY ("req_item_id") REFERENCES "public"."items" ("id") ON DELETE CASCADE ON UPDATE CASCADE;

ALTER TABLE "public"."item_skills" ADD CONSTRAINT "fk-item_skills-item_id" FOREIGN KEY ("item_id") REFERENCES "public"."items" ("id") ON DELETE CASCADE ON UPDATE CASCADE;

ALTER TABLE "public"."item_skills" ADD CONSTRAINT "fk-item_skills-skill_id" FOREIGN KEY ("skill_id") REFERENCES "public"."skills" ("id") ON DELETE CASCADE ON UPDATE CASCADE;

ALTER TABLE "public"."skill_auras" ADD CONSTRAINT "fk-skill_auras-aura_id" FOREIGN KEY ("aura_id") REFERENCES "public"."auras" ("id") ON DELETE CASCADE ON UPDATE CASCADE;

ALTER TABLE "public"."skill_auras" ADD CONSTRAINT "fk-skill_auras-skill_id" FOREIGN KEY ("skill_id") REFERENCES "public"."skills" ("id") ON DELETE CASCADE ON UPDATE CASCADE;

ALTER TABLE "public"."aura_effects" ADD CONSTRAINT "fk-aura_effects-aura_id" FOREIGN KEY ("aura_id") REFERENCES "public"."auras" ("id") ON DELETE CASCADE ON UPDATE CASCADE;

ALTER TABLE "public"."enhancements" ADD CONSTRAINT "fk-enhancements-pattern_id" FOREIGN KEY ("pattern_id") REFERENCES "public"."enhancement_patterns" ("id") ON DELETE CASCADE ON UPDATE CASCADE;

ALTER TABLE "public"."monster_skills" ADD CONSTRAINT "fk-monster_skills-monster_id" FOREIGN KEY ("monster_id") REFERENCES "public"."monsters" ("id") ON DELETE CASCADE ON UPDATE CASCADE;

ALTER TABLE "public"."monster_skills" ADD CONSTRAINT "fk-monster_skills-skill_id" FOREIGN KEY ("skill_id") REFERENCES "public"."skills" ("id") ON DELETE CASCADE ON UPDATE CASCADE;

ALTER TABLE "public"."monster_drops" ADD CONSTRAINT "fk-monster_drops-item_id" FOREIGN KEY ("item_id") REFERENCES "public"."items" ("id") ON DELETE CASCADE ON UPDATE CASCADE;

ALTER TABLE "public"."monster_drops" ADD CONSTRAINT "fk-monster_drops-monster_id" FOREIGN KEY ("monster_id") REFERENCES "public"."monsters" ("id") ON DELETE CASCADE ON UPDATE CASCADE;

ALTER TABLE "public"."map_cells" ADD CONSTRAINT "fk-map_cells-map_id" FOREIGN KEY ("map_id") REFERENCES "public"."maps" ("id") ON DELETE CASCADE ON UPDATE CASCADE;

ALTER TABLE "public"."map_items" ADD CONSTRAINT "fk-map_items-map_id" FOREIGN KEY ("map_id") REFERENCES "public"."maps" ("id") ON DELETE CASCADE ON UPDATE CASCADE;

ALTER TABLE "public"."map_items" ADD CONSTRAINT "fk-map_items-item_id" FOREIGN KEY ("item_id") REFERENCES "public"."items" ("id") ON DELETE CASCADE ON UPDATE CASCADE;

ALTER TABLE "public"."map_monsters" ADD CONSTRAINT "fk-map_monsters-map_id" FOREIGN KEY ("map_id") REFERENCES "public"."maps" ("id") ON DELETE CASCADE ON UPDATE CASCADE;

ALTER TABLE "public"."map_monsters" ADD CONSTRAINT "fk-map_monsters-monster_id" FOREIGN KEY ("monster_id") REFERENCES "public"."monsters" ("id") ON DELETE CASCADE ON UPDATE CASCADE;

ALTER TABLE "public"."monster_bosses" ADD CONSTRAINT "fk-monster_bosses-map_id" FOREIGN KEY ("map_id") REFERENCES "public"."maps" ("id") ON DELETE CASCADE ON UPDATE CASCADE;

ALTER TABLE "public"."monster_bosses" ADD CONSTRAINT "fk-monster_bosses-monster_id" FOREIGN KEY ("monster_id") REFERENCES "public"."monsters" ("id") ON DELETE CASCADE ON UPDATE CASCADE;

ALTER TABLE "public"."shops" ADD CONSTRAINT "fk-shops-item_id" FOREIGN KEY ("item_id") REFERENCES "public"."items" ("id") ON DELETE SET NULL ON UPDATE CASCADE;

ALTER TABLE "public"."shop_items" ADD CONSTRAINT "fk-shop_items-item_id" FOREIGN KEY ("item_id") REFERENCES "public"."items" ("id") ON DELETE CASCADE ON UPDATE CASCADE;

ALTER TABLE "public"."shop_items" ADD CONSTRAINT "fk-shop_items-shop_id" FOREIGN KEY ("shop_id") REFERENCES "public"."shops" ("id") ON DELETE CASCADE ON UPDATE CASCADE;

ALTER TABLE "public"."shop_locations" ADD CONSTRAINT "fk-shop_locations-shop_id" FOREIGN KEY ("shop_id") REFERENCES "public"."shops" ("id") ON DELETE CASCADE ON UPDATE CASCADE;

ALTER TABLE "public"."shop_locations" ADD CONSTRAINT "fk-shop_locations-map_id" FOREIGN KEY ("map_id") REFERENCES "public"."maps" ("id") ON DELETE CASCADE ON UPDATE CASCADE;

ALTER TABLE "public"."users" ADD CONSTRAINT "fk-users-role" FOREIGN KEY ("role_id") REFERENCES "public"."roles" ("id") ON DELETE RESTRICT ON UPDATE CASCADE;

ALTER TABLE "public"."profiles" ADD CONSTRAINT "fk-users-profile" FOREIGN KEY ("user_id") REFERENCES "public"."users" ("id") ON DELETE CASCADE ON UPDATE CASCADE;

ALTER TABLE "public"."users" ADD CONSTRAINT "fk-users-title_id" FOREIGN KEY ("title_id") REFERENCES "public"."titles" ("id") ON DELETE CASCADE ON UPDATE CASCADE;

ALTER TABLE "public"."redeem_codes" ADD CONSTRAINT "fk-redeem_codes-item_id" FOREIGN KEY ("item_id") REFERENCES "public"."items" ("id") ON DELETE SET NULL ON UPDATE CASCADE;

ALTER TABLE "public"."sessions" ADD CONSTRAINT "fk-sessions-user_id" FOREIGN KEY ("user_id") REFERENCES "public"."users" ("id") ON DELETE CASCADE ON UPDATE CASCADE;

ALTER TABLE "public"."user_achievements" ADD CONSTRAINT "fk-user_achievements-achievement_id" FOREIGN KEY ("achievement_id") REFERENCES "public"."achievements" ("id") ON DELETE CASCADE ON UPDATE CASCADE;

ALTER TABLE "public"."user_achievements" ADD CONSTRAINT "fk-user_achievements-user_id" FOREIGN KEY ("user_id") REFERENCES "public"."users" ("id") ON DELETE CASCADE ON UPDATE CASCADE;

ALTER TABLE "public"."user_boosts" ADD CONSTRAINT "fk-user_boosts-user_id" FOREIGN KEY ("user_id") REFERENCES "public"."users" ("id") ON DELETE CASCADE ON UPDATE CASCADE;

ALTER TABLE "public"."user_browsers" ADD CONSTRAINT "fk-user_browsers-user_id" FOREIGN KEY ("user_id") REFERENCES "public"."users" ("id") ON DELETE CASCADE ON UPDATE CASCADE;

ALTER TABLE "public"."user_colors" ADD CONSTRAINT "fk-user_colors-user_id" FOREIGN KEY ("user_id") REFERENCES "public"."users" ("id") ON DELETE CASCADE ON UPDATE CASCADE;

ALTER TABLE "public"."user_currencies" ADD CONSTRAINT "fk-user_currencies-user_id" FOREIGN KEY ("user_id") REFERENCES "public"."users" ("id") ON DELETE CASCADE ON UPDATE CASCADE;

ALTER TABLE "public"."user_exps" ADD CONSTRAINT "fk-user_exps-user_id" FOREIGN KEY ("user_id") REFERENCES "public"."users" ("id") ON DELETE CASCADE ON UPDATE CASCADE;

ALTER TABLE "public"."user_factions" ADD CONSTRAINT "fk-user_factions-user_id" FOREIGN KEY ("user_id") REFERENCES "public"."users" ("id") ON DELETE CASCADE ON UPDATE CASCADE;

ALTER TABLE "public"."user_factions" ADD CONSTRAINT "fk-user_factions-faction_id" FOREIGN KEY ("faction_id") REFERENCES "public"."factions" ("id") ON DELETE CASCADE ON UPDATE CASCADE;

ALTER TABLE "public"."user_friends" ADD CONSTRAINT "fk-user_friends-user_id" FOREIGN KEY ("user_id") REFERENCES "public"."users" ("id") ON DELETE CASCADE ON UPDATE CASCADE;

ALTER TABLE "public"."user_friends" ADD CONSTRAINT "fk-user_friends-friend_id" FOREIGN KEY ("friend_id") REFERENCES "public"."users" ("id") ON DELETE CASCADE ON UPDATE CASCADE;

ALTER TABLE "public"."deleted_user_items" ADD CONSTRAINT "fk-deleted_user_items-item_id" FOREIGN KEY ("item_id") REFERENCES "public"."items" ("id") ON DELETE CASCADE ON UPDATE CASCADE;

ALTER TABLE "public"."deleted_user_items" ADD CONSTRAINT "fk-deleted_user_items-user_id" FOREIGN KEY ("user_id") REFERENCES "public"."users" ("id") ON DELETE CASCADE ON UPDATE CASCADE;

ALTER TABLE "public"."user_items" ADD CONSTRAINT "fk-user_items-item_id" FOREIGN KEY ("item_id") REFERENCES "public"."items" ("id") ON DELETE CASCADE ON UPDATE CASCADE;

ALTER TABLE "public"."user_items" ADD CONSTRAINT "fk-user_items-user_id" FOREIGN KEY ("user_id") REFERENCES "public"."users" ("id") ON DELETE CASCADE ON UPDATE CASCADE;

ALTER TABLE "public"."user_items" ADD CONSTRAINT "fk-user_items-enh_id" FOREIGN KEY ("enh_id") REFERENCES "public"."enhancements" ("id") ON DELETE CASCADE ON UPDATE CASCADE;

ALTER TABLE "public"."user_livedrops" ADD CONSTRAINT "fk-user_livedrops-achievement_id" FOREIGN KEY ("achievement_id") REFERENCES "public"."achievements" ("id") ON DELETE SET NULL ON UPDATE CASCADE;

ALTER TABLE "public"."user_livedrops" ADD CONSTRAINT "fk-user_livedrops-item_id" FOREIGN KEY ("item_id") REFERENCES "public"."items" ("id") ON DELETE SET NULL ON UPDATE CASCADE;

ALTER TABLE "public"."user_livedrops" ADD CONSTRAINT "fk-user_livedrops-title_id" FOREIGN KEY ("title_id") REFERENCES "public"."titles" ("id") ON DELETE SET NULL ON UPDATE CASCADE;

ALTER TABLE "public"."user_livedrops" ADD CONSTRAINT "fk-user_livedrops-user_id" FOREIGN KEY ("user_id") REFERENCES "public"."users" ("id") ON DELETE CASCADE ON UPDATE CASCADE;

ALTER TABLE "public"."user_logins" ADD CONSTRAINT "fk-user_logins-user_id" FOREIGN KEY ("user_id") REFERENCES "public"."users" ("id") ON DELETE CASCADE ON UPDATE CASCADE;

ALTER TABLE "public"."user_markets" ADD CONSTRAINT "fk-user_markets-item_id" FOREIGN KEY ("item_id") REFERENCES "public"."items" ("id") ON DELETE CASCADE ON UPDATE CASCADE;

ALTER TABLE "public"."user_markets" ADD CONSTRAINT "fk-user_markets-user_id" FOREIGN KEY ("user_id") REFERENCES "public"."users" ("id") ON DELETE CASCADE ON UPDATE CASCADE;

ALTER TABLE "public"."user_markets" ADD CONSTRAINT "fk-user_markets-buyer_id" FOREIGN KEY ("buyer_id") REFERENCES "public"."users" ("id") ON DELETE SET NULL ON UPDATE CASCADE;

ALTER TABLE "public"."user_purchases" ADD CONSTRAINT "fk-user_purchases-item_id" FOREIGN KEY ("item_id") REFERENCES "public"."stores" ("id") ON DELETE CASCADE ON UPDATE CASCADE;

ALTER TABLE "public"."user_purchases" ADD CONSTRAINT "fk-user_purchases-user_id" FOREIGN KEY ("user_id") REFERENCES "public"."users" ("id") ON DELETE CASCADE ON UPDATE CASCADE;

ALTER TABLE "public"."user_redeems" ADD CONSTRAINT "fk-user_redeems-user_id" FOREIGN KEY ("user_id") REFERENCES "public"."users" ("id") ON DELETE CASCADE ON UPDATE CASCADE;

ALTER TABLE "public"."user_redeems" ADD CONSTRAINT "fk-user_redeems-redeem_id" FOREIGN KEY ("redeem_id") REFERENCES "public"."redeem_codes" ("id") ON DELETE CASCADE ON UPDATE CASCADE;

ALTER TABLE "public"."user_reports" ADD CONSTRAINT "fk-user_reports-user_id" FOREIGN KEY ("user_id") REFERENCES "public"."users" ("id") ON DELETE CASCADE ON UPDATE CASCADE;

ALTER TABLE "public"."user_slots" ADD CONSTRAINT "fk-user_slots-user_id" FOREIGN KEY ("user_id") REFERENCES "public"."users" ("id") ON DELETE CASCADE ON UPDATE CASCADE;

ALTER TABLE "public"."user_stats" ADD CONSTRAINT "fk-user_stats-user_id" FOREIGN KEY ("user_id") REFERENCES "public"."users" ("id") ON DELETE CASCADE ON UPDATE CASCADE;

ALTER TABLE "public"."user_titles" ADD CONSTRAINT "fk-user_titles-user_id" FOREIGN KEY ("user_id") REFERENCES "public"."users" ("id") ON DELETE CASCADE ON UPDATE CASCADE;

ALTER TABLE "public"."user_titles" ADD CONSTRAINT "fk-user_titles-title_id" FOREIGN KEY ("title_id") REFERENCES "public"."titles" ("id") ON DELETE CASCADE ON UPDATE CASCADE;

ALTER TABLE "public"."guilds" ADD CONSTRAINT "fk-guilds-staff_g" FOREIGN KEY ("staff_g") REFERENCES "public"."users" ("id") ON DELETE CASCADE ON UPDATE CASCADE;

ALTER TABLE "public"."user_guilds" ADD CONSTRAINT "fk-user_guilds-guild_id" FOREIGN KEY ("guild_id") REFERENCES "public"."guilds" ("id") ON DELETE CASCADE ON UPDATE CASCADE;

ALTER TABLE "public"."user_guilds" ADD CONSTRAINT "fk-user_guilds-user_id" FOREIGN KEY ("user_id") REFERENCES "public"."users" ("id") ON DELETE CASCADE ON UPDATE CASCADE;

ALTER TABLE "public"."guild_halls" ADD CONSTRAINT "fk-guild_halls-guild_id" FOREIGN KEY ("guild_id") REFERENCES "public"."guilds" ("id") ON DELETE CASCADE ON UPDATE CASCADE;

ALTER TABLE "public"."guild_hall_buildings" ADD CONSTRAINT "fk-guild_hall_buildings-hall_id" FOREIGN KEY ("hall_id") REFERENCES "public"."guild_halls" ("id") ON DELETE CASCADE ON UPDATE CASCADE;

ALTER TABLE "public"."guild_hall_buildings" ADD CONSTRAINT "fk-guild_hall_buildings-item_id" FOREIGN KEY ("item_id") REFERENCES "public"."items" ("id") ON DELETE CASCADE ON UPDATE CASCADE;

ALTER TABLE "public"."guild_hall_connections" ADD CONSTRAINT "fk-guild_hall_connections-hall_id" FOREIGN KEY ("hall_id") REFERENCES "public"."guild_halls" ("id") ON DELETE CASCADE ON UPDATE CASCADE;

ALTER TABLE "public"."guild_levels" ADD CONSTRAINT "fk-guild_levels-guild_id" FOREIGN KEY ("guild_id") REFERENCES "public"."guilds" ("id") ON DELETE CASCADE ON UPDATE CASCADE;

ALTER TABLE "public"."guild_items" ADD CONSTRAINT "fk-guild_items-guild_id" FOREIGN KEY ("guild_id") REFERENCES "public"."guilds" ("id") ON DELETE CASCADE ON UPDATE CASCADE;

ALTER TABLE "public"."guild_items" ADD CONSTRAINT "fk-guild_items-item_id" FOREIGN KEY ("item_id") REFERENCES "public"."items" ("id") ON DELETE CASCADE ON UPDATE CASCADE;

ALTER TABLE "public"."guild_items" ADD CONSTRAINT "fk-guild_items-user_id" FOREIGN KEY ("user_id") REFERENCES "public"."users" ("id") ON DELETE CASCADE ON UPDATE CASCADE;

ALTER TABLE "public"."articles" ADD CONSTRAINT "fk-articles-author_id" FOREIGN KEY ("author_id") REFERENCES "public"."users" ("id") ON DELETE CASCADE ON UPDATE CASCADE;

ALTER TABLE "public"."book_quests" ADD CONSTRAINT "fk-book_quests-id" FOREIGN KEY ("id") REFERENCES "public"."books" ("id") ON DELETE CASCADE ON UPDATE CASCADE;

ALTER TABLE "public"."hair_shop_items" ADD CONSTRAINT "fk-hair_shop_items-hair_id" FOREIGN KEY ("hair_id") REFERENCES "public"."hairs" ("id") ON DELETE CASCADE ON UPDATE CASCADE;

ALTER TABLE "public"."hair_shop_items" ADD CONSTRAINT "fk-hair_shop_items-shop_id" FOREIGN KEY ("shop_id") REFERENCES "public"."hair_shops" ("id") ON DELETE CASCADE ON UPDATE CASCADE;

ALTER TABLE "public"."quests" ADD CONSTRAINT "fk-quests-achievement_id" FOREIGN KEY ("achievement_id") REFERENCES "public"."achievements" ("id") ON DELETE SET NULL ON UPDATE CASCADE;

ALTER TABLE "public"."quests" ADD CONSTRAINT "fk-quests-war_id" FOREIGN KEY ("war_id") REFERENCES "public"."wars" ("id") ON DELETE SET NULL ON UPDATE CASCADE;

ALTER TABLE "public"."user_quests" ADD CONSTRAINT "fk-user_quests-user_id" FOREIGN KEY ("user_id") REFERENCES "public"."users" ("id") ON DELETE CASCADE ON UPDATE CASCADE;

ALTER TABLE "public"."quest_locations" ADD CONSTRAINT "fk-quest_locations-quest_id" FOREIGN KEY ("quest_id") REFERENCES "public"."quests" ("id") ON DELETE CASCADE ON UPDATE CASCADE;

ALTER TABLE "public"."quest_locations" ADD CONSTRAINT "fk-quest_locations-map_id" FOREIGN KEY ("map_id") REFERENCES "public"."maps" ("id") ON DELETE CASCADE ON UPDATE CASCADE;

ALTER TABLE "public"."quest_requirements" ADD CONSTRAINT "fk-quest_requirements-quest_id" FOREIGN KEY ("quest_id") REFERENCES "public"."quests" ("id") ON DELETE CASCADE ON UPDATE CASCADE;

ALTER TABLE "public"."quest_requirements" ADD CONSTRAINT "fk-quest_requirements-item_id" FOREIGN KEY ("item_id") REFERENCES "public"."items" ("id") ON DELETE CASCADE ON UPDATE CASCADE;

ALTER TABLE "public"."quest_rewards" ADD CONSTRAINT "fk-quest_rewards-quest_id" FOREIGN KEY ("quest_id") REFERENCES "public"."quests" ("id") ON DELETE CASCADE ON UPDATE CASCADE;

ALTER TABLE "public"."quest_rewards" ADD CONSTRAINT "fk-quest_rewards-item_id" FOREIGN KEY ("item_id") REFERENCES "public"."items" ("id") ON DELETE CASCADE ON UPDATE CASCADE;

ALTER TABLE "public"."quest_reqditems" ADD CONSTRAINT "fk-quest_reqditems-quest_id" FOREIGN KEY ("quest_id") REFERENCES "public"."quests" ("id") ON DELETE CASCADE ON UPDATE CASCADE;

ALTER TABLE "public"."quest_reqditems" ADD CONSTRAINT "fk-quest_reqditems-item_id" FOREIGN KEY ("item_id") REFERENCES "public"."items" ("id") ON DELETE CASCADE ON UPDATE CASCADE;
