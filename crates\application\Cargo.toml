[package]
name = "reforged-application"
version = "0.1.0"
edition = "2024"

[dependencies]
anyhow.workspace = true
async-stream = "0.3.5"
async-trait.workspace = true
bon.workspace = true

chrono.workspace = true
esrs.workspace = true
rand.workspace = true
reforged-domain = { path = "../domain" }
reforged-shared = { path = "../shared" }
serde.workspace = true
serde_json.workspace = true
thiserror.workspace = true
tracing.workspace = true
tracing-subscriber.workspace = true
uuid.workspace = true

[dev-dependencies]
mockall.workspace = true
tokio = { version = "1", features = ["macros", "rt-multi-thread"] }
