use async_trait::async_trait;
use mockall::automock;

use crate::error::RepositoryError;
use crate::models::profile::entity::{Profile, ProfileId};

#[automock]
#[async_trait]
pub trait ProfileRepository: Send + Sync {
    async fn save(&self, entity: &Profile) -> Result<(), RepositoryError>;
    async fn update(&self, entity: &Profile) -> Result<(), RepositoryError>;
    async fn delete(&self, id: &ProfileId) -> Result<(), RepositoryError>;
}

#[automock]
#[async_trait]
pub trait ProfileReadRepository: Send + Sync {
    async fn find_by_id(&self, id: &ProfileId) -> Result<Option<Profile>, RepositoryError>;
    async fn find_all(&self) -> Result<Vec<Profile>, RepositoryError>;
}
