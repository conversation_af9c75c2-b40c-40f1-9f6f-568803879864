use super::value_object::{AchievementDate, UserAchievementId};
use crate::models::{achievement::value_object::AchievementId, user::value_object::UserId};
use getset::{<PERSON><PERSON>, Set<PERSON>};

#[derive(<PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, bon::<PERSON><PERSON><PERSON>, <PERSON><PERSON>, Setters)]
#[getset(get = "pub", set = "pub")]
pub struct UserAchievement {
    id: UserAchievementId,
    user_id: UserId,
    achievement_id: AchievementId,
    date: AchievementDate,
}

impl UserAchievement {
    pub fn new(
        id: UserAchievementId,
        user_id: UserId,
        achievement_id: AchievementId,
        date: AchievementDate,
    ) -> Self {
        Self {
            id,
            user_id,
            achievement_id,
            date,
        }
    }
}

#[cfg(test)]
mod tests {
    use chrono::Utc;
    use reforged_shared::{IdTrait, Value};

    use super::*;

    #[test]
    fn test_user_achievement_new() {
        let id = UserAchievementId::new(uuid::Uuid::now_v7());
        let user_id = UserId::new(uuid::Uuid::now_v7());
        let achievement_id = AchievementId::new(uuid::Uuid::now_v7());
        let date = AchievementDate::new(Utc::now());

        let user_achievement = UserAchievement::new(
            id.clone(),
            user_id.clone(),
            achievement_id.clone(),
            date.clone(),
        );

        assert_eq!(user_achievement.user_id.get_id(), user_id.get_id());
        assert_eq!(
            user_achievement.achievement_id.get_id(),
            achievement_id.get_id()
        );
        assert_eq!(user_achievement.date.value(), date.value());
    }

    #[test]
    fn test_user_achievement_builder() {
        let id = UserAchievementId::new(uuid::Uuid::now_v7());
        let user_id = UserId::new(uuid::Uuid::now_v7());
        let achievement_id = AchievementId::new(uuid::Uuid::now_v7());
        let date = AchievementDate::new(Utc::now());

        let user_achievement = UserAchievement::builder()
            .id(id.clone())
            .user_id(user_id.clone())
            .achievement_id(achievement_id.clone())
            .date(date.clone())
            .build();

        assert_eq!(user_achievement.id().get_id(), id.get_id());
        assert_eq!(user_achievement.user_id.get_id(), user_id.get_id());
        assert_eq!(
            user_achievement.achievement_id.get_id(),
            achievement_id.get_id()
        );
        assert_eq!(user_achievement.date.value(), date.value());
    }

    #[test]
    fn test_getters_and_setters() {
        let mut user_achievement = UserAchievement::default();

        let id = UserAchievementId::new(uuid::Uuid::now_v7());
        let user_id = UserId::new(uuid::Uuid::now_v7());
        let achievement_id = AchievementId::new(uuid::Uuid::now_v7());
        let date = AchievementDate::new(Utc::now());

        user_achievement.set_id(id.clone());
        user_achievement.set_user_id(user_id.clone());
        user_achievement.set_achievement_id(achievement_id.clone());
        user_achievement.set_date(date.clone());

        assert_eq!(user_achievement.id().get_id(), id.get_id());
        assert_eq!(user_achievement.user_id().get_id(), user_id.get_id());
        assert_eq!(
            user_achievement.achievement_id().get_id(),
            achievement_id.get_id()
        );
        assert_eq!(user_achievement.date().value(), date.value());
    }
}
