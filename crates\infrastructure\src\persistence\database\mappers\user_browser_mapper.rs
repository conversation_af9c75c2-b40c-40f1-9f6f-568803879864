use reforged_domain::models::user_browser::entity::UserBrowser;
use reforged_domain::models::user_browser::value_object::UserBrowserId;
use reforged_domain::models::user::value_object::UserId;

#[derive(Debug)]
pub struct UserBrowserDbModelMapper(super::super::models::user_browsers::Model);

impl UserBrowserDbModelMapper {
    pub fn new(model: super::super::models::user_browsers::Model) -> Self {
        Self(model)
    }
}

impl From<UserBrowserDbModelMapper> for UserBrowser {
    fn from(value: UserBrowserDbModelMapper) -> Self {
        let model = value.0;
        
        UserBrowser::builder()
            .id(UserBrowserId::new(model.id))
            .user_id(UserId::new(model.user_id))
            .referer(model.referer.into())
            .engine(model.engine.into())
            .platform(model.platform.into())
            .browser(model.browser.into())
            .build()
    }
}
