//! `SeaORM` Entity, @generated by sea-orm-codegen 1.1.0

use sea_orm::entity::prelude::*;
use serde::Deserialize;

#[derive(<PERSON><PERSON>, Debug, PartialEq, DeriveEntityModel, Eq, Deserialize)]
#[sea_orm(table_name = "titles")]
pub struct Model {
    #[sea_orm(primary_key, auto_increment = false)]
    pub id: Uuid,
    pub name: String,
    #[sea_orm(column_type = "Text")]
    pub description: String,
    pub color: String,
    pub strength: i32,
    pub intellect: i32,
    pub endurance: i32,
    pub dexterity: i32,
    pub wisdom: i32,
    pub luck: i32,
    pub role_id: Uuid,
}

#[derive(Co<PERSON>, <PERSON><PERSON>, Debug, EnumIter, DeriveRelation)]
pub enum Relation {
    #[sea_orm(has_many = "super::user_livedrops::Entity")]
    UserLivedrops,
    #[sea_orm(has_many = "super::user_titles::Entity")]
    UserTitles,
    #[sea_orm(has_many = "super::users::Entity")]
    Users,
}

impl Related<super::user_livedrops::Entity> for Entity {
    fn to() -> RelationDef {
        Relation::UserLivedrops.def()
    }
}

impl Related<super::user_titles::Entity> for Entity {
    fn to() -> RelationDef {
        Relation::UserTitles.def()
    }
}

impl Related<super::users::Entity> for Entity {
    fn to() -> RelationDef {
        Relation::Users.def()
    }
}

impl ActiveModelBehavior for ActiveModel {}
