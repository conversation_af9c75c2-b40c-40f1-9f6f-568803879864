To avoid foreign key errors when populating the database based on the schema in `crates/infrastructure/src/persistence/database/schema/reforged.sql`, you should first populate the tables that do not have foreign key constraints referencing other tables.

Based on the `ALTER TABLE ... ADD FOREIGN KEY ... REFERENCES ...` statements (lines 1121 onwards), the following tables appear to have **no foreign key dependencies** on other tables within this schema and can be populated first (in any order relative to each other):

*   `access` (line 26)
*   `achievements` (line 33)
*   `auras` (line 63)
*   `books` (line 84)
*   `book_quests` (line 102)
*   `classes` (line 115)
*   `class_categories` (line 125)
*   `enhancement_patterns` (line 155)
*   `factions` (line 168)
*   `guilds` (line 174)
*   `guild_levels` (line 228)
*   `hairs` (line 234)
*   `hair_shops` (line 242)
*   `item_rarities` (line 324)
*   `maps` (line 346)
*   `monsters` (line 383) - *Note: `team_id` (line 399) might imply a dependency, but no corresponding table/constraint is defined.*
*   `servers` (line 521)
*   `filter_settings` (line 536)
*   `login_settings` (line 542)
*   `rate_settings` (line 549)
*   `skills` (line 583)
*   `stores` (line 615)
*   `titles` (line 638)
*   `wars` (line 1041)
*   `user_colors` (line 709)
*   `user_currencies` (line 722)
*   `user_exps` (line 731)
*   `user_slots` (line 737)
*   `user_boosts` (line 746)

After populating these tables, you can proceed to populate tables that depend on them, following the dependency chain defined by the foreign key constraints. For example, `users` depends on `titles`, `access`, `user_colors`, `user_currencies`, `user_exps`, `user_slots`, and `user_boosts`, so these must exist before `users` can be populated.