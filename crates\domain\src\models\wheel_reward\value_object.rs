use reforged_shared::{UuidId, Value};

use super::entity::WheelReward;

pub type WheelRewardId = UuidId<WheelReward>;

#[derive(Debug, PartialEq, PartialOrd, Clone, Default)]
pub struct WheelRewardChance(f64);

impl WheelRewardChance {
    pub fn new(chance: f64) -> Self {
        Self(chance)
    }
}

impl Value<f64> for WheelRewardChance {
    fn value(&self) -> f64 {
        self.0
    }
}

impl From<f64> for WheelRewardChance {
    fn from(value: f64) -> Self {
        Self(value)
    }
}
