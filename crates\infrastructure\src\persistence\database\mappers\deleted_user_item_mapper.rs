use chrono::{DateTime, Utc};
use reforged_domain::models::deleted_user_item::value_object::DeletedDate;
use reforged_domain::models::deleted_user_item::{
    entity::DeletedUserItem, value_object::DeletedUserItemId,
};
use reforged_domain::models::item::value_object::ItemId;
use reforged_domain::models::user::value_object::UserId;

#[derive(Debug)]
pub struct DeletedUserItemDbModelMapper(super::super::models::deleted_user_items::Model);

impl DeletedUserItemDbModelMapper {
    pub fn new(deleted_user_item: super::super::models::deleted_user_items::Model) -> Self {
        Self(deleted_user_item)
    }
}

impl From<DeletedUserItemDbModelMapper> for DeletedUserItem {
    fn from(value: DeletedUserItemDbModelMapper) -> Self {
        let deleted_user_item_model = value.0;

        let id = DeletedUserItemId::new(deleted_user_item_model.id);

        DeletedUserItem::builder()
            .id(id)
            .user_id(UserId::new(deleted_user_item_model.user_id))
            .item_id(ItemId::new(deleted_user_item_model.item_id))
            .quantity(deleted_user_item_model.quantity.into())
            .date(DeletedDate::new(
                DateTime::<Utc>::from_naive_utc_and_offset(deleted_user_item_model.date, Utc),
            ))
            .build()
    }
}
