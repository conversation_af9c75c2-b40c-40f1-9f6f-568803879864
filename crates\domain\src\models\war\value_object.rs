use reforged_shared::{UuidId, Value};

use super::entity::War;

pub type WarId = UuidId<War>;

#[derive(Debug, PartialEq, Eq, PartialOrd, Ord, Clone, De<PERSON>ult)]

pub struct WarName(String);

impl WarName {
    pub fn new(name: impl Into<String>) -> Self {
        Self(name.into())
    }
}

impl Value<String> for WarName {
    fn value(&self) -> String {
        self.0.clone()
    }
}

impl From<String> for WarName {
    fn from(value: String) -> Self {
        Self(value)
    }
}

impl From<&str> for WarName {
    fn from(value: &str) -> Self {
        Self(value.to_string())
    }
}

#[derive(Debug, PartialEq, Eq, PartialOrd, Ord, Clone, Default)]

pub struct WarPoints(i32);

impl WarPoints {
    pub fn new(points: i32) -> Self {
        Self(points)
    }
}

impl Value<i32> for WarPoints {
    fn value(&self) -> i32 {
        self.0
    }
}

impl From<i32> for WarPoints {
    fn from(value: i32) -> Self {
        Self(value)
    }
}

#[derive(Debug, PartialEq, Eq, PartialOrd, Ord, <PERSON>lone, De<PERSON>ult)]

pub struct WarMaxPoints(i32);

impl WarMaxPoints {
    pub fn new(max_points: i32) -> Self {
        Self(max_points)
    }
}

impl Value<i32> for WarMaxPoints {
    fn value(&self) -> i32 {
        self.0
    }
}

impl From<i32> for WarMaxPoints {
    fn from(value: i32) -> Self {
        Self(value)
    }
}
