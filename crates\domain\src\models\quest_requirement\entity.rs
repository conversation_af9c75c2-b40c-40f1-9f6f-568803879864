use crate::models::item::value_object::ItemId;
use crate::models::quest::value_object::QuestId;
use crate::models::quest_requirement::value_object::*;
use getset::{<PERSON><PERSON>, <PERSON><PERSON>};

#[derive(<PERSON>bu<PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, bon::<PERSON><PERSON><PERSON>, <PERSON><PERSON>, Set<PERSON>)]
#[getset(get = "pub", set = "pub")]
pub struct QuestRequirement {
    pub id: QuestRequirementId,
    pub quest_id: QuestId,
    pub item_id: ItemId,
    pub quantity: QuestRequirementQuantity,
}

impl QuestRequirement {
    pub fn new(
        id: QuestRequirementId,
        quest_id: QuestId,
        item_id: ItemId,
        quantity: QuestRequirementQuantity,
    ) -> Self {
        Self {
            id,
            quest_id,
            item_id,
            quantity,
        }
    }
}

#[cfg(test)]
mod tests {
    use reforged_shared::{IdTrait, Value};

    use super::*;

    #[test]
    fn test_quest_requirement_new() {
        let id = QuestRequirementId::new(uuid::Uuid::now_v7());
        let quest_id = QuestId::new(uuid::Uuid::now_v7());
        let item_id = ItemId::new(uuid::Uuid::now_v7());
        let quantity = QuestRequirementQuantity::new(5);

        let quest_requirement = QuestRequirement::new(
            id.clone(),
            quest_id.clone(),
            item_id.clone(),
            quantity.clone(),
        );

        assert_eq!(quest_requirement.id().get_id(), id.get_id());
        assert_eq!(quest_requirement.quest_id().get_id(), quest_id.get_id());
        assert_eq!(quest_requirement.item_id().get_id(), item_id.get_id());
        assert_eq!(quest_requirement.quantity().value(), quantity.value());
    }

    #[test]
    fn test_quest_requirement_builder() {
        let id = QuestRequirementId::new(uuid::Uuid::now_v7());
        let quest_id = QuestId::new(uuid::Uuid::now_v7());
        let item_id = ItemId::new(uuid::Uuid::now_v7());
        let quantity = QuestRequirementQuantity::new(5);

        let quest_requirement = QuestRequirement::builder()
            .id(id.clone())
            .quest_id(quest_id.clone())
            .item_id(item_id.clone())
            .quantity(quantity.clone())
            .build();

        assert_eq!(quest_requirement.id().get_id(), id.get_id());
        assert_eq!(quest_requirement.quest_id().get_id(), quest_id.get_id());
        assert_eq!(quest_requirement.item_id().get_id(), item_id.get_id());
        assert_eq!(quest_requirement.quantity().value(), quantity.value());
    }

    #[test]
    fn test_getters_and_setters() {
        let mut quest_requirement = QuestRequirement::default();

        let id = QuestRequirementId::new(uuid::Uuid::now_v7());
        let quest_id = QuestId::new(uuid::Uuid::now_v7());
        let item_id = ItemId::new(uuid::Uuid::now_v7());
        let quantity = QuestRequirementQuantity::new(5);

        quest_requirement.set_id(id.clone());
        quest_requirement.set_quest_id(quest_id.clone());
        quest_requirement.set_item_id(item_id.clone());
        quest_requirement.set_quantity(quantity.clone());

        assert_eq!(quest_requirement.id().get_id(), id.get_id());
        assert_eq!(quest_requirement.quest_id().get_id(), quest_id.get_id());
        assert_eq!(quest_requirement.item_id().get_id(), item_id.get_id());
        assert_eq!(quest_requirement.quantity().value(), quantity.value());
    }
}
