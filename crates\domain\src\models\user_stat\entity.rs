use super::value_object::*;
use crate::models::user::value_object::UserId;
use getset::{<PERSON><PERSON>, <PERSON><PERSON>};

#[derive(<PERSON>bu<PERSON>, <PERSON><PERSON>, De<PERSON>ult, bon::<PERSON><PERSON><PERSON>, <PERSON><PERSON>, Set<PERSON>)]
#[getset(get = "pub", set = "pub")]
pub struct UserStat {
    id: UserStatId,
    user_id: UserId,
    last_area: UserStatLastArea,
    current_server: UserStatCurrentServer,
    house_info: UserStatHouseInfo,
    kill_count: UserStatKillCount,
    death_count: UserStatDeathCount,
    pvp_ratio: UserStatPvpRatio,
}

impl UserStat {
    pub fn new(
        id: UserStatId,
        user_id: UserId,
        last_area: UserStatLastArea,
        current_server: UserStatCurrentServer,
        house_info: UserStatHouseInfo,
        kill_count: UserStatKillCount,
        death_count: UserStatDeathCount,
        pvp_ratio: UserStatPvpRatio,
    ) -> Self {
        Self {
            id,
            user_id,
            last_area,
            current_server,
            house_info,
            kill_count,
            death_count,
            pvp_ratio,
        }
    }
}

#[cfg(test)]
mod tests {
    use reforged_shared::{IdTrait, Value};

    use super::*;

    #[test]
    fn test_user_stat_new() {
        let id = UserStatId::new(uuid::Uuid::now_v7());
        let user_id = UserId::new(uuid::Uuid::now_v7());
        let last_area = UserStatLastArea::new("town".to_string());
        let current_server = UserStatCurrentServer::new("server1".to_string());
        let house_info = UserStatHouseInfo::new("house_info".to_string());
        let kill_count = UserStatKillCount::new(100);
        let death_count = UserStatDeathCount::new(10);
        let pvp_ratio = UserStatPvpRatio::new(Some(10));

        let user_stat = UserStat::new(
            id.clone(),
            user_id.clone(),
            last_area.clone(),
            current_server.clone(),
            house_info.clone(),
            kill_count.clone(),
            death_count.clone(),
            pvp_ratio.clone(),
        );

        assert_eq!(user_stat.id().get_id(), id.get_id());
        assert_eq!(user_stat.user_id.get_id(), user_id.get_id());
        assert_eq!(user_stat.last_area.value(), last_area.value());
        assert_eq!(user_stat.current_server.value(), current_server.value());
        assert_eq!(user_stat.house_info.value(), house_info.value());
        assert_eq!(user_stat.kill_count.value(), kill_count.value());
        assert_eq!(user_stat.death_count.value(), death_count.value());
        assert_eq!(user_stat.pvp_ratio.value(), pvp_ratio.value());
    }

    #[test]
    fn test_user_stat_builder() {
        let id = UserStatId::new(uuid::Uuid::now_v7());
        let user_id = UserId::new(uuid::Uuid::now_v7());
        let last_area = UserStatLastArea::new("town".to_string());
        let current_server = UserStatCurrentServer::new("server1".to_string());
        let house_info = UserStatHouseInfo::new("house_info".to_string());
        let kill_count = UserStatKillCount::new(100);
        let death_count = UserStatDeathCount::new(10);
        let pvp_ratio = UserStatPvpRatio::new(Some(10));

        let user_stat = UserStat::builder()
            .id(id.clone())
            .user_id(user_id.clone())
            .last_area(last_area.clone())
            .current_server(current_server.clone())
            .house_info(house_info.clone())
            .kill_count(kill_count.clone())
            .death_count(death_count.clone())
            .pvp_ratio(pvp_ratio.clone())
            .build();

        assert_eq!(user_stat.id().get_id(), id.get_id());
        assert_eq!(user_stat.user_id.get_id(), user_id.get_id());
        assert_eq!(user_stat.last_area.value(), last_area.value());
        assert_eq!(user_stat.current_server.value(), current_server.value());
        assert_eq!(user_stat.house_info.value(), house_info.value());
        assert_eq!(user_stat.kill_count.value(), kill_count.value());
        assert_eq!(user_stat.death_count.value(), death_count.value());
        assert_eq!(user_stat.pvp_ratio.value(), pvp_ratio.value());
    }

    #[test]
    fn test_getters_and_setters() {
        let mut user_stat = UserStat::default();

        let id = UserStatId::new(uuid::Uuid::now_v7());
        let user_id = UserId::new(uuid::Uuid::now_v7());
        let last_area = UserStatLastArea::new("town".to_string());
        let current_server = UserStatCurrentServer::new("server1".to_string());
        let house_info = UserStatHouseInfo::new("house_info".to_string());
        let kill_count = UserStatKillCount::new(100);
        let death_count = UserStatDeathCount::new(10);
        let pvp_ratio = UserStatPvpRatio::new(Some(10));

        user_stat.set_id(id.clone());
        user_stat.set_user_id(user_id.clone());
        user_stat.set_last_area(last_area.clone());
        user_stat.set_current_server(current_server.clone());
        user_stat.set_house_info(house_info.clone());
        user_stat.set_kill_count(kill_count.clone());
        user_stat.set_death_count(death_count.clone());
        user_stat.set_pvp_ratio(pvp_ratio.clone());

        assert_eq!(user_stat.id().get_id(), id.get_id());
        assert_eq!(user_stat.user_id().get_id(), user_id.get_id());
        assert_eq!(user_stat.last_area().value(), last_area.value());
        assert_eq!(user_stat.current_server().value(), current_server.value());
        assert_eq!(user_stat.house_info().value(), house_info.value());
        assert_eq!(user_stat.kill_count().value(), kill_count.value());
        assert_eq!(user_stat.death_count().value(), death_count.value());
        assert_eq!(user_stat.pvp_ratio().value(), pvp_ratio.value());
    }
}
