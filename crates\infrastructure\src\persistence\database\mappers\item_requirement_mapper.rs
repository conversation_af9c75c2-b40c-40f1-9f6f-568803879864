use reforged_domain::models::item_requirement::entity::ItemRequirement;
use reforged_domain::models::item_requirement::value_object::ItemRequirementId;
use reforged_domain::models::item::value_object::ItemId;

#[derive(Debug)]
pub struct ItemRequirementDbModelMapper(super::super::models::item_requirements::Model);

impl ItemRequirementDbModelMapper {
    pub fn new(model: super::super::models::item_requirements::Model) -> Self {
        Self(model)
    }
}

impl From<ItemRequirementDbModelMapper> for ItemRequirement {
    fn from(value: ItemRequirementDbModelMapper) -> Self {
        let model = value.0;
        
        ItemRequirement::builder()
            .id(ItemRequirementId::new(model.id))
            .item_id(ItemId::new(model.item_id))
            .req_item_id(ItemId::new(model.req_item_id))
            .quantity(model.quantity.into())
            .build()
    }
}
