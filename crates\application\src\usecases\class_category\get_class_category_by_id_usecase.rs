use crate::{
    error::ApplicationError,
    queries::{
        class_category_queries::GetClassCategoryByIdQuery,
        class_category_query_handlers::{ClassCategory<PERSON>uery<PERSON>and<PERSON>, ClassCategoryResponse},
    },
    traits::Query<PERSON><PERSON><PERSON>,
};

pub struct GetClassCategoryByIdUsecase {
    class_category_query_handler: ClassCategoryQueryHandler,
}

impl GetClassCategoryByIdUsecase {
    pub fn new(class_category_query_handler: ClassCategoryQueryHandler) -> Self {
        Self {
            class_category_query_handler,
        }
    }
}

impl GetClassCategoryByIdUsecase {
    pub async fn execute(&self, id: uuid::Uuid) -> Result<ClassCategoryResponse, ApplicationError> {
        let query = GetClassCategoryByIdQuery { id };
        let response = self
            .class_category_query_handler
            .handle(query)
            .await?
            .ok_or(ApplicationError::EntityNotFound(id.to_string()))?;

        Ok(response)
    }
}
