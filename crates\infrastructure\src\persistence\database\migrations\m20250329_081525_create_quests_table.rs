use crate::models::prelude::*;
use sea_orm::Schema;
use sea_orm_migration::prelude::*;

#[derive(DeriveMigrationName)]
pub struct Migration;

#[async_trait::async_trait]
impl MigrationTrait for Migration {
    async fn up(&self, manager: &SchemaManager) -> Result<(), DbErr> {
        let backend = manager.get_database_backend();
        let schema = Schema::new(backend);

        // MARK: quests tables and junction tables
        manager
            .create_table(schema.create_table_from_entity(Quests))
            .await?;

        manager
            .create_table(schema.create_table_from_entity(UserQuests))
            .await?;

        manager
            .create_table(schema.create_table_from_entity(QuestLocations))
            .await?;
        manager
            .create_table(schema.create_table_from_entity(QuestRequirements))
            .await?;
        manager
            .create_table(schema.create_table_from_entity(QuestRewards))
            .await?;
        manager
            .create_table(schema.create_table_from_entity(QuestReqditems))
            .await?;

        Ok(())
    }

    async fn down(&self, manager: &SchemaManager) -> Result<(), DbErr> {
        manager
            .drop_table(Table::drop().table(QuestReqditems).to_owned())
            .await?;
        manager
            .drop_table(Table::drop().table(QuestRewards).to_owned())
            .await?;
        manager
            .drop_table(Table::drop().table(QuestRequirements).to_owned())
            .await?;
        manager
            .drop_table(Table::drop().table(QuestLocations).to_owned())
            .await?;
        manager
            .drop_table(Table::drop().table(UserQuests).to_owned())
            .await?;
        manager
            .drop_table(Table::drop().table(Quests).to_owned())
            .await
    }
}
