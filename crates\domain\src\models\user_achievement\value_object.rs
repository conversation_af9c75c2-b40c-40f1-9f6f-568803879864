use chrono::{DateTime, Utc};
use reforged_shared::{UuidId, Value};

use super::entity::UserAchievement;

pub type UserAchievementId = UuidId<UserAchievement>;

#[derive(Debug, PartialEq, Eq, PartialOrd, Ord, <PERSON>lone, Default)]
pub struct AchievementDate(DateTime<Utc>);

impl AchievementDate {
    pub fn new(date: DateTime<Utc>) -> Self {
        Self(date)
    }
}

impl Value<DateTime<Utc>> for AchievementDate {
    fn value(&self) -> DateTime<Utc> {
        self.0
    }
}

impl From<DateTime<Utc>> for AchievementDate {
    fn from(value: DateTime<Utc>) -> Self {
        Self(value)
    }
}
