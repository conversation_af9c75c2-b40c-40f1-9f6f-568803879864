//! `SeaORM` Entity, @generated by sea-orm-codegen 1.1.0

use sea_orm::entity::prelude::*;
use serde::Deserialize;

#[derive(Clone, Debug, PartialEq, DeriveEntityModel, Eq, Deserialize)]
#[sea_orm(table_name = "quests")]
pub struct Model {
    #[sea_orm(primary_key, auto_increment = false)]
    pub id: Uuid,
    pub faction_id: Uuid,
    pub req_achievement: Option<Uuid>,
    pub req_reputation: i32,
    pub req_class_id: Option<Uuid>,
    pub req_class_points: i32,
    pub name: String,
    #[sea_orm(column_type = "Text")]
    pub description: String,
    #[sea_orm(column_type = "Text", nullable)]
    pub end_text: Option<String>,
    pub experience: i64,
    pub g_experience: i32,
    pub gold: i64,
    pub coins: i32,
    pub reputation: i32,
    pub class_points: i32,
    pub reward_type: String,
    pub level: i16,
    pub upgrade: bool,
    pub once: bool,
    pub slot: i32,
    pub value: i32,
    pub field: String,
    pub index: i32,
    pub badges: Option<String>,
    pub give_membership: Option<i32>,
    pub war_id: Option<Uuid>,
    pub achievement_id: Option<Uuid>,
    pub war_mega: bool,
    pub req_guild_level: Option<i32>,
    pub staff: bool,
    pub check: bool,
}

#[derive(Copy, Clone, Debug, EnumIter, DeriveRelation)]
pub enum Relation {
    #[sea_orm(
        belongs_to = "super::achievements::Entity",
        from = "Column::AchievementId",
        to = "super::achievements::Column::Id",
        on_update = "Cascade",
        on_delete = "SetNull"
    )]
    Achievements2,
    #[sea_orm(
        belongs_to = "super::achievements::Entity",
        from = "Column::ReqAchievement",
        to = "super::achievements::Column::Id",
        on_update = "Cascade",
        on_delete = "SetNull"
    )]
    Achievements1,
    #[sea_orm(has_many = "super::quest_locations::Entity")]
    QuestLocations,
    #[sea_orm(has_many = "super::quest_reqditems::Entity")]
    QuestReqditems,
    #[sea_orm(has_many = "super::quest_requirements::Entity")]
    QuestRequirements,
    #[sea_orm(has_many = "super::quest_rewards::Entity")]
    QuestRewards,
    #[sea_orm(
        belongs_to = "super::wars::Entity",
        from = "Column::WarId",
        to = "super::wars::Column::Id",
        on_update = "Cascade",
        on_delete = "SetNull"
    )]
    Wars,
}

impl Related<super::quest_locations::Entity> for Entity {
    fn to() -> RelationDef {
        Relation::QuestLocations.def()
    }
}

impl Related<super::quest_reqditems::Entity> for Entity {
    fn to() -> RelationDef {
        Relation::QuestReqditems.def()
    }
}

impl Related<super::quest_requirements::Entity> for Entity {
    fn to() -> RelationDef {
        Relation::QuestRequirements.def()
    }
}

impl Related<super::quest_rewards::Entity> for Entity {
    fn to() -> RelationDef {
        Relation::QuestRewards.def()
    }
}

impl Related<super::wars::Entity> for Entity {
    fn to() -> RelationDef {
        Relation::Wars.def()
    }
}

impl ActiveModelBehavior for ActiveModel {}
