use reforged_domain::models::map_cell::entity::MapCell;
use reforged_domain::models::map_cell::value_object::MapCellId;
use reforged_domain::models::map::value_object::MapId;

#[derive(Debug)]
pub struct MapCellDbModelMapper(super::super::models::map_cells::Model);

impl MapCellDbModelMapper {
    pub fn new(model: super::super::models::map_cells::Model) -> Self {
        Self(model)
    }
}

impl From<MapCellDbModelMapper> for MapCell {
    fn from(value: MapCellDbModelMapper) -> Self {
        let model = value.0;
        
        MapCell::builder()
            .id(MapCellId::new(model.id))
            .map_id(MapId::new(model.map_id))
            .frame(model.frame.into())
            .pad(model.pad.into())
            .build()
    }
}
