use crate::models::map::value_object::MapId;
use crate::models::quest::value_object::QuestId;
use crate::models::quest_location::value_object::*;
use getset::{<PERSON><PERSON>, Set<PERSON>};

#[derive(<PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, bon::<PERSON><PERSON><PERSON>, <PERSON><PERSON>, Set<PERSON>)]
#[getset(get = "pub", set = "pub")]
pub struct QuestLocation {
    pub id: QuestLocationId,
    pub quest_id: QuestId,
    pub map_id: MapId,
}

impl QuestLocation {
    pub fn new(id: QuestLocationId, quest_id: QuestId, map_id: MapId) -> Self {
        Self {
            id,
            quest_id,
            map_id,
        }
    }
}

#[cfg(test)]
mod tests {
    use reforged_shared::IdTrait;

    use super::*;

    #[test]
    fn test_quest_location_new() {
        let id = QuestLocationId::new(uuid::Uuid::now_v7());
        let quest_id = QuestId::new(uuid::Uuid::now_v7());
        let map_id = MapId::new(uuid::Uuid::now_v7());

        let quest_location = QuestLocation::new(id.clone(), quest_id.clone(), map_id.clone());

        assert_eq!(quest_location.id().get_id(), id.get_id());
        assert_eq!(quest_location.quest_id().get_id(), quest_id.get_id());
        assert_eq!(quest_location.map_id().get_id(), map_id.get_id());
    }

    #[test]
    fn test_quest_location_builder() {
        let id = QuestLocationId::new(uuid::Uuid::now_v7());
        let quest_id = QuestId::new(uuid::Uuid::now_v7());
        let map_id = MapId::new(uuid::Uuid::now_v7());

        let quest_location = QuestLocation::builder()
            .id(id.clone())
            .quest_id(quest_id.clone())
            .map_id(map_id.clone())
            .build();

        assert_eq!(quest_location.id().get_id(), id.get_id());
        assert_eq!(quest_location.quest_id().get_id(), quest_id.get_id());
        assert_eq!(quest_location.map_id().get_id(), map_id.get_id());
    }

    #[test]
    fn test_getters_and_setters() {
        let mut quest_location = QuestLocation::default();

        let id = QuestLocationId::new(uuid::Uuid::now_v7());
        let quest_id = QuestId::new(uuid::Uuid::now_v7());
        let map_id = MapId::new(uuid::Uuid::now_v7());

        quest_location.set_id(id.clone());
        quest_location.set_quest_id(quest_id.clone());
        quest_location.set_map_id(map_id.clone());

        assert_eq!(quest_location.id().get_id(), id.get_id());
        assert_eq!(quest_location.quest_id().get_id(), quest_id.get_id());
        assert_eq!(quest_location.map_id().get_id(), map_id.get_id());
    }
}
