use super::{Config, ConfigError};

pub struct CaptchaConfig {
    secret_key: String,
}

impl CaptchaConfig {
    pub fn new(secret_key: String) -> Self {
        Self { secret_key }
    }

    pub fn secret_key(&self) -> String {
        self.secret_key.clone()
    }
}

impl Config for CaptchaConfig {
    fn from_env() -> Result<Self, ConfigError> {
        let secret_key = std::env::var("CAPTCHA_SECRET_KEY")
            .map_err(|_| ConfigError::EnvVarNotFound("CAPTCHA_SECRET_KEY".to_string()))?;

        Ok(Self::new(secret_key))
    }
}
