use super::entity::EnhancementPattern;
use reforged_shared::{UuidId, Value};

pub type EnhancementPatternId = UuidId<EnhancementPattern>;

#[derive(Debug, PartialEq, Eq, PartialOrd, Ord, <PERSON>lone, Default)]

pub struct EnhancementPatternName(String);

impl EnhancementPatternName {
    pub fn new(name: impl Into<String>) -> Self {
        Self(name.into())
    }
}

impl Value<String> for EnhancementPatternName {
    fn value(&self) -> String {
        self.0.clone()
    }
}

impl From<String> for EnhancementPatternName {
    fn from(value: String) -> Self {
        Self(value)
    }
}

impl From<&str> for EnhancementPatternName {
    fn from(value: &str) -> Self {
        Self(value.to_string())
    }
}

#[derive(Debug, PartialEq, Eq, PartialOrd, Ord, <PERSON><PERSON>, Default)]

pub struct Description(String);

impl Description {
    pub fn new(description: impl Into<String>) -> Self {
        Self(description.into())
    }
}

impl Value<String> for Description {
    fn value(&self) -> String {
        self.0.clone()
    }
}

impl From<String> for Description {
    fn from(value: String) -> Self {
        Self(value)
    }
}

impl From<&str> for Description {
    fn from(value: &str) -> Self {
        Self(value.to_string())
    }
}

#[derive(Debug, PartialEq, Eq, PartialOrd, Ord, Clone, Default)]

pub struct Wisdom(i16);

impl Wisdom {
    pub fn new(value: i16) -> Self {
        Self(value)
    }
}

impl Value<i16> for Wisdom {
    fn value(&self) -> i16 {
        self.0
    }
}

impl From<i16> for Wisdom {
    fn from(value: i16) -> Self {
        Self(value)
    }
}

#[derive(Debug, PartialEq, Eq, PartialOrd, Ord, Clone, Default)]

pub struct Strength(i16);

impl Strength {
    pub fn new(value: i16) -> Self {
        Self(value)
    }
}

impl Value<i16> for Strength {
    fn value(&self) -> i16 {
        self.0
    }
}

impl From<i16> for Strength {
    fn from(value: i16) -> Self {
        Self(value)
    }
}

#[derive(Debug, PartialEq, Eq, PartialOrd, Ord, Clone, Default)]

pub struct Luck(i16);

impl Luck {
    pub fn new(value: i16) -> Self {
        Self(value)
    }
}

impl Value<i16> for Luck {
    fn value(&self) -> i16 {
        self.0
    }
}

impl From<i16> for Luck {
    fn from(value: i16) -> Self {
        Self(value)
    }
}

#[derive(Debug, PartialEq, Eq, PartialOrd, Ord, Clone, Default)]

pub struct Dexterity(i16);

impl Dexterity {
    pub fn new(value: i16) -> Self {
        Self(value)
    }
}

impl Value<i16> for Dexterity {
    fn value(&self) -> i16 {
        self.0
    }
}

impl From<i16> for Dexterity {
    fn from(value: i16) -> Self {
        Self(value)
    }
}

#[derive(Debug, PartialEq, Eq, PartialOrd, Ord, Clone, Default)]

pub struct Endurance(i16);

impl Endurance {
    pub fn new(value: i16) -> Self {
        Self(value)
    }
}

impl Value<i16> for Endurance {
    fn value(&self) -> i16 {
        self.0
    }
}

impl From<i16> for Endurance {
    fn from(value: i16) -> Self {
        Self(value)
    }
}

#[derive(Debug, PartialEq, Eq, PartialOrd, Ord, Clone, Default)]

pub struct Intelligence(i16);

impl Intelligence {
    pub fn new(value: i16) -> Self {
        Self(value)
    }
}

impl Value<i16> for Intelligence {
    fn value(&self) -> i16 {
        self.0
    }
}

impl From<i16> for Intelligence {
    fn from(value: i16) -> Self {
        Self(value)
    }
}
