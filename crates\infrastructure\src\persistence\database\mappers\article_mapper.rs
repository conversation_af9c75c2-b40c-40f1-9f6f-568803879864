use reforged_domain::models::article::entity::Article;
use reforged_domain::models::article::value_object::{
    ArticleContent, ArticleDate, ArticleImage, ArticlePostId, ArticleSubject, ArticleTags,
};
use reforged_domain::models::user::value_object::UserId;

#[derive(Debug)]
pub struct ArticleDbModelMapper(super::super::models::articles::Model);

impl ArticleDbModelMapper {
    pub fn new(article: super::super::models::articles::Model) -> Self {
        Self(article)
    }
}

impl From<ArticleDbModelMapper> for Article {
    fn from(value: ArticleDbModelMapper) -> Self {
        let article_model = value.0;

        let post_id = ArticlePostId::new(article_model.id);
        let author = UserId::new(article_model.author_id);
        let subject = ArticleSubject::from(article_model.subject);
        let content = ArticleContent::from(article_model.content);
        let image = ArticleImage::from(article_model.image.unwrap_or_default());
        let tags = ArticleTags::from(article_model.tags.unwrap_or_default());
        let date = ArticleDate::from(article_model.date.and_utc());

        Article::builder()
            .post_id(post_id)
            .author(author)
            .subject(subject)
            .content(content)
            .image(image)
            .tags(tags)
            .date(date)
            .build()
    }
}
