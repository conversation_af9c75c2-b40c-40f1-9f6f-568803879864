use async_trait::async_trait;
use mockall::automock;

use crate::error::RepositoryError;
use crate::models::cms_article::entity::CMSArticle;
use crate::models::cms_article::value_object::CMSArticleId;

#[automock]
#[async_trait]
pub trait CMSArticleRepository: Send + Sync {
    async fn save(&self, entity: &CMSArticle) -> Result<(), RepositoryError>;
    async fn update(&self, entity: &CMSArticle) -> Result<(), RepositoryError>;
    async fn delete(&self, id: &CMSArticleId) -> Result<(), RepositoryError>;
}

#[automock]
#[async_trait]
pub trait CMSArticleReadRepository: Send + Sync {
    async fn find_by_id(&self, id: &CMSArticleId) -> Result<Option<CMSArticle>, RepositoryError>;
    async fn find_all(&self) -> Result<Vec<CMSArticle>, RepositoryError>;
}
