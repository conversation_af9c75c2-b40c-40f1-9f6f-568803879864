use reforged_shared::{UuidId, Value};

use super::entity::BookQuest;

pub type BookQuestId = UuidId<BookQuest>;

#[derive(Debug, PartialEq, Eq, PartialOrd, Ord, Clone, Default)]

pub struct BookQuestName(String);

impl BookQuestName {
    pub fn new(name: impl Into<String>) -> Self {
        Self(name.into())
    }
}

impl Value<String> for BookQuestName {
    fn value(&self) -> String {
        self.0.clone()
    }
}

impl From<String> for BookQuestName {
    fn from(value: String) -> Self {
        Self(value)
    }
}

impl From<&str> for BookQuestName {
    fn from(value: &str) -> Self {
        Self(value.to_string())
    }
}

#[derive(Debug, PartialEq, Eq, PartialOrd, Ord, Clone, Default)]

pub struct BookQuestField(String);

impl BookQuestField {
    pub fn new(field: impl Into<String>) -> Self {
        Self(field.into())
    }
}

impl Value<String> for BookQuestField {
    fn value(&self) -> String {
        self.0.clone()
    }
}

impl From<String> for BookQuestField {
    fn from(value: String) -> Self {
        Self(value)
    }
}

impl From<&str> for BookQuestField {
    fn from(value: &str) -> Self {
        Self(value.to_string())
    }
}

#[derive(Debug, PartialEq, Eq, PartialOrd, Ord, Clone, Default)]

pub struct BookQuestLock(String);

impl BookQuestLock {
    pub fn new(lock: impl Into<String>) -> Self {
        Self(lock.into())
    }
}

impl Value<String> for BookQuestLock {
    fn value(&self) -> String {
        self.0.clone()
    }
}

impl From<String> for BookQuestLock {
    fn from(value: String) -> Self {
        Self(value)
    }
}

impl From<&str> for BookQuestLock {
    fn from(value: &str) -> Self {
        Self(value.to_string())
    }
}

#[derive(Debug, PartialEq, Eq, PartialOrd, Ord, Clone, Default)]

pub struct BookQuestMap(String);

impl BookQuestMap {
    pub fn new(map: impl Into<String>) -> Self {
        Self(map.into())
    }
}

impl Value<String> for BookQuestMap {
    fn value(&self) -> String {
        self.0.clone()
    }
}

impl From<String> for BookQuestMap {
    fn from(value: String) -> Self {
        Self(value)
    }
}

impl From<&str> for BookQuestMap {
    fn from(value: &str) -> Self {
        Self(value.to_string())
    }
}

#[derive(Debug, PartialEq, Eq, PartialOrd, Ord, Clone, Default)]

pub struct BookQuestType(String);

impl BookQuestType {
    pub fn new(quest_type: impl Into<String>) -> Self {
        Self(quest_type.into())
    }
}

impl Value<String> for BookQuestType {
    fn value(&self) -> String {
        self.0.clone()
    }
}

impl From<String> for BookQuestType {
    fn from(value: String) -> Self {
        Self(value)
    }
}

impl From<&str> for BookQuestType {
    fn from(value: &str) -> Self {
        Self(value.to_string())
    }
}

#[derive(Debug, PartialEq, Eq, PartialOrd, Ord, Clone, Default)]

pub struct BookQuestHide(i32);

impl BookQuestHide {
    pub fn new(hide: i32) -> Self {
        Self(hide)
    }
}

impl Value<i32> for BookQuestHide {
    fn value(&self) -> i32 {
        self.0
    }
}

impl From<i32> for BookQuestHide {
    fn from(value: i32) -> Self {
        Self(value)
    }
}

#[derive(Debug, PartialEq, Eq, PartialOrd, Ord, Clone, Default)]

pub struct BookQuestIndex(i32);

impl BookQuestIndex {
    pub fn new(index: i32) -> Self {
        Self(index)
    }
}

impl Value<i32> for BookQuestIndex {
    fn value(&self) -> i32 {
        self.0
    }
}

impl From<i32> for BookQuestIndex {
    fn from(value: i32) -> Self {
        Self(value)
    }
}

#[derive(Debug, PartialEq, Eq, PartialOrd, Ord, Clone, Default)]

pub struct BookQuestValue(i32);

impl BookQuestValue {
    pub fn new(value: i32) -> Self {
        Self(value)
    }
}

impl Value<i32> for BookQuestValue {
    fn value(&self) -> i32 {
        self.0
    }
}

impl From<i32> for BookQuestValue {
    fn from(value: i32) -> Self {
        Self(value)
    }
}
