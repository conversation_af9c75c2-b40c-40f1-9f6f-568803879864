use reforged_domain::error::RepositoryError;
use sea_orm::{DbErr, RuntimeErr, SqlxError};

pub struct SeaORMErr(pub DbErr);

impl From<DbErr> for SeaORMErr {
    fn from(err: DbErr) -> Self {
        SeaORMErr(err)
    }
}

impl From<SeaORMErr> for RepositoryError {
    fn from(value: SeaORMErr) -> Self {
        match value.0 {
            DbErr::RecordNotFound(record) => RepositoryError::NotFound(record),
            DbErr::Query(err) if err.to_string().contains("duplicate") => {
                RepositoryError::Conflict(err.to_string())
            }
            DbErr::Exec(err) => match err {
                RuntimeErr::SqlxError(sqlx_error) => match sqlx_error {
                    SqlxError::Database(db_err) => {
                        let code = db_err.code().unwrap();

                        match code.as_str() {
                            "23505" => {
                                return RepositoryError::Conflict(db_err.message().to_string());
                            }
                            _ => RepositoryError::Unknown,
                        }
                    }
                    _ => RepositoryError::Unknown,
                },
                _ => RepositoryError::InvalidData(err.to_string()),
            },
            DbErr::Conn(_) => RepositoryError::ConnectionError,
            err => RepositoryError::DatabaseError(err.to_string()),
        }
    }
}
