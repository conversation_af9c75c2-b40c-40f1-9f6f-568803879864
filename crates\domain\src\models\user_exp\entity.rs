use getset::{<PERSON><PERSON>, <PERSON><PERSON>};

use crate::models::user::value_object::UserId;

use super::value_object::{Experience, Level, UserExperienceId};

#[allow(unused)]
#[derive(<PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>ult, bon::<PERSON><PERSON><PERSON>, <PERSON><PERSON>, Set<PERSON>)]
#[getset(get = "pub", set = "pub")]
pub struct UserExperience {
    id: UserExperienceId,
    user_id: UserId,
    level: Level,
    experience: Experience,
}

impl UserExperience {
    pub fn new(
        id: UserExperienceId,
        user_id: UserId,
        level: Level,
        experience: Experience,
    ) -> Self {
        Self {
            id,
            user_id,
            level,
            experience,
        }
    }
}

#[cfg(test)]
mod tests {
    use reforged_shared::{IdTrait, Value};

    use super::*;

    #[test]
    fn test_user_experience_new() {
        let id = UserExperienceId::new(uuid::Uuid::now_v7());
        let user_id = UserId::new(uuid::Uuid::now_v7());
        let level = Level::new(10);
        let experience = Experience::new(5000);

        let user_experience = UserExperience::new(
            id.clone(),
            user_id.clone(),
            level.clone(),
            experience.clone(),
        );

        assert_eq!(user_experience.id().get_id(), id.get_id());
        assert_eq!(user_experience.user_id.get_id(), user_id.get_id());
        assert_eq!(user_experience.level.value(), level.value());
        assert_eq!(user_experience.experience.value(), experience.value());
    }

    #[test]
    fn test_user_experience_builder() {
        let id = UserExperienceId::new(uuid::Uuid::now_v7());
        let user_id = UserId::new(uuid::Uuid::now_v7());
        let level = Level::new(10);
        let experience = Experience::new(5000);

        let user_experience = UserExperience::builder()
            .id(id.clone())
            .user_id(user_id.clone())
            .level(level.clone())
            .experience(experience.clone())
            .build();

        assert_eq!(user_experience.id().get_id(), id.get_id());
        assert_eq!(user_experience.user_id.get_id(), user_id.get_id());
        assert_eq!(user_experience.level.value(), level.value());
        assert_eq!(user_experience.experience.value(), experience.value());
    }

    #[test]
    fn test_getters_and_setters() {
        let mut user_experience = UserExperience::default();

        let id = UserExperienceId::new(uuid::Uuid::now_v7());
        let user_id = UserId::new(uuid::Uuid::now_v7());
        let level = Level::new(10);
        let experience = Experience::new(5000);

        user_experience.set_id(id.clone());
        user_experience.set_user_id(user_id.clone());
        user_experience.set_level(level.clone());
        user_experience.set_experience(experience.clone());

        assert_eq!(user_experience.id().get_id(), id.get_id());
        assert_eq!(user_experience.user_id().get_id(), user_id.get_id());
        assert_eq!(user_experience.level().value(), level.value());
        assert_eq!(user_experience.experience().value(), experience.value());
    }
}
