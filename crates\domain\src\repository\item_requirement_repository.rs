use async_trait::async_trait;
use mockall::automock;

use crate::error::RepositoryError;
use crate::models::item_requirement::entity::ItemRequirement;
use crate::models::item_requirement::value_object::ItemRequirementId;

#[automock]
#[async_trait]
pub trait ItemRequirementRepository: Send + Sync {
    async fn save(&self, entity: &ItemRequirement) -> Result<(), RepositoryError>;
    async fn update(&self, entity: &ItemRequirement) -> Result<(), RepositoryError>;
    async fn delete(&self, id: &ItemRequirementId) -> Result<(), RepositoryError>;
}

#[automock]
#[async_trait]
pub trait ItemRequirementReadRepository: Send + Sync {
    async fn find_by_id(
        &self,
        id: &ItemRequirementId,
    ) -> Result<Option<ItemRequirement>, RepositoryError>;
    async fn find_all(&self) -> Result<Vec<ItemRequirement>, RepositoryError>;
}
