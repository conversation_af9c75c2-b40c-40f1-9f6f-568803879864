use crate::models::{
    enhancement::value_object::EnhancementId, item::value_object::ItemId,
    user::value_object::UserId,
};
use getset::{Get<PERSON>, Setters};

use super::value_object::*;

#[derive(<PERSON>bu<PERSON>, <PERSON><PERSON>, De<PERSON>ult, bon::<PERSON><PERSON><PERSON>, Get<PERSON>, Setters)]
#[getset(get = "pub", set = "pub")]
pub struct UserItem {
    id: UserItemId,
    user_id: UserId,
    item_id: ItemId,
    enh_id: EnhancementId,
    equipped: UserItemEquipped,
    quantity: UserItemQuantity,
    bank: UserItemBank,
    bind: UserItemBind,
    date_purchased: UserItemDate,
}

impl UserItem {
    pub fn new(
        id: UserItemId,
        user_id: UserId,
        item_id: ItemId,
        enh_id: EnhancementId,
        quantity: UserItemQuantity,
        date_purchased: UserItemDate,
        bind: UserItemBind,
        bank: UserItemBank,
        equipped: UserItemEquipped,
    ) -> Self {
        Self {
            id,
            user_id,
            item_id,
            enh_id,
            quantity,
            date_purchased,
            bind,
            bank,
            equipped,
        }
    }
}
