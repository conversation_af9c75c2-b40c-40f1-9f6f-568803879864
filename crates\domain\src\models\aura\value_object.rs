use reforged_shared::{UuidId, Value};

use super::entity::Aura;

pub type AuraId = UuidId<Aura>;

#[derive(Debug, PartialEq, Eq, PartialOrd, Ord, <PERSON>lone, <PERSON><PERSON>ult)]

pub struct AuraName(String);

impl AuraName {
    pub fn new(name: impl Into<String>) -> Self {
        Self(name.into())
    }
}

impl Value<String> for AuraName {
    fn value(&self) -> String {
        self.0.clone()
    }
}

impl From<String> for AuraName {
    fn from(value: String) -> Self {
        Self(value)
    }
}

impl From<&str> for AuraName {
    fn from(value: &str) -> Self {
        Self(value.to_string())
    }
}

#[derive(Debug, PartialEq, Eq, PartialOrd, Ord, <PERSON><PERSON>, <PERSON><PERSON>ult)]

pub struct AuraDuration(i16);

impl AuraDuration {
    pub fn new(duration: i16) -> Self {
        Self(duration)
    }
}

impl Value<i16> for AuraDuration {
    fn value(&self) -> i16 {
        self.0
    }
}

impl From<i16> for AuraDuration {
    fn from(value: i16) -> Self {
        Self(value)
    }
}

#[derive(Debu<PERSON>, PartialEq, Eq, PartialOrd, Or<PERSON>, <PERSON><PERSON>, Default)]

pub struct AuraCategory(String);

impl AuraCategory {
    pub fn new(category: impl Into<String>) -> Self {
        Self(category.into())
    }
}

impl Value<String> for AuraCategory {
    fn value(&self) -> String {
        self.0.clone()
    }
}

impl From<String> for AuraCategory {
    fn from(value: String) -> Self {
        Self(value)
    }
}

impl From<&str> for AuraCategory {
    fn from(value: &str) -> Self {
        Self(value.to_string())
    }
}

#[derive(Debug, PartialEq, PartialOrd, Clone, Default)]

pub struct AuraDamageIncrease(f64);

impl AuraDamageIncrease {
    pub fn new(damage_increase: f64) -> Self {
        Self(damage_increase)
    }
}

impl Value<f64> for AuraDamageIncrease {
    fn value(&self) -> f64 {
        self.0
    }
}

impl From<f64> for AuraDamageIncrease {
    fn from(value: f64) -> Self {
        Self(value)
    }
}

#[derive(Debug, PartialEq, PartialOrd, Clone, Default)]

pub struct AuraDamageTakenDecrease(f64);

impl AuraDamageTakenDecrease {
    pub fn new(damage_taken_decrease: f64) -> Self {
        Self(damage_taken_decrease)
    }
}

impl Value<f64> for AuraDamageTakenDecrease {
    fn value(&self) -> f64 {
        self.0
    }
}

impl From<f64> for AuraDamageTakenDecrease {
    fn from(value: f64) -> Self {
        Self(value)
    }
}

#[derive(Debug, PartialEq, PartialOrd, Clone, Default)]

pub struct AuraChance(f64);

impl AuraChance {
    pub fn new(chance: f64) -> Self {
        Self(chance)
    }
}

impl Value<f64> for AuraChance {
    fn value(&self) -> f64 {
        self.0
    }
}

impl From<f64> for AuraChance {
    fn from(value: f64) -> Self {
        Self(value)
    }
}

#[derive(Debug, PartialEq, Eq, PartialOrd, Ord, Clone, Default)]

pub struct AuraSelfTarget(bool);

impl AuraSelfTarget {
    pub fn new(self_target: bool) -> Self {
        Self(self_target)
    }
}

impl Value<bool> for AuraSelfTarget {
    fn value(&self) -> bool {
        self.0
    }
}

impl From<bool> for AuraSelfTarget {
    fn from(value: bool) -> Self {
        Self(value)
    }
}
