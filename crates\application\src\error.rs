use std::fmt::Display;

use reforged_domain::error::RepositoryError;

use crate::services::captcha_service::CaptchaError;

pub enum UserServiceError {
    UserNotFound,
    UnknownError,
}

impl From<RepositoryError> for UserServiceError {
    fn from(err: RepositoryError) -> Self {
        match err {
            RepositoryError::NotFound(_) => UserServiceError::UserNotFound,
            _ => UserServiceError::UnknownError,
        }
    }
}

#[derive(Debug)]
pub enum ApplicationError {
    UserNotFound,
    EntityNotFound(String),
    EntityAlreadyExists(String),
    InvalidVariant(String),
    InvalidData(String),
    UserALreadyExists,
    Conflict(String),
    InvalidGender,
    PasswordNotChanged,
    InvalidCredentials,
    CreationFailed,
    UnknownError,
    InvalidCaptcha,
    TokenExpired,
    TokenInvalid,
    TokenGenerationFailed,
    JsonSerializationError,
    JsonDeserializationError,
    Unauthorized,
    Forbidden,
    InternalError(String),
}

impl Display for ApplicationError {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        match self {
            ApplicationError::EntityNotFound(entity_name) => {
                write!(f, "Entity not found {}", entity_name)
            }
            ApplicationError::EntityAlreadyExists(entity_name) => {
                write!(f, "Entity already exists: {}", entity_name)
            }
            ApplicationError::InvalidVariant(variant) => {
                write!(f, "Invalid variant: {}", variant)
            }
            ApplicationError::InvalidData(data) => {
                write!(f, "Invalid data: {}", data)
            }
            ApplicationError::UserNotFound => write!(f, "User not found"),
            ApplicationError::UserALreadyExists => write!(f, "User already exists"),
            ApplicationError::InvalidGender => write!(f, "Invalid gender"),
            ApplicationError::PasswordNotChanged => {
                write!(f, "New password is the same as the old one")
            }
            ApplicationError::InvalidCredentials => write!(f, "Invalid credentials"),
            ApplicationError::CreationFailed => write!(f, "User creation failed"),
            ApplicationError::UnknownError => write!(f, "Unknown error"),
            ApplicationError::InvalidCaptcha => write!(f, "Invalid captcha"),
            ApplicationError::TokenExpired => write!(f, "Token expired"),
            ApplicationError::TokenInvalid => write!(f, "Token invalid"),
            ApplicationError::TokenGenerationFailed => write!(f, "Token generation failed"),
            ApplicationError::JsonSerializationError => write!(f, "Json serialization error"),
            ApplicationError::JsonDeserializationError => write!(f, "Json deserialization error"),
            ApplicationError::Unauthorized => write!(f, "Unauthorized"),
            ApplicationError::Forbidden => write!(f, "Forbidden"),
            ApplicationError::InternalError(e) => write!(f, "{e}"),
            ApplicationError::Conflict(err) => write!(f, "Conflict: {}", err),
        }
    }
}

impl From<RepositoryError> for ApplicationError {
    fn from(err: RepositoryError) -> Self {
        match err {
            RepositoryError::NotFound(entity) => ApplicationError::EntityNotFound(entity),
            RepositoryError::ResourceAlreadyExists(_id) => ApplicationError::UserALreadyExists,
            RepositoryError::Conflict(err) => ApplicationError::Conflict(err),
            RepositoryError::ConnectionError => {
                ApplicationError::InternalError("Connection error".to_string())
            }
            RepositoryError::InvalidData(err) => ApplicationError::InvalidData(err),
            RepositoryError::Unknown => {
                ApplicationError::InternalError("Unknown error".to_string())
            }
            RepositoryError::DatabaseError(err) => ApplicationError::InternalError(err),
        }
    }
}

impl From<CaptchaError> for ApplicationError {
    fn from(err: CaptchaError) -> Self {
        match err {
            CaptchaError::InvalidCaptcha => ApplicationError::InvalidCaptcha,
            _ => ApplicationError::UnknownError,
        }
    }
}
