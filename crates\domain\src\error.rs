#[derive(Debug, thiserror::Error)]
pub enum RepositoryError {
    #[error("Entity not found, {0}")]
    NotFound(String),
    #[error("Database conflict: {0}")]
    Conflict(String),
    #[error("Invalid data format: {0}")]
    InvalidData(String),
    #[error("Resource with an ID of {0} already exists")]
    ResourceAlreadyExists(i32),
    #[error("Database connection error")]
    ConnectionError,
    #[error("Database error: {0}")]
    DatabaseError(String),
    #[error("Unknown repository error")]
    Unknown,
}

#[derive(Debug, thiserror::Error)]
pub enum UserValidationError {}

#[derive(Debug, thiserror::Error)]
pub enum UserError {
    #[error("User already exist")]
    UserALreadyExists,
}
