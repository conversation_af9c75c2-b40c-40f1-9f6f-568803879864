use super::value_object::{
    CpBoostExpire, ExpBoostExpire, GoldBoostExpire, RepBoostExpire, UpgradeDays, UpgradeExpire,
    Upgraded, UserBoostId,
};
use crate::models::user::value_object::UserId;
use getset::{Get<PERSON>, Set<PERSON>};

#[derive(Debug, <PERSON><PERSON>, De<PERSON>ult, bon::<PERSON><PERSON><PERSON>, <PERSON><PERSON>, Set<PERSON>)]
#[getset(get = "pub", set = "pub")]
pub struct UserBoost {
    id: UserBoostId,
    user_id: UserId,
    cp_boost_expire: CpBoostExpire,
    rep_boost_expire: RepBoostExpire,
    gold_boost_expire: GoldBoostExpire,
    exp_boost_expire: ExpBoostExpire,
    upgrade_expire: UpgradeExpire,
    upgrade_days: UpgradeDays,
    upgraded: Upgraded,
}

impl UserBoost {
    pub fn new(
        id: UserBoostId,
        user_id: UserId,
        cp_boost_expire: CpBoostExpire,
        rep_boost_expire: RepBoostExpire,
        gold_boost_expire: GoldBoostExpire,
        exp_boost_expire: ExpBoostExpire,
        upgrade_expire: UpgradeExpire,
        upgrade_days: UpgradeDays,
        upgraded: Upgraded,
    ) -> Self {
        Self {
            id,
            user_id,
            cp_boost_expire,
            rep_boost_expire,
            gold_boost_expire,
            exp_boost_expire,
            upgrade_expire,
            upgrade_days,
            upgraded,
        }
    }
}

#[cfg(test)]
mod tests {
    use chrono::Utc;
    use reforged_shared::{IdTrait, Value};

    use super::*;

    #[test]
    fn test_user_boost_new() {
        let id = UserBoostId::new(uuid::Uuid::now_v7());
        let user_id = UserId::new(uuid::Uuid::now_v7());
        let cp_boost_expire = CpBoostExpire::new(Utc::now().date_naive());
        let rep_boost_expire = RepBoostExpire::new(Utc::now().date_naive());
        let gold_boost_expire = GoldBoostExpire::new(Utc::now().date_naive());
        let exp_boost_expire = ExpBoostExpire::new(Utc::now().date_naive());
        let upgrade_expire = UpgradeExpire::new(Utc::now().date_naive());
        let upgrade_days = UpgradeDays::new(30);
        let upgraded = Upgraded::new(true);

        let user_boost = UserBoost::new(
            id.clone(),
            user_id.clone(),
            cp_boost_expire.clone(),
            rep_boost_expire.clone(),
            gold_boost_expire.clone(),
            exp_boost_expire.clone(),
            upgrade_expire.clone(),
            upgrade_days.clone(),
            upgraded.clone(),
        );

        assert_eq!(user_boost.id().get_id(), id.get_id());
        assert_eq!(user_boost.user_id.get_id(), user_id.get_id());
        assert_eq!(user_boost.cp_boost_expire.value(), cp_boost_expire.value());
        assert_eq!(
            user_boost.rep_boost_expire.value(),
            rep_boost_expire.value()
        );
        assert_eq!(
            user_boost.gold_boost_expire.value(),
            gold_boost_expire.value()
        );
        assert_eq!(
            user_boost.exp_boost_expire.value(),
            exp_boost_expire.value()
        );
        assert_eq!(user_boost.upgrade_expire.value(), upgrade_expire.value());
        assert_eq!(user_boost.upgrade_days.value(), upgrade_days.value());
        assert_eq!(user_boost.upgraded.value(), upgraded.value());
    }

    #[test]
    fn test_user_boost_builder() {
        let id = UserBoostId::new(uuid::Uuid::now_v7());
        let user_id = UserId::new(uuid::Uuid::now_v7());
        let cp_boost_expire = CpBoostExpire::new(Utc::now().date_naive());
        let rep_boost_expire = RepBoostExpire::new(Utc::now().date_naive());
        let gold_boost_expire = GoldBoostExpire::new(Utc::now().date_naive());
        let exp_boost_expire = ExpBoostExpire::new(Utc::now().date_naive());
        let upgrade_expire = UpgradeExpire::new(Utc::now().date_naive());
        let upgrade_days = UpgradeDays::new(30);
        let upgraded = Upgraded::new(true);

        let user_boost = UserBoost::builder()
            .id(id.clone())
            .user_id(user_id.clone())
            .cp_boost_expire(cp_boost_expire.clone())
            .rep_boost_expire(rep_boost_expire.clone())
            .gold_boost_expire(gold_boost_expire.clone())
            .exp_boost_expire(exp_boost_expire.clone())
            .upgrade_expire(upgrade_expire.clone())
            .upgrade_days(upgrade_days.clone())
            .upgraded(upgraded.clone())
            .build();

        assert_eq!(user_boost.id().get_id(), id.get_id());
        assert_eq!(user_boost.user_id.get_id(), user_id.get_id());
        assert_eq!(user_boost.cp_boost_expire.value(), cp_boost_expire.value());
        assert_eq!(
            user_boost.rep_boost_expire.value(),
            rep_boost_expire.value()
        );
        assert_eq!(
            user_boost.gold_boost_expire.value(),
            gold_boost_expire.value()
        );
        assert_eq!(
            user_boost.exp_boost_expire.value(),
            exp_boost_expire.value()
        );
        assert_eq!(user_boost.upgrade_expire.value(), upgrade_expire.value());
        assert_eq!(user_boost.upgrade_days.value(), upgrade_days.value());
        assert_eq!(user_boost.upgraded.value(), upgraded.value());
    }

    #[test]
    fn test_getters_and_setters() {
        let mut user_boost = UserBoost::default();

        let id = UserBoostId::new(uuid::Uuid::now_v7());
        let user_id = UserId::new(uuid::Uuid::now_v7());
        let cp_boost_expire = CpBoostExpire::new(Utc::now().date_naive());
        let rep_boost_expire = RepBoostExpire::new(Utc::now().date_naive());
        let gold_boost_expire = GoldBoostExpire::new(Utc::now().date_naive());
        let exp_boost_expire = ExpBoostExpire::new(Utc::now().date_naive());
        let upgrade_expire = UpgradeExpire::new(Utc::now().date_naive());
        let upgrade_days = UpgradeDays::new(30);
        let upgraded = Upgraded::new(true);

        user_boost.set_id(id.clone());
        user_boost.set_user_id(user_id.clone());
        user_boost.set_cp_boost_expire(cp_boost_expire.clone());
        user_boost.set_rep_boost_expire(rep_boost_expire.clone());
        user_boost.set_gold_boost_expire(gold_boost_expire.clone());
        user_boost.set_exp_boost_expire(exp_boost_expire.clone());
        user_boost.set_upgrade_expire(upgrade_expire.clone());
        user_boost.set_upgrade_days(upgrade_days.clone());
        user_boost.set_upgraded(upgraded.clone());

        assert_eq!(user_boost.id().get_id(), id.get_id());
        assert_eq!(user_boost.user_id().get_id(), user_id.get_id());
        assert_eq!(
            user_boost.cp_boost_expire().value(),
            cp_boost_expire.value()
        );
        assert_eq!(
            user_boost.rep_boost_expire().value(),
            rep_boost_expire.value()
        );
        assert_eq!(
            user_boost.gold_boost_expire().value(),
            gold_boost_expire.value()
        );
        assert_eq!(
            user_boost.exp_boost_expire().value(),
            exp_boost_expire.value()
        );
        assert_eq!(user_boost.upgrade_expire().value(), upgrade_expire.value());
        assert_eq!(user_boost.upgrade_days().value(), upgrade_days.value());
        assert_eq!(user_boost.upgraded().value(), upgraded.value());
    }
}
