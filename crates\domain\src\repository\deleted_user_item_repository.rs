use async_trait::async_trait;
use mockall::automock;

use crate::error::RepositoryError;
use crate::models::deleted_user_item::entity::DeletedUserItem;
use crate::models::deleted_user_item::value_object::DeletedUserItemId;

#[automock]
#[async_trait]
pub trait DeletedUserItemRepository: Send + Sync {
    async fn save(&self, entity: &DeletedUserItem) -> Result<(), RepositoryError>;
    async fn update(&self, entity: &DeletedUserItem) -> Result<(), RepositoryError>;
    async fn delete(&self, id: &DeletedUserItemId) -> Result<(), RepositoryError>;
}

#[automock]
#[async_trait]
pub trait DeletedUserItemReadRepository: Send + Sync {
    async fn find_by_id(
        &self,
        id: &DeletedUserItemId,
    ) -> Result<Option<DeletedUserItem>, RepositoryError>;
    async fn find_all(&self) -> Result<Vec<DeletedUserItem>, RepositoryError>;
}
