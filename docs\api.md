# API Documentation
This is the API documentation for the project.

## TODO(s):
The following TODO(s) are required to be completed before creating the endpoints with relations to other tables.

***class_categories***
- [x] Repository
- [x] Usecase
- [x] Endpoint

***skills***
- [ ] Repository
- [ ] Usecase
- [ ] Endpoint

***item_rarities***
- [ ] Repository
- [ ] Usecase
- [ ] Endpoint

***auras***
- [ ] Repository
- [ ] Usecase
- [ ] Endpoint

***enhancement_patterns***
- [ ] Repository
- [ ] Usecase
- [ ] Endpoint

***monsters***
- [ ] Repository
- [ ] Usecase
- [ ] Endpoint

***maps***
- [ ] Repository
- [ ] Usecase
- [ ] Endpoint

***roles***
- [ ] Repository
- [ ] Usecase
- [ ] Endpoint

***titles***
- [ ] Repository
- [ ] Usecase
- [ ] Endpoint

***achievements***
- [ ] Repository
- [ ] Usecase
- [ ] Endpoint

***factions***
- [ ] Repository
- [ ] Usecase
- [ ] Endpoint

***stores***
- [ ] Repository
- [ ] Usecase
- [ ] Endpoint

***books***
- [ ] Repository
- [ ] Usecase
- [ ] Endpoint

***hairs***
- [ ] Repository
- [ ] Usecase
- [ ] Endpoint

***hair_shops***
- [ ] Repository
- [ ] Usecase
- [ ] Endpoint

***wars***
- [ ] Repository
- [ ] Usecase
- [ ] Endpoint

***admin_uploads***
- [ ] Repository
- [ ] Usecase
- [ ] Endpoint

***servers***
- [ ] Repository
- [ ] Usecase
- [ ] Endpoint

***discord_commands***
- [ ] Repository
- [ ] Usecase
- [ ] Endpoint

***profanity_filter_settings***
- [ ] Repository
- [ ] Usecase
- [ ] Endpoint

***game_settings***
- [ ] Repository
- [ ] Usecase
- [ ] Endpoint

***game_rates_settings***
- [ ] Repository
- [ ] Usecase
- [ ] Endpoint

***cms_articles***
- [ ] Repository
- [ ] Usecase
- [ ] Endpoint