use crate::models::item::value_object::ItemId;
use crate::models::item_bundle::value_object::*;
use getset::{<PERSON><PERSON>, Set<PERSON>};

#[derive(<PERSON>bu<PERSON>, <PERSON><PERSON>, <PERSON><PERSON>ult, bon::<PERSON><PERSON><PERSON>, <PERSON><PERSON>, Set<PERSON>)]
#[getset(get = "pub", set = "pub")]
pub struct ItemBundle {
    pub id: ItemBundleId,
    pub item_id: ItemId,
    pub reward_id: ItemId,
    pub quantity: Option<ItemBundleQuantity>,
}

impl ItemBundle {
    pub fn new(
        id: ItemBundleId,
        item_id: ItemId,
        reward_id: ItemId,
        quantity: Option<ItemBundleQuantity>,
    ) -> Self {
        Self {
            id,
            item_id,
            reward_id,
            quantity,
        }
    }
}

#[cfg(test)]
mod tests {
    use reforged_shared::IdTrait;

    use super::*;

    #[test]
    fn test_item_bundle_new() {
        let id = ItemBundleId::new(uuid::Uuid::now_v7());
        let item_id = ItemId::new(uuid::Uuid::now_v7());
        let reward_id = ItemId::new(uuid::Uuid::now_v7());
        let quantity = Some(ItemBundleQuantity::new(5));

        let bundle = ItemBundle::new(
            id.clone(),
            item_id.clone(),
            reward_id.clone(),
            quantity.clone(),
        );

        assert_eq!(bundle.id.get_id(), id.get_id());
        assert_eq!(bundle.item_id.get_id(), item_id.get_id());
        assert_eq!(bundle.reward_id.get_id(), reward_id.get_id());
        assert_eq!(bundle.quantity, quantity);
    }

    #[test]
    fn test_item_bundle_builder() {
        let id = ItemBundleId::new(uuid::Uuid::now_v7());
        let item_id = ItemId::new(uuid::Uuid::now_v7());
        let reward_id = ItemId::new(uuid::Uuid::now_v7());
        let quantity = ItemBundleQuantity::new(5);

        let bundle = ItemBundle::builder()
            .id(id.clone())
            .item_id(item_id.clone())
            .reward_id(reward_id.clone())
            .quantity(quantity.clone())
            .build();

        assert_eq!(bundle.id.get_id(), id.get_id());
        assert_eq!(bundle.item_id.get_id(), item_id.get_id());
        assert_eq!(bundle.reward_id.get_id(), reward_id.get_id());
        assert_eq!(bundle.quantity, Some(quantity));
    }

    #[test]
    fn test_getters_and_setters() {
        let mut bundle = ItemBundle::default();

        let id = ItemBundleId::new(uuid::Uuid::now_v7());
        let item_id = ItemId::new(uuid::Uuid::now_v7());
        let reward_id = ItemId::new(uuid::Uuid::now_v7());
        let quantity = Some(ItemBundleQuantity::new(5));

        bundle.set_id(id.clone());
        bundle.set_item_id(item_id.clone());
        bundle.set_reward_id(reward_id.clone());
        bundle.set_quantity(quantity.clone());

        assert_eq!(bundle.id().get_id(), id.get_id());
        assert_eq!(bundle.item_id().get_id(), item_id.get_id());
        assert_eq!(bundle.reward_id().get_id(), reward_id.get_id());
        assert_eq!(bundle.quantity(), &quantity);
    }
}
