//! `SeaORM` Entity, @generated by sea-orm-codegen 1.1.0

pub mod prelude;

pub mod achievements;
pub mod admin_uploads;
pub mod articles;
pub mod aura_effects;
pub mod auras;
pub mod book_quests;
pub mod books;
pub mod class_categories;
pub mod class_skills;
pub mod classes;
pub mod cms_articles;
pub mod deleted_user_items;
pub mod discord_commands;
pub mod enhancement_patterns;
pub mod enhancements;
pub mod factions;
pub mod game_rates_settings;
pub mod game_settings;
pub mod guild_hall_buildings;
pub mod guild_hall_connections;
pub mod guild_halls;
pub mod guild_items;
pub mod guild_levels;
pub mod guilds;
pub mod hair_shop_items;
pub mod hair_shops;
pub mod hairs;
pub mod item_bundles;
pub mod item_effects;
pub mod item_lotteries;
pub mod item_rarities;
pub mod item_requirements;
pub mod item_skills;
pub mod items;
pub mod map_cells;
pub mod map_items;
pub mod map_monsters;
pub mod maps;
pub mod monster_bosses;
pub mod monster_drops;
pub mod monster_skills;
pub mod monsters;
pub mod password_reset_tokens;
pub mod profanity_filter_settings;
pub mod profiles;
pub mod quest_locations;
pub mod quest_reqditems;
pub mod quest_requirements;
pub mod quest_rewards;
pub mod quests;
pub mod redeem_codes;
pub mod roles;
pub mod sea_orm_active_enums;
pub mod servers;
pub mod sessions;
pub mod shop_items;
pub mod shop_locations;
pub mod shops;
pub mod skill_auras;
pub mod skills;
pub mod stores;
pub mod titles;
pub mod user_achievements;
pub mod user_boosts;
pub mod user_browsers;
pub mod user_colors;
pub mod user_currencies;
pub mod user_exps;
pub mod user_factions;
pub mod user_friends;
pub mod user_guilds;
pub mod user_items;
pub mod user_livedrops;
pub mod user_logins;
pub mod user_markets;
pub mod user_purchases;
pub mod user_quests;
pub mod user_redeems;
pub mod user_reports;
pub mod user_slots;
pub mod user_stats;
pub mod user_titles;
pub mod users;
pub mod wars;
pub mod wheel_rewards;
