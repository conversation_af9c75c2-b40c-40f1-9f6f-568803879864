use std::sync::Arc;

use getset::Getters;
use reforged_application::{
    eventsource::user::UserStoreManager,
    queries::{
        class_category_query_handlers::ClassCategory<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
        password_reset_token_query_handlers::Password<PERSON><PERSON>tT<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
        user_query_handlers::UserQueryHand<PERSON>,
    },
    services::captcha_service::Captcha,
    traits::{BrokerServicePublisher, TokenService},
};
use reforged_domain::{
    repository::{
        class_category_repository::ClassCategoryRepository,
        password_reset_token_repository::PasswordResetTokenRepository,
        user_repository::UserRepository,
    },
    traits::password_hasher::PasswordHasher,
};

#[derive(Get<PERSON>, <PERSON>lone, bon::Builder)]
#[getset(get = "pub")]
pub struct ApiState {
    user_query_handler: User<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
    class_category_query_handler: ClassCategory<PERSON>uery<PERSON><PERSON><PERSON>,
    password_reset_token_query_handler: PasswordReset<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
    captcha_service: Arc<dyn Captcha>,
    password_hasher: Arc<dyn PasswordHasher>,
    token_service: Arc<dyn TokenService>,
    user_repository: Arc<dyn UserRepository>,
    class_category_repository: Arc<dyn ClassCategoryRepository>,
    password_reset_token_repository: Arc<dyn PasswordResetTokenRepository>,
    allowed_origins: Vec<String>,
    broker_service: Arc<dyn BrokerServicePublisher>,
    user_store_manager: Arc<UserStoreManager>,
}
