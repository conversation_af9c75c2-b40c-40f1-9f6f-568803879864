use crate::models::achievement::value_object::*;

use getset::{<PERSON><PERSON>, <PERSON><PERSON>};

#[derive(<PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, bon::<PERSON><PERSON><PERSON>, <PERSON><PERSON>, Set<PERSON>)]
#[getset(get = "pub", set = "pub")]
pub struct Achievement {
    id: AchievementId,
    name: AchievementName,
    description: AchievementDescription,
    file: AchievementFile,
    category: AchievementCategory,
    show: AchievementShow,
}

impl Achievement {
    pub fn new(
        id: AchievementId,
        name: AchievementName,
        description: AchievementDescription,
        file: AchievementFile,
        category: AchievementCategory,
        show: AchievementShow,
    ) -> Self {
        Self {
            id,
            name,
            description,
            file,
            category,
            show,
        }
    }
}

#[cfg(test)]
mod tests {
    use reforged_shared::{IdTrait, Value};

    use super::*;

    #[test]
    fn test_achievement_new() {
        let id = AchievementId::new(uuid::Uuid::now_v7());
        let name = AchievementName::new("Test Achievement".to_string());
        let description = AchievementDescription::new("Test Description".to_string());
        let file = AchievementFile::new("test.png".to_string());
        let category = AchievementCategory::new("Test Category".to_string());
        let show = AchievementShow::new(true);

        let achievement = Achievement::new(
            id.clone(),
            name.clone(),
            description.clone(),
            file.clone(),
            category.clone(),
            show.clone(),
        );

        assert_eq!(achievement.id().get_id(), id.get_id());
        assert_eq!(achievement.name().value(), name.value());
        assert_eq!(achievement.description().value(), description.value());
        assert_eq!(achievement.file().value(), file.value());
        assert_eq!(achievement.category().value(), category.value());
        assert_eq!(achievement.show().value(), show.value());
    }

    #[test]
    fn test_achievement_builder() {
        let id = AchievementId::new(uuid::Uuid::now_v7());
        let name = AchievementName::new("Test Achievement".to_string());
        let description = AchievementDescription::new("Test Description".to_string());
        let file = AchievementFile::new("test.png".to_string());
        let category = AchievementCategory::new("Test Category".to_string());
        let show = AchievementShow::new(true);

        let achievement = Achievement::builder()
            .id(id.clone())
            .name(name.clone())
            .description(description.clone())
            .file(file.clone())
            .category(category.clone())
            .show(show.clone())
            .build();

        assert_eq!(achievement.id().get_id(), id.get_id());
        assert_eq!(achievement.name().value(), name.value());
        assert_eq!(achievement.description().value(), description.value());
        assert_eq!(achievement.file().value(), file.value());
        assert_eq!(achievement.category().value(), category.value());
        assert_eq!(achievement.show().value(), show.value());
    }

    #[test]
    fn test_getters_and_setters() {
        let mut achievement = Achievement::default();

        let id = AchievementId::new(uuid::Uuid::now_v7());
        let name = AchievementName::new("Test Achievement".to_string());
        let description = AchievementDescription::new("Test Description".to_string());
        let file = AchievementFile::new("test.png".to_string());
        let category = AchievementCategory::new("Test Category".to_string());
        let show = AchievementShow::new(true);

        achievement.set_id(id.clone());
        achievement.set_name(name.clone());
        achievement.set_description(description.clone());
        achievement.set_file(file.clone());
        achievement.set_category(category.clone());
        achievement.set_show(show.clone());

        assert_eq!(achievement.id().get_id(), id.get_id());
        assert_eq!(achievement.name().value(), name.value());
        assert_eq!(achievement.description().value(), description.value());
        assert_eq!(achievement.file().value(), file.value());
        assert_eq!(achievement.category().value(), category.value());
        assert_eq!(achievement.show().value(), show.value());
    }
}
