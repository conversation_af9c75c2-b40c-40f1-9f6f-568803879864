use reforged_domain::models::user_livedrop::entity::UserLivedrop;
use reforged_domain::models::user_livedrop::value_object::UserLivedropId;
use reforged_domain::models::user::value_object::UserId;
use reforged_domain::models::item::value_object::ItemId;
use reforged_domain::models::achievement::value_object::AchievementId;
use reforged_domain::models::title::value_object::TitleId;
use chrono::{DateTime, Utc};

#[derive(Debug)]
pub struct UserLivedropDbModelMapper(super::super::models::user_livedrops::Model);

impl UserLivedropDbModelMapper {
    pub fn new(model: super::super::models::user_livedrops::Model) -> Self {
        Self(model)
    }
}

impl From<UserLivedropDbModelMapper> for UserLivedrop {
    fn from(value: UserLivedropDbModelMapper) -> Self {
        let model = value.0;

        UserLivedrop::builder()
            .id(UserLivedropId::new(model.id))
            .maybe_user_id(model.user_id.map(UserId::new))
            .maybe_item_id(model.item_id.map(ItemId::new))
            .quantity(model.quantity.into())
            .sent(model.sent.into())
            .date(DateTime::<Utc>::from_naive_utc_and_offset(model.date, Utc).into())
            .message(model.message.into())
            .maybe_achievement_id(model.achievement_id.map(AchievementId::new))
            .maybe_title_id(model.title_id.map(TitleId::new))
            .experience(model.experience.into())
            .gold(model.gold.into())
            .coins(model.coins.into())
            .crystal(model.crystal.into())
            .upgrade_days(model.upgrade_days.into())
            .bag_slots(model.bag_slots.into())
            .bank_slots(model.bank_slots.into())
            .house_slots(model.house_slots.into())
            .build()
    }
}
