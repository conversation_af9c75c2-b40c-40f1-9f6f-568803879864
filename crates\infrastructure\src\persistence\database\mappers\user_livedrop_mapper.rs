use reforged_domain::models::user_livedrop::entity::{UserLivedrop, UserLivedropId};
use reforged_domain::models::user::value_object::UserId;

#[derive(Debug)]
pub struct UserLivedropDbModelMapper(super::super::models::user_livedrops::Model);

impl UserLivedropDbModelMapper {
    pub fn new(model: super::super::models::user_livedrops::Model) -> Self {
        Self(model)
    }
}

impl From<UserLivedropDbModelMapper> for UserLivedrop {
    fn from(value: UserLivedropDbModelMapper) -> Self {
        let model = value.0;
        
        UserLivedrop::builder()
            .id(UserLivedropId::new(model.id))
            .user_id(UserId::new(model.user_id))
            .item_id(model.item_id.into())
            .quantity(model.quantity.into())
            .claimed_at(model.claimed_at.into())
            .build()
    }
}
