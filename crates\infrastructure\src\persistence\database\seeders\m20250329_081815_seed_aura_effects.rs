use crate::models;
use sea_orm::{ActiveModelTrait, IntoActiveModel};
use sea_orm_migration::prelude::*;

#[derive(DeriveMigrationName)]
pub struct Migration;

#[async_trait::async_trait]
impl MigrationTrait for Migration {
    async fn up(&self, manager: &SchemaManager) -> Result<(), DbErr> {
        let db = manager.get_connection();

        let aura_effects = include_str!("../json/auras_effects.json");
        let aura_effects: Vec<models::aura_effects::Model> =
            serde_json::from_str(aura_effects).unwrap();

        for model in aura_effects {
            let aura_effect = model.into_active_model();
            aura_effect.insert(db).await?;
        }

        Ok(())
    }

    async fn down(&self, _manager: &SchemaManager) -> Result<(), DbErr> {
        Ok(())
    }
}
