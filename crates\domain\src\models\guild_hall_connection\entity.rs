use super::value_object::{<PERSON>, GuildHallConnectionId, Pad, PadPosition};
use crate::models::guild_hall::value_object::GuildHallId;
use getset::{<PERSON><PERSON>, Set<PERSON>};

#[derive(<PERSON>bug, <PERSON><PERSON>, De<PERSON>ult, bon::<PERSON><PERSON><PERSON>, <PERSON><PERSON>, Setters)]
#[getset(get = "pub", set = "pub")]
pub struct GuildHallConnection {
    id: GuildHallConnectionId,
    hall_id: GuildHallId,
    pad: Pad,
    cell: Cell,
    pad_position: PadPosition,
}

impl GuildHallConnection {
    pub fn new(
        id: GuildHallConnectionId,
        hall_id: GuildHallId,
        pad: Pad,
        cell: Cell,
        pad_position: PadPosition,
    ) -> Self {
        Self {
            id,
            hall_id,
            pad,
            cell,
            pad_position,
        }
    }
}

#[cfg(test)]
mod tests {
    use reforged_shared::{IdTrait, Value};

    use super::*;

    #[test]
    fn test_guild_hall_connection_new() {
        let id = GuildHallConnectionId::new(uuid::Uuid::now_v7());
        let hall_id = GuildHallId::new(uuid::Uuid::now_v7());
        let pad = Pad::new("pad".to_string());
        let cell = Cell::new("cell".to_string());
        let pad_position = PadPosition::new("pad_position".to_string());

        let connection = GuildHallConnection::new(
            id,
            hall_id.clone(),
            pad.clone(),
            cell.clone(),
            pad_position.clone(),
        );

        assert_eq!(connection.hall_id().get_id(), hall_id.get_id());
        assert_eq!(connection.pad().value(), pad.value());
        assert_eq!(connection.cell().value(), cell.value());
        assert_eq!(connection.pad_position().value(), pad_position.value());
    }

    #[test]
    fn test_guild_hall_connection_builder() {
        let id = GuildHallConnectionId::new(uuid::Uuid::now_v7());
        let hall_id = GuildHallId::new(uuid::Uuid::now_v7());
        let pad = Pad::new("pad".to_string());
        let cell = Cell::new("cell".to_string());
        let pad_position = PadPosition::new("pad_position".to_string());

        let connection = GuildHallConnection::builder()
            .id(id.clone())
            .hall_id(hall_id.clone())
            .pad(pad.clone())
            .cell(cell.clone())
            .pad_position(pad_position.clone())
            .build();

        assert_eq!(connection.hall_id().get_id(), hall_id.get_id());
        assert_eq!(connection.pad().value(), pad.value());
        assert_eq!(connection.cell().value(), cell.value());
        assert_eq!(connection.pad_position().value(), pad_position.value());
    }

    #[test]
    fn test_getters_and_setters() {
        let mut connection = GuildHallConnection::default();

        let id = GuildHallConnectionId::new(uuid::Uuid::now_v7());
        let hall_id = GuildHallId::new(uuid::Uuid::now_v7());
        let pad = Pad::new("pad".to_string());
        let cell = Cell::new("cell".to_string());
        let pad_position = PadPosition::new("pad_position".to_string());

        connection.set_id(id.clone());
        connection.set_hall_id(hall_id.clone());
        connection.set_pad(pad.clone());
        connection.set_cell(cell.clone());
        connection.set_pad_position(pad_position.clone());

        assert_eq!(connection.id().get_id(), id.get_id());
        assert_eq!(connection.hall_id().get_id(), hall_id.get_id());
        assert_eq!(connection.pad().value(), pad.value());
        assert_eq!(connection.cell().value(), cell.value());
        assert_eq!(connection.pad_position().value(), pad_position.value());
    }
}
