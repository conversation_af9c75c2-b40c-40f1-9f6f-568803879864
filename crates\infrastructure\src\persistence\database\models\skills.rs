//! `SeaORM` Entity, @generated by sea-orm-codegen 1.1.0

use sea_orm::entity::prelude::*;
use serde::Deserialize;

#[derive(Clone, Debug, PartialEq, DeriveEntityModel, Eq, Deserialize)]
#[sea_orm(table_name = "skills")]
pub struct Model {
    #[sea_orm(primary_key, auto_increment = false)]
    pub id: Uuid,
    pub name: String,
    pub animation: String,
    #[sea_orm(column_type = "Text")]
    pub description: String,
    pub damage: Decimal,
    pub mana: i16,
    pub mana_back: i16,
    pub life_steal: Decimal,
    pub icon: String,
    pub range: i32,
    pub dsrc: String,
    pub reference: String,
    pub target: String,
    pub effects: String,
    pub r#type: String,
    pub strl: String,
    pub cooldown: i32,
    pub hit_targets: i16,
    pub pet: i32,
    pub chance: Option<Decimal>,
    pub show_damage: bool,
}

#[derive(Co<PERSON>, Clone, Debug, EnumIter, DeriveRelation)]
pub enum Relation {
    #[sea_orm(has_many = "super::class_skills::Entity")]
    ClassSkills,
    #[sea_orm(has_many = "super::item_skills::Entity")]
    ItemSkills,
    #[sea_orm(has_many = "super::monster_skills::Entity")]
    MonsterSkills,
    #[sea_orm(has_many = "super::skill_auras::Entity")]
    SkillAuras,
}

impl Related<super::class_skills::Entity> for Entity {
    fn to() -> RelationDef {
        Relation::ClassSkills.def()
    }
}

impl Related<super::item_skills::Entity> for Entity {
    fn to() -> RelationDef {
        Relation::ItemSkills.def()
    }
}

impl Related<super::monster_skills::Entity> for Entity {
    fn to() -> RelationDef {
        Relation::MonsterSkills.def()
    }
}

impl Related<super::skill_auras::Entity> for Entity {
    fn to() -> RelationDef {
        Relation::SkillAuras.def()
    }
}

impl ActiveModelBehavior for ActiveModel {}
