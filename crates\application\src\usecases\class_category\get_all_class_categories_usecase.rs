use crate::{
    error::ApplicationError,
    queries::{
        class_category_queries::ListAllClassCategoriesQuery,
        class_category_query_handlers::{ClassCategory<PERSON>uery<PERSON>and<PERSON>, ClassCategoryResponse},
    },
    traits::QueryHand<PERSON>,
};

pub struct GetAllClassCategoriesUsecase {
    class_category_query_handler: ClassCategoryQueryHandler,
}

impl GetAllClassCategoriesUsecase {
    pub fn new(class_category_query_handler: ClassCategoryQueryHandler) -> Self {
        Self {
            class_category_query_handler,
        }
    }
}

impl GetAllClassCategoriesUsecase {
    pub async fn execute(&self) -> Result<Vec<ClassCategoryResponse>, ApplicationError> {
        let query = ListAllClassCategoriesQuery;
        let categories = self.class_category_query_handler.handle(query).await?;

        Ok(categories)
    }
}
