use async_trait::async_trait;
use mockall::automock;

use crate::error::RepositoryError;
use crate::models::admin_upload::entity::AdminUpload;
use crate::models::admin_upload::value_object::AdminUploadId;

#[automock]
#[async_trait]
pub trait AdminUploadRepository: Send + Sync {
    async fn save(&self, entity: &AdminUpload) -> Result<(), RepositoryError>;
    async fn update(&self, entity: &AdminUpload) -> Result<(), RepositoryError>;
    async fn delete(&self, id: &AdminUploadId) -> Result<(), RepositoryError>;
}

#[automock]
#[async_trait]
pub trait AdminUploadReadRepository: Send + Sync {
    async fn find_by_id(&self, id: &AdminUploadId) -> Result<Option<AdminUpload>, RepositoryError>;
    async fn find_all(&self) -> Result<Vec<AdminUpload>, RepositoryError>;
}
