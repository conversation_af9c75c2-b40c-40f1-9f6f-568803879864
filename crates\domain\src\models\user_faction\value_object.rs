use reforged_shared::{UuidId, Value};

use super::entity::UserFaction;

pub type UserFactionId = UuidId<UserFaction>;

#[derive(Debug, PartialEq, Eq, PartialOrd, Ord, Clone, Default)]

pub struct Reputation(i64);

impl Reputation {
    pub fn new(value: i64) -> Self {
        Self(value)
    }
}

impl Value<i64> for Reputation {
    fn value(&self) -> i64 {
        self.0
    }
}

impl From<i64> for Reputation {
    fn from(value: i64) -> Self {
        Self(value)
    }
}
