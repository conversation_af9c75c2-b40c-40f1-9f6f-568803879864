use async_trait::async_trait;
use mockall::automock;

use crate::error::RepositoryError;
use crate::models::achievement::entity::Achievement;
use crate::models::achievement::value_object::AchievementId;

#[automock]
#[async_trait]
pub trait AchievementRepository: Send + Sync {
    async fn save(&self, entity: &Achievement) -> Result<(), RepositoryError>;
    async fn update(&self, entity: &Achievement) -> Result<(), RepositoryError>;
    async fn delete(&self, id: &AchievementId) -> Result<(), RepositoryError>;
}

#[automock]
#[async_trait]
pub trait AchievementReadRepository: Send + Sync {
    async fn find_by_id(&self, id: &AchievementId) -> Result<Option<Achievement>, RepositoryError>;
    async fn find_all(&self) -> Result<Vec<Achievement>, RepositoryError>;
}
