use async_trait::async_trait;
use reforged_domain::models::password_reset_token::value_object::PasswordResetTokenId;
use reforged_domain::{
    error::RepositoryError, models::password_reset_token::entity::PasswordResetToken,
    repository::password_reset_token_repository::PasswordResetTokenRepository,
};
use reforged_shared::{IdTrait, Value};
use sea_orm::EntityTrait;
use sea_orm::{ActiveModelTrait, ActiveValue::Set, DatabaseConnection};

use crate::SeaORMErr;

use crate::models::password_reset_tokens::ActiveModel as PasswordResetTokensActiveModel;
use crate::models::password_reset_tokens::Entity as PasswordResetTokenModel;

#[allow(dead_code)]
pub struct PostgresPasswordResetTokenRepository {
    pool: DatabaseConnection,
}

impl PostgresPasswordResetTokenRepository {
    pub fn new(pool: DatabaseConnection) -> Self {
        Self { pool }
    }
}

#[async_trait]
impl PasswordResetTokenRepository for PostgresPasswordResetTokenRepository {
    async fn save(&self, data: &PasswordResetToken) -> Result<(), RepositoryError> {
        let new_token = PasswordResetTokensActiveModel {
            id: Set(data.id().get_id()),
            user_id: Set(data.user_id().get_id()),
            token: Set(data.token().value()),
            created_at: Set(data.created_at().value().naive_utc()),
            expires_at: Set(data.expires_at().value().naive_utc()),
        };

        new_token
            .insert(&self.pool)
            .await
            .map_err(SeaORMErr::from)?;

        Ok(())
    }

    async fn delete(&self, id: &PasswordResetTokenId) -> Result<(), RepositoryError> {
        PasswordResetTokenModel::delete_by_id(id.get_id())
            .exec(&self.pool)
            .await
            .map_err(SeaORMErr::from)?;

        Ok(())
    }
}
