use num_traits::ToPrimitive;
use reforged_domain::models::aura::value_object::AuraId;
use reforged_domain::models::aura_effect::entity::AuraEffect;
use reforged_domain::models::aura_effect::value_object::{
    AuraEffectId, AuraEffectStat, AuraEffectType, AuraEffectValue,
};

#[derive(Debug)]
pub struct AuraEffectDbModelMapper(super::super::models::aura_effects::Model);

impl AuraEffectDbModelMapper {
    pub fn new(aura_effect: super::super::models::aura_effects::Model) -> Self {
        Self(aura_effect)
    }
}

impl From<AuraEffectDbModelMapper> for AuraEffect {
    fn from(value: AuraEffectDbModelMapper) -> Self {
        let aura_effect_model = value.0;

        let id = AuraEffectId::new(aura_effect_model.id);
        let aura_id = AuraId::new(aura_effect_model.aura_id);
        let stat = AuraEffectStat::from(aura_effect_model.stat);
        let value = AuraEffectValue::new(aura_effect_model.value.to_f64().unwrap_or(0.0));
        let effect_type = AuraEffectType::from(aura_effect_model.r#type);

        AuraEffect::builder()
            .id(id)
            .aura_id(aura_id)
            .stat(stat)
            .value(value)
            .effect_type(effect_type)
            .build()
    }
}
