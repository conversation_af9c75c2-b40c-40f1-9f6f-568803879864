[package]
name = "reforged-api"
version = "0.1.0"
edition = "2024"

[dependencies]
actix-cors.workspace = true
actix-governor.workspace = true

actix-web.workspace = true
bon.workspace = true
chrono.workspace = true
futures-util.workspace = true
getset.workspace = true
reforged-application = { path = "../application" }
reforged-domain = { path = "../domain" }
reforged-shared = { path = "../shared" }
serde.workspace = true
serde_json.workspace = true
tower.workspace = true
tower-http.workspace = true
tracing.workspace = true
uuid.workspace = true
validator.workspace = true
