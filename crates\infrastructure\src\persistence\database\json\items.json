[{"id": "0196ba87-e697-763b-a8f7-7e0ca172e655", "name": "Nytherian Staff", "description": "Nytherian weapon for Magical uses.", "type": "Staff", "element": "None", "file": "items/staves/NytherianStaff.swf", "link": "StaffWeaponStaff", "icon": "iwstaff", "equipment": "Weapon", "level": 1, "dps": 25, "range": 10, "rarity": "0196ba87-e69e-75ac-9885-5763facdf6ca", "quantity": 1, "stack": 1, "cost": 0, "coins": 0, "diamonds": 0, "crystal": 0, "sell": true, "market": false, "temporary": false, "upgrade": false, "staff": false, "enh_id": "0196ba87-e691-7757-a7dd-b3fcba14451c", "req_reputation": 0, "req_class_points": 0, "req_quests": "", "quest_string_index": -1, "quest_string_value": 0, "meta": "", "color": "ffffff", "pid": 1}, {"id": "0196ba87-e697-763b-a8f7-83ebfe9aa6e9", "class_id": "0196ba87-e68e-72f0-93f5-964b7d1f2256", "name": "<PERSON><PERSON><PERSON><PERSON>", "description": "A Nytherian that doesn't have much that of experience;we must keep moving and learn more about our Adventure!", "type": "Class", "element": "None", "file": "Nytherian.swf", "link": "DWMage", "icon": "iiclass", "equipment": "ar", "level": 1, "dps": 25, "range": 50, "rarity": "0196ba87-e69e-75ac-9885-5b8b0c71fdb4", "quantity": 1, "stack": 1, "cost": 1000, "coins": 0, "diamonds": 0, "crystal": 0, "sell": true, "market": true, "temporary": false, "upgrade": false, "staff": false, "enh_id": "0196ba87-e691-7757-a7dd-b3fcba14451c", "req_reputation": 0, "req_class_id": "0196ba87-e68e-72f0-93f5-964b7d1f2256", "req_class_points": 0, "req_quests": "", "quest_string_index": -1, "quest_string_value": 0, "meta": "", "color": "ffffff", "pid": 2}, {"id": "0196ba87-e697-763b-a8f7-86357595d830", "name": "Skeletal Bone", "description": "Skeletal Bone - Use for stuffs and medicinal herbs.", "type": "<PERSON><PERSON>", "element": "None", "file": "none.swf", "link": "iibag", "icon": "iibag", "equipment": "None", "level": 1, "dps": 25, "range": 50, "rarity": "0196ba87-e69e-75ac-9885-5124623d1f3f", "quantity": 1, "stack": 500, "cost": 0, "coins": 1, "diamonds": 0, "crystal": 0, "sell": true, "market": false, "temporary": false, "upgrade": false, "staff": false, "enh_id": "0196ba87-e691-7757-a7dd-b3fcba14451c", "faction_id": "0196ba87-e693-7421-951a-e35274e5fe7a", "req_reputation": 0, "req_class_points": 0, "req_quests": "", "quest_string_index": -1, "quest_string_value": 0, "meta": "", "color": "c5c2b4", "pid": 3}, {"id": "0196ba87-e697-763b-a8f7-8af2d050b6df", "name": "Darkness Absolution", "description": "Darkness Absolution;magical fire provided by the monster in Nasari;you could reserve the darkness absolution they will serve you!", "type": "<PERSON><PERSON>", "element": "None", "file": "ief1", "link": "ief1", "icon": "ief1", "equipment": "None", "level": 1, "dps": 25, "range": 50, "rarity": "0196ba87-e69e-75ac-9885-5763facdf6ca", "quantity": 1, "stack": 500, "cost": 0, "coins": 1, "diamonds": 0, "crystal": 0, "sell": true, "market": false, "temporary": false, "upgrade": false, "staff": false, "enh_id": "0196ba87-e691-7757-a7dd-b3fcba14451c", "req_reputation": 0, "req_class_points": 0, "req_quests": "", "quest_string_index": -1, "quest_string_value": 0, "meta": "", "color": "353626", "pid": 4}, {"id": "0196ba87-e697-763b-a8f7-8c01e5168718", "name": "Balrog Blade (Beta)", "description": "Weapons of such ferocity are a rare find indeed. Its blade burns with eternal fire.", "type": "Sword", "element": "None", "file": "items/swords/sword20.swf", "link": "sword20", "icon": "iwsword", "equipment": "Weapon", "level": 1, "dps": 25, "range": 50, "rarity": "0196ba87-e69e-75ac-9885-5b8b0c71fdb4", "quantity": 1, "stack": 1, "cost": 80000, "coins": 0, "diamonds": 0, "crystal": 0, "sell": true, "market": true, "temporary": false, "upgrade": false, "staff": false, "enh_id": "0196ba87-e691-7757-a7dd-b3fcba14451c", "req_reputation": 0, "req_class_points": 0, "req_quests": "", "quest_string_index": -1, "quest_string_value": 0, "meta": "", "color": "ffffff", "pid": 5}, {"id": "0196ba87-e697-763b-a8f7-93d558d16727", "name": "Beta Tester Star Sword", "description": "This blade seems to harness the power of light itself. It's a force to be reckoned with.", "type": "Sword", "element": "None", "file": "items/swords/sword23.swf", "link": "sword23", "icon": "iwsword", "equipment": "Weapon", "level": 1, "dps": 25, "range": 50, "rarity": "0196ba87-e69e-75ac-9885-5b8b0c71fdb4", "quantity": 1, "stack": 1, "cost": 100000, "coins": 0, "diamonds": 0, "crystal": 0, "sell": true, "market": true, "temporary": false, "upgrade": false, "staff": false, "enh_id": "0196ba87-e691-7757-a7dd-b3fcba14451c", "req_reputation": 0, "req_class_points": 0, "req_quests": "", "quest_string_index": -1, "quest_string_value": 0, "meta": "", "color": "ffffff", "pid": 6}, {"id": "0196ba87-e697-763b-a8f7-96de8ab30286", "name": "Grumpy <PERSON>", "description": "This hammer is visibly upset. You would be too if people used your head as a weapon!", "type": "Mace", "element": "None", "file": "items/maces/mace03.swf", "link": "mace03", "icon": "iw<PERSON><PERSON>", "equipment": "Weapon", "level": 1, "dps": 25, "range": 50, "rarity": "0196ba87-e69e-75ac-9885-5b8b0c71fdb4", "quantity": 1, "stack": 1, "cost": 40000, "coins": 0, "diamonds": 0, "crystal": 0, "sell": true, "market": true, "temporary": false, "upgrade": false, "staff": false, "enh_id": "0196ba87-e691-7757-a7dd-b3fcba14451c", "req_reputation": 0, "req_class_points": 0, "req_quests": "", "quest_string_index": -1, "quest_string_value": 0, "meta": "", "color": "ffffff", "pid": 7}, {"id": "0196ba87-e697-763b-a8f7-989c1eb860fe", "name": "<PERSON><PERSON><PERSON>", "description": "A sword commonly used by ninja. The blade is much shorter than the traditional daito katana used by the samurai of feudal Japan.", "type": "Sword", "element": "None", "file": "items/swords/sword27.swf", "link": "sword27", "icon": "iwsword", "equipment": "Weapon", "level": 1, "dps": 25, "range": 50, "rarity": "0196ba87-e69e-75ac-9885-5b8b0c71fdb4", "quantity": 1, "stack": 1, "cost": 80000, "coins": 0, "diamonds": 0, "crystal": 0, "sell": true, "market": true, "temporary": false, "upgrade": false, "staff": false, "enh_id": "0196ba87-e691-7757-a7dd-b3fcba14451c", "req_reputation": 0, "req_class_points": 0, "req_quests": "", "quest_string_index": -1, "quest_string_value": 0, "meta": "", "color": "ffffff", "pid": 8}, {"id": "0196ba87-e697-763b-a8f7-9dadcb71945f", "name": "Undead Plague Spear", "description": "A mere scrape from this vile weapon is deadly.", "type": "Polearm", "element": "None", "file": "items/polearms/polearm03.swf", "link": "polearm03", "icon": "iwpolearm", "equipment": "Weapon", "level": 1, "dps": 25, "range": 50, "rarity": "0196ba87-e69e-75ac-9885-5b8b0c71fdb4", "quantity": 1, "stack": 1, "cost": 5000, "coins": 0, "diamonds": 0, "crystal": 0, "sell": true, "market": true, "temporary": false, "upgrade": false, "staff": false, "enh_id": "0196ba87-e691-7757-a7dd-b3fcba14451c", "req_reputation": 0, "req_class_points": 0, "req_quests": "", "quest_string_index": -1, "quest_string_value": 0, "meta": "", "color": "ffffff", "pid": 9}, {"id": "0196ba87-e697-763b-a8f7-a34cb37f6175", "name": "Dual Shin<PERSON><PERSON>", "description": "A sword commonly used by ninja. The blade is much shorter than the traditional daito katana used by the samurai of feudal Japan.", "type": "<PERSON>gger", "element": "None", "file": "items/daggers/sword27.swf", "link": "sword27", "icon": "iwda<PERSON>", "equipment": "Weapon", "level": 1, "dps": 25, "range": 50, "rarity": "0196ba87-e69e-75ac-9885-5b8b0c71fdb4", "quantity": 1, "stack": 1, "cost": 100000, "coins": 0, "diamonds": 0, "crystal": 0, "sell": true, "market": true, "temporary": false, "upgrade": false, "staff": false, "enh_id": "0196ba87-e691-7757-a7dd-b3fcba14451c", "req_reputation": 0, "req_class_points": 0, "req_quests": "", "quest_string_index": -1, "quest_string_value": 0, "meta": "", "color": "ffffff", "pid": 10}, {"id": "0196ba87-e697-763b-a8f7-a6e083855d02", "name": "<PERSON> (Beta)", "description": "Ranger Hat. (This items is color customizable)", "type": "<PERSON><PERSON>", "element": "None", "file": "items/helms/rangerhat.swf", "link": "RangerHat", "icon": "iihelm", "equipment": "he", "level": 1, "dps": 25, "range": 50, "rarity": "0196ba87-e69e-75ac-9885-5b8b0c71fdb4", "quantity": 1, "stack": 1, "cost": 5000, "coins": 0, "diamonds": 0, "crystal": 0, "sell": true, "market": true, "temporary": false, "upgrade": false, "staff": false, "enh_id": "0196ba87-e691-7757-a7dd-b3fcba14451c", "req_reputation": 0, "req_class_points": 0, "req_quests": "", "quest_string_index": -1, "quest_string_value": 0, "meta": "", "color": "ffffff", "pid": 11}, {"id": "0196ba87-e697-763b-a8f7-ab6487a26fa6", "name": "Dragons<PERSON> (Beta)", "description": "The Helm worn by the elite order of Dragonslayers.", "type": "<PERSON><PERSON>", "element": "None", "file": "items/helms/Dragonslayer.swf", "link": "DragonslayerFHair", "icon": "iihelm", "equipment": "he", "level": 1, "dps": 25, "range": 50, "rarity": "0196ba87-e69e-75ac-9885-5b8b0c71fdb4", "quantity": 1, "stack": 1, "cost": 50000, "coins": 0, "diamonds": 0, "crystal": 0, "sell": true, "market": true, "temporary": false, "upgrade": false, "staff": false, "enh_id": "0196ba87-e691-7757-a7dd-b3fcba14451c", "req_reputation": 0, "req_class_points": 0, "req_quests": "", "quest_string_index": -1, "quest_string_value": 0, "meta": "", "color": "ffffff", "pid": 12}, {"id": "0196ba87-e697-763b-a8f7-af97b4c12d19", "name": "Blue Cape", "description": "Blue Cape", "type": "Cape", "element": "None", "file": "items/capes/bluecape.swf", "link": "BlueCape", "icon": "iicape", "equipment": "ba", "level": 1, "dps": 25, "range": 50, "rarity": "0196ba87-e69e-75ac-9885-5b8b0c71fdb4", "quantity": 1, "stack": 1, "cost": 500, "coins": 0, "diamonds": 0, "crystal": 0, "sell": true, "market": true, "temporary": false, "upgrade": false, "staff": false, "enh_id": "0196ba87-e691-7757-a7dd-b3fcba14451c", "req_reputation": 0, "req_class_points": 0, "req_quests": "", "quest_string_index": -1, "quest_string_value": 0, "meta": "", "color": "ffffff", "pid": 13}, {"id": "0196ba87-e697-763b-a8f7-b07029daf843", "name": "Brown <PERSON>", "description": "Brown <PERSON>", "type": "Cape", "element": "None", "file": "items/capes/browncape.swf", "link": "BrownCape", "icon": "iicape", "equipment": "ba", "level": 1, "dps": 25, "range": 50, "rarity": "0196ba87-e69e-75ac-9885-5b8b0c71fdb4", "quantity": 1, "stack": 1, "cost": 500, "coins": 0, "diamonds": 0, "crystal": 0, "sell": true, "market": true, "temporary": false, "upgrade": false, "staff": false, "enh_id": "0196ba87-e691-7757-a7dd-b3fcba14451c", "req_reputation": 0, "req_class_points": 0, "req_quests": "", "quest_string_index": -1, "quest_string_value": 0, "meta": "", "color": "ffffff", "pid": 14}, {"id": "0196ba87-e697-763b-a8f7-b6620a4df1ce", "name": "Dark Blue Cape", "description": "Dark Blue Cape", "type": "Cape", "element": "None", "file": "items/capes/darkbluecape.swf", "link": "DarkblueCape", "icon": "iicape", "equipment": "ba", "level": 1, "dps": 25, "range": 50, "rarity": "0196ba87-e69e-75ac-9885-5b8b0c71fdb4", "quantity": 1, "stack": 1, "cost": 500, "coins": 0, "diamonds": 0, "crystal": 0, "sell": true, "market": true, "temporary": false, "upgrade": false, "staff": false, "enh_id": "0196ba87-e691-7757-a7dd-b3fcba14451c", "req_reputation": 0, "req_class_points": 0, "req_quests": "", "quest_string_index": -1, "quest_string_value": 0, "meta": "", "color": "ffffff", "pid": 15}, {"id": "0196ba87-e697-763b-a8f7-bbcc613c2146", "name": "Green Cape", "description": "Green Cape", "type": "Cape", "element": "None", "file": "items/capes/greencape.swf", "link": "GreenCape", "icon": "iicape", "equipment": "ba", "level": 1, "dps": 25, "range": 50, "rarity": "0196ba87-e69e-75ac-9885-5b8b0c71fdb4", "quantity": 1, "stack": 1, "cost": 500, "coins": 0, "diamonds": 0, "crystal": 0, "sell": true, "market": true, "temporary": false, "upgrade": false, "staff": false, "enh_id": "0196ba87-e691-7757-a7dd-b3fcba14451c", "req_reputation": 0, "req_class_points": 0, "req_quests": "", "quest_string_index": -1, "quest_string_value": 0, "meta": "", "color": "ffffff", "pid": 16}, {"id": "0196ba87-e697-763b-a8f7-bcc2d00b6fb6", "name": "Red Cape", "description": "Red Cape", "type": "Cape", "element": "None", "file": "items/capes/redcape.swf", "link": "RedCape", "icon": "iicape", "equipment": "ba", "level": 1, "dps": 25, "range": 50, "rarity": "0196ba87-e69e-75ac-9885-5b8b0c71fdb4", "quantity": 1, "stack": 1, "cost": 500, "coins": 0, "diamonds": 0, "crystal": 0, "sell": true, "market": true, "temporary": false, "upgrade": false, "staff": false, "enh_id": "0196ba87-e691-7757-a7dd-b3fcba14451c", "req_reputation": 0, "req_class_points": 0, "req_quests": "", "quest_string_index": -1, "quest_string_value": 0, "meta": "", "color": "ffffff", "pid": 17}, {"id": "0196ba87-e697-763b-a8f7-c104e3e430a4", "name": "Yellow Cape", "description": "Yellow Cape", "type": "Cape", "element": "None", "file": "items/capes/yellowcape.swf", "link": "yellowcape", "icon": "iicape", "equipment": "ba", "level": 1, "dps": 25, "range": 50, "rarity": "0196ba87-e69e-75ac-9885-5b8b0c71fdb4", "quantity": 1, "stack": 1, "cost": 500, "coins": 0, "diamonds": 0, "crystal": 0, "sell": true, "market": true, "temporary": false, "upgrade": false, "staff": false, "enh_id": "0196ba87-e691-7757-a7dd-b3fcba14451c", "req_reputation": 0, "req_class_points": 0, "req_quests": "", "quest_string_index": -1, "quest_string_value": 0, "meta": "", "color": "ffffff", "pid": 18}, {"id": "0196ba87-e697-763b-a8f7-c648491ce06e", "name": "Blade of the Fallen", "description": "A terrifying powerful artifact of the Void;this one has been enchanted with a small amount of the Purple Mage's immense power. This makes it all the more terrifying and all the more powerful.", "type": "Sword", "element": "None", "file": "items/swords/FallensJusticeFix.swf", "link": "FallensJusticeFix", "icon": "iwsword", "equipment": "Weapon", "level": 1, "dps": 27, "range": 50, "rarity": "0196ba87-e69e-75ac-9885-940e18f9c3e6", "quantity": 1, "stack": 1, "cost": 0, "coins": 1, "diamonds": 0, "crystal": 0, "sell": true, "market": false, "temporary": false, "upgrade": false, "staff": false, "enh_id": "0196ba87-e691-7757-a7dd-b3fcba14451c", "req_reputation": 0, "req_class_points": 0, "req_quests": "", "quest_string_index": -1, "quest_string_value": 0, "meta": "", "color": "b577df", "pid": 19}, {"id": "0196ba87-e697-763b-a8f7-c97da72ee3da", "name": "Soon", "description": "Soon", "type": "Resource", "element": "None", "file": "Soon", "link": "Soon", "icon": "iibag", "equipment": "Weapon", "level": 1, "dps": 27, "range": 50, "rarity": "0196ba87-e69e-75ac-9885-940e18f9c3e6", "quantity": 1, "stack": 1, "cost": 0, "coins": 1, "diamonds": 0, "crystal": 0, "sell": true, "market": false, "temporary": false, "upgrade": false, "staff": false, "enh_id": "0196ba87-e691-7757-a7dd-b3fcba14451c", "req_reputation": 0, "req_class_points": 0, "req_quests": "", "quest_string_index": -1, "quest_string_value": 0, "meta": "", "color": "b577df", "pid": 20}, {"id": "0196ba87-e697-763b-a8f7-cc23260e98a0", "name": "<PERSON><PERSON>", "description": "All I wanted was to be a nightmare;like my ancestors. But the dream I thought would bring me luck;brpught only a curse. I lost my moglin;my mustache;and my maracas.", "type": "Axe", "element": "None", "file": "items/axes/MariachiHeroGuitar.swf", "link": "MariachiHeroGuitar", "icon": "iwaxe", "equipment": "Weapon", "level": 1, "dps": 25, "range": 50, "rarity": "0196ba87-e69e-75ac-9885-9cc909ea652d", "quantity": 1, "stack": 1, "cost": 25555, "coins": 0, "diamonds": 0, "crystal": 0, "sell": true, "market": true, "temporary": false, "upgrade": false, "staff": false, "enh_id": "0196ba87-e691-7757-a7dd-b3fcba14451c", "req_reputation": 0, "req_class_points": 0, "req_quests": "", "quest_string_index": -1, "quest_string_value": 0, "meta": "", "color": "ffffff", "pid": 21}, {"id": "0196ba87-e697-763b-a8f7-d0aab288810d", "name": "Victoria Magica", "description": "<PERSON><PERSON><PERSON><PERSON> C<PERSON><PERSON> de Mayo with this epic more colorful dancewear!", "type": "Armor", "element": "None", "file": "LaDancerCC.swf", "link": "LaDancerCC", "icon": "i<PERSON>or", "equipment": "co", "level": 1, "dps": 25, "range": 50, "rarity": "0196ba87-e69e-75ac-9885-9cc909ea652d", "quantity": 1, "stack": 1, "cost": 750, "coins": 1, "diamonds": 0, "crystal": 0, "sell": true, "market": true, "temporary": false, "upgrade": false, "staff": false, "enh_id": "0196ba87-e691-7757-a7dd-b3fcba14451c", "req_reputation": 0, "req_class_points": 0, "req_quests": "", "quest_string_index": -1, "quest_string_value": 0, "meta": "", "color": "ffffff", "pid": 22}, {"id": "0196ba87-e697-763b-a8f7-d4d080c6d84e", "name": "The Ol Fishin' Garb", "description": "How many fisherman do you see wearing a full suit of chain mail? I rest my case. Grab your Fishin' Rod and favorite hat and let's catch some grub!", "type": "Armor", "element": "None", "file": "Fishing1.swf", "link": "Fishing1", "icon": "i<PERSON>or", "equipment": "co", "level": 1, "dps": 25, "range": 50, "rarity": "0196ba87-e69e-75ac-9885-9cc909ea652d", "quantity": 1, "stack": 1, "cost": 400, "coins": 1, "diamonds": 0, "crystal": 0, "sell": true, "market": true, "temporary": false, "upgrade": false, "staff": false, "enh_id": "0196ba87-e691-7757-a7dd-b3fcba14451c", "req_reputation": 0, "req_class_points": 0, "req_quests": "", "quest_string_index": -1, "quest_string_value": 0, "meta": "", "color": "ffffff", "pid": 23}, {"id": "0196ba87-e697-763b-a8f7-d8f43fa32de7", "name": "Skeleton Bones", "description": "-", "type": "<PERSON><PERSON>", "element": "None", "file": "iibag", "link": "iibag", "icon": "iibag", "equipment": "<PERSON><PERSON>", "level": 1, "dps": 25, "range": 50, "rarity": "0196ba87-e69e-75ac-9885-5763facdf6ca", "quantity": 1, "stack": 50, "cost": 0, "coins": 0, "diamonds": 0, "crystal": 0, "sell": true, "market": true, "temporary": true, "upgrade": false, "staff": false, "enh_id": "0196ba87-e691-7757-a7dd-b3fcba14451c", "req_reputation": 0, "req_class_points": 0, "req_quests": "", "quest_string_index": -1, "quest_string_value": 0, "meta": "", "color": "ffffff", "pid": 24}, {"id": "0196ba87-e697-763b-a8f7-df964b666e2b", "name": "Skeletons Part's", "description": "-", "type": "<PERSON><PERSON>", "element": "None", "file": "iibag", "link": "iibag", "icon": "iibag", "equipment": "<PERSON><PERSON>", "level": 1, "dps": 25, "range": 50, "rarity": "0196ba87-e69e-75ac-9885-5763facdf6ca", "quantity": 1, "stack": 50, "cost": 0, "coins": 0, "diamonds": 0, "crystal": 0, "sell": true, "market": true, "temporary": true, "upgrade": false, "staff": false, "enh_id": "0196ba87-e691-7757-a7dd-b3fcba14451c", "req_reputation": 0, "req_class_points": 0, "req_quests": "", "quest_string_index": -1, "quest_string_value": 0, "meta": "", "color": "ffffff", "pid": 25}, {"id": "0196ba87-e697-763b-a8f7-e24bf3d38130", "name": "Undead Giant Defeated", "description": "", "type": "<PERSON><PERSON>", "element": "None", "file": "iibag", "link": "iibag", "icon": "iibag", "equipment": "<PERSON><PERSON>", "level": 1, "dps": 25, "range": 50, "rarity": "0196ba87-e69e-75ac-9885-5763facdf6ca", "quantity": 1, "stack": 10, "cost": 0, "coins": 0, "diamonds": 0, "crystal": 0, "sell": true, "market": true, "temporary": true, "upgrade": false, "staff": false, "enh_id": "0196ba87-e691-7757-a7dd-b3fcba14451c", "req_reputation": 0, "req_class_points": 0, "req_quests": "", "quest_string_index": -1, "quest_string_value": 0, "meta": "", "color": "ffffff", "pid": 26}, {"id": "0196ba87-e697-763b-a8f7-e7f4cb6103cc", "name": "<PERSON> part", "description": "-", "type": "<PERSON><PERSON>", "element": "None", "file": "iibag", "link": "iibag", "icon": "iibag", "equipment": "<PERSON><PERSON>", "level": 1, "dps": 25, "range": 50, "rarity": "0196ba87-e69e-75ac-9885-5763facdf6ca", "quantity": 1, "stack": 5, "cost": 0, "coins": 0, "diamonds": 0, "crystal": 0, "sell": true, "market": false, "temporary": true, "upgrade": false, "staff": false, "enh_id": "0196ba87-e691-7757-a7dd-b3fcba14451c", "req_reputation": 0, "req_class_points": 0, "req_quests": "", "quest_string_index": -1, "quest_string_value": 0, "meta": "", "color": "ffffff", "pid": 27}, {"id": "0196ba87-e697-763b-a8f7-e9125deed8cf", "name": "<PERSON><PERSON>", "description": "All I wanted was to be a nightmare;like my ancestors. But the dream I thought would bring me luck;brought only a curse. I lost my moglin;my mustache;and my maracas.", "type": "<PERSON><PERSON>", "element": "None", "file": "items/helms/MariachiHeroHair.swf", "link": "MariachiHeroHair", "icon": "iihelm", "equipment": "he", "level": 1, "dps": 25, "range": 50, "rarity": "0196ba87-e69e-75ac-9885-5763facdf6ca", "quantity": 1, "stack": 1, "cost": 5555, "coins": 0, "diamonds": 0, "crystal": 0, "sell": true, "market": true, "temporary": false, "upgrade": false, "staff": false, "enh_id": "0196ba87-e691-7757-a7dd-b3fcba14451c", "req_reputation": 0, "req_class_points": 0, "req_quests": "", "quest_string_index": -1, "quest_string_value": 0, "meta": "", "color": "ffffff", "pid": 28}, {"id": "0196ba87-e697-763b-a8f7-ecc9c1bdebdf", "name": "<PERSON><PERSON>s", "description": "All I wanted was to be a nightmare;like my ancestors. But the dream I thought would bring me luck;brought only a curse. I lost my moglin;my mustache;and my maracas.", "type": "<PERSON><PERSON>", "element": "None", "file": "items/helms/MariachiHeroLocks.swf", "link": "MariachiHeroLocks", "icon": "iihelm", "equipment": "he", "level": 1, "dps": 25, "range": 50, "rarity": "0196ba87-e69e-75ac-9885-9cc909ea652d", "quantity": 1, "stack": 1, "cost": 5555, "coins": 0, "diamonds": 0, "crystal": 0, "sell": true, "market": true, "temporary": false, "upgrade": false, "staff": false, "enh_id": "0196ba87-e691-7757-a7dd-b3fcba14451c", "req_reputation": 0, "req_class_points": 0, "req_quests": "", "quest_string_index": -1, "quest_string_value": 0, "meta": "", "color": "ffffff", "pid": 29}, {"id": "0196ba87-e697-763b-a8f7-f10324571d9c", "name": "Sombrero de la Magica", "description": "<PERSON><PERSON><PERSON><PERSON> C<PERSON><PERSON> de Mayo with this epic more colorful dancewear!", "type": "<PERSON><PERSON>", "element": "None", "file": "items/helms/DancerHatCC.swf", "link": "DancerHatCC", "icon": "iihelm", "equipment": "he", "level": 1, "dps": 25, "range": 50, "rarity": "0196ba87-e69e-75ac-9885-9cc909ea652d", "quantity": 1, "stack": 1, "cost": 150, "coins": 1, "diamonds": 0, "crystal": 0, "sell": true, "market": true, "temporary": false, "upgrade": false, "staff": false, "enh_id": "0196ba87-e691-7757-a7dd-b3fcba14451c", "req_reputation": 0, "req_class_points": 0, "req_quests": "", "quest_string_index": -1, "quest_string_value": 0, "meta": "", "color": "ffffff", "pid": 30}, {"id": "0196ba87-e697-763b-a8f7-f5b106bdcd3f", "name": "BBQ Spatula", "description": "Extra-wide stainless steel spatula blade will hold the largest cuts of meat and has the capability to flip a fully grown cow.", "type": "Mace", "element": "None", "file": "items/maces/SpatulaBBQSword.swf", "link": "SpatulaBBQSword", "icon": "iw<PERSON><PERSON>", "equipment": "Weapon", "level": 1, "dps": 25, "range": 50, "rarity": "0196ba87-e69e-75ac-9885-9cc909ea652d", "quantity": 1, "stack": 1, "cost": 5000, "coins": 0, "diamonds": 0, "crystal": 0, "sell": true, "market": true, "temporary": false, "upgrade": false, "staff": false, "enh_id": "0196ba87-e691-7757-a7dd-b3fcba14451c", "req_reputation": 0, "req_class_points": 0, "req_quests": "", "quest_string_index": -1, "quest_string_value": 0, "meta": "", "color": "ffffff", "pid": 31}, {"id": "0196ba87-e697-763b-a8f7-fbb11e52171f", "name": "Gravity Defying Coffee Mug", "description": "A steaming cup of coffee that will never spill - the perfect gift for the AQWorld's best dad this Father's Day!", "type": "Mace", "element": "None", "file": "items/maces/mugCoffee.swf", "link": "<PERSON><PERSON><PERSON><PERSON>", "icon": "iw<PERSON><PERSON>", "equipment": "Weapon", "level": 1, "dps": 25, "range": 50, "rarity": "0196ba87-e69e-75ac-9885-9cc909ea652d", "quantity": 1, "stack": 1, "cost": 150, "coins": 1, "diamonds": 0, "crystal": 0, "sell": true, "market": true, "temporary": false, "upgrade": false, "staff": false, "enh_id": "0196ba87-e691-7757-a7dd-b3fcba14451c", "req_reputation": 0, "req_class_points": 0, "req_quests": "", "quest_string_index": -1, "quest_string_value": 0, "meta": "", "color": "ffffff", "pid": 32}, {"id": "0196ba87-e697-763b-a8f7-fe8f0c04a884", "name": "The Ol Fishin' Hat", "description": "Anglers swear by the khaki bucket hat. Never leave on your fishing trip without one!", "type": "<PERSON><PERSON>", "element": "None", "file": "items/helms/FishingHat1.swf", "link": "FishingHat1", "icon": "iihelm", "equipment": "he", "level": 1, "dps": 25, "range": 50, "rarity": "0196ba87-e69e-75ac-9885-9cc909ea652d", "quantity": 1, "stack": 1, "cost": 120, "coins": 1, "diamonds": 0, "crystal": 0, "sell": true, "market": true, "temporary": false, "upgrade": false, "staff": false, "enh_id": "0196ba87-e691-7757-a7dd-b3fcba14451c", "req_reputation": 0, "req_class_points": 0, "req_quests": "", "quest_string_index": -1, "quest_string_value": 0, "meta": "", "color": "ffffff", "pid": 33}, {"id": "0196ba87-e697-763b-a8f8-00ab6924219e", "name": "Fairy Godmother", "description": "She'll bippity boppity boop all 'round you. Happy Mother's Day!", "type": "Pet", "element": "None", "file": "items/pets/godmother.swf", "link": "<PERSON><PERSON><PERSON><PERSON>", "icon": "iipet", "equipment": "pe", "level": 1, "dps": 25, "range": 50, "rarity": "0196ba87-e69e-75ac-9885-9cc909ea652d", "quantity": 1, "stack": 1, "cost": 25000, "coins": 0, "diamonds": 0, "crystal": 0, "sell": true, "market": true, "temporary": false, "upgrade": true, "staff": false, "enh_id": "0196ba87-e691-7757-a7dd-b3fcba14451c", "req_reputation": 0, "req_class_points": 0, "req_quests": "", "quest_string_index": -1, "quest_string_value": 0, "meta": "", "color": "ffffff", "pid": 34}, {"id": "0196ba87-e697-763b-a8f8-06c862f8b054", "name": "Acalagon Demise", "description": "Exclusive Item", "type": "Sword", "element": "None", "file": "items/swords/DracoLichKingSword.swf", "link": "DracoLichKingSword", "icon": "iwsword", "equipment": "Weapon", "level": 1, "dps": 25, "range": 50, "rarity": "0196ba87-e69e-75ac-9885-940e18f9c3e6", "quantity": 1, "stack": 1, "cost": 500, "coins": 1, "diamonds": 0, "crystal": 0, "sell": true, "market": false, "temporary": false, "upgrade": true, "staff": false, "enh_id": "0196ba87-e691-7757-a7dd-b3fcba14451c", "req_reputation": 0, "req_class_points": 0, "req_quests": "", "quest_string_index": -1, "quest_string_value": 0, "meta": "", "color": "00ffff", "pid": 35}, {"id": "0196ba87-e697-763b-a8f8-0a2d1ed81108", "name": "XP Boost! (1 hr)", "description": "Using this item will DOUBLE all experience gained from killing monsters or completing quests for 1 hour of in-game play time.", "type": "ServerUse", "element": "None", "file": "icbxp", "link": "xpboost::60::true", "icon": "icbxp", "equipment": "None", "level": 1, "dps": 25, "range": 50, "rarity": "0196ba87-e69e-75ac-9885-5b8b0c71fdb4", "quantity": 1, "stack": 30, "cost": 150, "coins": 1, "diamonds": 0, "crystal": 0, "sell": true, "market": true, "temporary": false, "upgrade": false, "staff": false, "enh_id": "0196ba87-e691-7757-a7dd-b3fcba14451c", "req_reputation": 0, "req_class_points": 0, "req_quests": "", "quest_string_index": -1, "quest_string_value": 0, "meta": "", "color": "ffffff", "pid": 36}, {"id": "0196ba87-e697-763b-a8f8-0eb951c36aa5", "name": "Legion Acalagon Demise", "description": "Exclusive Item", "type": "Sword", "element": "None", "file": "items/swords/LegDracoLichKingSword.swf", "link": "LegDracoLichKingSword", "icon": "iwsword", "equipment": "Weapon", "level": 1, "dps": 25, "range": 50, "rarity": "0196ba87-e69e-75ac-9885-940e18f9c3e6", "quantity": 1, "stack": 1, "cost": 500, "coins": 1, "diamonds": 0, "crystal": 0, "sell": true, "market": false, "temporary": false, "upgrade": true, "staff": false, "enh_id": "0196ba87-e691-7757-a7dd-b3fcba14451c", "req_reputation": 0, "req_class_points": 0, "req_quests": "", "quest_string_index": -1, "quest_string_value": 0, "meta": "", "color": "04def0", "pid": 37}, {"id": "0196ba87-e697-763b-a8f8-127c518e95e7", "name": "CP Boost! (1 hr)", "description": "Using this item will DOUBLE all cp gained from killing monsters or completing quests for 1 hour of in-game play time. Does not expire while logged out. Only available every four years for Leap Day.", "type": "ServerUse", "element": "None", "file": "icbcp", "link": "cpboost::60::false", "icon": "icbcp", "equipment": "None", "level": 1, "dps": 25, "range": 50, "rarity": "0196ba87-e69e-75ac-9885-5763facdf6ca", "quantity": 1, "stack": 30, "cost": 150, "coins": 1, "diamonds": 0, "crystal": 0, "sell": true, "market": true, "temporary": false, "upgrade": false, "staff": false, "enh_id": "0196ba87-e691-7757-a7dd-b3fcba14451c", "req_reputation": 0, "req_class_points": 0, "req_quests": "", "quest_string_index": -1, "quest_string_value": 0, "meta": "", "color": "ffffff", "pid": 38}, {"id": "0196ba87-e697-763b-a8f8-16a459d11207", "name": "<PERSON><PERSON><PERSON><PERSON> King", "description": "Exclusive Item", "type": "Armor", "element": "None", "file": "DracoLichKing.swf", "link": "DracoLichKing", "icon": "i<PERSON>or", "equipment": "co", "level": 1, "dps": 25, "range": 50, "rarity": "0196ba87-e69e-75ac-9885-940e18f9c3e6", "quantity": 1, "stack": 1, "cost": 1000, "coins": 1, "diamonds": 0, "crystal": 0, "sell": true, "market": false, "temporary": false, "upgrade": true, "staff": false, "enh_id": "0196ba87-e691-7757-a7dd-b3fcba14451c", "req_reputation": 0, "req_class_points": 0, "req_quests": "", "quest_string_index": -1, "quest_string_value": 0, "meta": "", "color": "00ffff", "pid": 39}, {"id": "0196ba87-e697-763b-a8f8-1a852491f682", "name": "Legion DracoLich King", "description": "Exclusive Item", "type": "Armor", "element": "None", "file": "LegDracoLichKing.swf", "link": "LegDracoLichKing", "icon": "i<PERSON>or", "equipment": "co", "level": 1, "dps": 25, "range": 50, "rarity": "0196ba87-e69e-75ac-9885-940e18f9c3e6", "quantity": 1, "stack": 1, "cost": 1000, "coins": 1, "diamonds": 0, "crystal": 0, "sell": true, "market": false, "temporary": false, "upgrade": true, "staff": false, "enh_id": "0196ba87-e691-7757-a7dd-b3fcba14451c", "req_reputation": 0, "req_class_points": 0, "req_quests": "", "quest_string_index": -1, "quest_string_value": 0, "meta": "", "color": "00ffff", "pid": 40}, {"id": "0196ba87-e697-763b-a8f8-1d8dae1c16dc", "name": "DracoLich Crown", "description": "Exclusive Item", "type": "<PERSON><PERSON>", "element": "None", "file": "items/helms/DracoLichKingHelm.swf", "link": "DracoLichKingHelm", "icon": "iihelm", "equipment": "he", "level": 1, "dps": 25, "range": 50, "rarity": "0196ba87-e69e-75ac-9885-940e18f9c3e6", "quantity": 1, "stack": 1, "cost": 100, "coins": 1, "diamonds": 0, "crystal": 0, "sell": true, "market": false, "temporary": false, "upgrade": true, "staff": false, "enh_id": "0196ba87-e691-7757-a7dd-b3fcba14451c", "req_reputation": 0, "req_class_points": 0, "req_quests": "", "quest_string_index": -1, "quest_string_value": 0, "meta": "", "color": "00ffff", "pid": 41}, {"id": "0196ba87-e697-763b-a8f8-225f4db1e27b", "name": "REP Boost! (1 hr)", "description": "Equip this boost to gain double rep for 60 minutes!", "type": "ServerUse", "element": "None", "file": "icbrep", "link": "repboost::60::false", "icon": "icbrep", "equipment": "None", "level": 1, "dps": 25, "range": 50, "rarity": "0196ba87-e69e-75ac-9885-5b8b0c71fdb4", "quantity": 1, "stack": 30, "cost": 150, "coins": 1, "diamonds": 0, "crystal": 0, "sell": true, "market": true, "temporary": false, "upgrade": false, "staff": false, "enh_id": "0196ba87-e691-7757-a7dd-b3fcba14451c", "req_reputation": 0, "req_class_points": 0, "req_quests": "", "quest_string_index": -1, "quest_string_value": 0, "meta": "", "color": "ffffff", "pid": 42}, {"id": "0196ba87-e697-763b-a8f8-27b610b679a8", "name": "Legion DracoLich Crown", "description": "Exclusive Item", "type": "<PERSON><PERSON>", "element": "None", "file": "items/helms/LegDracoLichKingHelm.swf", "link": "LegDracoLichKingHelm", "icon": "iihelm", "equipment": "he", "level": 1, "dps": 25, "range": 50, "rarity": "0196ba87-e69e-75ac-9885-940e18f9c3e6", "quantity": 1, "stack": 1, "cost": 100, "coins": 1, "diamonds": 0, "crystal": 0, "sell": true, "market": false, "temporary": false, "upgrade": true, "staff": false, "enh_id": "0196ba87-e691-7757-a7dd-b3fcba14451c", "req_reputation": 0, "req_class_points": 0, "req_quests": "", "quest_string_index": -1, "quest_string_value": 0, "meta": "", "color": "00ffff", "pid": 43}, {"id": "0196ba87-e697-763b-a8f8-2b8452e5383c", "name": "Acalagon Shadow", "description": "Exlcusive Item", "type": "Cape", "element": "None", "file": "items/capes/AncalagonCape.swf", "link": "AncalagonCape", "icon": "iicape", "equipment": "ba", "level": 1, "dps": 25, "range": 50, "rarity": "0196ba87-e69e-75ac-9885-940e18f9c3e6", "quantity": 1, "stack": 1, "cost": 500, "coins": 1, "diamonds": 0, "crystal": 0, "sell": true, "market": false, "temporary": false, "upgrade": true, "staff": false, "enh_id": "0196ba87-e691-7757-a7dd-b3fcba14451c", "req_reputation": 0, "req_class_points": 0, "req_quests": "", "quest_string_index": -1, "quest_string_value": 0, "meta": "", "color": "00ffff", "pid": 44}, {"id": "0196ba87-e697-763b-a8f8-2fb162467699", "name": "DracoLich King BackBlade and Cape", "description": "Exclusive Item", "type": "Cape", "element": "None", "file": "items/capes/DLKBladeNCape.swf", "link": "DLKBladeNCape", "icon": "iicape", "equipment": "ba", "level": 1, "dps": 25, "range": 50, "rarity": "0196ba87-e69e-75ac-9885-940e18f9c3e6", "quantity": 1, "stack": 1, "cost": 500, "coins": 1, "diamonds": 0, "crystal": 0, "sell": true, "market": false, "temporary": false, "upgrade": true, "staff": false, "enh_id": "0196ba87-e691-7757-a7dd-b3fcba14451c", "req_reputation": 0, "req_class_points": 0, "req_quests": "", "quest_string_index": -1, "quest_string_value": 0, "meta": "", "color": "00ffff", "pid": 45}, {"id": "0196ba87-e697-763b-a8f8-32395779be08", "name": "<PERSON><PERSON><PERSON><PERSON> King BackBlades", "description": "Exclusive Item", "type": "Cape", "element": "None", "file": "/items/capes/DLKBladesCape.swf", "link": "DLKBladesCape", "icon": "iicape", "equipment": "ba", "level": 1, "dps": 25, "range": 50, "rarity": "0196ba87-e69e-75ac-9885-940e18f9c3e6", "quantity": 1, "stack": 1, "cost": 500, "coins": 1, "diamonds": 0, "crystal": 0, "sell": true, "market": false, "temporary": false, "upgrade": true, "staff": false, "enh_id": "0196ba87-e691-7757-a7dd-b3fcba14451c", "req_reputation": 0, "req_class_points": 0, "req_quests": "", "quest_string_index": -1, "quest_string_value": 0, "meta": "", "color": "00ffff", "pid": 46}, {"id": "0196ba87-e697-763b-a8f8-3611d47f5255", "name": "Legion DracoLichKing BackBlades and Cape", "description": "Exclusive Item", "type": "Cape", "element": "None", "file": "items/capes/LegDLKBladeNCape.swf", "link": "LegDLKBladeNCape", "icon": "iicape", "equipment": "ba", "level": 1, "dps": 25, "range": 50, "rarity": "0196ba87-e69e-75ac-9885-940e18f9c3e6", "quantity": 1, "stack": 1, "cost": 500, "coins": 1, "diamonds": 0, "crystal": 0, "sell": true, "market": false, "temporary": false, "upgrade": true, "staff": false, "enh_id": "0196ba87-e691-7757-a7dd-b3fcba14451c", "req_reputation": 0, "req_class_points": 0, "req_quests": "", "quest_string_index": -1, "quest_string_value": 0, "meta": "", "color": "00ffff", "pid": 47}, {"id": "0196ba87-e697-763b-a8f8-3a2456b8831b", "name": "Legion DracoLichKing BackBlades", "description": "Exclusive Item", "type": "Cape", "element": "None", "file": "items/capes/LegDLKBladesCape.swf", "link": "LegDLKBladesCape", "icon": "iicape", "equipment": "ba", "level": 1, "dps": 25, "range": 50, "rarity": "0196ba87-e69e-75ac-9885-940e18f9c3e6", "quantity": 1, "stack": 1, "cost": 500, "coins": 1, "diamonds": 0, "crystal": 0, "sell": true, "market": false, "temporary": false, "upgrade": true, "staff": false, "enh_id": "0196ba87-e691-7757-a7dd-b3fcba14451c", "req_reputation": 0, "req_class_points": 0, "req_quests": "", "quest_string_index": -1, "quest_string_value": 0, "meta": "", "color": "00ffff", "pid": 48}, {"id": "0196ba87-e697-763b-a8f8-3ca0d95ddaa8", "name": "Acalagon Guardian", "description": "Exclusive Item", "type": "Pet", "element": "None", "file": "items/pets/DLKSwordPet.swf", "link": "DLKSwordPet", "icon": "iipet", "equipment": "pe", "level": 1, "dps": 25, "range": 50, "rarity": "0196ba87-e69e-75ac-9885-940e18f9c3e6", "quantity": 1, "stack": 1, "cost": 250, "coins": 1, "diamonds": 0, "crystal": 0, "sell": true, "market": false, "temporary": false, "upgrade": true, "staff": false, "enh_id": "0196ba87-e691-7757-a7dd-b3fcba14451c", "req_reputation": 0, "req_class_points": 0, "req_quests": "", "quest_string_index": -1, "quest_string_value": 0, "meta": "", "color": "00ffff", "pid": 49}, {"id": "0196ba87-e697-763b-a8f8-41af2fb4e8c2", "name": "Legion Acalagon Guardian", "description": "Exclusive Item", "type": "Pet", "element": "None", "file": "items/pets/LegDLKSwordPet.swf", "link": "LegDLKSwordPet", "icon": "iipet", "equipment": "pe", "level": 1, "dps": 25, "range": 50, "rarity": "0196ba87-e69e-75ac-9885-940e18f9c3e6", "quantity": 1, "stack": 1, "cost": 250, "coins": 1, "diamonds": 0, "crystal": 0, "sell": true, "market": false, "temporary": false, "upgrade": true, "staff": false, "enh_id": "0196ba87-e691-7757-a7dd-b3fcba14451c", "req_reputation": 0, "req_class_points": 0, "req_quests": "", "quest_string_index": -1, "quest_string_value": 0, "meta": "", "color": "00ffff", "pid": 50}, {"id": "0196ba87-e697-763b-a8f8-449394d94445", "class_id": "0196ba87-e68f-7738-8b2a-d87085129246", "name": "<PERSON> (RARE)", "description": "Recommended enhancement: Fighter. Special Berserker class available only to characters who played in the Beta version!", "type": "Class", "element": "None", "file": "warrior2a_skin.swf", "link": "Warrior2a", "icon": "iiclass", "equipment": "ar", "level": 1, "dps": 25, "range": 50, "rarity": "0196ba87-e69e-75ac-9885-c0ff0942e277", "quantity": 1, "stack": 1, "cost": 100000, "coins": 0, "diamonds": 0, "crystal": 0, "sell": true, "market": false, "temporary": false, "upgrade": false, "staff": false, "enh_id": "0196ba87-e691-7757-a7dd-b3fcba14451c", "req_reputation": 0, "req_class_points": 0, "req_quests": "", "quest_string_index": -1, "quest_string_value": 0, "meta": "", "color": "ffffff", "pid": 51, "req_class_id": "0196ba87-e68f-7738-8b2a-d87085129246"}, {"id": "0196ba87-e697-763b-a8f8-4b5b97723a1d", "name": "Dual Beta Tester Star Sword", "description": "-", "type": "<PERSON>gger", "element": "None", "file": "items/daggers/Dualsword23.swf", "link": "sword23", "icon": "iwda<PERSON>", "equipment": "Weapon", "level": 1, "dps": 25, "range": 50, "rarity": "0196ba87-e69e-75ac-9885-5b8b0c71fdb4", "quantity": 1, "stack": 1, "cost": 50000, "coins": 0, "diamonds": 0, "crystal": 0, "sell": true, "market": false, "temporary": false, "upgrade": true, "staff": false, "enh_id": "0196ba87-e691-7757-a7dd-b3fcba14451c", "req_reputation": 0, "req_class_points": 0, "req_quests": "", "quest_string_index": -1, "quest_string_value": 0, "meta": "", "color": "ffffff", "pid": 52}, {"id": "0196ba87-e697-763b-a8f8-4ea2066bd30b", "name": "Beta Berserker Armor", "description": "Special Berserker Armor available only to characters who played in the Beta version!", "type": "Armor", "element": "None", "file": "warrior2a_skin.swf", "link": "Warrior2a", "icon": "i<PERSON>or", "equipment": "co", "level": 1, "dps": 25, "range": 50, "rarity": "0196ba87-e69e-75ac-9885-940e18f9c3e6", "quantity": 1, "stack": 1, "cost": 100000, "coins": 0, "diamonds": 0, "crystal": 0, "sell": true, "market": false, "temporary": false, "upgrade": false, "staff": false, "enh_id": "0196ba87-e691-7757-a7dd-b3fcba14451c", "req_reputation": 0, "req_class_points": 0, "req_quests": "", "quest_string_index": -1, "quest_string_value": 0, "meta": "", "color": "ffffff", "pid": 53}, {"id": "0196ba87-e697-763b-a8f8-50f41baa0524", "name": "<PERSON><PERSON> Of Doom", "description": "Even Evil heroes (maybe ESPECIALLY Evil heroes) need to chill out after battle. With your Icy Blade of Doom;you can cool off without sacrificing that battle-ready edge.", "type": "Sword", "element": "None", "file": "items/swords/IcyBladeofDoom.swf", "link": "IcyBladeofDoom", "icon": "iwsword", "equipment": "Weapon", "level": 1, "dps": 25, "range": 50, "rarity": "0196ba87-e69e-75ac-9885-91ec33cb57dc", "quantity": 1, "stack": 1, "cost": 450, "coins": 1, "diamonds": 0, "crystal": 0, "sell": true, "market": true, "temporary": false, "upgrade": false, "staff": false, "enh_id": "0196ba87-e691-7757-a7dd-b3fcba14451c", "req_reputation": 0, "req_class_points": 0, "req_quests": "", "quest_string_index": -1, "quest_string_value": 0, "meta": "", "color": "ffffff", "pid": 54}, {"id": "0196ba87-e697-763b-a8f8-5672ab82a0cd", "name": "Molten GreatSword", "description": "Pyroclastic necromancers walk where the faint-hearted fear to tread;seeking undead minions among those who've perished in the hearts of volcanic lands.", "type": "Sword", "element": "None", "file": "items/swords/MoltenCalamityGreatSword.swf", "link": "MoltenCalamityGreatSword", "icon": "iwsword", "equipment": "Weapon", "level": 1, "dps": 25, "range": 50, "rarity": "0196ba87-e69e-75ac-9885-91ec33cb57dc", "quantity": 1, "stack": 1, "cost": 450, "coins": 1, "diamonds": 0, "crystal": 0, "sell": true, "market": true, "temporary": false, "upgrade": false, "staff": false, "enh_id": "0196ba87-e691-7757-a7dd-b3fcba14451c", "req_reputation": 0, "req_class_points": 0, "req_quests": "", "quest_string_index": -1, "quest_string_value": 0, "meta": "", "color": "ffffff", "pid": 55}, {"id": "0196ba87-e697-763b-a8f8-5a09e85f62a5", "name": "Riptide Blade", "description": "Everybody needs to get away after a long session of waging war;boss battles;and hauling home loot… and a hidden island retreat is the BEST place to /rest. recoup;and re-energize.", "type": "Sword", "element": "None", "file": "items/swords/Riptide.swf", "link": "Riptide", "icon": "iwsword", "equipment": "Weapon", "level": 1, "dps": 25, "range": 50, "rarity": "0196ba87-e69e-75ac-9885-91ec33cb57dc", "quantity": 1, "stack": 1, "cost": 75000, "coins": 0, "diamonds": 0, "crystal": 0, "sell": true, "market": true, "temporary": false, "upgrade": false, "staff": false, "enh_id": "0196ba87-e691-7757-a7dd-b3fcba14451c", "req_reputation": 0, "req_class_points": 0, "req_quests": "", "quest_string_index": -1, "quest_string_value": 0, "meta": "", "color": "ffffff", "pid": 56}, {"id": "0196ba87-e697-763b-a8f8-5cf01bd1ffa1", "name": "Summer Rescuer <PERSON><PERSON>", "description": "Citizens Lifeguards are encouraged by the Crystallis Administration to follow all stated rules and procedures before attempting rescue. Failure to comply will result in censure or sanctioning.", "type": "Sword", "element": "None", "file": "items/swords/CrystallisSummerRescuerOath.swf", "link": "CrystallisSummerRescuerOath", "icon": "iwsword", "equipment": "Weapon", "level": 1, "dps": 25, "range": 50, "rarity": "0196ba87-e69e-75ac-9885-91ec33cb57dc", "quantity": 1, "stack": 1, "cost": 350, "coins": 1, "diamonds": 0, "crystal": 0, "sell": true, "market": true, "temporary": false, "upgrade": false, "staff": false, "enh_id": "0196ba87-e691-7757-a7dd-b3fcba14451c", "req_reputation": 0, "req_class_points": 0, "req_quests": "", "quest_string_index": -1, "quest_string_value": 0, "meta": "", "color": "ffffff", "pid": 57}, {"id": "0196ba87-e697-763b-a8f8-63898f18ad5e", "name": "<PERSON><PERSON> Of Doom", "description": "Even Evil heroes (maybe ESPECIALLY Evil heroes) need to chill out after battle. With your Icy Blade of Doom;you can cool off without sacrificing that battle-ready edge.", "type": "<PERSON>gger", "element": "None", "file": "items/daggers/IcyBladeofDoom.swf", "link": "IcyBladeofDoom", "icon": "iwda<PERSON>", "equipment": "Weapon", "level": 1, "dps": 25, "range": 50, "rarity": "0196ba87-e69e-75ac-9885-91ec33cb57dc", "quantity": 1, "stack": 1, "cost": 100000, "coins": 0, "diamonds": 0, "crystal": 0, "sell": true, "market": true, "temporary": false, "upgrade": true, "staff": false, "enh_id": "0196ba87-e691-7757-a7dd-b3fcba14451c", "req_reputation": 0, "req_class_points": 0, "req_quests": "", "quest_string_index": -1, "quest_string_value": 0, "meta": "", "color": "ffffff", "pid": 58}, {"id": "0196ba87-e697-763b-a8f8-66208947301b", "name": "Riptide Blade", "description": "Everybody needs to get away after a long session of waging war;boss battles;and hauling home loot… and a hidden island retreat is the BEST place to /rest. recoup;and re-energize.", "type": "<PERSON>gger", "element": "None", "file": "items/daggers/Riptide.swf", "link": "Riptide", "icon": "iwda<PERSON>", "equipment": "Weapon", "level": 1, "dps": 25, "range": 50, "rarity": "0196ba87-e69e-75ac-9885-91ec33cb57dc", "quantity": 1, "stack": 1, "cost": 75000, "coins": 0, "diamonds": 0, "crystal": 0, "sell": true, "market": true, "temporary": false, "upgrade": true, "staff": false, "enh_id": "0196ba87-e691-7757-a7dd-b3fcba14451c", "req_reputation": 0, "req_class_points": 0, "req_quests": "", "quest_string_index": -1, "quest_string_value": 0, "meta": "", "color": "ffffff", "pid": 59}, {"id": "0196ba87-e697-763b-a8f8-699e238de538", "name": "Molten GreatSwords", "description": "Pyroclastic necromancers walk where the faint-hearted fear to tread;seeking undead minions among those who've perished in the hearts of volcanic lands.", "type": "<PERSON>gger", "element": "None", "file": "items/daggers/MoltenCalamityGreatSword.swf", "link": "MoltenCalamityGreatSword", "icon": "iwda<PERSON>", "equipment": "Weapon", "level": 1, "dps": 25, "range": 50, "rarity": "0196ba87-e69e-75ac-9885-91ec33cb57dc", "quantity": 1, "stack": 1, "cost": 450, "coins": 1, "diamonds": 0, "crystal": 0, "sell": true, "market": true, "temporary": false, "upgrade": false, "staff": false, "enh_id": "0196ba87-e691-7757-a7dd-b3fcba14451c", "req_reputation": 0, "req_class_points": 0, "req_quests": "", "quest_string_index": -1, "quest_string_value": 0, "meta": "", "color": "ffffff", "pid": 60}, {"id": "0196ba87-e697-763b-a8f8-6e969a4a4bfe", "name": "Claws of the Pyroclastic Necromancer", "description": "Pyroclastic necromancers walk where the faint-hearted fear to tread;seeking undead minions among those who've perished in the hearts of volcanic lands.", "type": "Gauntlet", "element": "None", "file": "items/gauntlets/HandofCalamity.swf", "link": "HandofCalamity", "icon": "iwclaws", "equipment": "Weapon", "level": 1, "dps": 25, "range": 50, "rarity": "0196ba87-e69e-75ac-9885-91ec33cb57dc", "quantity": 1, "stack": 1, "cost": 400, "coins": 1, "diamonds": 0, "crystal": 0, "sell": true, "market": true, "temporary": false, "upgrade": false, "staff": false, "enh_id": "0196ba87-e691-7757-a7dd-b3fcba14451c", "req_reputation": 0, "req_class_points": 0, "req_quests": "", "quest_string_index": -1, "quest_string_value": 0, "meta": "", "color": "ffffff", "pid": 61}, {"id": "0196ba87-e697-763b-a8f8-71d9c1fdda4a", "name": "Claws of the Pyroclastic Necromancer", "description": "Pyroclastic necromancers walk where the faint-hearted fear to tread;seeking undead minions among those who've perished in the hearts of volcanic lands.", "type": "Gauntlet", "element": "None", "file": "items/gauntlets/ClawsofCalamity.swf", "link": "ClawsofCalamity", "icon": "iwclaws", "equipment": "Weapon", "level": 1, "dps": 25, "range": 50, "rarity": "0196ba87-e69e-75ac-9885-91ec33cb57dc", "quantity": 1, "stack": 1, "cost": 400, "coins": 1, "diamonds": 0, "crystal": 0, "sell": true, "market": true, "temporary": false, "upgrade": false, "staff": false, "enh_id": "0196ba87-e691-7757-a7dd-b3fcba14451c", "req_reputation": 0, "req_class_points": 0, "req_quests": "", "quest_string_index": -1, "quest_string_value": 0, "meta": "", "color": "ffffff", "pid": 62}, {"id": "0196ba87-e697-763b-a8f8-75e845254500", "name": "Crystallis Summer Salvation", "description": "Citizens Lifeguards are encouraged by the Crystallis Administration to follow all stated rules and procedures before attempting rescue. Failure to comply will result in censure or sanctioning.", "type": "Gauntlet", "element": "None", "file": "items/gauntlets/CrystallisSummerSaver.swf", "link": "CrystallisSummerSaver", "icon": "iwda<PERSON>", "equipment": "Weapon", "level": 1, "dps": 25, "range": 50, "rarity": "0196ba87-e69e-75ac-9885-91ec33cb57dc", "quantity": 1, "stack": 1, "cost": 350, "coins": 1, "diamonds": 0, "crystal": 0, "sell": true, "market": true, "temporary": false, "upgrade": false, "staff": false, "enh_id": "0196ba87-e691-7757-a7dd-b3fcba14451c", "req_reputation": 0, "req_class_points": 0, "req_quests": "", "quest_string_index": -1, "quest_string_value": 0, "meta": "", "color": "ffffff", "pid": 63}, {"id": "0196ba87-e697-763b-a8f8-7a34c130517f", "name": "Crystallis Summer Salvation", "description": "Citizens Lifeguards are encouraged by the Crystallis Administration to follow all stated rules and procedures before attempting rescue. Failure to comply will result in censure or sanctioning.", "type": "Gauntlet", "element": "None", "file": "items/gauntlets/CrystallisSummerSaver.swf", "link": "CrystallisSummerSaver", "icon": "iwda<PERSON>", "equipment": "Weapon", "level": 1, "dps": 25, "range": 50, "rarity": "0196ba87-e69e-75ac-9885-91ec33cb57dc", "quantity": 1, "stack": 1, "cost": 75000, "coins": 0, "diamonds": 0, "crystal": 0, "sell": true, "market": true, "temporary": false, "upgrade": true, "staff": false, "enh_id": "0196ba87-e691-7757-a7dd-b3fcba14451c", "req_reputation": 0, "req_class_points": 0, "req_quests": "", "quest_string_index": -1, "quest_string_value": 0, "meta": "", "color": "ffffff", "pid": 64}, {"id": "0196ba87-e697-763b-a8f8-7e3fb20d57d1", "name": "Pyroclastic Necromancer's Whip", "description": "Pyroclastic necromancers walk where the faint-hearted fear to tread;seeking undead minions among those who've perished in the hearts of volcanic lands.", "type": "Mace", "element": "None", "file": "items/maces/CalamityWhip.swf", "link": "CalamityWhip", "icon": "iw<PERSON><PERSON>", "equipment": "Weapon", "level": 1, "dps": 25, "range": 50, "rarity": "0196ba87-e69e-75ac-9885-91ec33cb57dc", "quantity": 1, "stack": 1, "cost": 350, "coins": 1, "diamonds": 0, "crystal": 0, "sell": true, "market": true, "temporary": false, "upgrade": false, "staff": false, "enh_id": "0196ba87-e691-7757-a7dd-b3fcba14451c", "req_reputation": 0, "req_class_points": 0, "req_quests": "", "quest_string_index": -1, "quest_string_value": 0, "meta": "", "color": "ffffff", "pid": 65}, {"id": "0196ba87-e697-763b-a8f8-8257a94fd915", "name": "Bow of Embers", "description": "This bow as discovered by a Pyroclastic Necromancer as they journeyed deep into the heart of a volcano;hunting for new minions.", "type": "Gun", "element": "None", "file": "items/bows/BowofEmbers.swf", "link": "BowofEmbers", "icon": "iwbow", "equipment": "Weapon", "level": 1, "dps": 25, "range": 50, "rarity": "0196ba87-e69e-75ac-9885-91ec33cb57dc", "quantity": 1, "stack": 1, "cost": 350, "coins": 1, "diamonds": 0, "crystal": 0, "sell": true, "market": true, "temporary": false, "upgrade": false, "staff": false, "enh_id": "0196ba87-e691-7757-a7dd-b3fcba14451c", "req_reputation": 0, "req_class_points": 0, "req_quests": "", "quest_string_index": -1, "quest_string_value": 0, "meta": "", "color": "ffffff", "pid": 66}, {"id": "0196ba87-e697-763b-a8f8-850166b018da", "name": "Oceanic Symphony Cane", "description": "Still waters run deep;but those who chance to adventure below will find a world of magic;mystery;and beauty.", "type": "Staff", "element": "None", "file": "items/staves/ClassyOceanCane.swf", "link": "ClassyOceanCane", "icon": "iwstaff", "equipment": "Weapon", "level": 1, "dps": 25, "range": 50, "rarity": "0196ba87-e69e-75ac-9885-91ec33cb57dc", "quantity": 1, "stack": 1, "cost": 50000, "coins": 0, "diamonds": 0, "crystal": 0, "sell": true, "market": true, "temporary": false, "upgrade": false, "staff": false, "enh_id": "0196ba87-e691-7757-a7dd-b3fcba14451c", "req_reputation": 0, "req_class_points": 0, "req_quests": "", "quest_string_index": -1, "quest_string_value": 0, "meta": "", "color": "ffffff", "pid": 67}, {"id": "0196ba87-e697-763b-a8f8-8aaa4598b2f8", "name": "Pyroclastic Necromancer", "description": "Pyroclastic necromancers walk where the faint-hearted fear to tread;seeking undead minions among those who've perished in the hearts of volcanic lands.", "type": "Armor", "element": "None", "file": "CalamityNecromancer.swf", "link": "CalamityNecromancer", "icon": "i<PERSON>or", "equipment": "co", "level": 1, "dps": 25, "range": 50, "rarity": "0196ba87-e69e-75ac-9885-91ec33cb57dc", "quantity": 1, "stack": 1, "cost": 1000, "coins": 1, "diamonds": 0, "crystal": 0, "sell": true, "market": true, "temporary": false, "upgrade": false, "staff": false, "enh_id": "0196ba87-e691-7757-a7dd-b3fcba14451c", "req_reputation": 0, "req_class_points": 0, "req_quests": "", "quest_string_index": -1, "quest_string_value": 0, "meta": "", "color": "ffffff", "pid": 68}, {"id": "0196ba87-e697-763b-a8f8-8d57e43e211c", "name": "Oceanic Symphony Armor", "description": "Still waters run deep;but those who chance to adventure below will find a world of magic;mystery;and beauty.", "type": "Armor", "element": "None", "file": "ClassyOcean.swf", "link": "ClassyOcean", "icon": "i<PERSON>or", "equipment": "co", "level": 1, "dps": 25, "range": 50, "rarity": "0196ba87-e69e-75ac-9885-a05920d0e5e9", "quantity": 1, "stack": 1, "cost": 750, "coins": 1, "diamonds": 0, "crystal": 0, "sell": true, "market": true, "temporary": false, "upgrade": false, "staff": false, "enh_id": "0196ba87-e691-7757-a7dd-b3fcba14451c", "req_reputation": 0, "req_class_points": 0, "req_quests": "", "quest_string_index": -1, "quest_string_value": 0, "meta": "", "color": "ffffff", "pid": 69}, {"id": "0196ba87-e697-763b-a8f8-9261dc8c90ec", "name": "Crystallis Casual Rescuer", "description": "The design of this official off-duty Citizen Lifeguard gear has been approved by the Department of Outfit Inspection and Couture Correction. (This item is color-customizable to trim and accessory)", "type": "Armor", "element": "None", "file": "CrystallisRescuerCasual.swf", "link": "CrystallisRescuerCasual", "icon": "i<PERSON>or", "equipment": "co", "level": 1, "dps": 25, "range": 50, "rarity": "0196ba87-e69e-75ac-9885-91ec33cb57dc", "quantity": 1, "stack": 1, "cost": 1000, "coins": 1, "diamonds": 0, "crystal": 0, "sell": true, "market": true, "temporary": false, "upgrade": false, "staff": false, "enh_id": "0196ba87-e691-7757-a7dd-b3fcba14451c", "req_reputation": 0, "req_class_points": 0, "req_quests": "", "quest_string_index": -1, "quest_string_value": 0, "meta": "", "color": "ffffff", "pid": 70}, {"id": "0196ba87-e697-763b-a8f8-9601fd564b18", "name": "Crystallis Summer Rescuer", "description": "Citizens Lifeguards are encouraged by the Crystallis Administration to follow all stated rules and procedures before attempting rescue. Failure to comply will result in censure or sanctioning.", "type": "Armor", "element": "None", "file": "CrystallisSummerRescuer1.swf", "link": "CrystallisSummer<PERSON><PERSON><PERSON>", "icon": "i<PERSON>or", "equipment": "co", "level": 1, "dps": 25, "range": 50, "rarity": "0196ba87-e69e-75ac-9885-91ec33cb57dc", "quantity": 1, "stack": 1, "cost": 150000, "coins": 0, "diamonds": 0, "crystal": 0, "sell": true, "market": true, "temporary": false, "upgrade": true, "staff": false, "enh_id": "0196ba87-e691-7757-a7dd-b3fcba14451c", "req_reputation": 0, "req_class_points": 0, "req_quests": "", "quest_string_index": -1, "quest_string_value": 0, "meta": "", "color": "ffffff", "pid": 71}, {"id": "0196ba87-e697-763b-a8f8-9b5ed3605c38", "name": "Crystallis Summer Rescuer", "description": "Citizens Lifeguards are encouraged by the Crystallis Administration to follow all stated rules and procedures before attempting rescue. Failure to comply will result in censure or sanctioning.", "type": "Armor", "element": "None", "file": "CrystallisSummerRescuer1.swf", "link": "CrystallisSummer<PERSON><PERSON><PERSON>", "icon": "i<PERSON>or", "equipment": "co", "level": 1, "dps": 25, "range": 50, "rarity": "0196ba87-e69e-75ac-9885-91ec33cb57dc", "quantity": 1, "stack": 1, "cost": 1000, "coins": 1, "diamonds": 0, "crystal": 0, "sell": true, "market": true, "temporary": false, "upgrade": false, "staff": false, "enh_id": "0196ba87-e691-7757-a7dd-b3fcba14451c", "req_reputation": 0, "req_class_points": 0, "req_quests": "", "quest_string_index": -1, "quest_string_value": 0, "meta": "", "color": "ffffff", "pid": 72}, {"id": "0196ba87-e697-763b-a8f8-9e417c742442", "name": "Island Retreat Outfit", "description": "Everybody needs to get away after a long season of waging war;boss battles;and hauling home loot… and a hidden island retreat is the BEST place to /rent;recoup;and re-energize. (Item is color-customizable to base and trim)", "type": "Armor", "element": "None", "file": "LoneIslander1.swf", "link": "LoneIslander", "icon": "i<PERSON>or", "equipment": "co", "level": 1, "dps": 25, "range": 50, "rarity": "0196ba87-e69e-75ac-9885-91ec33cb57dc", "quantity": 1, "stack": 1, "cost": 900, "coins": 1, "diamonds": 0, "crystal": 0, "sell": true, "market": true, "temporary": false, "upgrade": false, "staff": false, "enh_id": "0196ba87-e691-7757-a7dd-b3fcba14451c", "req_reputation": 0, "req_class_points": 0, "req_quests": "", "quest_string_index": -1, "quest_string_value": 0, "meta": "", "color": "ffffff", "pid": 73}, {"id": "0196ba87-e697-763b-a8f8-a20bd433b7e6", "name": "Prismatic Starry Swimsuit", "description": "As you stare across the water;stars reflect on its surface;an ocean of diamond-like points of light. This suit lets you capture the magic of that moment. (This armor is color-customizable to base)", "type": "Armor", "element": "None", "file": "StarrySwimsuit1CC.swf", "link": "StarrySwimsuit1CC", "icon": "i<PERSON>or", "equipment": "co", "level": 1, "dps": 25, "range": 50, "rarity": "0196ba87-e69e-75ac-9885-91ec33cb57dc", "quantity": 1, "stack": 1, "cost": 850, "coins": 1, "diamonds": 0, "crystal": 0, "sell": true, "market": true, "temporary": false, "upgrade": false, "staff": false, "enh_id": "0196ba87-e691-7757-a7dd-b3fcba14451c", "req_reputation": 0, "req_class_points": 0, "req_quests": "", "quest_string_index": -1, "quest_string_value": 0, "meta": "", "color": "ffffff", "pid": 74}, {"id": "0196ba87-e697-763b-a8f8-a5b06059cb91", "name": "Prismatic Starry Swimwear", "description": "As you stare across the water;stars reflect on its surface;an ocean of diamond-like points of light. This suit lets you capture the magic of that moment. (This armor is color-customizable to base).", "type": "Armor", "element": "None", "file": "StarrySwimsuit2CC.swf", "link": "StarrySwimsuit2CC", "icon": "i<PERSON>or", "equipment": "co", "level": 1, "dps": 25, "range": 50, "rarity": "0196ba87-e69e-75ac-9885-91ec33cb57dc", "quantity": 1, "stack": 1, "cost": 850, "coins": 1, "diamonds": 0, "crystal": 0, "sell": true, "market": true, "temporary": false, "upgrade": false, "staff": false, "enh_id": "0196ba87-e691-7757-a7dd-b3fcba14451c", "req_reputation": 0, "req_class_points": 0, "req_quests": "", "quest_string_index": -1, "quest_string_value": 0, "meta": "", "color": "ffffff", "pid": 75}, {"id": "0196ba87-e697-763b-a8f8-a8a31d7e1462", "name": "Starry Swimsuit", "description": "As you stare across the water;stars reflect on its surface;an ocean of diamond-like points of light. This suit lets you capture the magic of that moment.", "type": "Armor", "element": "None", "file": "StarrySwimsuit1r2.swf", "link": "StarrySwimsuit1", "icon": "i<PERSON>or", "equipment": "co", "level": 1, "dps": 25, "range": 50, "rarity": "0196ba87-e69e-75ac-9885-91ec33cb57dc", "quantity": 1, "stack": 1, "cost": 750, "coins": 1, "diamonds": 0, "crystal": 0, "sell": true, "market": true, "temporary": false, "upgrade": false, "staff": false, "enh_id": "0196ba87-e691-7757-a7dd-b3fcba14451c", "req_reputation": 0, "req_class_points": 0, "req_quests": "", "quest_string_index": -1, "quest_string_value": 0, "meta": "", "color": "ffffff", "pid": 76}, {"id": "0196ba87-e697-763b-a8f8-ae5667341bf2", "name": "Starry Swimwear", "description": "As you stare across the water;stars reflect on its surface;an ocean of diamond-like points of light. This suit lets you capture the magic of that moment.", "type": "Armor", "element": "None", "file": "StarrySwimsuit2r4.swf", "link": "StarrySwimsuit2", "icon": "i<PERSON>or", "equipment": "co", "level": 1, "dps": 25, "range": 50, "rarity": "0196ba87-e69e-75ac-9885-91ec33cb57dc", "quantity": 1, "stack": 1, "cost": 750, "coins": 1, "diamonds": 0, "crystal": 0, "sell": true, "market": true, "temporary": false, "upgrade": false, "staff": false, "enh_id": "0196ba87-e691-7757-a7dd-b3fcba14451c", "req_reputation": 0, "req_class_points": 0, "req_quests": "", "quest_string_index": -1, "quest_string_value": 0, "meta": "", "color": "ffffff", "pid": 77}, {"id": "0196ba87-e697-763b-a8f8-b0fe7447b845", "name": "Pyroclastic Necromancer's Bangs", "description": "Pyroclastic necromancers walk where the faint-hearted fear to tread;seeking undead minions among those who've perished in the hearts of volcanic lands.", "type": "<PERSON><PERSON>", "element": "None", "file": "items/helms/CalamityBangs.swf", "link": "CalamityBangs", "icon": "iihelm", "equipment": "he", "level": 1, "dps": 25, "range": 50, "rarity": "0196ba87-e69e-75ac-9885-91ec33cb57dc", "quantity": 1, "stack": 1, "cost": 25000, "coins": 0, "diamonds": 0, "crystal": 0, "sell": true, "market": true, "temporary": false, "upgrade": false, "staff": false, "enh_id": "0196ba87-e691-7757-a7dd-b3fcba14451c", "req_reputation": 0, "req_class_points": 0, "req_quests": "", "quest_string_index": -1, "quest_string_value": 0, "meta": "", "color": "ffffff", "pid": 78}, {"id": "0196ba87-e697-763b-a8f8-b70f80150e19", "name": "Pyroclastic Necromancer's Hair", "description": "Pyroclastic necromancers walk where the faint-hearted fear to tread;seeking undead minions among those who've perished in the hearts of volcanic lands.", "type": "<PERSON><PERSON>", "element": "None", "file": "items/helms/CalamityHair.swf", "link": "CalamityHair", "icon": "iihelm", "equipment": "he", "level": 1, "dps": 25, "range": 50, "rarity": "0196ba87-e69e-75ac-9885-91ec33cb57dc", "quantity": 1, "stack": 1, "cost": 25000, "coins": 0, "diamonds": 0, "crystal": 0, "sell": true, "market": true, "temporary": false, "upgrade": false, "staff": false, "enh_id": "0196ba87-e691-7757-a7dd-b3fcba14451c", "req_reputation": 0, "req_class_points": 0, "req_quests": "", "quest_string_index": -1, "quest_string_value": 0, "meta": "", "color": "ffffff", "pid": 79}, {"id": "0196ba87-e697-763b-a8f8-b8a3ab109e7e", "name": "Pyroclastic Necromancer's Hood", "description": "Pyroclastic necromancers walk where the faint-hearted fear to tread;seeking undead minions among those who've perished in the hearts of volcanic lands.", "type": "<PERSON><PERSON>", "element": "None", "file": "items/helms/CalamityHood.swf", "link": "CalamityHood", "icon": "iihelm", "equipment": "he", "level": 1, "dps": 25, "range": 50, "rarity": "0196ba87-e69e-75ac-9885-91ec33cb57dc", "quantity": 1, "stack": 1, "cost": 45000, "coins": 0, "diamonds": 0, "crystal": 0, "sell": true, "market": true, "temporary": false, "upgrade": false, "staff": false, "enh_id": "0196ba87-e691-7757-a7dd-b3fcba14451c", "req_reputation": 0, "req_class_points": 0, "req_quests": "", "quest_string_index": -1, "quest_string_value": 0, "meta": "", "color": "ffffff", "pid": 80}, {"id": "0196ba87-e697-763b-a8f8-bf5058e3d120", "name": "Pyroclastic Female Visage", "description": "Pyroclastic necromancers walk where the faint-hearted fear to tread;seeking undead minions among those who've perished in the hearts of volcanic lands.", "type": "<PERSON><PERSON>", "element": "None", "file": "items/helms/CalamityVisageF.swf", "link": "CalamityVisageF", "icon": "iihelm", "equipment": "he", "level": 1, "dps": 25, "range": 50, "rarity": "0196ba87-e69e-75ac-9885-91ec33cb57dc", "quantity": 1, "stack": 1, "cost": 25000, "coins": 0, "diamonds": 0, "crystal": 0, "sell": true, "market": true, "temporary": false, "upgrade": true, "staff": false, "enh_id": "0196ba87-e691-7757-a7dd-b3fcba14451c", "req_reputation": 0, "req_class_points": 0, "req_quests": "", "quest_string_index": -1, "quest_string_value": 0, "meta": "", "color": "ffffff", "pid": 81}, {"id": "0196ba87-e697-763b-a8f8-c2961ecdf2eb", "name": "Pyroclastic Male Visage", "description": "Pyroclastic necromancers walk where the faint-hearted fear to tread;seeking undead minions among those who've perished in the hearts of volcanic lands.", "type": "<PERSON><PERSON>", "element": "None", "file": "items/helms/CalamityVisageM.swf", "link": "CalamityVisageM", "icon": "iihelm", "equipment": "he", "level": 1, "dps": 25, "range": 50, "rarity": "0196ba87-e69e-75ac-9885-91ec33cb57dc", "quantity": 1, "stack": 1, "cost": 25000, "coins": 0, "diamonds": 0, "crystal": 0, "sell": true, "market": true, "temporary": false, "upgrade": true, "staff": false, "enh_id": "0196ba87-e691-7757-a7dd-b3fcba14451c", "req_reputation": 0, "req_class_points": 0, "req_quests": "", "quest_string_index": -1, "quest_string_value": 0, "meta": "", "color": "ffffff", "pid": 82}, {"id": "0196ba87-e697-763b-a8f8-c6019d1b7148", "name": "Pyroclastic Necromancer's Rage", "description": "Pyroclastic necromancers walk where the faint-hearted fear to tread;seeking undead minions among those who've perished in the hearts of volcanic lands.", "type": "<PERSON><PERSON>", "element": "None", "file": "items/helms/ScreamofCalamity.swf", "link": "ScreamofCalamity", "icon": "iihelm", "equipment": "he", "level": 1, "dps": 25, "range": 50, "rarity": "0196ba87-e69e-75ac-9885-91ec33cb57dc", "quantity": 1, "stack": 1, "cost": 300, "coins": 1, "diamonds": 0, "crystal": 0, "sell": true, "market": true, "temporary": false, "upgrade": false, "staff": false, "enh_id": "0196ba87-e691-7757-a7dd-b3fcba14451c", "req_reputation": 0, "req_class_points": 0, "req_quests": "", "quest_string_index": -1, "quest_string_value": 0, "meta": "", "color": "ffffff", "pid": 83}, {"id": "0196ba87-e697-763b-a8f8-c84145cfefd7", "name": "Chill Island Lass' Hat", "description": "Everybody need to get away after a long season of waging war;boss battles;and hauling home loot… and a hidden island retreat is the BEST place to /rest;recoup;and re-energize.", "type": "<PERSON><PERSON>", "element": "None", "file": "items/helms/ChillLoneIslanderStrawHatF.swf", "link": "ChillLoneIslanderStrawHatF", "icon": "iihelm", "equipment": "he", "level": 1, "dps": 25, "range": 50, "rarity": "0196ba87-e69e-75ac-9885-91ec33cb57dc", "quantity": 1, "stack": 1, "cost": 250, "coins": 1, "diamonds": 0, "crystal": 0, "sell": true, "market": true, "temporary": false, "upgrade": false, "staff": false, "enh_id": "0196ba87-e691-7757-a7dd-b3fcba14451c", "req_reputation": 0, "req_class_points": 0, "req_quests": "", "quest_string_index": -1, "quest_string_value": 0, "meta": "", "color": "ffffff", "pid": 84}, {"id": "0196ba87-e697-763b-a8f8-ce46f1a9aac9", "name": "Chill Island Lad's Hat", "description": "Everybody need to get away after a long season of waging war;boss battles;and hauling home loot… and a hidden island retreat is the BEST place to /rest;recoup;and re-energize.", "type": "<PERSON><PERSON>", "element": "None", "file": "items/helms/ChillLoneIslanderStrawHatM.swf", "link": "ChillLoneIslanderStrawHatM", "icon": "iihelm", "equipment": "he", "level": 1, "dps": 25, "range": 50, "rarity": "0196ba87-e69e-75ac-9885-91ec33cb57dc", "quantity": 1, "stack": 1, "cost": 250, "coins": 1, "diamonds": 0, "crystal": 0, "sell": true, "market": true, "temporary": false, "upgrade": false, "staff": false, "enh_id": "0196ba87-e691-7757-a7dd-b3fcba14451c", "req_reputation": 0, "req_class_points": 0, "req_quests": "", "quest_string_index": -1, "quest_string_value": 0, "meta": "", "color": "ffffff", "pid": 85}, {"id": "0196ba87-e697-763b-a8f8-d28ad32508cd", "name": "Oceanic Accessories Top Hat + Locks", "description": "Still waters run deep;but those who chance to adventure below will find a world of magic;mystery;and beauty.", "type": "<PERSON><PERSON>", "element": "None", "file": "items/helms/ClassyOceanHatF.swf", "link": "ClassyOceanHatF", "icon": "iihelm", "equipment": "he", "level": 1, "dps": 25, "range": 50, "rarity": "0196ba87-e69e-75ac-9885-a05920d0e5e9", "quantity": 1, "stack": 1, "cost": 200, "coins": 1, "diamonds": 0, "crystal": 0, "sell": true, "market": true, "temporary": false, "upgrade": false, "staff": false, "enh_id": "0196ba87-e691-7757-a7dd-b3fcba14451c", "req_reputation": 0, "req_class_points": 0, "req_quests": "", "quest_string_index": -1, "quest_string_value": 0, "meta": "", "color": "ffffff", "pid": 86}, {"id": "0196ba87-e697-763b-a8f8-d40085224f37", "name": "Oceanic Accessories Top Hat", "description": "Still waters run deep;but those who chance to adventure below will find a world of magic;mystery;and beauty.", "type": "<PERSON><PERSON>", "element": "None", "file": "items/helms/ClassyOceanHatM.swf", "link": "ClassyOceanHatM", "icon": "iihelm", "equipment": "he", "level": 1, "dps": 25, "range": 50, "rarity": "0196ba87-e69e-75ac-9885-a05920d0e5e9", "quantity": 1, "stack": 1, "cost": 200, "coins": 1, "diamonds": 0, "crystal": 0, "sell": true, "market": true, "temporary": false, "upgrade": false, "staff": false, "enh_id": "0196ba87-e691-7757-a7dd-b3fcba14451c", "req_reputation": 0, "req_class_points": 0, "req_quests": "", "quest_string_index": -1, "quest_string_value": 0, "meta": "", "color": "ffffff", "pid": 87}, {"id": "0196ba87-e697-763b-a8f8-da113ee02754", "name": "Oceanic Accessories Locks", "description": "Still waters run deep;but those who chance to adventure below will find a world of magic;mystery;and beauty.", "type": "<PERSON><PERSON>", "element": "None", "file": "items/helms/ClassyOceanHairF.swf", "link": "ClassyOceanHairF", "icon": "iihelm", "equipment": "he", "level": 1, "dps": 25, "range": 50, "rarity": "0196ba87-e69e-75ac-9885-a05920d0e5e9", "quantity": 1, "stack": 1, "cost": 200, "coins": 1, "diamonds": 0, "crystal": 0, "sell": true, "market": true, "temporary": false, "upgrade": false, "staff": false, "enh_id": "0196ba87-e691-7757-a7dd-b3fcba14451c", "req_reputation": 0, "req_class_points": 0, "req_quests": "", "quest_string_index": -1, "quest_string_value": 0, "meta": "", "color": "ffffff", "pid": 88}, {"id": "0196ba87-e697-763b-a8f8-de7d20cb1825", "name": "Oceanic Accessories Hair", "description": "Still waters run deep;but those who chance to adventure below will find a world of magic;mystery;and beauty.", "type": "<PERSON><PERSON>", "element": "None", "file": "items/helms/ClassyOceanHairM.swf", "link": "ClassyOceanHairM", "icon": "iihelm", "equipment": "he", "level": 1, "dps": 25, "range": 50, "rarity": "0196ba87-e69e-75ac-9885-a05920d0e5e9", "quantity": 1, "stack": 1, "cost": 200, "coins": 1, "diamonds": 0, "crystal": 0, "sell": true, "market": true, "temporary": false, "upgrade": false, "staff": false, "enh_id": "0196ba87-e691-7757-a7dd-b3fcba14451c", "req_reputation": 0, "req_class_points": 0, "req_quests": "", "quest_string_index": -1, "quest_string_value": 0, "meta": "", "color": "ffffff", "pid": 89}, {"id": "0196ba87-e697-763b-a8f8-e3b8e3acf040", "name": "Island Retreat Hair", "description": "Everybody needs to get away after a long season of waging war;boss battles;and hauling home loot… and a hidden island retreat is the BEST place to /rest;recoup;and re-energize.", "type": "<PERSON><PERSON>", "element": "None", "file": "items/helms/LoneIslanderHair.swf", "link": "LoneIslanderHair", "icon": "iihelm", "equipment": "he", "level": 1, "dps": 25, "range": 50, "rarity": "0196ba87-e69e-75ac-9885-91ec33cb57dc", "quantity": 1, "stack": 1, "cost": 25000, "coins": 0, "diamonds": 0, "crystal": 0, "sell": true, "market": true, "temporary": false, "upgrade": false, "staff": false, "enh_id": "0196ba87-e691-7757-a7dd-b3fcba14451c", "req_reputation": 0, "req_class_points": 0, "req_quests": "", "quest_string_index": -1, "quest_string_value": 0, "meta": "", "color": "ffffff", "pid": 90}, {"id": "0196ba87-e697-763b-a8f8-e50a2f19c002", "name": "Island Retreat Lad's Hat", "description": "Everybody needs to get away after a long season of waging war;boss battles;and hauling home loot… and a hidden island retreat is the BEST place to /rest;recoup;and re-energize.", "type": "<PERSON><PERSON>", "element": "None", "file": "items/helms/LoneIslanderStrawHatM.swf", "link": "LoneIslanderStrawHatM", "icon": "iihelm", "equipment": "he", "level": 1, "dps": 25, "range": 50, "rarity": "0196ba87-e69e-75ac-9885-91ec33cb57dc", "quantity": 1, "stack": 1, "cost": 350000, "coins": 0, "diamonds": 0, "crystal": 0, "sell": true, "market": true, "temporary": false, "upgrade": true, "staff": false, "enh_id": "0196ba87-e691-7757-a7dd-b3fcba14451c", "req_reputation": 0, "req_class_points": 0, "req_quests": "", "quest_string_index": -1, "quest_string_value": 0, "meta": "", "color": "ffffff", "pid": 91}, {"id": "0196ba87-e697-763b-a8f8-ebb9ac401fe4", "name": "Island Retreat Lass' Hat", "description": "Everybody needs to get away after a long season of waging war;boss battles;and hauling home loot… and a hidden island retreat is the BEST place to /rest;recoup;and re-energize.", "type": "<PERSON><PERSON>", "element": "None", "file": "items/helms/LoneIslanderStrawHatF.swf", "link": "LoneIslanderStrawHatF", "icon": "iihelm", "equipment": "he", "level": 1, "dps": 25, "range": 50, "rarity": "0196ba87-e69e-75ac-9885-91ec33cb57dc", "quantity": 1, "stack": 1, "cost": 35000, "coins": 0, "diamonds": 0, "crystal": 0, "sell": true, "market": true, "temporary": false, "upgrade": true, "staff": false, "enh_id": "0196ba87-e691-7757-a7dd-b3fcba14451c", "req_reputation": 0, "req_class_points": 0, "req_quests": "", "quest_string_index": -1, "quest_string_value": 0, "meta": "", "color": "ffffff", "pid": 92}, {"id": "0196ba87-e697-763b-a8f8-ec676fe91d56", "name": "Island Retreat Locks", "description": "Everybody needs to get away after a long season of waging war;boss battles;and hauling home loot… and a hidden island retreat is the BEST place to /rest;recoup;and re-energize.", "type": "<PERSON><PERSON>", "element": "None", "file": "items/helms/LoneIslanderLocks.swf", "link": "LoneIslanderLocks", "icon": "iihelm", "equipment": "he", "level": 1, "dps": 25, "range": 50, "rarity": "0196ba87-e69e-75ac-9885-91ec33cb57dc", "quantity": 1, "stack": 1, "cost": 25000, "coins": 0, "diamonds": 0, "crystal": 0, "sell": true, "market": true, "temporary": false, "upgrade": false, "staff": false, "enh_id": "0196ba87-e691-7757-a7dd-b3fcba14451c", "req_reputation": 0, "req_class_points": 0, "req_quests": "", "quest_string_index": -1, "quest_string_value": 0, "meta": "", "color": "ffffff", "pid": 93}, {"id": "0196ba87-e697-763b-a8f8-f106d47c78f7", "name": "Island Retreat Morph Locks", "description": "Everybody needs to get away after a long season of waging war;boss battles;and hauling home loot… and a hidden island retreat is the BEST place to /rest;recoup;and re-energize.", "type": "<PERSON><PERSON>", "element": "None", "file": "items/helms/LoneIslanderMorphF.swf", "link": "LoneIslanderMorphF", "icon": "iihelm", "equipment": "he", "level": 1, "dps": 25, "range": 50, "rarity": "0196ba87-e69e-75ac-9885-91ec33cb57dc", "quantity": 1, "stack": 1, "cost": 200, "coins": 1, "diamonds": 0, "crystal": 0, "sell": true, "market": true, "temporary": false, "upgrade": false, "staff": false, "enh_id": "0196ba87-e691-7757-a7dd-b3fcba14451c", "req_reputation": 0, "req_class_points": 0, "req_quests": "", "quest_string_index": -1, "quest_string_value": 0, "meta": "", "color": "ffffff", "pid": 94}, {"id": "0196ba87-e697-763b-a8f8-f77d47ca12ee", "name": "Island Retreat Morph Hair", "description": "Everybody needs to get away after a long season of waging war;boss battles;and hauling home loot… and a hidden island retreat is the BEST place to /rest;recoup;and re-energize.", "type": "<PERSON><PERSON>", "element": "None", "file": "items/helms/LoneIslanderMorphM.swf", "link": "LoneIslanderMorphM", "icon": "iihelm", "equipment": "he", "level": 1, "dps": 25, "range": 50, "rarity": "0196ba87-e69e-75ac-9885-91ec33cb57dc", "quantity": 1, "stack": 1, "cost": 200, "coins": 1, "diamonds": 0, "crystal": 0, "sell": true, "market": true, "temporary": false, "upgrade": false, "staff": false, "enh_id": "0196ba87-e691-7757-a7dd-b3fcba14451c", "req_reputation": 0, "req_class_points": 0, "req_quests": "", "quest_string_index": -1, "quest_string_value": 0, "meta": "", "color": "ffffff", "pid": 95}, {"id": "0196ba87-e697-763b-a8f8-f8e73498caad", "name": "<PERSON>h", "description": "Everybody need to get away after a long season of waging war;boss battles;and hauling home loot… and a hidden island retreat is the BEST place to /rest;recoup;and re-energize. (Flag is color-customizable to trim)", "type": "<PERSON><PERSON>", "element": "None", "file": "items/helms/SandBuddyMorph.swf", "link": "SandBuddyMorph", "icon": "iihelm", "equipment": "he", "level": 1, "dps": 25, "range": 50, "rarity": "0196ba87-e69e-75ac-9885-91ec33cb57dc", "quantity": 1, "stack": 1, "cost": 25000, "coins": 0, "diamonds": 0, "crystal": 0, "sell": true, "market": true, "temporary": false, "upgrade": false, "staff": false, "enh_id": "0196ba87-e691-7757-a7dd-b3fcba14451c", "req_reputation": 0, "req_class_points": 0, "req_quests": "", "quest_string_index": -1, "quest_string_value": 0, "meta": "", "color": "ffffff", "pid": 96}, {"id": "0196ba87-e697-763b-a8f8-fea37ccc29c3", "name": "Starry <PERSON>", "description": "As you stare across the water;stars reflect on its surface;an ocean of diamond-like points of light. This suit lets you capture the magic of that moment.", "type": "<PERSON><PERSON>", "element": "None", "file": "items/helms/StarrySwimsuitHair1.swf", "link": "StarrySwimsuitHair1", "icon": "iihelm", "equipment": "he", "level": 1, "dps": 25, "range": 50, "rarity": "0196ba87-e69e-75ac-9885-91ec33cb57dc", "quantity": 1, "stack": 1, "cost": 150, "coins": 1, "diamonds": 0, "crystal": 0, "sell": true, "market": true, "temporary": false, "upgrade": false, "staff": false, "enh_id": "0196ba87-e691-7757-a7dd-b3fcba14451c", "req_reputation": 0, "req_class_points": 0, "req_quests": "", "quest_string_index": -1, "quest_string_value": 0, "meta": "", "color": "ffffff", "pid": 97}, {"id": "0196ba87-e697-763b-a8f9-01cc4a4c3949", "name": "Starry Summer Locks", "description": "As you stare across the water;stars reflect on its surface;an ocean of diamond-like points of light. This suit lets you capture the magic of that moment.", "type": "<PERSON><PERSON>", "element": "None", "file": "items/helms/StarrySwimsuitHair2.swf", "link": "StarrySwimsuitHair2", "icon": "iihelm", "equipment": "he", "level": 1, "dps": 25, "range": 50, "rarity": "0196ba87-e69e-75ac-9885-91ec33cb57dc", "quantity": 1, "stack": 1, "cost": 150, "coins": 1, "diamonds": 0, "crystal": 0, "sell": true, "market": true, "temporary": false, "upgrade": false, "staff": false, "enh_id": "0196ba87-e691-7757-a7dd-b3fcba14451c", "req_reputation": 0, "req_class_points": 0, "req_quests": "", "quest_string_index": -1, "quest_string_value": 0, "meta": "", "color": "ffffff", "pid": 98}, {"id": "0196ba87-e697-763b-a8f9-049ddc414b9b", "name": "Crystallis Rescuer Lad's Snorkel", "description": "Citizens Lifeguards are encouraged by the Crystallis Administration to follow all stated rules and procedures before attempting rescue. Failure to comply will result in censure or sanctioning. (Highlights are CC to Trim)", "type": "<PERSON><PERSON>", "element": "None", "file": "items/helms/CrystallisSummerRescuerSnorkelM.swf", "link": "CrystallisSummerRescuerSnorkelM", "icon": "iihelm", "equipment": "he", "level": 1, "dps": 25, "range": 50, "rarity": "0196ba87-e69e-75ac-9885-91ec33cb57dc", "quantity": 1, "stack": 1, "cost": 200, "coins": 1, "diamonds": 0, "crystal": 0, "sell": true, "market": true, "temporary": false, "upgrade": false, "staff": false, "enh_id": "0196ba87-e691-7757-a7dd-b3fcba14451c", "req_reputation": 0, "req_class_points": 0, "req_quests": "", "quest_string_index": -1, "quest_string_value": 0, "meta": "", "color": "ffffff", "pid": 99}, {"id": "0196ba87-e697-763b-a8f9-0a945fd64210", "name": "Crystallis Rescuer Lass' Snorkel", "description": "Citizens Lifeguards are encouraged by the Crystallis Administration to follow all stated rules and procedures before attempting rescue. Failure to comply will result in censure or sanctioning. (Highlights are CC to Trim)", "type": "<PERSON><PERSON>", "element": "None", "file": "items/helms/CrystallisSummerRescuerSnorkelF.swf", "link": "CrystallisSummerRescuerSnorkelF", "icon": "iihelm", "equipment": "he", "level": 1, "dps": 25, "range": 50, "rarity": "0196ba87-e69e-75ac-9885-91ec33cb57dc", "quantity": 1, "stack": 1, "cost": 200, "coins": 1, "diamonds": 0, "crystal": 0, "sell": true, "market": true, "temporary": false, "upgrade": false, "staff": false, "enh_id": "0196ba87-e691-7757-a7dd-b3fcba14451c", "req_reputation": 0, "req_class_points": 0, "req_quests": "", "quest_string_index": -1, "quest_string_value": 0, "meta": "", "color": "ffffff", "pid": 100}, {"id": "0196ba87-e697-763b-a8f9-0ff85d1f3424", "name": "Crystallis Rescuer Morph", "description": "Citizens Lifeguards are encouraged by the Crystallis Administration to follow all stated rules and procedures before attempting rescue. Failure to comply will result in censure or sanctioning. (Highlights are CC to Trim)", "type": "<PERSON><PERSON>", "element": "None", "file": "items/helms/CrystallisSummerRescuerMorphM.swf", "link": "CrystallisSummerRescuerMorphM", "icon": "iihelm", "equipment": "he", "level": 1, "dps": 25, "range": 50, "rarity": "0196ba87-e69e-75ac-9885-91ec33cb57dc", "quantity": 1, "stack": 1, "cost": 200, "coins": 1, "diamonds": 0, "crystal": 0, "sell": true, "market": true, "temporary": false, "upgrade": false, "staff": false, "enh_id": "0196ba87-e691-7757-a7dd-b3fcba14451c", "req_reputation": 0, "req_class_points": 0, "req_quests": "", "quest_string_index": -1, "quest_string_value": 0, "meta": "", "color": "ffffff", "pid": 101}, {"id": "0196ba87-e697-763b-a8f9-1300a5be280d", "name": "Crystallis Rescuer Morph + Locks", "description": "Citizens Lifeguards are encouraged by the Crystallis Administration to follow all stated rules and procedures before attempting rescue. Failure to comply will result in censure or sanctioning. (Highlights are CC to Trim)", "type": "<PERSON><PERSON>", "element": "None", "file": "items/helms/CrystallisSummerRescuerMorphF.swf", "link": "CrystallisSummerRescuerMorphF", "icon": "iihelm", "equipment": "he", "level": 1, "dps": 25, "range": 50, "rarity": "0196ba87-e69e-75ac-9885-91ec33cb57dc", "quantity": 1, "stack": 1, "cost": 200, "coins": 1, "diamonds": 0, "crystal": 0, "sell": true, "market": true, "temporary": false, "upgrade": false, "staff": false, "enh_id": "0196ba87-e691-7757-a7dd-b3fcba14451c", "req_reputation": 0, "req_class_points": 0, "req_quests": "", "quest_string_index": -1, "quest_string_value": 0, "meta": "", "color": "ffffff", "pid": 102}, {"id": "0196ba87-e697-763b-a8f9-15e038c22622", "name": "Crystallis Rescuer V<PERSON>or", "description": "Citizens Lifeguards are encouraged by the Crystallis Administration to follow all stated rules and procedures before attempting rescue. Failure to comply will result in censure or sanctioning. (Highlights are CC to Trim)", "type": "<PERSON><PERSON>", "element": "None", "file": "items/helms/CrystallisSummerRescuerVisor.swf", "link": "CrystallisSummerRescuerVisor", "icon": "iihelm", "equipment": "he", "level": 1, "dps": 25, "range": 50, "rarity": "0196ba87-e69e-75ac-9885-91ec33cb57dc", "quantity": 1, "stack": 1, "cost": 35000, "coins": 0, "diamonds": 0, "crystal": 0, "sell": true, "market": true, "temporary": false, "upgrade": true, "staff": false, "enh_id": "0196ba87-e691-7757-a7dd-b3fcba14451c", "req_reputation": 0, "req_class_points": 0, "req_quests": "", "quest_string_index": -1, "quest_string_value": 0, "meta": "", "color": "ffffff", "pid": 103}, {"id": "0196ba87-e697-763b-a8f9-18441a025476", "name": "Crystallis Rescuer V<PERSON>or", "description": "Citizens Lifeguards are encouraged by the Crystallis Administration to follow all stated rules and procedures before attempting rescue. Failure to comply will result in censure or sanctioning.", "type": "<PERSON><PERSON>", "element": "None", "file": "items/helms/CrystallisSummerRescuerVisor.swf", "link": "CrystallisSummerRescuerVisor", "icon": "iihelm", "equipment": "he", "level": 1, "dps": 25, "range": 50, "rarity": "0196ba87-e69e-75ac-9885-91ec33cb57dc", "quantity": 1, "stack": 1, "cost": 200, "coins": 1, "diamonds": 0, "crystal": 0, "sell": true, "market": true, "temporary": false, "upgrade": false, "staff": false, "enh_id": "0196ba87-e691-7757-a7dd-b3fcba14451c", "req_reputation": 0, "req_class_points": 0, "req_quests": "", "quest_string_index": -1, "quest_string_value": 0, "meta": "", "color": "ffffff", "pid": 104}, {"id": "0196ba87-e697-763b-a8f9-1c8617e226be", "name": "<PERSON>lis <PERSON> Ponytail", "description": "Citizens Lifeguards are encouraged by the Crystallis Administration to follow all stated rules and procedures before attempting rescue. Failure to comply will result in censure or sanctioning. (Highlights are CC to Trim)", "type": "<PERSON><PERSON>", "element": "None", "file": "items/helms/CrystallisSummerRescuerLocksTied.swf", "link": "CrystallisSummerRescuerLocksTied", "icon": "iihelm", "equipment": "he", "level": 1, "dps": 25, "range": 50, "rarity": "0196ba87-e69e-75ac-9885-91ec33cb57dc", "quantity": 1, "stack": 1, "cost": 50000, "coins": 0, "diamonds": 0, "crystal": 0, "sell": true, "market": true, "temporary": false, "upgrade": true, "staff": false, "enh_id": "0196ba87-e691-7757-a7dd-b3fcba14451c", "req_reputation": 0, "req_class_points": 0, "req_quests": "", "quest_string_index": -1, "quest_string_value": 0, "meta": "", "color": "ffffff", "pid": 105}, {"id": "0196ba87-e698-771d-b7f9-89499b44e546", "name": "<PERSON><PERSON> Summer Rescuer Hair", "description": "Citizens Lifeguards are encouraged by the Crystallis Administration to follow all stated rules and procedures before attempting rescue. Failure to comply will result in censure or sanctioning. (Highlights are CC to Trim)", "type": "<PERSON><PERSON>", "element": "None", "file": "items/helms/CrystallisSummerRescuerHair.swf", "link": "CrystallisSummerRescuerHair", "icon": "iihelm", "equipment": "he", "level": 1, "dps": 25, "range": 50, "rarity": "0196ba87-e69e-75ac-9885-91ec33cb57dc", "quantity": 1, "stack": 1, "cost": 50000, "coins": 0, "diamonds": 0, "crystal": 0, "sell": true, "market": true, "temporary": false, "upgrade": true, "staff": false, "enh_id": "0196ba87-e691-7757-a7dd-b3fcba14451c", "req_reputation": 0, "req_class_points": 0, "req_quests": "", "quest_string_index": -1, "quest_string_value": 0, "meta": "", "color": "ffffff", "pid": 106}, {"id": "0196ba87-e698-771d-b7f9-8f5b870d1e69", "name": "Crystallis Summer Tumbled Locks", "description": "Citizens Lifeguards are encouraged by the Crystallis Administration to follow all stated rules and procedures before attempting rescue. Failure to comply will result in censure or sanctioning. (Highlights are CC to Trim)", "type": "<PERSON><PERSON>", "element": "None", "file": "items/helms/CrystallisSummerRescuerLocksDown.swf", "link": "CrystallisSummerRescuerLocksDown", "icon": "iihelm", "equipment": "he", "level": 1, "dps": 25, "range": 50, "rarity": "0196ba87-e69e-75ac-9885-91ec33cb57dc", "quantity": 1, "stack": 1, "cost": 50000, "coins": 0, "diamonds": 0, "crystal": 0, "sell": true, "market": true, "temporary": false, "upgrade": true, "staff": false, "enh_id": "0196ba87-e691-7757-a7dd-b3fcba14451c", "req_reputation": 0, "req_class_points": 0, "req_quests": "", "quest_string_index": -1, "quest_string_value": 0, "meta": "", "color": "ffffff", "pid": 107}, {"id": "0196ba87-e698-771d-b7f9-93846cd861fc", "name": "Celestial Water Spirit", "description": "From deep below <PERSON><PERSON>'s surface;the neriads dance to honor the celestial water spirits who first gave them life.", "type": "Cape", "element": "None", "file": "items/capes/HeavenlyWaterSpirit1.swf", "link": "HeavenlyWaterSpirit", "icon": "iicape", "equipment": "ba", "level": 1, "dps": 25, "range": 50, "rarity": "0196ba87-e69e-75ac-9885-91ec33cb57dc", "quantity": 1, "stack": 1, "cost": 500, "coins": 1, "diamonds": 0, "crystal": 0, "sell": true, "market": true, "temporary": false, "upgrade": false, "staff": false, "enh_id": "0196ba87-e691-7757-a7dd-b3fcba14451c", "req_reputation": 0, "req_class_points": 0, "req_quests": "", "quest_string_index": -1, "quest_string_value": 0, "meta": "", "color": "ffffff", "pid": 108}, {"id": "0196ba87-e698-771d-b7f9-947f704c3ce3", "name": "Crystallis First Aid Kit Cape", "description": "Citizens Lifeguards are encouraged by the Crystallis Administration to follow all stated rules and procedures before attempting rescue. Failure to comply will result in censure of sanctioning.", "type": "Cape", "element": "None", "file": "items/capes/CrystallisAidKitCape.swf", "link": "CrystallisAidKitCape", "icon": "iicape", "equipment": "ba", "level": 1, "dps": 25, "range": 50, "rarity": "0196ba87-e69e-75ac-9885-91ec33cb57dc", "quantity": 1, "stack": 1, "cost": 15000, "coins": 0, "diamonds": 0, "crystal": 0, "sell": true, "market": true, "temporary": false, "upgrade": false, "staff": false, "enh_id": "0196ba87-e691-7757-a7dd-b3fcba14451c", "req_reputation": 0, "req_class_points": 0, "req_quests": "", "quest_string_index": -1, "quest_string_value": 0, "meta": "", "color": "ffffff", "pid": 109}, {"id": "0196ba87-e698-771d-b7f9-9b21a15d1a74", "name": "Floating Sand Buddy Cape", "description": "Everybody needs to get away after a long season of waging war;boss battles;and hauling some loot… and a hidden island retreat is the BEST place to /rest;recoup;and re-energize. (Item is color-customizable to trim and accessory)", "type": "Cape", "element": "None", "file": "items/capes/SandBuddy.swf", "link": "SandBuddy", "icon": "iicape", "equipment": "ba", "level": 1, "dps": 25, "range": 50, "rarity": "0196ba87-e69e-75ac-9885-91ec33cb57dc", "quantity": 1, "stack": 1, "cost": 350, "coins": 1, "diamonds": 0, "crystal": 0, "sell": true, "market": true, "temporary": false, "upgrade": false, "staff": false, "enh_id": "0196ba87-e691-7757-a7dd-b3fcba14451c", "req_reputation": 0, "req_class_points": 0, "req_quests": "", "quest_string_index": -1, "quest_string_value": 0, "meta": "", "color": "ffffff", "pid": 110}, {"id": "0196ba87-e698-771d-b7f9-9c352db24aad", "name": "Mystical Oceanic Butterflies", "description": "Still waters run deep;but those who chance to adventure below will find a world of magic;mystery;and beauty.", "type": "Cape", "element": "None", "file": "items/capes/ClassyOceanButterflyCape.swf", "link": "ClassyOceanButterflyCape", "icon": "iicape", "equipment": "ba", "level": 1, "dps": 25, "range": 50, "rarity": "0196ba87-e69e-75ac-9885-91ec33cb57dc", "quantity": 1, "stack": 1, "cost": 350, "coins": 1, "diamonds": 0, "crystal": 0, "sell": true, "market": true, "temporary": false, "upgrade": false, "staff": false, "enh_id": "0196ba87-e691-7757-a7dd-b3fcba14451c", "req_reputation": 0, "req_class_points": 0, "req_quests": "", "quest_string_index": -1, "quest_string_value": 0, "meta": "", "color": "ffffff", "pid": 111}, {"id": "0196ba87-e698-771d-b7f9-a39c9b60adb1", "name": "Mystical Oceanic Butterflies Wrap", "description": "Still waters run deep;but those who chance to adventure below will find a world of magic;mystery;and beauty.", "type": "Cape", "element": "None", "file": "items/capes/ClassyOceanCapeAndButterfly.swf", "link": "ClassyOceanCapeAndButterfly", "icon": "iicape", "equipment": "ba", "level": 1, "dps": 25, "range": 50, "rarity": "0196ba87-e69e-75ac-9885-a05920d0e5e9", "quantity": 1, "stack": 1, "cost": 45000, "coins": 0, "diamonds": 0, "crystal": 0, "sell": true, "market": true, "temporary": false, "upgrade": true, "staff": false, "enh_id": "0196ba87-e691-7757-a7dd-b3fcba14451c", "req_reputation": 0, "req_class_points": 0, "req_quests": "", "quest_string_index": -1, "quest_string_value": 0, "meta": "", "color": "ffffff", "pid": 112}, {"id": "0196ba87-e698-771d-b7f9-a781cb8096c3", "name": "Mystical Oceanic Butterflies Wrap", "description": "Still waters run deep;but those who chance to adventure below will find a world of magic;mystery;and beauty.", "type": "Cape", "element": "None", "file": "items/capes/ClassyOceanCapeAndButterfly.swf", "link": "ClassyOceanCapeAndButterfly", "icon": "iicape", "equipment": "ba", "level": 1, "dps": 25, "range": 50, "rarity": "0196ba87-e69e-75ac-9885-a05920d0e5e9", "quantity": 1, "stack": 1, "cost": 350, "coins": 1, "diamonds": 0, "crystal": 0, "sell": true, "market": true, "temporary": false, "upgrade": false, "staff": false, "enh_id": "0196ba87-e691-7757-a7dd-b3fcba14451c", "req_reputation": 0, "req_class_points": 0, "req_quests": "", "quest_string_index": -1, "quest_string_value": 0, "meta": "", "color": "ffffff", "pid": 113}, {"id": "0196ba87-e698-771d-b7f9-a852394a07cb", "name": "Oceanic Symphony Wrap", "description": "Still waters run deep;but those who chance to adventure below will find a world of magic;mystery;and beauty.", "type": "Cape", "element": "None", "file": "items/capes/ClassyOceanCape.swf", "link": "ClassyOceanCape", "icon": "iicape", "equipment": "ba", "level": 1, "dps": 25, "range": 50, "rarity": "0196ba87-e69e-75ac-9885-91ec33cb57dc", "quantity": 1, "stack": 1, "cost": 15000, "coins": 0, "diamonds": 0, "crystal": 0, "sell": true, "market": true, "temporary": false, "upgrade": false, "staff": false, "enh_id": "0196ba87-e691-7757-a7dd-b3fcba14451c", "req_reputation": 0, "req_class_points": 0, "req_quests": "", "quest_string_index": -1, "quest_string_value": 0, "meta": "", "color": "ffffff", "pid": 114}, {"id": "0196ba87-e698-771d-b7f9-ac84daa41534", "name": "Pyroclastic Necromancer's <PERSON>on", "description": "Pyroclastic necromancers walk where the faint-hearted fear to tread;seeking undead minions among those who've perished in the hearts of volcanic lands.", "type": "Cape", "element": "None", "file": "items/capes/MinionofCalamity.swf", "link": "MinionofCalamity", "icon": "iicape", "equipment": "ba", "level": 1, "dps": 25, "range": 50, "rarity": "0196ba87-e69e-75ac-9885-91ec33cb57dc", "quantity": 1, "stack": 1, "cost": 500, "coins": 1, "diamonds": 0, "crystal": 0, "sell": true, "market": true, "temporary": false, "upgrade": false, "staff": false, "enh_id": "0196ba87-e691-7757-a7dd-b3fcba14451c", "req_reputation": 0, "req_class_points": 0, "req_quests": "", "quest_string_index": -1, "quest_string_value": 0, "meta": "", "color": "ffffff", "pid": 115}, {"id": "0196ba87-e698-771d-b7f9-b2c4d1663ea6", "name": "Summer Rescuer Parachute", "description": "Citizens Lifeguards are encouraged by the Crystallis Administration to follow all stated rules and procedures before attempting rescue. Failure to comply will result in censure of sanctioning.", "type": "Cape", "element": "None", "file": "items/capes/CrystallisSummerRescuerParachute.swf", "link": "CrystallisSummerRescuerParachute", "icon": "iicape", "equipment": "ba", "level": 1, "dps": 25, "range": 50, "rarity": "0196ba87-e69e-75ac-9885-91ec33cb57dc", "quantity": 1, "stack": 1, "cost": 500, "coins": 0, "diamonds": 0, "crystal": 0, "sell": true, "market": true, "temporary": false, "upgrade": false, "staff": false, "enh_id": "0196ba87-e691-7757-a7dd-b3fcba14451c", "req_reputation": 0, "req_class_points": 0, "req_quests": "", "quest_string_index": -1, "quest_string_value": 0, "meta": "", "color": "ffffff", "pid": 116}, {"id": "0196ba87-e698-771d-b7f9-b69222569169", "name": "Islander Quibble Bank Pet", "description": "Even time-traveling sales moglins need to take a break and get away from it all once in a while.", "type": "Pet", "element": "None", "file": "items/pets/QuibbleIslander2022Bank.swf", "link": "QuibbleIslander2022Bank", "icon": "iipet", "equipment": "pe", "level": 1, "dps": 25, "range": 50, "rarity": "0196ba87-e69e-75ac-9885-91ec33cb57dc", "quantity": 1, "stack": 1, "cost": 2000, "coins": 1, "diamonds": 0, "crystal": 0, "sell": true, "market": true, "temporary": false, "upgrade": false, "staff": false, "enh_id": "0196ba87-e691-7757-a7dd-b3fcba14451c", "req_reputation": 0, "req_class_points": 0, "req_quests": "", "quest_string_index": -1, "quest_string_value": 0, "meta": "", "color": "ffffff", "pid": 117}, {"id": "0196ba87-e698-771d-b7f9-bb71998100d0", "name": "<PERSON> Quibble Pet", "description": "Even time-traveling sales moglins need to take a break and get away from it all once in a while.", "type": "Pet", "element": "None", "file": "items/pets/QuibbleIslander2022Pet.swf", "link": "QuibbleIslander2022Pet", "icon": "iipet", "equipment": "pe", "level": 1, "dps": 25, "range": 50, "rarity": "0196ba87-e69e-75ac-9885-91ec33cb57dc", "quantity": 1, "stack": 1, "cost": 400, "coins": 1, "diamonds": 0, "crystal": 0, "sell": true, "market": true, "temporary": false, "upgrade": false, "staff": false, "enh_id": "0196ba87-e691-7757-a7dd-b3fcba14451c", "req_reputation": 0, "req_class_points": 0, "req_quests": "", "quest_string_index": -1, "quest_string_value": 0, "meta": "", "color": "ffffff", "pid": 118}, {"id": "0196ba87-e698-771d-b7f9-bf613bf99af7", "name": "Sand Buddy Pet", "description": "Everybody needs to get away after a long season of waging war;boss battles;and hauling loot… and a hidden island retreat is the BEST place to /rest;recoup;and re-energize.", "type": "Pet", "element": "None", "file": "items/pets/SandBuddyPet.swf", "link": "SandBuddyPet", "icon": "iipet", "equipment": "pe", "level": 1, "dps": 25, "range": 50, "rarity": "0196ba87-e69e-75ac-9885-91ec33cb57dc", "quantity": 1, "stack": 1, "cost": 300, "coins": 1, "diamonds": 0, "crystal": 0, "sell": true, "market": true, "temporary": false, "upgrade": false, "staff": false, "enh_id": "0196ba87-e691-7757-a7dd-b3fcba14451c", "req_reputation": 0, "req_class_points": 0, "req_quests": "", "quest_string_index": -1, "quest_string_value": 0, "meta": "", "color": "ffffff", "pid": 119}, {"id": "0196ba87-e698-771d-b7f9-c3480094ad01", "name": "Sand Buddy Pet", "description": "Everybody needs to get away after a long season of waging war;boss battles;and hauling loot… and a hidden island retreat is the BEST place to /rest;recoup;and re-energize.", "type": "Pet", "element": "None", "file": "items/pets/SandBuddyPet.swf", "link": "SandBuddyPet", "icon": "iipet", "equipment": "pe", "level": 1, "dps": 25, "range": 50, "rarity": "0196ba87-e69e-75ac-9885-91ec33cb57dc", "quantity": 1, "stack": 1, "cost": 75000, "coins": 0, "diamonds": 0, "crystal": 0, "sell": true, "market": true, "temporary": false, "upgrade": true, "staff": false, "enh_id": "0196ba87-e691-7757-a7dd-b3fcba14451c", "req_reputation": 0, "req_class_points": 0, "req_quests": "", "quest_string_index": -1, "quest_string_value": 0, "meta": "", "color": "ffffff", "pid": 120}, {"id": "0196ba87-e698-771d-b7f9-c4126761d19b", "name": "Hand of the Pyroclastic Necromancer", "description": "Pyroclastic necromancers walk where the faint-hearted fear to tread;seeking undead minions among those who've perished in the hearts of volcanic lands.", "type": "Gauntlet", "element": "None", "file": "items/gauntlets/HandofCalamity.swf", "link": "HandofCalamity", "icon": "iwclaws", "equipment": "Weapon", "level": 1, "dps": 25, "range": 50, "rarity": "0196ba87-e69e-75ac-9885-91ec33cb57dc", "quantity": 1, "stack": 1, "cost": 400, "coins": 1, "diamonds": 0, "crystal": 0, "sell": true, "market": true, "temporary": false, "upgrade": false, "staff": false, "enh_id": "0196ba87-e691-7757-a7dd-b3fcba14451c", "req_reputation": 0, "req_class_points": 0, "req_quests": "", "quest_string_index": -1, "quest_string_value": 0, "meta": "", "color": "ffffff", "pid": 121}, {"id": "0196ba87-e698-771d-b7f9-cbcad4e78554", "name": "Oceanic Pool", "description": "Still waters run deep;but those who chance to adventure below will find a world of magic;mystery;and beauty.", "type": "Amulet", "element": "None", "file": "items/grounds/ClassyOceanGroundRune.swf", "link": "ClassyOceanGroundRune", "icon": "imr2", "equipment": "mi", "level": 1, "dps": 25, "range": 50, "rarity": "0196ba87-e69e-75ac-9885-a05920d0e5e9", "quantity": 1, "stack": 1, "cost": 500, "coins": 1, "diamonds": 0, "crystal": 0, "sell": true, "market": true, "temporary": false, "upgrade": false, "staff": false, "enh_id": "0196ba87-e691-7757-a7dd-b3fcba14451c", "req_reputation": 0, "req_class_points": 0, "req_quests": "", "quest_string_index": -1, "quest_string_value": 0, "meta": "", "color": "ffffff", "pid": 122}, {"id": "0196ba87-e698-771d-b7f9-ce5f562d8b55", "name": "Little Island Retreat", "description": "Everybody needs to get away after a long season of waging war;boss battles;and hauling some loot… and a hidden island retreat is the BEST place to /rest;recoup;and re-energize.", "type": "Amulet", "element": "None", "file": "items/grounds/LittleIsland.swf", "link": "LittleIsland", "icon": "imr2", "equipment": "mi", "level": 1, "dps": 25, "range": 50, "rarity": "0196ba87-e69e-75ac-9885-91ec33cb57dc", "quantity": 1, "stack": 1, "cost": 1000, "coins": 1, "diamonds": 0, "crystal": 0, "sell": true, "market": true, "temporary": false, "upgrade": false, "staff": false, "enh_id": "0196ba87-e691-7757-a7dd-b3fcba14451c", "req_reputation": 0, "req_class_points": 0, "req_quests": "", "quest_string_index": -1, "quest_string_value": 0, "meta": "", "color": "ffffff", "pid": 123}, {"id": "0196ba87-e698-771d-b7f9-d29294f19171", "name": "Beta Player", "description": "Thank you for playing our Beta Phase! we reward you this great Title that will be yours forever! Keep it on Nytherians!!", "type": "ServerUse", "element": "None", "file": "title::24", "link": "title::24", "icon": "iCheck", "equipment": "None", "level": 1, "dps": 25, "range": 50, "rarity": "0196ba87-e69e-75ac-9885-5763facdf6ca", "quantity": 1, "stack": 1, "cost": 0, "coins": 0, "diamonds": 0, "crystal": 0, "sell": true, "market": false, "temporary": false, "upgrade": false, "staff": false, "enh_id": "0196ba87-e691-7757-a7dd-b3fcba14451c", "req_reputation": 0, "req_class_points": 0, "req_quests": "", "quest_string_index": -1, "quest_string_value": 0, "meta": "", "color": "ffffff", "pid": 124}, {"id": "0196ba87-e698-771d-b7f9-d48b24c04459", "name": "Sun God Warrior", "description": "Sun God Warrior the Sun God demands vengeance. Pay homage to his power with the blood of your enemies;and you will prosper in his service. (<PERSON><PERSON><PERSON> is CC to trim)", "type": "Armor", "element": "None", "file": "AzuraArmor2CC2.swf", "link": "AzuraArmor2CC", "icon": "i<PERSON>or", "equipment": "co", "level": 1, "dps": 25, "range": 50, "rarity": "0196ba87-e69e-75ac-9885-91ec33cb57dc", "quantity": 1, "stack": 1, "cost": 350, "coins": 1, "diamonds": 0, "crystal": 0, "sell": true, "market": true, "temporary": false, "upgrade": false, "staff": false, "enh_id": "0196ba87-e691-7757-a7dd-b3fcba14451c", "req_reputation": 0, "req_class_points": 0, "req_quests": "", "quest_string_index": -1, "quest_string_value": 0, "meta": "", "color": "ffffff", "pid": 125}, {"id": "0196ba87-e698-771d-b7f9-db759966e8c7", "name": "Lightning Bolt", "description": "Damage 20% to all Monster\\r\\nDefense 10%", "type": "Sword", "element": "None", "file": "items/swords/LightningBotlInHandsDatapaw.swf", "link": "HSElectinaLightingRodCC", "icon": "iwsword", "equipment": "Weapon", "level": 1, "dps": 50, "range": 50, "rarity": "0196ba87-e69e-75ac-9885-5b8b0c71fdb4", "quantity": 1, "stack": 1, "cost": 0, "coins": 0, "diamonds": 0, "crystal": 0, "sell": true, "market": true, "temporary": false, "upgrade": false, "staff": false, "enh_id": "0196ba87-e691-7757-a7dd-b3fcba14451c", "req_reputation": 0, "req_class_points": 0, "req_quests": "", "quest_string_index": -1, "quest_string_value": 0, "meta": "", "color": "e9bb03", "pid": 126}, {"id": "0196ba87-e698-771d-b7f9-dc5e6607d96c", "name": "Sun God Hair", "description": "Son God the Sun God demands vengeance. Pay homage to his power with the blood of your enemies;and you will prosper in his service.", "type": "<PERSON><PERSON>", "element": "None", "file": "items/helms/AzuraHelmMCC.swf", "link": "AzuraHelmMCC", "icon": "iihelm", "equipment": "he", "level": 1, "dps": 25, "range": 50, "rarity": "0196ba87-e69e-75ac-9885-91ec33cb57dc", "quantity": 1, "stack": 1, "cost": 0, "coins": 1, "diamonds": 0, "crystal": 0, "sell": true, "market": true, "temporary": false, "upgrade": false, "staff": false, "enh_id": "0196ba87-e691-7757-a7dd-b3fcba14451c", "req_reputation": 0, "req_class_points": 0, "req_quests": "", "quest_string_index": -1, "quest_string_value": 0, "meta": "", "color": "ffffff", "pid": 127}, {"id": "0196ba87-e698-771d-b7f9-e1883f47bcef", "name": "Sun God Cape", "description": "Sun God the Sun God demands vengeance. Pay homage to his power with the blood of your enemies;and you will prosper in his service.", "type": "Cape", "element": "None", "file": "items/capes/Azura3MCapeCC.swf", "link": "Azura3MCapeCC", "icon": "iicape", "equipment": "ba", "level": 1, "dps": 25, "range": 50, "rarity": "0196ba87-e69e-75ac-9885-91ec33cb57dc", "quantity": 1, "stack": 1, "cost": 0, "coins": 1, "diamonds": 0, "crystal": 0, "sell": true, "market": true, "temporary": false, "upgrade": false, "staff": false, "enh_id": "0196ba87-e691-7757-a7dd-b3fcba14451c", "req_reputation": 0, "req_class_points": 0, "req_quests": "", "quest_string_index": -1, "quest_string_value": 0, "meta": "", "color": "ffffff", "pid": 128}, {"id": "0196ba87-e698-771d-b7f9-e71987f440bf", "name": "CC Elder big Squid", "description": "Amulet", "type": "None", "element": "items/grounds/BigSquid.swf", "file": "BEldWarriorbigSquid", "link": "imr2", "icon": "mi", "equipment": "1", "level": 25, "dps": 50, "range": 35, "rarity": "0196ba87-e69e-75ac-9885-5124623d1f3f", "quantity": 1, "stack": 5000000, "cost": 1, "coins": 0, "diamonds": 0, "crystal": 1, "sell": true, "market": false, "temporary": false, "upgrade": false, "staff": true, "faction_id": "0196ba87-e693-7421-951a-e35274e5fe7a", "req_reputation": 0, "req_class_points": 0, "req_quests": "", "quest_string_index": 0, "quest_string_value": 0, "meta": "ffffff", "color": "", "pid": 129}, {"id": "0196ba87-e698-771d-b7f9-e841782e41cb", "name": "Elder Warrior of Squid", "description": "Armor", "type": "None", "element": "BEldWarrior2.swf", "file": "BEldWarrior2", "link": "i<PERSON>or", "icon": "co", "equipment": "1", "level": 25, "dps": 50, "range": 35, "rarity": "0196ba87-e69e-75ac-9885-5124623d1f3f", "quantity": 1, "stack": 50000, "cost": 1, "coins": 0, "diamonds": 0, "crystal": 1, "sell": true, "market": false, "temporary": false, "upgrade": false, "staff": true, "faction_id": "0196ba87-e693-7421-951a-e35274e5fe7a", "req_reputation": 0, "req_class_points": 0, "req_quests": "", "quest_string_index": 0, "quest_string_value": 0, "meta": "ffffff", "color": "", "pid": 130}, {"id": "0196ba87-e698-771d-b7f9-ee8e6ece25a7", "name": "Elder Squid Sword", "description": "", "type": "Sword", "element": "None", "file": "items/swords/BEldWarriorBlade.swf", "link": "BEldWarriorBlade", "icon": "iwsword", "equipment": "Weapon", "level": 1, "dps": 25, "range": 50, "rarity": "0196ba87-e69e-75ac-9885-940e18f9c3e6", "quantity": 1, "stack": 1, "cost": 50000, "coins": 1, "diamonds": 0, "crystal": 0, "sell": true, "market": true, "temporary": false, "upgrade": false, "staff": false, "enh_id": "0196ba87-e691-7757-a7dd-b3fcba14451c", "req_reputation": 0, "req_class_points": 0, "req_quests": "", "quest_string_index": -1, "quest_string_value": 0, "meta": "", "color": "ffffff", "pid": 131}, {"id": "0196ba87-e698-771d-b7f9-f175ae45f758", "name": "Elder <PERSON><PERSON>", "description": "<PERSON><PERSON>", "type": "None", "element": "items/helms/BEldWarriorH2.swf", "file": "BEldWarriorH2", "link": "iihelm", "icon": "he", "equipment": "1", "level": 25, "dps": 50, "range": 10, "rarity": "0196ba87-e69e-75ac-9885-5124623d1f3f", "quantity": 1, "stack": 0, "cost": 0, "coins": 0, "diamonds": 0, "crystal": 1, "sell": true, "market": false, "temporary": false, "upgrade": false, "staff": true, "faction_id": "0196ba87-e693-7421-951a-e35274e5fe7a", "req_reputation": 0, "req_class_points": 0, "req_quests": "", "quest_string_index": 0, "quest_string_value": 0, "meta": "ffffff", "color": "", "pid": 132}, {"id": "0196ba87-e698-771d-b7f9-f68714d02b27", "name": "Elder Warrior of Squid", "description": "Armor", "type": "None", "element": "BEldWarrior2.swf", "file": "BEldWarrior2", "link": "i<PERSON>or", "icon": "co", "equipment": "1", "level": 25, "dps": 50, "range": 10, "rarity": "0196ba87-e69e-75ac-9885-5124623d1f3f", "quantity": 1, "stack": 0, "cost": 0, "coins": 0, "diamonds": 0, "crystal": 1, "sell": true, "market": false, "temporary": false, "upgrade": false, "staff": true, "faction_id": "0196ba87-e693-7421-951a-e35274e5fe7a", "req_reputation": 0, "req_class_points": 0, "req_quests": "", "quest_string_index": 0, "quest_string_value": 0, "meta": "ffffff", "color": "", "pid": 133}, {"id": "0196ba87-e698-771d-b7f9-fbb0d83bc6e9", "name": "Elder Warrior Squid", "description": "Armor", "type": "None", "element": "BEldWarrior2.swf", "file": "BEldWarrior2", "link": "i<PERSON>or", "icon": "co", "equipment": "1", "level": 25, "dps": 50, "range": 10, "rarity": "0196ba87-e69e-75ac-9885-5124623d1f3f", "quantity": 1, "stack": 0, "cost": 0, "coins": 0, "diamonds": 0, "crystal": 1, "sell": true, "market": false, "temporary": false, "upgrade": false, "staff": true, "faction_id": "0196ba87-e693-7421-951a-e35274e5fe7a", "req_reputation": 0, "req_class_points": 0, "req_quests": "", "quest_string_index": 0, "quest_string_value": 0, "meta": "ffffff", "color": "", "pid": 134}, {"id": "0196ba87-e698-771d-b7f9-fe2c1226b88c", "name": "Squid Warrior", "description": "Armor", "type": "None", "element": "BEldWarrior21.swf", "file": "BEldWarrior2", "link": "i<PERSON>or", "icon": "co", "equipment": "1", "level": 25, "dps": 50, "range": 10, "rarity": "0196ba87-e69e-75ac-9885-5124623d1f3f", "quantity": 1, "stack": 50000, "cost": 1, "coins": 0, "diamonds": 0, "crystal": 1, "sell": true, "market": false, "temporary": false, "upgrade": false, "staff": true, "faction_id": "0196ba87-e693-7421-951a-e35274e5fe7a", "req_reputation": 0, "req_class_points": 0, "req_quests": "", "quest_string_index": 0, "quest_string_value": 0, "meta": "ffffff", "color": "", "pid": 135}, {"id": "0196ba87-e698-771d-b7fa-009227d2892b", "name": "Squid Tactecle", "description": "", "type": "Cape", "element": "None", "file": "items/capes/BEldWarriorTentaclesC.swf", "link": "BEldWarriorTentaclesC", "icon": "iicape", "equipment": "ba", "level": 1, "dps": 25, "range": 50, "rarity": "0196ba87-e69e-75ac-9885-940e18f9c3e6", "quantity": 1, "stack": 1, "cost": 60000, "coins": 1, "diamonds": 0, "crystal": 0, "sell": true, "market": true, "temporary": false, "upgrade": false, "staff": false, "enh_id": "0196ba87-e691-7757-a7dd-b3fcba14451c", "req_reputation": 0, "req_class_points": 0, "req_quests": "", "quest_string_index": -1, "quest_string_value": 0, "meta": "", "color": "ffffff", "pid": 136}, {"id": "0196ba87-e698-771d-b7fa-04c8411db3ae", "name": "Jungle Fragment", "description": "Kill Them all <PERSON><PERSON> is so scared of them and <PERSON> god Called You to Defeat them and save the jungle", "type": "<PERSON><PERSON>", "element": "None", "file": "none.swf", "link": "iibag", "icon": "iibag", "equipment": "None", "level": 1, "dps": 25, "range": 50, "rarity": "0196ba87-e69e-75ac-9885-5b8b0c71fdb4", "quantity": 1, "stack": 1000, "cost": 0, "coins": 0, "diamonds": 0, "crystal": 0, "sell": true, "market": true, "temporary": false, "upgrade": false, "staff": false, "enh_id": "0196ba87-e691-7757-a7dd-b3fcba14451c", "req_reputation": 0, "req_class_points": 0, "req_quests": "", "quest_string_index": -1, "quest_string_value": 0, "meta": "", "color": "c0c0c0", "pid": 137}, {"id": "0196ba87-e698-771d-b7fa-0a42504cf23d", "name": "Soul of a Goddess", "description": "this can be found in roftitan", "type": "<PERSON><PERSON>", "element": "None", "file": "iibag", "link": "iibag", "icon": "iibag", "equipment": "None", "level": 1, "dps": 25, "range": 50, "rarity": "0196ba87-e69e-75ac-9885-5b8b0c71fdb4", "quantity": 1, "stack": 1300, "cost": 500, "coins": 1, "diamonds": 0, "crystal": 0, "sell": true, "market": true, "temporary": false, "upgrade": false, "staff": false, "enh_id": "0196ba87-e691-7757-a7dd-b3fcba14451c", "req_reputation": 0, "req_class_points": 0, "req_quests": "", "quest_string_index": -1, "quest_string_value": 0, "meta": "", "color": "ff0000", "pid": 138}, {"id": "0196ba87-e698-771d-b7fa-0dc2a6553dec", "name": "Skeleton Halt", "description": "Use skeleton resource to complete mision in newbie.", "type": "<PERSON><PERSON>", "element": "None", "file": "iibag", "link": "iibag", "icon": "ied1", "equipment": "None", "level": 1, "dps": 25, "range": 50, "rarity": "0196ba87-e69e-75ac-9885-5763facdf6ca", "quantity": 1, "stack": 1, "cost": 5000, "coins": 0, "diamonds": 0, "crystal": 0, "sell": true, "market": false, "temporary": false, "upgrade": false, "staff": false, "enh_id": "0196ba87-e691-7757-a7dd-b3fcba14451c", "req_reputation": 0, "req_class_points": 0, "req_quests": "", "quest_string_index": -1, "quest_string_value": 0, "meta": "", "color": "ffffff", "pid": 139}, {"id": "0196ba87-e698-771d-b7fa-104737d6d2d2", "name": "C Ultima Astral", "description": "Ultima Astral the last you;thing that you can get in LQS and it will never you can get it", "type": "Sword", "element": "None", "file": "items/swords/UltimaDatapaw.swf", "link": "CustomUltimaAstral", "icon": "iwsword", "equipment": "Weapon", "level": 1, "dps": 25, "range": 50, "rarity": "0196ba87-e69e-75ac-9885-af4e24f6923c", "quantity": 1, "stack": 1, "cost": 1000, "coins": 1, "diamonds": 0, "crystal": 0, "sell": true, "market": true, "temporary": false, "upgrade": false, "staff": false, "enh_id": "0196ba87-e691-7757-a7dd-b3fcba14451c", "req_reputation": 0, "req_class_points": 0, "req_quests": "", "quest_string_index": -1, "quest_string_value": 0, "meta": "", "color": "ffffff", "pid": 140}, {"id": "0196ba87-e698-771d-b7fa-1519f6081aca", "name": "<PERSON><PERSON>", "description": "Use in Estarta Shop.", "type": "<PERSON><PERSON>", "element": "None", "file": "imc3", "link": "imc3", "icon": "imc3", "equipment": "None", "level": 1, "dps": 25, "range": 50, "rarity": "0196ba87-e69e-75ac-9885-5124623d1f3f", "quantity": 1, "stack": 500, "cost": 0, "coins": 0, "diamonds": 0, "crystal": 0, "sell": true, "market": true, "temporary": false, "upgrade": false, "staff": false, "enh_id": "0196ba87-e691-7757-a7dd-b3fcba14451c", "req_reputation": 0, "req_class_points": 0, "req_quests": "", "quest_string_index": -1, "quest_string_value": 0, "meta": "", "color": "ffffff", "pid": 141}, {"id": "0196ba87-e698-771d-b7fa-19eabc2b670c", "name": "<PERSON>", "description": ".", "type": "Armor", "element": "None", "file": "Ethan1.swf", "link": "<PERSON>", "icon": "i<PERSON>or", "equipment": "co", "level": 1, "dps": 25, "range": 50, "rarity": "0196ba87-e69e-75ac-9885-5763facdf6ca", "quantity": 1, "stack": 1, "cost": 0, "coins": 0, "diamonds": 0, "crystal": 0, "sell": true, "market": true, "temporary": false, "upgrade": false, "staff": false, "enh_id": "0196ba87-e691-7757-a7dd-b3fcba14451c", "req_reputation": 0, "req_class_points": 0, "req_quests": "", "quest_string_index": -1, "quest_string_value": 0, "meta": "", "color": "ffffff", "pid": 142}, {"id": "0196ba87-e698-771d-b7fa-1e397bd9986a", "name": "<PERSON>", "description": ".", "type": "Armor", "element": "None", "file": "Ethan1.swf", "link": "<PERSON>", "icon": "i<PERSON>or", "equipment": "co", "level": 1, "dps": 25, "range": 50, "rarity": "0196ba87-e69e-75ac-9885-5763facdf6ca", "quantity": 1, "stack": 1, "cost": 0, "coins": 0, "diamonds": 0, "crystal": 0, "sell": true, "market": true, "temporary": false, "upgrade": false, "staff": false, "enh_id": "0196ba87-e691-7757-a7dd-b3fcba14451c", "req_reputation": 0, "req_class_points": 0, "req_quests": "", "quest_string_index": -1, "quest_string_value": 0, "meta": "", "color": "ffffff", "pid": 143}, {"id": "0196ba87-e698-771d-b7fa-23f43643c2c3", "name": "<PERSON>", "description": ".", "type": "Armor", "element": "None", "file": "Ethan1.swf", "link": "<PERSON>", "icon": "i<PERSON>or", "equipment": "co", "level": 1, "dps": 25, "range": 50, "rarity": "0196ba87-e69e-75ac-9885-5763facdf6ca", "quantity": 1, "stack": 1, "cost": 0, "coins": 0, "diamonds": 0, "crystal": 0, "sell": true, "market": true, "temporary": false, "upgrade": false, "staff": false, "enh_id": "0196ba87-e691-7757-a7dd-b3fcba14451c", "req_reputation": 0, "req_class_points": 0, "req_quests": "", "quest_string_index": -1, "quest_string_value": 0, "meta": "", "color": "ffffff", "pid": 144}, {"id": "0196ba87-e698-771d-b7fa-255adcdb78ee", "name": "Beta Berserker Access", "description": "Beta Berserker Access.", "type": "Resource", "element": "None", "file": "iibag", "link": "iibag", "icon": "iibag", "equipment": "None", "level": 1, "dps": 25, "range": 50, "rarity": "0196ba87-e69e-75ac-9885-5763facdf6ca", "quantity": 1, "stack": 1, "cost": 0, "coins": 0, "diamonds": 0, "crystal": 0, "sell": true, "market": true, "temporary": false, "upgrade": false, "staff": false, "enh_id": "0196ba87-e691-7757-a7dd-b3fcba14451c", "req_reputation": 0, "req_class_points": 0, "req_quests": "", "quest_string_index": -1, "quest_string_value": 0, "meta": "", "color": "ffffff", "pid": 145}, {"id": "0196ba87-e698-771d-b7fa-2a9328128f00", "name": "Pink Gift of The Fire", "description": "", "type": "<PERSON>gger", "element": "None", "file": "items/daggers/pinkgiftDatapaw.swf", "link": "PinkGiftOfTheFireAvatar", "icon": "iwda<PERSON>", "equipment": "Weapon", "level": 1, "dps": 25, "range": 50, "rarity": "0196ba87-e69e-75ac-9885-5763facdf6ca", "quantity": 1, "stack": 1, "cost": 0, "coins": 0, "diamonds": 0, "crystal": 0, "sell": true, "market": true, "temporary": false, "upgrade": false, "staff": false, "enh_id": "0196ba87-e691-7757-a7dd-b3fcba14451c", "req_reputation": 0, "req_class_points": 0, "req_quests": "", "quest_string_index": -1, "quest_string_value": 0, "meta": "", "color": "ffffff", "pid": 146}, {"id": "0196ba87-e698-771d-b7fa-2ffca68a6aad", "name": "Matrix", "description": "", "type": "Armor", "element": "None", "file": "Matrixr1.swf", "link": "Matrix", "icon": "i<PERSON>or", "equipment": "co", "level": 1, "dps": 25, "range": 50, "rarity": "0196ba87-e69e-75ac-9885-5763facdf6ca", "quantity": 1, "stack": 1, "cost": 0, "coins": 0, "diamonds": 0, "crystal": 0, "sell": true, "market": true, "temporary": false, "upgrade": false, "staff": false, "enh_id": "0196ba87-e691-7757-a7dd-b3fcba14451c", "req_reputation": 0, "req_class_points": 0, "req_quests": "", "quest_string_index": -1, "quest_string_value": 0, "meta": "", "color": "ffffff", "pid": 147}, {"id": "0196ba87-e698-771d-b7fa-32753b8ba2cd", "name": "The Voider Claws", "description": "", "type": "Gauntlet", "element": "None", "file": "items/gauntlets/VoidClaws.swf", "link": "VoidClaws", "icon": "iwclaws", "equipment": "Weapon", "level": 1, "dps": 25, "range": 50, "rarity": "0196ba87-e69e-75ac-9885-5763facdf6ca", "quantity": 1, "stack": 1, "cost": 0, "coins": 0, "diamonds": 0, "crystal": 0, "sell": true, "market": true, "temporary": false, "upgrade": false, "staff": false, "enh_id": "0196ba87-e691-7757-a7dd-b3fcba14451c", "req_reputation": 0, "req_class_points": 0, "req_quests": "", "quest_string_index": -1, "quest_string_value": 0, "meta": "", "color": "ffffff", "pid": 148}, {"id": "0196ba87-e698-771d-b7fa-34b24104c7a1", "name": "<PERSON>ray Hand's", "description": "", "type": "Amulet", "element": "None", "file": "items/grounds/LNHandsFiendGround1r1.swf", "link": "LNHandsFiendGround1", "icon": "imr2", "equipment": "mi", "level": 1, "dps": 25, "range": 50, "rarity": "0196ba87-e69e-75ac-9885-5763facdf6ca", "quantity": 1, "stack": 1, "cost": 0, "coins": 0, "diamonds": 0, "crystal": 0, "sell": true, "market": true, "temporary": false, "upgrade": false, "staff": false, "enh_id": "0196ba87-e691-7757-a7dd-b3fcba14451c", "req_reputation": 0, "req_class_points": 0, "req_quests": "", "quest_string_index": -1, "quest_string_value": 0, "meta": "", "color": "ffffff", "pid": 149}, {"id": "0196ba87-e698-771d-b7fa-3959a52d372f", "name": "<PERSON><PERSON><PERSON>'s", "description": "", "type": "<PERSON><PERSON>", "element": "None", "file": "items/helms/Creature16sHood.swf", "link": "Creature16sHood", "icon": "iihelm", "equipment": "he", "level": 1, "dps": 25, "range": 50, "rarity": "0196ba87-e69e-75ac-9885-5763facdf6ca", "quantity": 1, "stack": 1, "cost": 0, "coins": 0, "diamonds": 0, "crystal": 0, "sell": true, "market": true, "temporary": false, "upgrade": false, "staff": false, "enh_id": "0196ba87-e691-7757-a7dd-b3fcba14451c", "req_reputation": 0, "req_class_points": 0, "req_quests": "", "quest_string_index": -1, "quest_string_value": 0, "meta": "", "color": "ffffff", "pid": 150}, {"id": "0196ba87-e698-771d-b7fa-3de7534d5d2b", "name": "Ultra Hair", "description": "", "type": "<PERSON><PERSON>", "element": "None", "file": "items/helms/DataUltra.swf", "link": "UltraOverpoweredHair", "icon": "iihelm", "equipment": "he", "level": 1, "dps": 25, "range": 50, "rarity": "0196ba87-e69e-75ac-9885-5763facdf6ca", "quantity": 1, "stack": 1, "cost": 0, "coins": 0, "diamonds": 0, "crystal": 0, "sell": true, "market": true, "temporary": false, "upgrade": false, "staff": false, "enh_id": "0196ba87-e691-7757-a7dd-b3fcba14451c", "req_reputation": 0, "req_class_points": 0, "req_quests": "", "quest_string_index": -1, "quest_string_value": 0, "meta": "", "color": "ff0000", "pid": 151}, {"id": "0196ba87-e698-771d-b7fa-43eb6070a551", "name": "Warrior Weapon Enhancement Level 5", "description": "", "type": "Enhancement", "element": "None", "file": "Enhancement.swf", "link": "Enhancement", "icon": "iwsword", "equipment": "Weapon", "level": 5, "dps": 25, "range": 50, "rarity": "0196ba87-e69e-75ac-9885-5763facdf6ca", "quantity": 1, "stack": 1, "cost": 2500, "coins": 0, "diamonds": 0, "crystal": 0, "sell": true, "market": true, "temporary": false, "upgrade": false, "staff": false, "enh_id": "0196ba87-e691-7757-a7dd-b67cd0250b45", "req_reputation": 0, "req_class_points": 0, "req_quests": "", "quest_string_index": -1, "quest_string_value": 0, "meta": "", "color": "ffffff", "pid": 152}, {"id": "0196ba87-e698-771d-b7fa-440ce3b70597", "name": "<PERSON>", "description": "", "type": "Cape", "element": "None", "file": "items/capes/Onesssaannn.swf", "link": "Timewalk<PERSON><PERSON><PERSON><PERSON>", "icon": "iicape", "equipment": "ba", "level": 1, "dps": 25, "range": 50, "rarity": "0196ba87-e69e-75ac-9885-5763facdf6ca", "quantity": 1, "stack": 1, "cost": 0, "coins": 0, "diamonds": 0, "crystal": 0, "sell": true, "market": true, "temporary": false, "upgrade": false, "staff": false, "enh_id": "0196ba87-e691-7757-a7dd-b3fcba14451c", "req_reputation": 0, "req_class_points": 0, "req_quests": "", "quest_string_index": -1, "quest_string_value": 0, "meta": "", "color": "ffffff", "pid": 153}, {"id": "0196ba87-e698-771d-b7fa-48952c69d4cf", "name": "Katniss Access", "description": "Use to open Katniss NPC.", "type": "<PERSON><PERSON>", "element": "None", "file": "iCheck", "link": "iCheck", "icon": "iCheck", "equipment": "None", "level": 1, "dps": 25, "range": 50, "rarity": "0196ba87-e69e-75ac-9885-5763facdf6ca", "quantity": 1, "stack": 1, "cost": 0, "coins": 1, "diamonds": 0, "crystal": 0, "sell": true, "market": false, "temporary": false, "upgrade": false, "staff": false, "enh_id": "0196ba87-e691-7757-a7dd-b3fcba14451c", "req_reputation": 0, "req_class_points": 0, "req_quests": "", "quest_string_index": -1, "quest_string_value": 0, "meta": "", "color": "ffffff", "pid": 371}, {"id": "0196ba87-e698-771d-b7fa-4e6164e2f81a", "name": "Jungle Delivery", "description": "Jungle Delivery", "type": "<PERSON><PERSON>", "element": "None", "file": "iibag", "link": "iibag", "icon": "iibag", "equipment": "None", "level": 1, "dps": 25, "range": 50, "rarity": "0196ba87-e69e-75ac-9885-749ad6dbf732", "quantity": 1, "stack": 1, "cost": 2500, "coins": 0, "diamonds": 0, "crystal": 0, "sell": true, "market": false, "temporary": false, "upgrade": false, "staff": false, "enh_id": "0196ba87-e691-7757-a7dd-b3fcba14451c", "req_reputation": 0, "req_class_points": 0, "req_quests": "", "quest_string_index": -1, "quest_string_value": 0, "meta": "", "color": "ffffff", "pid": 372}, {"id": "0196ba87-e698-771d-b7fa-50790adcafa2", "class_id": "0196ba87-e68f-7738-8b2a-dfbecc44edee", "name": "Bloondermon", "description": "Congratulations to all the players who completed these quests and helped take back the jungle from the villains.", "type": "Class", "element": "None", "file": "Nytherian.swf", "link": "DWMage", "icon": "imc3", "equipment": "ar", "level": 1, "dps": 25, "range": 50, "rarity": "0196ba87-e69e-75ac-9885-89435355abd6", "quantity": 1, "stack": 1, "cost": 0, "coins": 1, "diamonds": 0, "crystal": 0, "sell": true, "market": false, "temporary": false, "upgrade": false, "staff": false, "enh_id": "0196ba87-e691-7757-a7dd-b3fcba14451c", "req_reputation": 0, "req_class_points": 0, "req_quests": "", "quest_string_index": -1, "quest_string_value": 0, "meta": "", "color": "ffffff", "pid": 373, "req_class_id": "0196ba87-e68f-7738-8b2a-dfbecc44edee"}, {"id": "0196ba87-e698-771d-b7fa-5612bfef9379", "name": "Warlord of Void", "description": "Attack Power (+10%) Defense (+40%)", "type": "Armor", "element": "None", "file": "Luciaa.swf", "link": "Lucia<PERSON>", "icon": "i<PERSON>or", "equipment": "co", "level": 1, "dps": 35, "range": 50, "rarity": "0196ba87-e69e-75ac-9885-91ec33cb57dc", "quantity": 1, "stack": 1, "cost": 0, "coins": 0, "diamonds": 0, "crystal": 0, "sell": true, "market": true, "temporary": false, "upgrade": false, "staff": false, "enh_id": "0196ba87-e691-7757-a7dd-b3fcba14451c", "req_reputation": 0, "req_class_points": 0, "req_quests": "", "quest_string_index": -1, "quest_string_value": 0, "meta": "", "color": "e1e401", "pid": 374}, {"id": "0196ba87-e698-771d-b7fa-5bf2f8b58d41", "name": "Warlord of Void Guard", "description": "Im Your Guid in the beginning until you in top of your dream just never give up of your dream loser always be quiter", "type": "<PERSON><PERSON>", "element": "None", "file": "items/helms/WOVhelmonly1.swf", "link": "WOVHelm", "icon": "iihelm", "equipment": "he", "level": 1, "dps": 25, "range": 50, "rarity": "0196ba87-e69e-75ac-9885-91ec33cb57dc", "quantity": 1, "stack": 1, "cost": 0, "coins": 0, "diamonds": 0, "crystal": 0, "sell": true, "market": true, "temporary": false, "upgrade": false, "staff": false, "enh_id": "0196ba87-e691-7757-a7dd-b3fcba14451c", "req_reputation": 0, "req_class_points": 0, "req_quests": "", "quest_string_index": -1, "quest_string_value": 0, "meta": "", "color": "e4d501", "pid": 375}, {"id": "0196ba87-e698-771d-b7fa-5d836c69782c", "name": "Pile of gold's", "description": "-", "type": "<PERSON><PERSON>", "element": "None", "file": "iibag", "link": "iibag", "icon": "iibag", "equipment": "<PERSON><PERSON>", "level": 1, "dps": 25, "range": 50, "rarity": "0196ba87-e69e-75ac-9885-5763facdf6ca", "quantity": 1, "stack": 30, "cost": 0, "coins": 0, "diamonds": 0, "crystal": 0, "sell": true, "market": true, "temporary": true, "upgrade": false, "staff": false, "enh_id": "0196ba87-e691-7757-a7dd-b3fcba14451c", "req_reputation": 0, "req_class_points": 0, "req_quests": "", "quest_string_index": -1, "quest_string_value": 0, "meta": "", "color": "ffffff", "pid": 376}, {"id": "0196ba87-e698-771d-b7fa-637ff2700eb1", "name": "Sketch Naval Commander", "description": "The highest ranking captain of all. Take control of the helm.", "type": "Armor", "element": "None", "file": "PencilHook1.swf", "link": "PencilHook1", "icon": "i<PERSON>or", "equipment": "co", "level": 1, "dps": 25, "range": 50, "rarity": "0196ba87-e69e-75ac-9885-91ec33cb57dc", "quantity": 1, "stack": 1, "cost": 0, "coins": 1, "diamonds": 0, "crystal": 0, "sell": true, "market": true, "temporary": false, "upgrade": false, "staff": false, "enh_id": "0196ba87-e691-7757-a7dd-b3fcba14451c", "req_reputation": 0, "req_class_points": 0, "req_quests": "", "quest_string_index": -1, "quest_string_value": 0, "meta": "", "color": "ffffff", "pid": 377}, {"id": "0196ba87-e698-771d-b7fa-64734391ed37", "name": "Paper Warrior", "description": "Literal.", "type": "Armor", "element": "None", "file": "PaperRareWarrior.swf", "link": "PaperRareWarrior", "icon": "i<PERSON>or", "equipment": "co", "level": 1, "dps": 25, "range": 50, "rarity": "0196ba87-e69e-75ac-9885-91ec33cb57dc", "quantity": 1, "stack": 1, "cost": 0, "coins": 1, "diamonds": 0, "crystal": 0, "sell": true, "market": true, "temporary": false, "upgrade": false, "staff": false, "enh_id": "0196ba87-e691-7757-a7dd-b3fcba14451c", "req_reputation": 0, "req_class_points": 0, "req_quests": "", "quest_string_index": -1, "quest_string_value": 0, "meta": "", "color": "ffffff", "pid": 378}, {"id": "0196ba87-e698-771d-b7fa-6816d46badd5", "name": "Paper Helm Warrior", "description": "Literal..", "type": "<PERSON><PERSON>", "element": "None", "file": "items/helms/RareWingedHelmKertas.swf", "link": "RareWingedHelmKertas", "icon": "iihelm", "equipment": "he", "level": 1, "dps": 25, "range": 50, "rarity": "0196ba87-e69e-75ac-9885-91ec33cb57dc", "quantity": 1, "stack": 1, "cost": 0, "coins": 1, "diamonds": 0, "crystal": 0, "sell": true, "market": true, "temporary": false, "upgrade": false, "staff": false, "enh_id": "0196ba87-e691-7757-a7dd-b3fcba14451c", "req_reputation": 0, "req_class_points": 0, "req_quests": "", "quest_string_index": -1, "quest_string_value": 0, "meta": "", "color": "ffffff", "pid": 379}, {"id": "0196ba87-e698-771d-b7fa-6d734cff5927", "name": "Cellulose Fiber Star Sword", "description": "This blade seems to harness the power of light itself. It's a force to be reckoned with.", "type": "Sword", "element": "None", "file": "items/swords/starswordpencil.swf", "link": "starswordpencil", "icon": "iwsword", "equipment": "Weapon", "level": 1, "dps": 25, "range": 50, "rarity": "0196ba87-e69e-75ac-9885-91ec33cb57dc", "quantity": 1, "stack": 1, "cost": 0, "coins": 1, "diamonds": 0, "crystal": 0, "sell": true, "market": true, "temporary": false, "upgrade": false, "staff": false, "enh_id": "0196ba87-e691-7757-a7dd-b3fcba14451c", "req_reputation": 0, "req_class_points": 0, "req_quests": "", "quest_string_index": -1, "quest_string_value": 0, "meta": "", "color": "ffffff", "pid": 380}, {"id": "0196ba87-e698-771d-b7fa-704409b9cf7a", "name": "Cellulose Fiber Star Sword B", "description": "This blade seems to harness the power of light itself. It's a force to be reckoned with.", "type": "Sword", "element": "None", "file": "items/swords/starswordpencilb.swf", "link": "starswordpencilb", "icon": "iwsword", "equipment": "Weapon", "level": 1, "dps": 25, "range": 50, "rarity": "0196ba87-e69e-75ac-9885-91ec33cb57dc", "quantity": 1, "stack": 1, "cost": 0, "coins": 1, "diamonds": 0, "crystal": 0, "sell": true, "market": true, "temporary": false, "upgrade": false, "staff": false, "enh_id": "0196ba87-e691-7757-a7dd-b3fcba14451c", "req_reputation": 0, "req_class_points": 0, "req_quests": "", "quest_string_index": -1, "quest_string_value": 0, "meta": "", "color": "ffffff", "pid": 381}, {"id": "0196ba87-e698-771d-b7fa-774fd4583728", "name": "Doodle Awe Sword", "description": "If you have a Legend only weapon equipped when your membership upgrade expires or when merging a weapon that's equipped into something else;this weapon is temporarily equipped by default.", "type": "Sword", "element": "None", "file": "items/swords/sword12kertas.swf", "link": "sword12kertas", "icon": "iwsword", "equipment": "Weapon", "level": 1, "dps": 25, "range": 50, "rarity": "0196ba87-e69e-75ac-9885-91ec33cb57dc", "quantity": 1, "stack": 1, "cost": 0, "coins": 1, "diamonds": 0, "crystal": 0, "sell": true, "market": true, "temporary": false, "upgrade": false, "staff": false, "enh_id": "0196ba87-e691-7757-a7dd-b3fcba14451c", "req_reputation": 0, "req_class_points": 0, "req_quests": "", "quest_string_index": -1, "quest_string_value": 0, "meta": "", "color": "ffffff", "pid": 382}, {"id": "0196ba87-e698-771d-b7fa-7b48a8062af4", "name": "Paper Burning Sword", "description": "Huh. This looks pretty cool. *gives it a click* Whoa. WHOA.'", "type": "Sword", "element": "None", "file": "items/swords/BurningPencil.swf", "link": "BurningPencil", "icon": "iwsword", "equipment": "Weapon", "level": 1, "dps": 25, "range": 50, "rarity": "0196ba87-e69e-75ac-9885-91ec33cb57dc", "quantity": 1, "stack": 1, "cost": 0, "coins": 1, "diamonds": 0, "crystal": 0, "sell": true, "market": true, "temporary": false, "upgrade": false, "staff": false, "enh_id": "0196ba87-e691-7757-a7dd-b3fcba14451c", "req_reputation": 0, "req_class_points": 0, "req_quests": "", "quest_string_index": -1, "quest_string_value": 0, "meta": "", "color": "ffffff", "pid": 383}, {"id": "0196ba87-e698-771d-b7fa-7c0ea0d8ca85", "name": "Nebula Star Sword", "description": "Beta Item", "type": "Sword", "element": "None", "file": "items/swords/AnaxianNebulaBreaker.swf", "link": "AnaxianNebulaBreaker", "icon": "iwsword", "equipment": "Weapon", "level": 1, "dps": 25, "range": 50, "rarity": "0196ba87-e69e-75ac-9885-5b8b0c71fdb4", "quantity": 1, "stack": 1, "cost": 0, "coins": 1, "diamonds": 0, "crystal": 0, "sell": true, "market": true, "temporary": false, "upgrade": false, "staff": false, "enh_id": "0196ba87-e691-7757-a7dd-b3fcba14451c", "req_reputation": 0, "req_class_points": 0, "req_quests": "", "quest_string_index": -1, "quest_string_value": 0, "meta": "", "color": "ffffff", "pid": 384}, {"id": "0196ba87-e698-771d-b7fa-807929d0add1", "name": "Anaxian Blade", "description": "Exclusive Item", "type": "<PERSON>gger", "element": "None", "file": "items/daggers/AnaxianBalrogBlade3.swf", "link": "AnaxianBalrogBlade", "icon": "iwda<PERSON>", "equipment": "Weapon", "level": 1, "dps": 25, "range": 50, "rarity": "0196ba87-e69e-75ac-9885-940e18f9c3e6", "quantity": 1, "stack": 1, "cost": 250, "coins": 1, "diamonds": 0, "crystal": 0, "sell": true, "market": false, "temporary": false, "upgrade": true, "staff": false, "enh_id": "0196ba87-e691-7757-a7dd-b3fcba14451c", "req_reputation": 0, "req_class_points": 0, "req_quests": "", "quest_string_index": -1, "quest_string_value": 0, "meta": "", "color": "00ffff", "pid": 385}, {"id": "0196ba87-e698-771d-b7fa-87cfce1d6410", "name": "Azure Anaxian Blade", "description": "Exclusive Item", "type": "<PERSON>gger", "element": "None", "file": "items/daggers/AzureBalrogBlade.swf", "link": "AzureBalrogBlade", "icon": "iwda<PERSON>", "equipment": "Weapon", "level": 1, "dps": 25, "range": 50, "rarity": "0196ba87-e69e-75ac-9885-940e18f9c3e6", "quantity": 1, "stack": 1, "cost": 250, "coins": 1, "diamonds": 0, "crystal": 0, "sell": true, "market": false, "temporary": false, "upgrade": true, "staff": false, "enh_id": "0196ba87-e691-7757-a7dd-b3fcba14451c", "req_reputation": 0, "req_class_points": 0, "req_quests": "", "quest_string_index": -1, "quest_string_value": 0, "meta": "", "color": "00ffff", "pid": 386}, {"id": "0196ba87-e698-771d-b7fa-8b705cc03c0b", "name": "<PERSON><PERSON>", "description": "", "type": "<PERSON><PERSON>", "element": "None", "file": "", "link": "iibag", "icon": "iibag", "equipment": "<PERSON><PERSON>", "level": 1, "dps": 25, "range": 50, "rarity": "0196ba87-e69e-75ac-9885-5763facdf6ca", "quantity": 1, "stack": 6, "cost": 0, "coins": 0, "diamonds": 0, "crystal": 0, "sell": true, "market": false, "temporary": true, "upgrade": false, "staff": false, "enh_id": "0196ba87-e691-7757-a7dd-b3fcba14451c", "req_reputation": 0, "req_class_points": 0, "req_quests": "685", "quest_string_index": -1, "quest_string_value": 0, "meta": "", "color": "ffffff", "pid": 387}, {"id": "0196ba87-e698-771d-b7fa-8f516466e265", "name": "Sketch Grizzly Defeated", "description": "", "type": "<PERSON><PERSON>", "element": "None", "file": "", "link": "iibag", "icon": "iibag", "equipment": "<PERSON><PERSON>", "level": 1, "dps": 25, "range": 50, "rarity": "0196ba87-e69e-75ac-9885-5763facdf6ca", "quantity": 1, "stack": 6, "cost": 0, "coins": 0, "diamonds": 0, "crystal": 0, "sell": true, "market": false, "temporary": true, "upgrade": false, "staff": false, "enh_id": "0196ba87-e691-7757-a7dd-b3fcba14451c", "req_reputation": 0, "req_class_points": 0, "req_quests": "686", "quest_string_index": -1, "quest_string_value": 0, "meta": "", "color": "ffffff", "pid": 388}, {"id": "0196ba87-e698-771d-b7fa-9225677d3a1a", "name": "Crown Sketch", "description": "", "type": "<PERSON><PERSON>", "element": "None", "file": "", "link": "iibag", "icon": "iibag", "equipment": "<PERSON><PERSON>", "level": 1, "dps": 25, "range": 50, "rarity": "0196ba87-e69e-75ac-9885-5763facdf6ca", "quantity": 1, "stack": 1, "cost": 0, "coins": 0, "diamonds": 0, "crystal": 0, "sell": true, "market": false, "temporary": true, "upgrade": false, "staff": false, "enh_id": "0196ba87-e691-7757-a7dd-b3fcba14451c", "req_reputation": 0, "req_class_points": 0, "req_quests": "687", "quest_string_index": -1, "quest_string_value": 0, "meta": "", "color": "ffffff", "pid": 389}, {"id": "0196ba87-e698-771d-b7fa-963e22689ec0", "name": "???", "description": "", "type": "<PERSON><PERSON>", "element": "None", "file": "", "link": "iibag", "icon": "iibag", "equipment": "<PERSON><PERSON>", "level": 1, "dps": 25, "range": 50, "rarity": "0196ba87-e69e-75ac-9885-5763facdf6ca", "quantity": 1, "stack": 1, "cost": 0, "coins": 0, "diamonds": 0, "crystal": 0, "sell": true, "market": false, "temporary": true, "upgrade": false, "staff": false, "enh_id": "0196ba87-e691-7757-a7dd-b3fcba14451c", "req_reputation": 0, "req_class_points": 0, "req_quests": "688", "quest_string_index": -1, "quest_string_value": 0, "meta": "", "color": "ffffff", "pid": 390}, {"id": "0196ba87-e698-771d-b7fa-99d9706e8d0e", "name": "Sketch Seal's", "description": "Crafting gear in /Sketch", "type": "<PERSON><PERSON>", "element": "None", "file": "", "link": "iibag", "icon": "iibag", "equipment": "<PERSON><PERSON>", "level": 1, "dps": 25, "range": 50, "rarity": "0196ba87-e69e-75ac-9885-5763facdf6ca", "quantity": 1, "stack": 100, "cost": 0, "coins": 1, "diamonds": 0, "crystal": 0, "sell": true, "market": true, "temporary": false, "upgrade": false, "staff": false, "enh_id": "0196ba87-e691-7757-a7dd-b3fcba14451c", "req_reputation": 0, "req_class_points": 0, "req_quests": "", "quest_string_index": -1, "quest_string_value": 0, "meta": "", "color": "ffffff", "pid": 391}, {"id": "0196ba87-e698-771d-b7fa-9d576fea86cc", "name": "Sketch Letter of Stroke", "description": "The dual blades of the Sketch Stroke.", "type": "<PERSON>gger", "element": "None", "file": "items/daggers/BloodletterOirgCCkertas.swf", "link": "BloodletterOirg<PERSON>", "icon": "iwda<PERSON>", "equipment": "Weapon", "level": 1, "dps": 25, "range": 50, "rarity": "0196ba87-e69e-75ac-9885-91ec33cb57dc", "quantity": 1, "stack": 1, "cost": 0, "coins": 1, "diamonds": 0, "crystal": 0, "sell": true, "market": true, "temporary": false, "upgrade": false, "staff": false, "enh_id": "0196ba87-e691-7757-a7dd-b3fcba14451c", "req_reputation": 0, "req_class_points": 0, "req_quests": "", "quest_string_index": -1, "quest_string_value": 0, "meta": "", "color": "ffffff", "pid": 392}, {"id": "0196ba87-e698-771d-b7fa-a352287577e7", "name": "Nulgath Sword of Sketch", "description": "<PERSON><PERSON><PERSON><PERSON>;<PERSON>byss General's monstrous form is the result of his original form shifting to another reality. His sword has also shifted. but sketch", "type": "Sword", "element": "None", "file": "items/swords/MiltoniusNulgathswordKertas.swf", "link": "MiltoniusNulgathswordKertas", "icon": "iwsword", "equipment": "Weapon", "level": 1, "dps": 25, "range": 50, "rarity": "0196ba87-e69e-75ac-9885-91ec33cb57dc", "quantity": 1, "stack": 1, "cost": 0, "coins": 1, "diamonds": 0, "crystal": 0, "sell": true, "market": true, "temporary": false, "upgrade": false, "staff": false, "enh_id": "0196ba87-e691-7757-a7dd-b3fcba14451c", "req_reputation": 0, "req_class_points": 0, "req_quests": "", "quest_string_index": -1, "quest_string_value": 0, "meta": "", "color": "ffffff", "pid": 393}, {"id": "0196ba87-e698-771d-b7fa-a6bbfeeb7dff", "name": "Crystal Phoenix Sketch of Nulgath", "description": "As he experiments with alchemy;<PERSON><PERSON><PERSON><PERSON> uses his power to trans-mutate the Phoenix Blade into an indestructible crystal sword. What other iconic items will <PERSON><PERSON><PERSON><PERSON> manipulate next?", "type": "Sword", "element": "None", "file": "items/swords/MiltonPoolPhoenixSwordKertas.swf", "link": "MiltonPoolPhoenixSwordKertas", "icon": "iwsword", "equipment": "Weapon", "level": 1, "dps": 25, "range": 50, "rarity": "0196ba87-e69e-75ac-9885-91ec33cb57dc", "quantity": 1, "stack": 1, "cost": 0, "coins": 1, "diamonds": 0, "crystal": 0, "sell": true, "market": true, "temporary": false, "upgrade": false, "staff": false, "enh_id": "0196ba87-e691-7757-a7dd-b3fcba14451c", "req_reputation": 0, "req_class_points": 0, "req_quests": "", "quest_string_index": -1, "quest_string_value": 0, "meta": "", "color": "ffffff", "pid": 394}, {"id": "0196ba87-e698-771d-b7fa-abe92170035d", "name": "Oblivion Sword Sketch of Nulgath", "description": "This mysterious;living blade has destroyed innumerable lives. You know of only one who has had the power to wield it;the old one. And now you may wield it's power and live to tell about it… for Sket<PERSON>.", "type": "Sword", "element": "None", "file": "items/swords/MiltoniusPencilsword01.swf", "link": "MiltoniusPencilsword01", "icon": "iwsword", "equipment": "Weapon", "level": 1, "dps": 25, "range": 50, "rarity": "0196ba87-e69e-75ac-9885-91ec33cb57dc", "quantity": 1, "stack": 1, "cost": 0, "coins": 1, "diamonds": 0, "crystal": 0, "sell": true, "market": true, "temporary": false, "upgrade": false, "staff": false, "enh_id": "0196ba87-e691-7757-a7dd-b3fcba14451c", "req_reputation": 0, "req_class_points": 0, "req_quests": "", "quest_string_index": -1, "quest_string_value": 0, "meta": "", "color": "ffffff", "pid": 395}, {"id": "0196ba87-e698-771d-b7fa-ad7e5d9be155", "name": "Sketch Pirate Cutlass", "description": "none...", "type": "Sword", "element": "None", "file": "items/swords/SketchRhubarbCutlass.swf", "link": "SketchRhubarbCutlass", "icon": "iwsword", "equipment": "Weapon", "level": 1, "dps": 25, "range": 50, "rarity": "0196ba87-e69e-75ac-9885-91ec33cb57dc", "quantity": 1, "stack": 1, "cost": 0, "coins": 1, "diamonds": 0, "crystal": 0, "sell": true, "market": true, "temporary": false, "upgrade": false, "staff": false, "enh_id": "0196ba87-e691-7757-a7dd-b3fcba14451c", "req_reputation": 0, "req_class_points": 0, "req_quests": "", "quest_string_index": -1, "quest_string_value": 0, "meta": "", "color": "ffffff", "pid": 396}, {"id": "0196ba87-e698-771d-b7fa-b2cc6d75ac25", "name": "Sketch Pirate Captain <PERSON><PERSON>", "description": "Everyone knows you're in charge now. Make those scallywags walk the plank!", "type": "<PERSON><PERSON>", "element": "None", "file": "items/helms/SketchPirateCaptainhat1.swf", "link": "SketchPirateCaptainhat1", "icon": "iihelm", "equipment": "he", "level": 1, "dps": 25, "range": 50, "rarity": "0196ba87-e69e-75ac-9885-91ec33cb57dc", "quantity": 1, "stack": 1, "cost": 0, "coins": 1, "diamonds": 0, "crystal": 0, "sell": true, "market": true, "temporary": false, "upgrade": false, "staff": false, "enh_id": "0196ba87-e691-7757-a7dd-b3fcba14451c", "req_reputation": 0, "req_class_points": 0, "req_quests": "", "quest_string_index": -1, "quest_string_value": 0, "meta": "", "color": "ffffff", "pid": 397}, {"id": "0196ba87-e698-771d-b7fa-b4f7ec7310c0", "name": "Sketch Pirate Captain Hat", "description": "Everyone knows you're in charge now. Make those scallywags walk the plank!", "type": "<PERSON><PERSON>", "element": "None", "file": "items/helms/SketchPirateCaptainhat2.swf", "link": "SketchPirateCaptainhat2", "icon": "iihelm", "equipment": "he", "level": 1, "dps": 25, "range": 50, "rarity": "0196ba87-e69e-75ac-9885-91ec33cb57dc", "quantity": 1, "stack": 1, "cost": 0, "coins": 1, "diamonds": 0, "crystal": 0, "sell": true, "market": true, "temporary": false, "upgrade": false, "staff": false, "enh_id": "0196ba87-e691-7757-a7dd-b3fcba14451c", "req_reputation": 0, "req_class_points": 0, "req_quests": "", "quest_string_index": -1, "quest_string_value": 0, "meta": "", "color": "ffffff", "pid": 398}, {"id": "0196ba87-e698-771d-b7fa-bb3d70c0de46", "name": "Sketch Royal Blade Alteon", "description": "It may seem abnormally heavy at first;but if the wielder is a worthy owner;the soothing power of light helps support the weight you must carry. It's the same way your burdened responsibility as a faithful commander must carry to save the world.", "type": "Sword", "element": "None", "file": "items/swords/SketchAltDragSword.swf", "link": "SketchAltDragSword", "icon": "iwsword", "equipment": "Weapon", "level": 1, "dps": 25, "range": 50, "rarity": "0196ba87-e69e-75ac-9885-87eb9358ada7", "quantity": 1, "stack": 1, "cost": 5000, "coins": 0, "diamonds": 0, "crystal": 0, "sell": true, "market": true, "temporary": false, "upgrade": false, "staff": false, "enh_id": "0196ba87-e691-7757-a7dd-b3fcba14451c", "req_reputation": 0, "req_class_points": 0, "req_quests": "", "quest_string_index": -1, "quest_string_value": 0, "meta": "", "color": "ffffff", "pid": 399}, {"id": "0196ba87-e698-771d-b7fa-be370e27abae", "name": "Sketch Baby Dragon", "description": "You must be very lucky to own such a dragon…", "type": "Pet", "element": "None", "file": "items/pets/SketchBabyDragon1.swf", "link": "SketchBabyDragon1", "icon": "iipet", "equipment": "pe", "level": 1, "dps": 25, "range": 50, "rarity": "0196ba87-e69e-75ac-9885-91ec33cb57dc", "quantity": 1, "stack": 1, "cost": 0, "coins": 1, "diamonds": 0, "crystal": 0, "sell": true, "market": true, "temporary": false, "upgrade": false, "staff": false, "enh_id": "0196ba87-e691-7757-a7dd-b3fcba14451c", "req_reputation": 0, "req_class_points": 0, "req_quests": "", "quest_string_index": -1, "quest_string_value": 0, "meta": "", "color": "ffffff", "pid": 400}, {"id": "0196ba87-e698-771d-b7fa-c03ca2f5fb6a", "class_id": "0196ba87-e68f-7738-8b2a-f0133a70ad86", "name": "Rogue (Class)", "description": "Rogues follow their own set of rules. With a combination of speed;cunning and poisons;you take your enemies by surprise.", "type": "Class", "element": "None", "file": "BaseRogue2xx.swf", "link": "Rogue2", "icon": "iiclass", "equipment": "ar", "level": 1, "dps": 25, "range": 50, "rarity": "0196ba87-e69e-75ac-9885-5b8b0c71fdb4", "quantity": 1, "stack": 1, "cost": 1000, "coins": 0, "diamonds": 0, "crystal": 0, "sell": true, "market": true, "temporary": false, "upgrade": false, "staff": false, "enh_id": "0196ba87-e691-7757-a7dd-b3fcba14451c", "req_reputation": 0, "req_class_points": 0, "req_quests": "", "quest_string_index": -1, "quest_string_value": 0, "meta": "", "color": "ffffff", "pid": 401, "req_class_id": "0196ba87-e68f-7738-8b2a-f0133a70ad86"}, {"id": "0196ba87-e698-771d-b7fa-c55d98cbbe70", "class_id": "0196ba87-e68f-7738-8b2a-e78aaf2afc0e", "name": "Warrior (Class)", "description": "The heart of a warrior is filled with courage and strength. Your skills with weapons in close combat make you a powerful force on the battlefield.", "type": "Class", "element": "None", "file": "NewWarriorB2.swf", "link": "NewWarriorB2", "icon": "iiclass", "equipment": "ar", "level": 1, "dps": 25, "range": 50, "rarity": "0196ba87-e69e-75ac-9885-5b8b0c71fdb4", "quantity": 1, "stack": 1, "cost": 1000, "coins": 0, "diamonds": 0, "crystal": 0, "sell": true, "market": true, "temporary": false, "upgrade": false, "staff": false, "enh_id": "0196ba87-e691-7757-a7dd-b3fcba14451c", "req_reputation": 0, "req_class_points": 0, "req_quests": "", "quest_string_index": -1, "quest_string_value": 0, "meta": "", "color": "ffffff", "pid": 402, "req_class_id": "0196ba87-e68f-7738-8b2a-e78aaf2afc0e"}, {"id": "0196ba87-e698-771d-b7fa-ca29ae600046", "class_id": "0196ba87-e68f-7738-8b2a-e9ed3dc29c19", "name": "Mage (Class)", "description": "Magic courses through your veins. Your arcane powers enable you to cast powerful spells.", "type": "Class", "element": "None", "file": "BaseMage2xx.swf", "link": "Mage2", "icon": "iiclass", "equipment": "ar", "level": 1, "dps": 25, "range": 50, "rarity": "0196ba87-e69e-75ac-9885-5b8b0c71fdb4", "quantity": 1, "stack": 1, "cost": 1000, "coins": 0, "diamonds": 0, "crystal": 0, "sell": true, "market": true, "temporary": false, "upgrade": false, "staff": false, "enh_id": "0196ba87-e691-7757-a7dd-b3fcba14451c", "req_reputation": 0, "req_class_points": 0, "req_quests": "", "quest_string_index": -1, "quest_string_value": 0, "meta": "", "color": "ffffff", "pid": 403, "req_class_id": "0196ba87-e68f-7738-8b2a-e9ed3dc29c19"}, {"id": "0196ba87-e698-771d-b7fa-cde7a15b3738", "class_id": "0196ba87-e68f-7738-8b2a-ed99f085bd6d", "name": "<PERSON><PERSON><PERSON> (Class)", "description": "Healers are servants of good who use their powers to aid the sick;weak;and injured. Their powerful healing magic is often the difference between a groups victory or doom.", "type": "Class", "element": "None", "file": "NewHealerR2.swf", "link": "NewHealerB2", "icon": "iiclass", "equipment": "ar", "level": 1, "dps": 25, "range": 50, "rarity": "0196ba87-e69e-75ac-9885-5b8b0c71fdb4", "quantity": 1, "stack": 1, "cost": 1000, "coins": 0, "diamonds": 0, "crystal": 0, "sell": true, "market": true, "temporary": false, "upgrade": false, "staff": false, "enh_id": "0196ba87-e691-7757-a7dd-b3fcba14451c", "req_reputation": 0, "req_class_points": 0, "req_quests": "", "quest_string_index": -1, "quest_string_value": 0, "meta": "", "color": "ffffff", "pid": 404, "req_class_id": "0196ba87-e68f-7738-8b2a-ed99f085bd6d"}, {"id": "0196ba87-e698-771d-b7fa-d02756174678", "class_id": "0196ba87-e68f-7738-8b2a-e3cc230d3e83", "name": "Alpha Pirate (Class)", "description": "Pirates are opportunistic rogues and treasure hunters of the sea. Combat might not always go their way but they will do the best they can to make the most of any combat situation they find themselves in. You never really know what move a pirate will make next;and just as often;they don't know either.", "type": "Class", "element": "None", "file": "pirate2_skin.swf", "link": "Pirate2", "icon": "iiclass", "equipment": "ar", "level": 1, "dps": 25, "range": 50, "rarity": "0196ba87-e69e-75ac-9885-a9a3fe9ab6b1", "quantity": 1, "stack": 1, "cost": 0, "coins": 1, "diamonds": 0, "crystal": 0, "sell": true, "market": false, "temporary": false, "upgrade": false, "staff": false, "enh_id": "0196ba87-e691-7757-a7dd-b3fcba14451c", "faction_id": "0196ba87-e693-7421-951a-e51b5a13f898", "req_reputation": 302500, "req_class_points": 0, "req_quests": "", "quest_string_index": -1, "quest_string_value": 0, "meta": "", "color": "ffffff", "pid": 405, "req_class_id": "0196ba87-e68f-7738-8b2a-e3cc230d3e83"}, {"id": "0196ba87-e698-771d-b7fa-d4b591d30a7d", "name": "Soul King Guard", "description": "Never more........ see what i can do", "type": "Armor", "element": "None", "file": "datapaw11.swf", "link": "Datapaw", "icon": "i<PERSON>or", "equipment": "co", "level": 1, "dps": 25, "range": 50, "rarity": "0196ba87-e69e-75ac-9885-5763facdf6ca", "quantity": 1, "stack": 1, "cost": 0, "coins": 0, "diamonds": 0, "crystal": 0, "sell": true, "market": true, "temporary": false, "upgrade": false, "staff": false, "enh_id": "0196ba87-e691-7757-a7dd-b3fcba14451c", "req_reputation": 0, "req_class_points": 0, "req_quests": "", "quest_string_index": -1, "quest_string_value": 0, "meta": "", "color": "ffffff", "pid": 406}, {"id": "0196ba87-e698-771d-b7fa-d8be6be4db19", "name": "Replacement Item #1459 - Erebus", "description": "Never more........ see what i can do", "type": "<PERSON><PERSON>", "element": "None", "file": "items/helms/Datapawhelm.swf", "link": "DatapawHelm", "icon": "iihelm", "equipment": "he", "level": 1, "dps": 25, "range": 50, "rarity": "0196ba87-e69e-75ac-9885-5124623d1f3f", "quantity": 1, "stack": 1, "cost": 0, "coins": 0, "diamonds": 0, "crystal": 0, "sell": true, "market": true, "temporary": false, "upgrade": false, "staff": false, "enh_id": "0196ba87-e691-7757-a7dd-b3fcba14451c", "req_reputation": 0, "req_class_points": 0, "req_quests": "", "quest_string_index": -1, "quest_string_value": 0, "meta": "", "color": "ffffff", "pid": 407}, {"id": "0196ba87-e698-771d-b7fa-de6617785721", "name": "Soul King Guard Aura", "description": "", "type": "Cape", "element": "None", "file": "items/capes/SoulKingGuardAura.swf", "link": "SoulKingGuardAura", "icon": "iicape", "equipment": "ba", "level": 1, "dps": 25, "range": 50, "rarity": "0196ba87-e69e-75ac-9885-5763facdf6ca", "quantity": 1, "stack": 1, "cost": 0, "coins": 0, "diamonds": 0, "crystal": 0, "sell": true, "market": true, "temporary": false, "upgrade": false, "staff": false, "enh_id": "0196ba87-e691-7757-a7dd-b3fcba14451c", "req_reputation": 0, "req_class_points": 0, "req_quests": "", "quest_string_index": -1, "quest_string_value": 0, "meta": "", "color": "ffffff", "pid": 408}, {"id": "0196ba87-e698-771d-b7fa-e30aac003ea6", "name": "Guardian Soul King", "description": "", "type": "Armor", "element": "None", "file": "DatapawFixed1.swf", "link": "datapaw11", "icon": "i<PERSON>or", "equipment": "co", "level": 1, "dps": 25, "range": 50, "rarity": "0196ba87-e69e-75ac-9885-5763facdf6ca", "quantity": 1, "stack": 1, "cost": 0, "coins": 0, "diamonds": 0, "crystal": 0, "sell": true, "market": true, "temporary": false, "upgrade": false, "staff": false, "enh_id": "0196ba87-e691-7757-a7dd-b3fcba14451c", "req_reputation": 0, "req_class_points": 0, "req_quests": "", "quest_string_index": -1, "quest_string_value": 0, "meta": "", "color": "ffffff", "pid": 409}, {"id": "0196ba87-e698-771d-b7fa-e5dfc514db04", "name": "<PERSON><PERSON>", "description": "", "type": "Armor", "element": "None", "file": "okayokay.swf", "link": "HBDynoRider", "icon": "i<PERSON>or", "equipment": "co", "level": 1, "dps": 25, "range": 50, "rarity": "0196ba87-e69e-75ac-9885-5763facdf6ca", "quantity": 1, "stack": 1, "cost": 0, "coins": 0, "diamonds": 0, "crystal": 0, "sell": true, "market": true, "temporary": false, "upgrade": false, "staff": false, "enh_id": "0196ba87-e691-7757-a7dd-b3fcba14451c", "req_reputation": 0, "req_class_points": 0, "req_quests": "", "quest_string_index": -1, "quest_string_value": 0, "meta": "", "color": "ffffff", "pid": 410}, {"id": "0196ba87-e698-771d-b7fa-eb9c78cd75e0", "name": "gwe yong", "description": "", "type": "Armor", "element": "None", "file": "Hakdog.swf", "link": "HBDynoRider", "icon": "i<PERSON>or", "equipment": "co", "level": 1, "dps": 25, "range": 50, "rarity": "0196ba87-e69e-75ac-9885-5763facdf6ca", "quantity": 1, "stack": 1, "cost": 0, "coins": 0, "diamonds": 0, "crystal": 0, "sell": true, "market": true, "temporary": false, "upgrade": false, "staff": false, "enh_id": "0196ba87-e691-7757-a7dd-b3fcba14451c", "req_reputation": 0, "req_class_points": 0, "req_quests": "", "quest_string_index": -1, "quest_string_value": 0, "meta": "", "color": "ffffff", "pid": 411}, {"id": "0196ba87-e698-771d-b7fa-ec8df2aa22b8", "name": "Supreme katana Cape", "description": "This cape are limted get this as soon possible because you will never get this again", "type": "Cape", "element": "None", "file": "items/capes/DatapawCapeKatana1.swf", "link": "DatapawCapeKatana", "icon": "iicape", "equipment": "ba", "level": 1, "dps": 25, "range": 50, "rarity": "0196ba87-e69e-75ac-9885-af4e24f6923c", "quantity": 1, "stack": 1, "cost": 1500, "coins": 1, "diamonds": 0, "crystal": 0, "sell": true, "market": true, "temporary": false, "upgrade": false, "staff": false, "enh_id": "0196ba87-e691-7757-a7dd-b3fcba14451c", "req_reputation": 0, "req_class_points": 0, "req_quests": "", "quest_string_index": -1, "quest_string_value": 0, "meta": "", "color": "ffffff", "pid": 412}, {"id": "0196ba87-e698-771d-b7fa-f27ad6ac9292", "name": "Ultima Devine Cape", "description": "This Cape has combined 7 strongest sword;this only 5x quantity", "type": "Cape", "element": "None", "file": "items/capes/DatapawDevineKatana1.swf", "link": "DatapawDevineKatana", "icon": "iicape", "equipment": "ba", "level": 1, "dps": 25, "range": 50, "rarity": "0196ba87-e69e-75ac-9885-af4e24f6923c", "quantity": 1, "stack": 1, "cost": 2000, "coins": 1, "diamonds": 0, "crystal": 0, "sell": true, "market": true, "temporary": false, "upgrade": false, "staff": false, "enh_id": "0196ba87-e691-7757-a7dd-b3fcba14451c", "req_reputation": 0, "req_class_points": 0, "req_quests": "", "quest_string_index": -1, "quest_string_value": 0, "meta": "", "color": "ffffff", "pid": 413}, {"id": "0196ba87-e698-771d-b7fa-f41b23b14656", "name": "Red Hunting Hood", "description": "-", "type": "<PERSON><PERSON>", "element": "None", "file": "items/helms/Hoodredriding1.swf", "link": "Hoodredriding1", "icon": "iihelm", "equipment": "he", "level": 1, "dps": 25, "range": 50, "rarity": "0196ba87-e69e-75ac-9885-5b8b0c71fdb4", "quantity": 1, "stack": 1, "cost": 0, "coins": 1, "diamonds": 0, "crystal": 0, "sell": true, "market": true, "temporary": false, "upgrade": false, "staff": false, "enh_id": "0196ba87-e691-7757-a7dd-b3fcba14451c", "req_reputation": 0, "req_class_points": 0, "req_quests": "", "quest_string_index": -1, "quest_string_value": 0, "meta": "", "color": "ffffff", "pid": 414}, {"id": "0196ba87-e698-771d-b7fa-fa3dc58ae838", "name": "Crystallis Rescuer", "description": "I <3 Pink", "type": "Armor", "element": "None", "file": "CrystallisRescuerCasual.swf", "link": "CrystallisRescuerCasual", "icon": "armor", "equipment": "co", "level": 1, "dps": 25, "range": 50, "rarity": "0196ba87-e69e-75ac-9885-5b8b0c71fdb4", "quantity": 1, "stack": 1, "cost": 100, "coins": 0, "diamonds": 0, "crystal": 0, "sell": true, "market": true, "temporary": false, "upgrade": false, "staff": true, "enh_id": "0196ba87-e691-7757-a7dd-b3fcba14451c", "faction_id": "0196ba87-e693-7421-951a-e35274e5fe7a", "req_reputation": 0, "req_class_points": 0, "req_quests": "", "quest_string_index": -1, "quest_string_value": 0, "meta": "", "color": "fffaff", "pid": 415}, {"id": "0196ba87-e698-771d-b7fa-fd6129b30b5f", "name": "Plate of the Fallen", "description": "Foes will want to make sure you stay down because if there is just a ounce of breath left in a member of the fallen;they will strike their enemies down with all their might. EVIL WILL FALL AS THE FALLEN", "type": "Armor", "element": "None", "file": "TheFallen-10Jul13.swf", "link": "TheFallen", "icon": "i<PERSON>or", "equipment": "co", "level": 1, "dps": 25, "range": 50, "rarity": "0196ba87-e69e-75ac-9885-940e18f9c3e6", "quantity": 1, "stack": 1, "cost": 0, "coins": 1, "diamonds": 0, "crystal": 0, "sell": true, "market": true, "temporary": false, "upgrade": false, "staff": false, "enh_id": "0196ba87-e691-7757-a7dd-b3fcba14451c", "req_reputation": 0, "req_class_points": 0, "req_quests": "", "quest_string_index": -1, "quest_string_value": 0, "meta": "", "color": "8080c0", "pid": 416}, {"id": "0196ba87-e698-771d-b7fb-03e819180ca3", "name": "<PERSON> of The Fallen", "description": "Evil will never know the face of The Fallen;but they will always remember The Fallen's smirk as their lives draw to an end!", "type": "<PERSON><PERSON>", "element": "None", "file": "items/helms/TheFallensHood2-20150219_r1.swf", "link": "TheFallensHood3b", "icon": "iihelm", "equipment": "he", "level": 1, "dps": 25, "range": 50, "rarity": "0196ba87-e69e-75ac-9885-940e18f9c3e6", "quantity": 1, "stack": 1, "cost": 0, "coins": 1, "diamonds": 0, "crystal": 0, "sell": true, "market": true, "temporary": false, "upgrade": false, "staff": false, "enh_id": "0196ba87-e691-7757-a7dd-b3fcba14451c", "req_reputation": 0, "req_class_points": 0, "req_quests": "", "quest_string_index": -1, "quest_string_value": 0, "meta": "", "color": "8080c0", "pid": 417}, {"id": "0196ba87-e698-771d-b7fb-077716c81252", "name": "Cape of the Fallen", "description": "Wrap your fallen hopes in this tattered cape. They wont save you now.", "type": "Cape", "element": "None", "file": "items/capes/SFCCape.swf", "link": "SFCCape", "icon": "iicape", "equipment": "ba", "level": 1, "dps": 25, "range": 50, "rarity": "0196ba87-e69e-75ac-9885-940e18f9c3e6", "quantity": 1, "stack": 1, "cost": 0, "coins": 1, "diamonds": 0, "crystal": 0, "sell": true, "market": true, "temporary": false, "upgrade": false, "staff": false, "enh_id": "0196ba87-e691-7757-a7dd-b3fcba14451c", "req_reputation": 0, "req_class_points": 0, "req_quests": "", "quest_string_index": -1, "quest_string_value": 0, "meta": "", "color": "8080c0", "pid": 418}, {"id": "0196ba87-e698-771d-b7fb-0833b6935807", "name": "Greenguard Ranger", "description": "Skulking through Greenguard Forest;the Greenguard Rangers are stealthy;wealthy;and wise to the ways of their opponents.", "type": "Armor", "element": "None", "file": "rangerbido.swf", "link": "RangerBido", "icon": "i<PERSON>or", "equipment": "co", "level": 1, "dps": 25, "range": 50, "rarity": "0196ba87-e69e-75ac-9885-5b8b0c71fdb4", "quantity": 1, "stack": 1, "cost": 0, "coins": 1, "diamonds": 0, "crystal": 0, "sell": true, "market": true, "temporary": false, "upgrade": false, "staff": false, "enh_id": "0196ba87-e691-7757-a7dd-b3fcba14451c", "req_reputation": 0, "req_class_points": 0, "req_quests": "", "quest_string_index": -1, "quest_string_value": 0, "meta": "", "color": "ffffff", "pid": 419}, {"id": "0196ba87-e698-771d-b7fb-0d623caaa99d", "name": "<PERSON><PERSON><PERSON>", "description": "Skulking through Greenguard Forest;the Greenguard Rangers are stealthy;wealthy and wise to the ways of their opponents.", "type": "<PERSON><PERSON>", "element": "None", "file": "items/helms/rangerbidohoodm.swf", "link": "RangerBidoHoodM", "icon": "iihelm", "equipment": "he", "level": 1, "dps": 25, "range": 50, "rarity": "0196ba87-e69e-75ac-9885-5b8b0c71fdb4", "quantity": 1, "stack": 1, "cost": 0, "coins": 1, "diamonds": 0, "crystal": 0, "sell": true, "market": true, "temporary": false, "upgrade": false, "staff": false, "enh_id": "0196ba87-e691-7757-a7dd-b3fcba14451c", "req_reputation": 0, "req_class_points": 0, "req_quests": "", "quest_string_index": -1, "quest_string_value": 0, "meta": "", "color": "ffffff", "pid": 420}, {"id": "0196ba87-e698-771d-b7fb-137e93a9b18b", "name": "Greenguard Ranger's Quiver", "description": "Skulking through Greenguard Forest;the Greenguard Rangers are stealthy;wealthy and wise to the ways of their opponents.", "type": "Cape", "element": "None", "file": "items/capes/rangerbidocape.swf", "link": "RangerBidoCape", "icon": "iicape", "equipment": "ba", "level": 1, "dps": 25, "range": 50, "rarity": "0196ba87-e69e-75ac-9885-5b8b0c71fdb4", "quantity": 1, "stack": 1, "cost": 0, "coins": 1, "diamonds": 0, "crystal": 0, "sell": true, "market": true, "temporary": false, "upgrade": false, "staff": false, "enh_id": "0196ba87-e691-7757-a7dd-b3fcba14451c", "req_reputation": 0, "req_class_points": 0, "req_quests": "", "quest_string_index": -1, "quest_string_value": 0, "meta": "", "color": "ffffff", "pid": 421}, {"id": "0196ba87-e698-771d-b7fb-1600eb413fb1", "name": "Rogue Fiend <PERSON>", "description": "Skulking through Greenguard Forest;the Greenguard Rangers are stealthy;wealthy and wise to the ways of their opponents.", "type": "Sword", "element": "None", "file": "items/bows/roguefiendkat.swf", "link": "RogueFiendKat", "icon": "iwsword", "equipment": "Weapon", "level": 1, "dps": 25, "range": 50, "rarity": "0196ba87-e69e-75ac-9885-5763facdf6ca", "quantity": 1, "stack": 1, "cost": 0, "coins": 1, "diamonds": 0, "crystal": 0, "sell": true, "market": true, "temporary": false, "upgrade": false, "staff": false, "enh_id": "0196ba87-e691-7757-a7dd-b3fcba14451c", "req_reputation": 0, "req_class_points": 0, "req_quests": "", "quest_string_index": -1, "quest_string_value": 0, "meta": "", "color": "ffffff", "pid": 422}, {"id": "0196ba87-e698-771d-b7fb-1a8005dac3a6", "name": "Greenguard Ranger's Bow", "description": "Skulking through Greenguard Forest;the Greenguard Rangers are stealthy;wealthy and wise to the ways of their opponents.", "type": "Sword", "element": "None", "file": "items/bows/rangerbidobowr1.swf", "link": "RangerBidoBow", "icon": "iwsword", "equipment": "Weapon", "level": 1, "dps": 25, "range": 50, "rarity": "0196ba87-e69e-75ac-9885-5763facdf6ca", "quantity": 1, "stack": 1, "cost": 0, "coins": 1, "diamonds": 0, "crystal": 0, "sell": true, "market": true, "temporary": false, "upgrade": false, "staff": false, "enh_id": "0196ba87-e691-7757-a7dd-b3fcba14451c", "req_reputation": 0, "req_class_points": 0, "req_quests": "", "quest_string_index": -1, "quest_string_value": 0, "meta": "", "color": "ffffff", "pid": 423}, {"id": "0196ba87-e698-771d-b7fb-1dd0662162b1", "name": "Classic Healer Armor", "description": "Healers are servants of good who use their powers to aid the sick;weak;and injured. Their powerful healing magic is often the difference between a groups victory or doom.", "type": "Armor", "element": "None", "file": "priest_skin.swf", "link": "Priest", "icon": "i<PERSON>or", "equipment": "co", "level": 1, "dps": 25, "range": 50, "rarity": "0196ba87-e69e-75ac-9885-5b8b0c71fdb4", "quantity": 1, "stack": 1, "cost": 3000, "coins": 0, "diamonds": 0, "crystal": 0, "sell": true, "market": true, "temporary": false, "upgrade": false, "staff": false, "enh_id": "0196ba87-e691-7757-a7dd-b3fcba14451c", "req_reputation": 0, "req_class_points": 0, "req_quests": "", "quest_string_index": -1, "quest_string_value": 0, "meta": "", "color": "ffffff", "pid": 424}, {"id": "0196ba87-e698-771d-b7fb-2008892fe5b3", "name": "Classic Rogue Armor", "description": "Rogues follow their own set of rules. With a combination of speed;cunning and poisons you take your enemies by surprise.", "type": "Armor", "element": "None", "file": "rogue_skin.swf", "link": "Rogue", "icon": "i<PERSON>or", "equipment": "co", "level": 1, "dps": 25, "range": 50, "rarity": "0196ba87-e69e-75ac-9885-5b8b0c71fdb4", "quantity": 1, "stack": 1, "cost": 3000, "coins": 0, "diamonds": 0, "crystal": 0, "sell": true, "market": true, "temporary": false, "upgrade": false, "staff": false, "enh_id": "0196ba87-e691-7757-a7dd-b3fcba14451c", "req_reputation": 0, "req_class_points": 0, "req_quests": "", "quest_string_index": -1, "quest_string_value": 0, "meta": "", "color": "ffffff", "pid": 425}, {"id": "0196ba87-e698-771d-b7fb-2495eeadf945", "name": "Classic Mage <PERSON>or", "description": "Magic courses through your veins. Your arcane powers enable you to cast powerful spells.", "type": "Armor", "element": "None", "file": "mage_skin.swf", "link": "Mage", "icon": "i<PERSON>or", "equipment": "co", "level": 1, "dps": 25, "range": 50, "rarity": "0196ba87-e69e-75ac-9885-5b8b0c71fdb4", "quantity": 1, "stack": 1, "cost": 3000, "coins": 0, "diamonds": 0, "crystal": 0, "sell": true, "market": true, "temporary": false, "upgrade": false, "staff": false, "enh_id": "0196ba87-e691-7757-a7dd-b3fcba14451c", "req_reputation": 0, "req_class_points": 0, "req_quests": "", "quest_string_index": -1, "quest_string_value": 0, "meta": "", "color": "ffffff", "pid": 426}, {"id": "0196ba87-e698-771d-b7fb-2aea1f1897d1", "name": "Undead Pirate Armor", "description": "An undead pirate's life for me!", "type": "Armor", "element": "None", "file": "undead4_skin.swf", "link": "Undead4", "icon": "i<PERSON>or", "equipment": "co", "level": 1, "dps": 25, "range": 50, "rarity": "0196ba87-e69e-75ac-9885-5c6a3b464a67", "quantity": 1, "stack": 1, "cost": 10000, "coins": 0, "diamonds": 0, "crystal": 0, "sell": true, "market": true, "temporary": false, "upgrade": false, "staff": false, "enh_id": "0196ba87-e691-7757-a7dd-b3fcba14451c", "req_reputation": 0, "req_class_points": 0, "req_quests": "", "quest_string_index": -1, "quest_string_value": 0, "meta": "", "color": "ffffff", "pid": 427}, {"id": "0196ba87-e698-771d-b7fb-2c751af88b3e", "name": "Undead Fighter Armor", "description": "Surprisingly strong for something that is just cartilage and calcium. Be careful;though; sticks and stones may break these bones!", "type": "Armor", "element": "None", "file": "TempUndeadMelee_skin.swf", "link": "TempUndeadMelee", "icon": "i<PERSON>or", "equipment": "co", "level": 1, "dps": 25, "range": 50, "rarity": "0196ba87-e69e-75ac-9885-5c6a3b464a67", "quantity": 1, "stack": 1, "cost": 10000, "coins": 0, "diamonds": 0, "crystal": 0, "sell": true, "market": true, "temporary": false, "upgrade": false, "staff": false, "enh_id": "0196ba87-e691-7757-a7dd-b3fcba14451c", "req_reputation": 0, "req_class_points": 0, "req_quests": "", "quest_string_index": -1, "quest_string_value": 0, "meta": "", "color": "ffffff", "pid": 428}, {"id": "0196ba87-e698-771d-b7fb-3249336f1866", "name": "Undead Mage Armor", "description": "You've been turned into a skeletal undead!", "type": "Armor", "element": "None", "file": "TempUndeadMage_skin.swf", "link": "TempUndeadMage", "icon": "i<PERSON>or", "equipment": "co", "level": 1, "dps": 25, "range": 50, "rarity": "0196ba87-e69e-75ac-9885-5c6a3b464a67", "quantity": 1, "stack": 1, "cost": 10000, "coins": 0, "diamonds": 0, "crystal": 0, "sell": true, "market": true, "temporary": false, "upgrade": false, "staff": false, "enh_id": "0196ba87-e691-7757-a7dd-b3fcba14451c", "req_reputation": 0, "req_class_points": 0, "req_quests": "", "quest_string_index": -1, "quest_string_value": 0, "meta": "", "color": "ffffff", "pid": 429}, {"id": "0196ba87-e698-771d-b7fb-34bdeea0b68e", "name": "Default Sword", "description": "If you have a Legend only weapon equipped when your membership upgrade expires or when merging a weapon that's equipped into something else;this weapon is temporarily equipped by default.", "type": "Sword", "element": "None", "file": "items/swords/sword01.swf", "link": "sword01", "icon": "iwsword", "equipment": "Weapon", "level": 1, "dps": 25, "range": 50, "rarity": "0196ba87-e69e-75ac-9885-5763facdf6ca", "quantity": 1, "stack": 1, "cost": 800, "coins": 0, "diamonds": 0, "crystal": 0, "sell": true, "market": true, "temporary": false, "upgrade": false, "staff": false, "enh_id": "0196ba87-e691-7757-a7dd-b3fcba14451c", "req_reputation": 0, "req_class_points": 0, "req_quests": "", "quest_string_index": -1, "quest_string_value": 0, "meta": "", "color": "ffffff", "pid": 430}, {"id": "0196ba87-e698-771d-b7fb-3ba3b0d4bfe5", "name": "Genesis Sword", "description": "No one's quite sure where this sword came from but it was one of the first to be found in this land.", "type": "Sword", "element": "None", "file": "items/swords/sword01a.swf", "link": "WarriorFWeapon", "icon": "iwsword", "equipment": "Weapon", "level": 1, "dps": 25, "range": 50, "rarity": "0196ba87-e69e-75ac-9885-5763facdf6ca", "quantity": 1, "stack": 1, "cost": 5000, "coins": 0, "diamonds": 0, "crystal": 0, "sell": true, "market": true, "temporary": false, "upgrade": false, "staff": false, "enh_id": "0196ba87-e691-7757-a7dd-b3fcba14451c", "req_reputation": 0, "req_class_points": 0, "req_quests": "", "quest_string_index": -1, "quest_string_value": 0, "meta": "", "color": "ffffff", "pid": 431}, {"id": "0196ba87-e698-771d-b7fb-3f10c7590b24", "name": "Dragon Saw", "description": "Carved from a massive scale;the damage this sword deals is outweighed only by its cool look.", "type": "Sword", "element": "None", "file": "items/swords/sword04.swf", "link": "sword04", "icon": "iwsword", "equipment": "Weapon", "level": 1, "dps": 25, "range": 50, "rarity": "0196ba87-e69e-75ac-9885-5763facdf6ca", "quantity": 1, "stack": 1, "cost": 2500, "coins": 0, "diamonds": 0, "crystal": 0, "sell": true, "market": true, "temporary": false, "upgrade": false, "staff": false, "enh_id": "0196ba87-e691-7757-a7dd-b3fcba14451c", "req_reputation": 0, "req_class_points": 0, "req_quests": "", "quest_string_index": -1, "quest_string_value": 0, "meta": "", "color": "ffffff", "pid": 432}, {"id": "0196ba87-e698-771d-b7fb-424eecc1dcd7", "name": "Sun Sabre", "description": "Sword of the burning sun!", "type": "Sword", "element": "None", "file": "items/swords/sword46.swf", "link": "sword46", "icon": "iwsword", "equipment": "Weapon", "level": 1, "dps": 25, "range": 50, "rarity": "0196ba87-e69e-75ac-9885-5763facdf6ca", "quantity": 1, "stack": 1, "cost": 20000, "coins": 0, "diamonds": 0, "crystal": 0, "sell": true, "market": true, "temporary": false, "upgrade": false, "staff": false, "enh_id": "0196ba87-e691-7757-a7dd-b3fcba14451c", "req_reputation": 0, "req_class_points": 0, "req_quests": "", "quest_string_index": -1, "quest_string_value": 0, "meta": "", "color": "ffffff", "pid": 433}, {"id": "0196ba87-e698-771d-b7fb-44a19c4129ec", "name": "Default Staff", "description": "Staff", "type": "Staff", "element": "None", "file": "items/staves/staff01.swf", "link": "staff01", "icon": "iwstaff", "equipment": "Weapon", "level": 1, "dps": 25, "range": 50, "rarity": "0196ba87-e69e-75ac-9885-5763facdf6ca", "quantity": 1, "stack": 1, "cost": 700, "coins": 0, "diamonds": 0, "crystal": 0, "sell": true, "market": true, "temporary": false, "upgrade": false, "staff": false, "enh_id": "0196ba87-e691-7757-a7dd-b3fcba14451c", "req_reputation": 0, "req_class_points": 0, "req_quests": "", "quest_string_index": -1, "quest_string_value": 0, "meta": "", "color": "ffffff", "pid": 434}, {"id": "0196ba87-e698-771d-b7fb-4850f6a0d67c", "name": "<PERSON><PERSON><PERSON>", "description": "<PERSON>gger", "type": "<PERSON>gger", "element": "None", "file": "items/daggers/dagger01.swf", "link": "dagger01", "icon": "iwda<PERSON>", "equipment": "Weapon", "level": 1, "dps": 25, "range": 50, "rarity": "0196ba87-e69e-75ac-9885-5763facdf6ca", "quantity": 1, "stack": 1, "cost": 700, "coins": 0, "diamonds": 0, "crystal": 0, "sell": true, "market": true, "temporary": false, "upgrade": false, "staff": false, "enh_id": "0196ba87-e691-7757-a7dd-b3fcba14451c", "req_reputation": 0, "req_class_points": 0, "req_quests": "", "quest_string_index": -1, "quest_string_value": 0, "meta": "", "color": "ffffff", "pid": 435}, {"id": "0196ba87-e698-771d-b7fb-4fefc2d0e188", "name": "Sword Breaker", "description": "This weapon was crafted by a strange race of beings. It was designed to be the only weapon capable of fighting a Starsword;but it was lost before it could be put into production.", "type": "Sword", "element": "None", "file": "items/swords/starswordbreaker.swf", "link": "starswordbreaker", "icon": "iwsword", "equipment": "Weapon", "level": 1, "dps": 25, "range": 50, "rarity": "0196ba87-e69e-75ac-9885-5763facdf6ca", "quantity": 1, "stack": 1, "cost": 35000, "coins": 0, "diamonds": 0, "crystal": 0, "sell": true, "market": true, "temporary": false, "upgrade": false, "staff": false, "enh_id": "0196ba87-e691-7757-a7dd-b3fcba14451c", "req_reputation": 0, "req_class_points": 0, "req_quests": "", "quest_string_index": -1, "quest_string_value": 0, "meta": "", "color": "ffffff", "pid": 436}, {"id": "0196ba87-e698-771d-b7fb-50ef01977c37", "name": "Camo Paintball Uniform", "description": "Paint the town red! (And blue;green;yellow;purple;pink;black…) this April Fool's Day! Show those bland corporate suits that they can't take away AQW's color!", "type": "Armor", "element": "None", "file": "JunglePaintball.swf", "link": "JunglePaintball", "icon": "i<PERSON>or", "equipment": "co", "level": 1, "dps": 25, "range": 50, "rarity": "0196ba87-e69e-75ac-9885-621e9f668f9c", "quantity": 1, "stack": 1, "cost": 2000, "coins": 0, "diamonds": 0, "crystal": 0, "sell": true, "market": true, "temporary": false, "upgrade": false, "staff": false, "enh_id": "0196ba87-e691-7757-a7dd-b3fcba14451c", "req_reputation": 0, "req_class_points": 0, "req_quests": "", "quest_string_index": -1, "quest_string_value": 0, "meta": "", "color": "ffffff", "pid": 437}, {"id": "0196ba87-e698-771d-b7fb-55e8ff270b04", "name": "Desaturated Paintball Uniform", "description": "Paint the town red! (And blue;green;yellow;purple;pink;black…)", "type": "Armor", "element": "None", "file": "SnowPaintball.swf", "link": "SnowPaintball", "icon": "i<PERSON>or", "equipment": "co", "level": 1, "dps": 25, "range": 50, "rarity": "0196ba87-e69e-75ac-9885-621e9f668f9c", "quantity": 1, "stack": 1, "cost": 30000, "coins": 0, "diamonds": 0, "crystal": 0, "sell": true, "market": true, "temporary": false, "upgrade": false, "staff": false, "enh_id": "0196ba87-e691-7757-a7dd-b3fcba14451c", "req_reputation": 0, "req_class_points": 0, "req_quests": "", "quest_string_index": -1, "quest_string_value": 0, "meta": "", "color": "ffffff", "pid": 438}, {"id": "0196ba87-e698-771d-b7fb-5b9f60d4b79c", "name": "Monochromancer", "description": "Paint the town red! (And blue;green;yellow;purple;pink;black…)", "type": "Armor", "element": "None", "file": "MageSat5.swf", "link": "MageSat", "icon": "i<PERSON>or", "equipment": "co", "level": 1, "dps": 25, "range": 50, "rarity": "0196ba87-e69e-75ac-9885-621e9f668f9c", "quantity": 1, "stack": 1, "cost": 10000, "coins": 0, "diamonds": 0, "crystal": 0, "sell": true, "market": true, "temporary": false, "upgrade": false, "staff": false, "enh_id": "0196ba87-e691-7757-a7dd-b3fcba14451c", "req_reputation": 0, "req_class_points": 0, "req_quests": "", "quest_string_index": -1, "quest_string_value": 0, "meta": "", "color": "ffffff", "pid": 439}, {"id": "0196ba87-e698-771d-b7fb-5d015fa86f47", "name": "Paintball Faceshield", "description": "Paint the town red! (And blue;green;yellow;purple;pink;black…)", "type": "<PERSON><PERSON>", "element": "None", "file": "items/helms/PaintballHelm.swf", "link": "PaintballHelm", "icon": "iihelm", "equipment": "he", "level": 1, "dps": 25, "range": 50, "rarity": "0196ba87-e69e-75ac-9885-621e9f668f9c", "quantity": 1, "stack": 1, "cost": 5000, "coins": 0, "diamonds": 0, "crystal": 0, "sell": true, "market": true, "temporary": false, "upgrade": false, "staff": false, "enh_id": "0196ba87-e691-7757-a7dd-b3fcba14451c", "req_reputation": 0, "req_class_points": 0, "req_quests": "", "quest_string_index": -1, "quest_string_value": 0, "meta": "", "color": "ffffff", "pid": 440}, {"id": "0196ba87-e698-771d-b7fb-626c37a2993c", "name": "Light Paintball Faceshield", "description": "Paint the town red! (And blue;green;yellow;purple;pink;black…)", "type": "<PERSON><PERSON>", "element": "None", "file": "items/helms/PaintballHelm2.swf", "link": "PaintballHelm2", "icon": "iihelm", "equipment": "he", "level": 1, "dps": 25, "range": 50, "rarity": "0196ba87-e69e-75ac-9885-621e9f668f9c", "quantity": 1, "stack": 1, "cost": 10000, "coins": 0, "diamonds": 0, "crystal": 0, "sell": true, "market": true, "temporary": false, "upgrade": false, "staff": false, "enh_id": "0196ba87-e691-7757-a7dd-b3fcba14451c", "req_reputation": 0, "req_class_points": 0, "req_quests": "", "quest_string_index": -1, "quest_string_value": 0, "meta": "", "color": "ffffff", "pid": 441}, {"id": "0196ba87-e698-771d-b7fb-6484e0b81bfc", "name": "Paintball FaceGuard", "description": "Paint the town red! (And blue;green;yellow;purple;pink;black…)", "type": "<PERSON><PERSON>", "element": "None", "file": "items/helms/PaintballHelmF.swf", "link": "PaintballHelmF", "icon": "iihelm", "equipment": "he", "level": 1, "dps": 25, "range": 50, "rarity": "0196ba87-e69e-75ac-9885-621e9f668f9c", "quantity": 1, "stack": 1, "cost": 5000, "coins": 0, "diamonds": 0, "crystal": 0, "sell": true, "market": true, "temporary": false, "upgrade": false, "staff": false, "enh_id": "0196ba87-e691-7757-a7dd-b3fcba14451c", "req_reputation": 0, "req_class_points": 0, "req_quests": "", "quest_string_index": -1, "quest_string_value": 0, "meta": "", "color": "ffffff", "pid": 442}, {"id": "0196ba87-e698-771d-b7fb-6b43b0afb334", "name": "Light Paintball FaceGuard", "description": "Paint the town red! (And blue;green;yellow;purple;pink;black…)", "type": "<PERSON><PERSON>", "element": "None", "file": "items/helms/PaintballHelmF2.swf", "link": "PaintballHelmF2", "icon": "iihelm", "equipment": "he", "level": 1, "dps": 25, "range": 50, "rarity": "0196ba87-e69e-75ac-9885-621e9f668f9c", "quantity": 1, "stack": 1, "cost": 10000, "coins": 0, "diamonds": 0, "crystal": 0, "sell": true, "market": true, "temporary": false, "upgrade": false, "staff": false, "enh_id": "0196ba87-e691-7757-a7dd-b3fcba14451c", "req_reputation": 0, "req_class_points": 0, "req_quests": "", "quest_string_index": -1, "quest_string_value": 0, "meta": "", "color": "ffffff", "pid": 443}, {"id": "0196ba87-e698-771d-b7fb-6c91fa365759", "name": "<PERSON><PERSON><PERSON>", "description": "Paint the town red! (And blue;green;yellow;purple;pink;black…)", "type": "Pet", "element": "None", "file": "items/pets/ArtMog.swf", "link": "ArtMog", "icon": "iipet", "equipment": "pe", "level": 1, "dps": 25, "range": 50, "rarity": "0196ba87-e69e-75ac-9885-621e9f668f9c", "quantity": 1, "stack": 1, "cost": 30000, "coins": 0, "diamonds": 0, "crystal": 0, "sell": true, "market": true, "temporary": false, "upgrade": false, "staff": false, "enh_id": "0196ba87-e691-7757-a7dd-b3fcba14451c", "req_reputation": 0, "req_class_points": 0, "req_quests": "", "quest_string_index": -1, "quest_string_value": 0, "meta": "", "color": "ffffff", "pid": 444}, {"id": "0196ba87-e698-771d-b7fb-704cbfb5adb3", "name": "Desaturated Brush and Shield", "description": "Paint the town red! (And blue;green;yellow;purple;pink;black…)", "type": "<PERSON>gger", "element": "None", "file": "items/daggers/BWBrushAndpshield.swf", "link": "BWBrushAndpshield", "icon": "iwda<PERSON>", "equipment": "Weapon", "level": 1, "dps": 25, "range": 50, "rarity": "0196ba87-e69e-75ac-9885-621e9f668f9c", "quantity": 1, "stack": 1, "cost": 15000, "coins": 0, "diamonds": 0, "crystal": 0, "sell": true, "market": true, "temporary": false, "upgrade": false, "staff": false, "enh_id": "0196ba87-e691-7757-a7dd-b3fcba14451c", "req_reputation": 0, "req_class_points": 0, "req_quests": "", "quest_string_index": -1, "quest_string_value": 0, "meta": "", "color": "ffffff", "pid": 445}, {"id": "0196ba87-e698-771d-b7fb-76640d999496", "name": "Paintball Gun", "description": "Paint the town red! (And blue;green;yellow;purple;pink;black…)", "type": "Gun", "element": "None", "file": "items/bows/PaintballGun.swf", "link": "PaintballGun", "icon": "iwgun", "equipment": "Weapon", "level": 1, "dps": 25, "range": 50, "rarity": "0196ba87-e69e-75ac-9885-621e9f668f9c", "quantity": 1, "stack": 1, "cost": 10000, "coins": 0, "diamonds": 0, "crystal": 0, "sell": true, "market": true, "temporary": false, "upgrade": false, "staff": false, "enh_id": "0196ba87-e691-7757-a7dd-b3fcba14451c", "req_reputation": 0, "req_class_points": 0, "req_quests": "", "quest_string_index": -1, "quest_string_value": 0, "meta": "", "color": "ffffff", "pid": 446}, {"id": "0196ba87-e698-771d-b7fb-794218d44fc5", "name": "Dr. <PERSON>'s Suit", "description": "The perfect look for a Purveyor of Fine Antiques;and fancy parties too!", "type": "Armor", "element": "None", "file": "DDarkwood.swf", "link": "DDarkwood", "icon": "i<PERSON>or", "equipment": "co", "level": 1, "dps": 25, "range": 50, "rarity": "0196ba87-e69e-75ac-9885-940e18f9c3e6", "quantity": 1, "stack": 1, "cost": 700, "coins": 1, "diamonds": 0, "crystal": 0, "sell": true, "market": true, "temporary": false, "upgrade": false, "staff": false, "enh_id": "0196ba87-e691-7757-a7dd-b3fcba14451c", "req_reputation": 0, "req_class_points": 0, "req_quests": "", "quest_string_index": -1, "quest_string_value": 0, "meta": "", "color": "ffffff", "pid": 447}, {"id": "0196ba87-e698-771d-b7fb-7fc33328148f", "name": "<PERSON><PERSON><PERSON>'s Uniform", "description": "Be careful! Try not to get captured by an evil villain and imprisoned in their awful lair… again…", "type": "Armor", "element": "None", "file": "MelodiaOutfit.swf", "link": "MelodiaOutfit", "icon": "i<PERSON>or", "equipment": "co", "level": 1, "dps": 25, "range": 50, "rarity": "0196ba87-e69e-75ac-9885-940e18f9c3e6", "quantity": 1, "stack": 1, "cost": 500, "coins": 1, "diamonds": 0, "crystal": 0, "sell": true, "market": true, "temporary": false, "upgrade": false, "staff": false, "enh_id": "0196ba87-e691-7757-a7dd-b3fcba14451c", "req_reputation": 0, "req_class_points": 0, "req_quests": "", "quest_string_index": -1, "quest_string_value": 0, "meta": "", "color": "ffffff", "pid": 448}, {"id": "0196ba87-e698-771d-b7fb-83885aa55005", "name": "Dr. <PERSON>'s Eyepatch", "description": "The perfect look for a Purveyor of Fine Antiques;and fancy parties too.", "type": "<PERSON><PERSON>", "element": "None", "file": "items/helms/DDarkwoodMorph.swf", "link": "DDarkwoodMorph", "icon": "iihelm", "equipment": "he", "level": 1, "dps": 25, "range": 50, "rarity": "0196ba87-e69e-75ac-9885-940e18f9c3e6", "quantity": 1, "stack": 1, "cost": 100, "coins": 1, "diamonds": 0, "crystal": 0, "sell": true, "market": true, "temporary": false, "upgrade": false, "staff": false, "enh_id": "0196ba87-e691-7757-a7dd-b3fcba14451c", "req_reputation": 0, "req_class_points": 0, "req_quests": "", "quest_string_index": -1, "quest_string_value": 0, "meta": "", "color": "ffffff", "pid": 449}, {"id": "0196ba87-e698-771d-b7fb-856585b3e18c", "name": "<PERSON><PERSON><PERSON>'s Ponytails", "description": "I wonder how <PERSON><PERSON><PERSON> can clean and sing with these.", "type": "<PERSON><PERSON>", "element": "None", "file": "items/helms/MelodiaOutfitPonytails2.swf", "link": "MelodiaOutfitPonytails2", "icon": "iihelm", "equipment": "he", "level": 1, "dps": 25, "range": 50, "rarity": "0196ba87-e69e-75ac-9885-5763facdf6ca", "quantity": 1, "stack": 1, "cost": 150, "coins": 1, "diamonds": 0, "crystal": 0, "sell": true, "market": true, "temporary": false, "upgrade": false, "staff": false, "enh_id": "0196ba87-e691-7757-a7dd-b3fcba14451c", "req_reputation": 0, "req_class_points": 0, "req_quests": "", "quest_string_index": -1, "quest_string_value": 0, "meta": "", "color": "ffffff", "pid": 450}, {"id": "0196ba87-e698-771d-b7fb-890e7bdb66e3", "name": "Dr. <PERSON>'s Gun", "description": "Dr. <PERSON><PERSON>'s fancy personal gun", "type": "Gun", "element": "None", "file": "items/bows/DDarkwoodPistol.swf", "link": "DDarkwoodPistol", "icon": "iwgun", "equipment": "Weapon", "level": 1, "dps": 25, "range": 50, "rarity": "0196ba87-e69e-75ac-9885-940e18f9c3e6", "quantity": 1, "stack": 1, "cost": 250, "coins": 1, "diamonds": 0, "crystal": 0, "sell": true, "market": true, "temporary": false, "upgrade": false, "staff": false, "enh_id": "0196ba87-e691-7757-a7dd-b3fcba14451c", "req_reputation": 0, "req_class_points": 0, "req_quests": "", "quest_string_index": -1, "quest_string_value": 0, "meta": "", "color": "ffffff", "pid": 451}, {"id": "0196ba87-e698-771d-b7fb-8f38f4947ad9", "name": "Eternal Warrior", "description": "Limited Rare", "type": "Armor", "element": "None", "file": "EW.swf", "link": "EW", "icon": "i<PERSON>or", "equipment": "co", "level": 1, "dps": 25, "range": 50, "rarity": "0196ba87-e69e-75ac-9885-af4e24f6923c", "quantity": 1, "stack": 1, "cost": 1000, "coins": 1, "diamonds": 0, "crystal": 0, "sell": true, "market": true, "temporary": false, "upgrade": false, "staff": false, "enh_id": "0196ba87-e691-7757-a7dd-b3fcba14451c", "req_reputation": 0, "req_class_points": 0, "req_quests": "", "quest_string_index": -1, "quest_string_value": 0, "meta": "", "color": "ffffff", "pid": 452}, {"id": "0196ba87-e698-771d-b7fb-906d943c1836", "name": "Dual Eternal Chakrams", "description": "Limited Rare", "type": "<PERSON>gger", "element": "None", "file": "items/daggers/EternalChakrams.swf", "link": "EternalChak<PERSON>", "icon": "iwda<PERSON>", "equipment": "Weapon", "level": 1, "dps": 25, "range": 50, "rarity": "0196ba87-e69e-75ac-9885-af4e24f6923c", "quantity": 1, "stack": 1, "cost": 450, "coins": 1, "diamonds": 0, "crystal": 0, "sell": true, "market": true, "temporary": false, "upgrade": false, "staff": false, "enh_id": "0196ba87-e691-7757-a7dd-b3fcba14451c", "req_reputation": 0, "req_class_points": 0, "req_quests": "", "quest_string_index": -1, "quest_string_value": 0, "meta": "", "color": "ffffff", "pid": 453}, {"id": "0196ba87-e698-771d-b7fb-9466c0982d97", "name": "Eternal Chakrams", "description": "Limited Rare", "type": "Mace", "element": "None", "file": "items/maces/EternalChakramsMace.swf", "link": "EternalChak<PERSON>", "icon": "iw<PERSON><PERSON>", "equipment": "Weapon", "level": 1, "dps": 25, "range": 50, "rarity": "0196ba87-e69e-75ac-9885-af4e24f6923c", "quantity": 1, "stack": 1, "cost": 350, "coins": 1, "diamonds": 0, "crystal": 0, "sell": true, "market": true, "temporary": false, "upgrade": false, "staff": false, "enh_id": "0196ba87-e691-7757-a7dd-b3fcba14451c", "req_reputation": 0, "req_class_points": 0, "req_quests": "", "quest_string_index": -1, "quest_string_value": 0, "meta": "", "color": "ffffff", "pid": 454}, {"id": "0196ba87-e698-771d-b7fb-9bf0866eed39", "name": "Infinity Hood", "description": "Limited Rare", "type": "<PERSON><PERSON>", "element": "None", "file": "items/helms/EWHood.swf", "link": "EWHood", "icon": "iihelm", "equipment": "he", "level": 1, "dps": 25, "range": 50, "rarity": "0196ba87-e69e-75ac-9885-af4e24f6923c", "quantity": 1, "stack": 1, "cost": 100, "coins": 1, "diamonds": 0, "crystal": 0, "sell": true, "market": true, "temporary": false, "upgrade": false, "staff": false, "enh_id": "0196ba87-e691-7757-a7dd-b3fcba14451c", "req_reputation": 0, "req_class_points": 0, "req_quests": "", "quest_string_index": -1, "quest_string_value": 0, "meta": "", "color": "ffffff", "pid": 455}, {"id": "0196ba87-e698-771d-b7fb-9f29abfce477", "name": "Infinite Visor", "description": "Limited Rare", "type": "<PERSON><PERSON>", "element": "None", "file": "items/helms/EWVisor.swf", "link": "EWVisor", "icon": "iihelm", "equipment": "he", "level": 1, "dps": 25, "range": 50, "rarity": "0196ba87-e69e-75ac-9885-af4e24f6923c", "quantity": 1, "stack": 1, "cost": 250, "coins": 1, "diamonds": 0, "crystal": 0, "sell": true, "market": true, "temporary": false, "upgrade": false, "staff": false, "enh_id": "0196ba87-e691-7757-a7dd-b3fcba14451c", "req_reputation": 0, "req_class_points": 0, "req_quests": "", "quest_string_index": -1, "quest_string_value": 0, "meta": "", "color": "ffffff", "pid": 456}, {"id": "0196ba87-e698-771d-b7fb-a26b1a207f8e", "name": "Chakrams Cape", "description": "Limited Rare", "type": "Cape", "element": "None", "file": "items/capes/ChakramsBackitem.swf", "link": "ChakramsBackitem", "icon": "iicape", "equipment": "ba", "level": 1, "dps": 25, "range": 50, "rarity": "0196ba87-e69e-75ac-9885-af4e24f6923c", "quantity": 1, "stack": 1, "cost": 300, "coins": 1, "diamonds": 0, "crystal": 0, "sell": true, "market": true, "temporary": false, "upgrade": false, "staff": false, "enh_id": "0196ba87-e691-7757-a7dd-b3fcba14451c", "req_reputation": 0, "req_class_points": 0, "req_quests": "", "quest_string_index": -1, "quest_string_value": 0, "meta": "", "color": "ffffff", "pid": 457}, {"id": "0196ba87-e698-771d-b7fb-a4435508e43c", "name": "Legendary Manslayer of Nythera", "description": "Damage 20% to all monster\\r\\nDefense 10% to all monster/Player", "type": "Sword", "element": "None", "file": "items/swords/DatapawTaroRevampe.swf", "link": "DatapawTaroRevampe", "icon": "iwsword", "equipment": "Weapon", "level": 1, "dps": 40, "range": 50, "rarity": "0196ba87-e69e-75ac-9885-940e18f9c3e6", "quantity": 1, "stack": 1, "cost": 0, "coins": 0, "diamonds": 0, "crystal": 0, "sell": true, "market": true, "temporary": false, "upgrade": false, "staff": false, "enh_id": "0196ba87-e691-7757-a7dd-b3fcba14451c", "req_reputation": 0, "req_class_points": 0, "req_quests": "", "quest_string_index": -1, "quest_string_value": 0, "meta": "", "color": "b300ad", "pid": 458}, {"id": "0196ba87-e698-771d-b7fb-a87fe12318aa", "name": "Treasures Key's", "description": "Treasure's Key", "type": "QuestItem", "element": "None", "file": "iibag", "link": "iibag", "icon": "iibag", "equipment": "None", "level": 1, "dps": 25, "range": 50, "rarity": "0196ba87-e69e-75ac-9885-99e8ae4740d6", "quantity": 1, "stack": 10, "cost": 0, "coins": 1, "diamonds": 0, "crystal": 0, "sell": true, "market": false, "temporary": false, "upgrade": false, "staff": false, "enh_id": "0196ba87-e691-7757-a7dd-b3fcba14451c", "req_reputation": 0, "req_class_points": 0, "req_quests": "", "quest_string_index": -1, "quest_string_value": 0, "meta": "", "color": "ffffff", "pid": 459}, {"id": "0196ba87-e698-771d-b7fb-af2ffcd23a4f", "name": "Fish Scale", "description": "Quest Item!", "type": "QuestItem", "element": "None", "file": "iibag", "link": "iibag", "icon": "iibag", "equipment": "None", "level": 1, "dps": 25, "range": 50, "rarity": "0196ba87-e69e-75ac-9885-5b8b0c71fdb4", "quantity": 1, "stack": 12, "cost": 0, "coins": 0, "diamonds": 0, "crystal": 0, "sell": true, "market": true, "temporary": true, "upgrade": false, "staff": false, "enh_id": "0196ba87-e691-7757-a7dd-b3fcba14451c", "req_reputation": 0, "req_class_points": 0, "req_quests": "", "quest_string_index": -1, "quest_string_value": 0, "meta": "", "color": "ffffff", "pid": 460}, {"id": "0196ba87-e698-771d-b7fb-b2c33c53da27", "name": "Map Fragment", "description": "Quest Item!", "type": "QuestItem", "element": "None", "file": "iidesign", "link": "iidesign", "icon": "iidesign", "equipment": "None", "level": 1, "dps": 25, "range": 50, "rarity": "0196ba87-e69e-75ac-9885-5b8b0c71fdb4", "quantity": 1, "stack": 5, "cost": 0, "coins": 0, "diamonds": 0, "crystal": 0, "sell": true, "market": false, "temporary": false, "upgrade": false, "staff": false, "enh_id": "0196ba87-e691-7757-a7dd-b3fcba14451c", "req_reputation": 0, "req_class_points": 0, "req_quests": "", "quest_string_index": -1, "quest_string_value": 0, "meta": "", "color": "ffffff", "pid": 461}, {"id": "0196ba87-e698-771d-b7fb-b40edfa879d7", "name": "??? P", "description": "", "type": "QuestItem", "element": "None", "file": "iibag", "link": "iibag", "icon": "iibag", "equipment": "None", "level": 1, "dps": 25, "range": 50, "rarity": "0196ba87-e69e-75ac-9885-5763facdf6ca", "quantity": 1, "stack": 15, "cost": 0, "coins": 0, "diamonds": 0, "crystal": 0, "sell": true, "market": true, "temporary": true, "upgrade": false, "staff": false, "enh_id": "0196ba87-e691-7757-a7dd-b3fcba14451c", "req_reputation": 0, "req_class_points": 0, "req_quests": "", "quest_string_index": -1, "quest_string_value": 0, "meta": "", "color": "ffffff", "pid": 462}, {"id": "0196ba87-e698-771d-b7fb-b87fee3f9974", "name": "??? F", "description": "", "type": "<PERSON><PERSON>", "element": "None", "file": "iibag", "link": "iibag", "icon": "iibag", "equipment": "None", "level": 1, "dps": 25, "range": 50, "rarity": "0196ba87-e69e-75ac-9885-5763facdf6ca", "quantity": 1, "stack": 15, "cost": 0, "coins": 0, "diamonds": 0, "crystal": 0, "sell": true, "market": true, "temporary": true, "upgrade": false, "staff": false, "enh_id": "0196ba87-e691-7757-a7dd-b3fcba14451c", "req_reputation": 0, "req_class_points": 0, "req_quests": "", "quest_string_index": -1, "quest_string_value": 0, "meta": "", "color": "ffffff", "pid": 463}, {"id": "0196ba87-e698-771d-b7fb-bc3e23254891", "name": "Pirate Bait", "description": "Pirate  Bait", "type": "QuestItem", "element": "None", "file": "iibag", "link": "iibag", "icon": "iibag", "equipment": "None", "level": 1, "dps": 25, "range": 50, "rarity": "0196ba87-e69e-75ac-9885-5b8b0c71fdb4", "quantity": 1, "stack": 10, "cost": 0, "coins": 0, "diamonds": 0, "crystal": 0, "sell": true, "market": true, "temporary": true, "upgrade": false, "staff": false, "enh_id": "0196ba87-e691-7757-a7dd-b3fcba14451c", "req_reputation": 0, "req_class_points": 0, "req_quests": "", "quest_string_index": -1, "quest_string_value": 0, "meta": "", "color": "ffffff", "pid": 464}, {"id": "0196ba87-e698-771d-b7fb-c2719b73d6f1", "name": "Pirate Key", "description": "Pirate Key", "type": "<PERSON><PERSON>", "element": "None", "file": "iibag", "link": "iibag", "icon": "iibag", "equipment": "None", "level": 1, "dps": 25, "range": 50, "rarity": "0196ba87-e69e-75ac-9885-5763facdf6ca", "quantity": 1, "stack": 1, "cost": 0, "coins": 0, "diamonds": 0, "crystal": 0, "sell": true, "market": true, "temporary": false, "upgrade": false, "staff": false, "enh_id": "0196ba87-e691-7757-a7dd-b3fcba14451c", "req_reputation": 0, "req_class_points": 0, "req_quests": "", "quest_string_index": -1, "quest_string_value": 0, "meta": "", "color": "ffffff", "pid": 465}, {"id": "0196ba87-e698-771d-b7fb-c47660b93105", "name": "Pirate I", "description": "Pirate I", "type": "<PERSON><PERSON>", "element": "None", "file": "iibag", "link": "iibag", "icon": "iibag", "equipment": "None", "level": 1, "dps": 25, "range": 50, "rarity": "0196ba87-e69e-75ac-9885-5763facdf6ca", "quantity": 1, "stack": 1, "cost": 0, "coins": 0, "diamonds": 0, "crystal": 0, "sell": true, "market": true, "temporary": false, "upgrade": false, "staff": false, "enh_id": "0196ba87-e691-7757-a7dd-b3fcba14451c", "req_reputation": 0, "req_class_points": 0, "req_quests": "", "quest_string_index": -1, "quest_string_value": 0, "meta": "", "color": "ffffff", "pid": 466}, {"id": "0196ba87-e698-771d-b7fb-c87e1313d96e", "name": "Pirate II", "description": "Pirate II", "type": "<PERSON><PERSON>", "element": "None", "file": "iibag", "link": "iibag", "icon": "iibag", "equipment": "None", "level": 1, "dps": 25, "range": 50, "rarity": "0196ba87-e69e-75ac-9885-5763facdf6ca", "quantity": 1, "stack": 1, "cost": 0, "coins": 0, "diamonds": 0, "crystal": 0, "sell": true, "market": true, "temporary": false, "upgrade": false, "staff": false, "enh_id": "0196ba87-e691-7757-a7dd-b3fcba14451c", "req_reputation": 0, "req_class_points": 0, "req_quests": "", "quest_string_index": -1, "quest_string_value": 0, "meta": "", "color": "ffffff", "pid": 467}, {"id": "0196ba87-e698-771d-b7fb-cc5941f5fe94", "name": "Pirate III", "description": "Pirate III", "type": "<PERSON><PERSON>", "element": "None", "file": "iibag", "link": "iibag", "icon": "iibag", "equipment": "None", "level": 1, "dps": 25, "range": 50, "rarity": "0196ba87-e69e-75ac-9885-5763facdf6ca", "quantity": 1, "stack": 1, "cost": 0, "coins": 0, "diamonds": 0, "crystal": 0, "sell": true, "market": true, "temporary": false, "upgrade": false, "staff": false, "enh_id": "0196ba87-e691-7757-a7dd-b3fcba14451c", "req_reputation": 0, "req_class_points": 0, "req_quests": "", "quest_string_index": -1, "quest_string_value": 0, "meta": "", "color": "ffffff", "pid": 468}, {"id": "0196ba87-e698-771d-b7fb-d2b0934223b3", "name": "Pie-rate Cutlass", "description": "Avast! Captain <PERSON><PERSON><PERSON><PERSON> has been Artix Entertainment's favorite programming pirate for 10 years! /cheer! Here's to many more years keeping AE sailing in the right course!", "type": "Sword", "element": "None", "file": "items/swords/RhubarbCutlass2.swf", "link": "RhubarbCutlass", "icon": "iwsword", "equipment": "Weapon", "level": 1, "dps": 25, "range": 50, "rarity": "0196ba87-e69e-75ac-9885-9cc909ea652d", "quantity": 1, "stack": 1, "cost": 1000, "coins": 0, "diamonds": 0, "crystal": 0, "sell": true, "market": true, "temporary": false, "upgrade": false, "staff": false, "enh_id": "0196ba87-e691-7757-a7dd-b3fcba14451c", "req_reputation": 0, "req_class_points": 0, "req_quests": "", "quest_string_index": -1, "quest_string_value": 0, "meta": "", "color": "ffffff", "pid": 469}, {"id": "0196ba87-e698-771d-b7fb-d4e3b8b254a9", "name": "<PERSON><PERSON><PERSON>'s <PERSON><PERSON> Cannon", "description": "Wish Captain <PERSON><PERSON><PERSON><PERSON> a VERY Happy birthday;and celebrate with this GIANT Cannon Cutlass. YARRRRRR!", "type": "Sword", "element": "None", "file": "items/swords/RevolvingRifleCutlass.swf", "link": "RevolvingRifleCutlass", "icon": "iwsword", "equipment": "Weapon", "level": 1, "dps": 25, "range": 50, "rarity": "0196ba87-e69e-75ac-9885-9cc909ea652d", "quantity": 1, "stack": 1, "cost": 100000, "coins": 0, "diamonds": 0, "crystal": 0, "sell": true, "market": true, "temporary": false, "upgrade": false, "staff": false, "enh_id": "0196ba87-e691-7757-a7dd-b3fcba14451c", "req_reputation": 0, "req_class_points": 0, "req_quests": "", "quest_string_index": -1, "quest_string_value": 0, "meta": "", "color": "ffffff", "pid": 470}, {"id": "0196ba87-e698-771d-b7fb-d88d31bf88ec", "name": "Pie-rate Captain", "description": "Avast! Captain <PERSON><PERSON><PERSON><PERSON> has been Artix Entertainment's favorite programming pirate for 10 years! /cheer! Here's to many more years keeping AE sailing in the right course!", "type": "Armor", "element": "None", "file": "CaptainRhubarbArmor8113.swf", "link": "CaptainRhubarbArmor8113", "icon": "i<PERSON>or", "equipment": "co", "level": 1, "dps": 25, "range": 50, "rarity": "0196ba87-e69e-75ac-9885-5c6a3b464a67", "quantity": 1, "stack": 1, "cost": 10000, "coins": 0, "diamonds": 0, "crystal": 0, "sell": true, "market": true, "temporary": false, "upgrade": false, "staff": false, "enh_id": "0196ba87-e691-7757-a7dd-b3fcba14451c", "req_reputation": 0, "req_class_points": 0, "req_quests": "", "quest_string_index": -1, "quest_string_value": 0, "meta": "", "color": "ffffff", "pid": 471}, {"id": "0196ba87-e698-771d-b7fb-df7de25af3b8", "name": "Furious Captain's Locks", "description": "Avast! Captain <PERSON><PERSON><PERSON><PERSON> has been Artix Entertainment's favorite programming pirate for 10 years! /cheer! Here's to many more years keeping AE sailing in the right course!", "type": "<PERSON><PERSON>", "element": "None", "file": "items/helms/RhubarbHatGirl.swf", "link": "RhubarbHatGirl", "icon": "iihelm", "equipment": "he", "level": 1, "dps": 25, "range": 50, "rarity": "0196ba87-e69e-75ac-9885-5b8b0c71fdb4", "quantity": 1, "stack": 1, "cost": 5000, "coins": 0, "diamonds": 0, "crystal": 0, "sell": true, "market": true, "temporary": false, "upgrade": false, "staff": false, "enh_id": "0196ba87-e691-7757-a7dd-b3fcba14451c", "req_reputation": 0, "req_class_points": 0, "req_quests": "", "quest_string_index": -1, "quest_string_value": 0, "meta": "", "color": "ffffff", "pid": 472}, {"id": "0196ba87-e698-771d-b7fb-e3efd58236b6", "name": "Rugged Pie-rate Helm", "description": "Avast! Captain <PERSON><PERSON><PERSON><PERSON> has been Artix Entertainment's favorite programming pirate for 10 years! /cheer! Here's to many more years keeping AE sailing in the right course!", "type": "<PERSON><PERSON>", "element": "None", "file": "items/helms/RhubarbHatGuy.swf", "link": "RhubarbHatGuy", "icon": "iihelm", "equipment": "he", "level": 1, "dps": 25, "range": 50, "rarity": "0196ba87-e69e-75ac-9885-5b8b0c71fdb4", "quantity": 1, "stack": 1, "cost": 5000, "coins": 0, "diamonds": 0, "crystal": 0, "sell": true, "market": true, "temporary": false, "upgrade": false, "staff": false, "enh_id": "0196ba87-e691-7757-a7dd-b3fcba14451c", "req_reputation": 0, "req_class_points": 0, "req_quests": "", "quest_string_index": -1, "quest_string_value": 0, "meta": "", "color": "ffffff", "pid": 473}, {"id": "0196ba87-e698-771d-b7fb-e4b5aba3c77a", "name": "Young Pie-rate Helm", "description": "Avast! Captain <PERSON><PERSON><PERSON><PERSON> has been Artix Entertainment's favorite programming pirate for 10 years! /cheer! Here's to many more years keeping AE sailing in the right course!", "type": "<PERSON><PERSON>", "element": "None", "file": "items/helms/RhubarbHatGuy2.swf", "link": "RhubarbHatGuy2", "icon": "iihelm", "equipment": "he", "level": 1, "dps": 25, "range": 50, "rarity": "0196ba87-e69e-75ac-9885-91ec33cb57dc", "quantity": 1, "stack": 1, "cost": 5033, "coins": 0, "diamonds": 0, "crystal": 0, "sell": true, "market": true, "temporary": false, "upgrade": false, "staff": false, "enh_id": "0196ba87-e691-7757-a7dd-b3fcba14451c", "req_reputation": 0, "req_class_points": 0, "req_quests": "", "quest_string_index": -1, "quest_string_value": 0, "meta": "", "color": "ffffff", "pid": 474}, {"id": "0196ba87-e698-771d-b7fb-e84812d64c40", "name": "Swabbie Cape", "description": "Avast! Captain <PERSON><PERSON><PERSON><PERSON> has been Artix Entertainment's favorite programming pirate for 10 years! /cheer! Here's to many more years keeping AE sailing in the right course!", "type": "Cape", "element": "None", "file": "items/capes/SwabbieCape.swf", "link": "SwabbieCape", "icon": "iicape", "equipment": "ba", "level": 1, "dps": 25, "range": 50, "rarity": "0196ba87-e69e-75ac-9885-91ec33cb57dc", "quantity": 1, "stack": 1, "cost": 500, "coins": 0, "diamonds": 0, "crystal": 0, "sell": true, "market": true, "temporary": false, "upgrade": false, "staff": false, "enh_id": "0196ba87-e691-7757-a7dd-b3fcba14451c", "req_reputation": 0, "req_class_points": 0, "req_quests": "", "quest_string_index": -1, "quest_string_value": 0, "meta": "", "color": "ffffff", "pid": 475}, {"id": "0196ba87-e698-771d-b7fb-eeac4dab6ce1", "name": "Pie-rate Cape", "description": "Avast! Captain <PERSON><PERSON><PERSON><PERSON> has been Artix Entertainment's favorite programming pirate for 10 years! /cheer! Here's to many more years keeping AE sailing in the right course!", "type": "Cape", "element": "None", "file": "items/capes/RhubarbCape1.swf", "link": "RhubarbCape1", "icon": "iicape", "equipment": "ba", "level": 1, "dps": 25, "range": 50, "rarity": "0196ba87-e69e-75ac-9885-9cc909ea652d", "quantity": 1, "stack": 1, "cost": 778, "coins": 0, "diamonds": 0, "crystal": 0, "sell": true, "market": true, "temporary": false, "upgrade": false, "staff": false, "enh_id": "0196ba87-e691-7757-a7dd-b3fcba14451c", "req_reputation": 0, "req_class_points": 0, "req_quests": "", "quest_string_index": -1, "quest_string_value": 0, "meta": "", "color": "ffffff", "pid": 476}, {"id": "0196ba87-e698-771d-b7fb-f2d3b1fd9346", "name": "Pie-rate Puppet", "description": "Avast! Captain <PERSON><PERSON><PERSON><PERSON> has been Artix Entertainment's favorite programming pirate for 10 years! /cheer! Here's to many more years keeping AE sailing in the right course!", "type": "Pet", "element": "None", "file": "items/pets/CaptainPuppet.swf", "link": "Captain<PERSON><PERSON><PERSON>", "icon": "iipet", "equipment": "pe", "level": 1, "dps": 25, "range": 50, "rarity": "0196ba87-e69e-75ac-9885-9cc909ea652d", "quantity": 1, "stack": 1, "cost": 5000, "coins": 0, "diamonds": 0, "crystal": 0, "sell": true, "market": true, "temporary": false, "upgrade": false, "staff": false, "enh_id": "0196ba87-e691-7757-a7dd-b3fcba14451c", "req_reputation": 0, "req_class_points": 0, "req_quests": "", "quest_string_index": -1, "quest_string_value": 0, "meta": "", "color": "ffffff", "pid": 477}, {"id": "0196ba87-e698-771d-b7fb-f4b56013c778", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "description": "<PERSON><PERSON><PERSON><PERSON><PERSON> is the most remembrance of soul king he used that to kill the all monster in the Newbie and save everyone to that dangerous place of war and defeated the all bosses (this weapon is boosted)", "type": "Polearm", "element": "None", "file": "items/polearms/1lastDatapawAmeno1.swf", "link": "22DatapawAmeno", "icon": "iwpolearm", "equipment": "Weapon", "level": 1, "dps": 60, "range": 50, "rarity": "0196ba87-e69e-75ac-9885-8e8caeb1b9db", "quantity": 1, "stack": 1, "cost": 0, "coins": 0, "diamonds": 0, "crystal": 0, "sell": true, "market": true, "temporary": false, "upgrade": false, "staff": false, "enh_id": "0196ba87-e691-7757-a7dd-cd61044c3726", "req_reputation": 0, "req_class_points": 0, "req_quests": "", "quest_string_index": -1, "quest_string_value": 0, "meta": "", "color": "e2e600", "pid": 478}, {"id": "0196ba87-e698-771d-b7fb-f8ee741a343b", "name": "Amenonuhoko <PERSON>e Cape", "description": "<PERSON><PERSON><PERSON><PERSON><PERSON> is the most remembrance of soul king he used that to kill the all monster in the Newbie and save everyone to that dangerous place of war and defeated the all bosses (this cape is boosted)", "type": "Cape", "element": "None", "file": "items/capes/DatapawAmeno1.swf", "link": "DatapawAmeno", "icon": "iicape", "equipment": "ba", "level": 1, "dps": 25, "range": 50, "rarity": "0196ba87-e69e-75ac-9885-8e8caeb1b9db", "quantity": 1, "stack": 1, "cost": 0, "coins": 0, "diamonds": 0, "crystal": 0, "sell": true, "market": true, "temporary": false, "upgrade": false, "staff": false, "enh_id": "0196ba87-e691-7757-a7dd-b3fcba14451c", "req_reputation": 0, "req_class_points": 0, "req_quests": "", "quest_string_index": -1, "quest_string_value": 0, "meta": "", "color": "f4e301", "pid": 479}, {"id": "0196ba87-e698-771d-b7fb-ff4e87412568", "name": "Alpha Pirate Armor", "description": "A commemorative armor reminding us of the infamous day where the pirate Captain <PERSON><PERSON><PERSON><PERSON> seized control of that database.", "type": "Armor", "element": "None", "file": "pirate2_skin.swf", "link": "Pirate2", "icon": "i<PERSON>or", "equipment": "co", "level": 1, "dps": 25, "range": 50, "rarity": "0196ba87-e69e-75ac-9885-a05920d0e5e9", "quantity": 1, "stack": 1, "cost": 100000, "coins": 0, "diamonds": 0, "crystal": 0, "sell": true, "market": false, "temporary": false, "upgrade": false, "staff": false, "enh_id": "0196ba87-e691-7757-a7dd-b3fcba14451c", "req_reputation": 0, "req_class_points": 0, "req_quests": "", "quest_string_index": -1, "quest_string_value": 0, "meta": "", "color": "ffffff", "pid": 480}, {"id": "0196ba87-e698-771d-b7fc-024638ca149a", "name": "Pirate Sea", "description": "Pirate Sea", "type": "<PERSON><PERSON>", "element": "None", "file": "iibag", "link": "iibag", "icon": "iibag", "equipment": "None", "level": 1, "dps": 25, "range": 50, "rarity": "0196ba87-e69e-75ac-9885-a6b4a8ed80ab", "quantity": 1, "stack": 500, "cost": 0, "coins": 0, "diamonds": 0, "crystal": 0, "sell": true, "market": true, "temporary": false, "upgrade": false, "staff": false, "enh_id": "0196ba87-e691-7757-a7dd-b3fcba14451c", "req_reputation": 0, "req_class_points": 0, "req_quests": "", "quest_string_index": -1, "quest_string_value": 0, "meta": "", "color": "ffffff", "pid": 481}, {"id": "0196ba87-e698-771d-b7fc-077f9ace7829", "name": "Black Dragonborn Naval Commander", "description": "A charming choice for a naval who is gifted when maintaining order in the crew is involved;yet mysterious. Be weary for not all commanders who wear black are wicked.", "type": "Armor", "element": "None", "file": "DragonbornNCBlackr1.swf", "link": "DragonbornNCBlack", "icon": "i<PERSON>or", "equipment": "co", "level": 1, "dps": 25, "range": 50, "rarity": "0196ba87-e69e-75ac-9885-91ec33cb57dc", "quantity": 1, "stack": 1, "cost": 0, "coins": 0, "diamonds": 0, "crystal": 0, "sell": true, "market": true, "temporary": false, "upgrade": false, "staff": false, "enh_id": "0196ba87-e691-7757-a7dd-b3fcba14451c", "req_reputation": 0, "req_class_points": 0, "req_quests": "", "quest_string_index": -1, "quest_string_value": 0, "meta": "", "color": "ffffff", "pid": 482}, {"id": "0196ba87-e698-771d-b7fc-08742632cd21", "name": "Galactic Naval Commander", "description": "Ye always be sayin' how pirates be out of this world… 'n now ye can shoot for the stars 'n plunder the planets as the Galactic Naval Commander!", "type": "Armor", "element": "None", "file": "GalacticNC1.swf", "link": "GalacticNC", "icon": "i<PERSON>or", "equipment": "co", "level": 1, "dps": 25, "range": 50, "rarity": "0196ba87-e69e-75ac-9885-91ec33cb57dc", "quantity": 1, "stack": 1, "cost": 0, "coins": 0, "diamonds": 0, "crystal": 0, "sell": true, "market": true, "temporary": false, "upgrade": false, "staff": false, "enh_id": "0196ba87-e691-7757-a7dd-b3fcba14451c", "req_reputation": 0, "req_class_points": 0, "req_quests": "", "quest_string_index": -1, "quest_string_value": 0, "meta": "", "color": "ffffff", "pid": 483}, {"id": "0196ba87-e698-771d-b7fc-0e9f23d28ff3", "name": "Naval Commander", "description": "A charming choice for a naval who is gifted when maintaining order in the crew is involved;yet mysterious. Be weary for not all commanders who wear black are wicked.", "type": "Armor", "element": "None", "file": "Explorer.swf", "link": "Explorer", "icon": "i<PERSON>or", "equipment": "co", "level": 1, "dps": 25, "range": 50, "rarity": "0196ba87-e69e-75ac-9885-91ec33cb57dc", "quantity": 1, "stack": 1, "cost": 0, "coins": 0, "diamonds": 0, "crystal": 0, "sell": true, "market": true, "temporary": false, "upgrade": false, "staff": false, "enh_id": "0196ba87-e691-7757-a7dd-b3fcba14451c", "req_reputation": 0, "req_class_points": 0, "req_quests": "", "quest_string_index": -1, "quest_string_value": 0, "meta": "", "color": "ffffff", "pid": 484}, {"id": "0196ba87-e698-771d-b7fc-10a82b68d9dd", "name": "Rotting Naval Commander", "description": "Dead men tell no tales. Except in this case;where it seems you have managed to escape <PERSON>' Locker. Yaaar!", "type": "Armor", "element": "None", "file": "Davy_skin.swf", "link": "Davy1", "icon": "i<PERSON>or", "equipment": "co", "level": 1, "dps": 25, "range": 50, "rarity": "0196ba87-e69e-75ac-9885-91ec33cb57dc", "quantity": 1, "stack": 1, "cost": 0, "coins": 0, "diamonds": 0, "crystal": 0, "sell": true, "market": true, "temporary": false, "upgrade": false, "staff": false, "enh_id": "0196ba87-e691-7757-a7dd-b3fcba14451c", "req_reputation": 0, "req_class_points": 0, "req_quests": "", "quest_string_index": -1, "quest_string_value": 0, "meta": "", "color": "ffffff", "pid": 485}, {"id": "0196ba87-e698-771d-b7fc-153abfa35e84", "name": "Void Naval Commander", "description": "Aye;even the Archfiend has his own fleet! No landlubbers allowed;<PERSON><PERSON><PERSON><PERSON> only wants the souls of the best Buccaneers in Lore. Savvy?", "type": "Armor", "element": "None", "file": "VoidPirate7r1.swf", "link": "VoidPirate7", "icon": "i<PERSON>or", "equipment": "co", "level": 1, "dps": 25, "range": 50, "rarity": "0196ba87-e69e-75ac-9885-91ec33cb57dc", "quantity": 1, "stack": 1, "cost": 0, "coins": 0, "diamonds": 0, "crystal": 0, "sell": true, "market": true, "temporary": false, "upgrade": false, "staff": false, "enh_id": "0196ba87-e691-7757-a7dd-b3fcba14451c", "req_reputation": 0, "req_class_points": 0, "req_quests": "", "quest_string_index": -1, "quest_string_value": 0, "meta": "", "color": "ffffff", "pid": 486}, {"id": "0196ba87-e698-771d-b7fc-1881f77d1660", "name": "Platinum Naval Commander", "description": "As a high ranking officer at the Royal navy;your job is to hunt down treasure seeking pirates;but if you WEAR the treasure then the pirates will come to you!", "type": "Armor", "element": "None", "file": "PNC.swf", "link": "PNC", "icon": "i<PERSON>or", "equipment": "co", "level": 1, "dps": 25, "range": 50, "rarity": "0196ba87-e69e-75ac-9885-91ec33cb57dc", "quantity": 1, "stack": 1, "cost": 0, "coins": 0, "diamonds": 0, "crystal": 0, "sell": true, "market": true, "temporary": false, "upgrade": false, "staff": false, "enh_id": "0196ba87-e691-7757-a7dd-b3fcba14451c", "req_reputation": 0, "req_class_points": 0, "req_quests": "", "quest_string_index": -1, "quest_string_value": 0, "meta": "", "color": "ffffff", "pid": 487}, {"id": "0196ba87-e698-771d-b7fc-1cb2b3302f07", "name": "Gold Voucher 20k", "description": "x", "type": "<PERSON><PERSON>", "element": "None", "file": "iibag", "link": "iibag", "icon": "iibag", "equipment": "<PERSON><PERSON>", "level": 1, "dps": 25, "range": 50, "rarity": "0196ba87-e69e-75ac-9885-5763facdf6ca", "quantity": 1, "stack": 1, "cost": 20000, "coins": 0, "diamonds": 0, "crystal": 0, "sell": true, "market": false, "temporary": false, "upgrade": false, "staff": false, "enh_id": "0196ba87-e691-7757-a7dd-b3fcba14451c", "req_reputation": 0, "req_class_points": 0, "req_quests": "", "quest_string_index": -1, "quest_string_value": 0, "meta": "", "color": "ffffff", "pid": 488}, {"id": "0196ba87-e698-771d-b7fc-223450677377", "name": "Double Future Gun", "description": "This Gun is Massive Bullet it can kill 1500 meters range of the enemy you see", "type": "<PERSON>gger", "element": "None", "file": "items/daggers/DatapawGunfix.swf", "link": "DatapawGun", "icon": "iwda<PERSON>", "equipment": "Weapon", "level": 1, "dps": 170, "range": 50, "rarity": "0196ba87-e69e-75ac-9885-940e18f9c3e6", "quantity": 1, "stack": 1, "cost": 0, "coins": 0, "diamonds": 0, "crystal": 0, "sell": true, "market": true, "temporary": false, "upgrade": false, "staff": false, "enh_id": "0196ba87-e691-7757-a7dd-b3fcba14451c", "req_reputation": 0, "req_class_points": 0, "req_quests": "", "quest_string_index": -1, "quest_string_value": 0, "meta": "", "color": "cf01f9", "pid": 489}, {"id": "0196ba87-e698-771d-b7fc-261427b1f4f2", "name": "Dual black Astral", "description": "Dark Astral counter of Datapaw to defeat this stupid Staff use this to get him down", "type": "<PERSON>gger", "element": "None", "file": "items/daggers/Dualultimateblackkatana.swf", "link": "ultimateblackkatana", "icon": "iwda<PERSON>", "equipment": "Weapon", "level": 1, "dps": 25, "range": 50, "rarity": "0196ba87-e69e-75ac-9885-af4e24f6923c", "quantity": 1, "stack": 1, "cost": 800, "coins": 1, "diamonds": 0, "crystal": 0, "sell": true, "market": true, "temporary": false, "upgrade": false, "staff": false, "enh_id": "0196ba87-e691-7757-a7dd-b3fcba14451c", "req_reputation": 0, "req_class_points": 0, "req_quests": "", "quest_string_index": -1, "quest_string_value": 0, "meta": "", "color": "ffffff", "pid": 490}, {"id": "0196ba87-e698-771d-b7fc-2a8702d493d5", "name": "Soul King Weapon Enhancement Level 10", "description": "", "type": "Enhancement", "element": "None", "file": "Enhancement.swf", "link": "Enhancement", "icon": "none", "equipment": "Weapon", "level": 10, "dps": 50, "range": 50, "rarity": "0196ba87-e69e-75ac-9885-5763facdf6ca", "quantity": 1, "stack": 1, "cost": 50000, "coins": 0, "diamonds": 0, "crystal": 0, "sell": true, "market": true, "temporary": false, "upgrade": false, "staff": false, "enh_id": "0196ba87-e691-7757-a7dd-b82d1d679d39", "req_reputation": 0, "req_class_points": 0, "req_quests": "", "quest_string_index": -1, "quest_string_value": 0, "meta": "", "color": "ffffff", "pid": 491}, {"id": "0196ba87-e698-771d-b7fc-2dfcdced17b4", "name": "Soul King Weapon Enhancement Level 15", "description": "", "type": "Enhancement", "element": "None", "file": "Enhancement.swf", "link": "Enhancement", "icon": "none", "equipment": "Weapon", "level": 15, "dps": 50, "range": 50, "rarity": "0196ba87-e69e-75ac-9885-5763facdf6ca", "quantity": 1, "stack": 1, "cost": 0, "coins": 0, "diamonds": 0, "crystal": 0, "sell": true, "market": true, "temporary": false, "upgrade": false, "staff": false, "enh_id": "0196ba87-e691-7757-a7dd-bcf75c49c24a", "req_reputation": 0, "req_class_points": 0, "req_quests": "", "quest_string_index": -1, "quest_string_value": 0, "meta": "", "color": "ffffff", "pid": 492}, {"id": "0196ba87-e698-771d-b7fc-308b45ced3b8", "name": "Soul King Weapon Enhancement Level 20", "description": "", "type": "Enhancement", "element": "None", "file": "Enhancement.swf", "link": "Enhancement", "icon": "none", "equipment": "Weapon", "level": 20, "dps": 50, "range": 50, "rarity": "0196ba87-e69e-75ac-9885-5763facdf6ca", "quantity": 1, "stack": 1, "cost": 50000, "coins": 0, "diamonds": 0, "crystal": 0, "sell": true, "market": true, "temporary": false, "upgrade": false, "staff": false, "enh_id": "0196ba87-e691-7757-a7dd-c14d5dd3a5ab", "req_reputation": 0, "req_class_points": 0, "req_quests": "", "quest_string_index": -1, "quest_string_value": 0, "meta": "", "color": "ffffff", "pid": 493}, {"id": "0196ba87-e698-771d-b7fc-35b7f3aba73e", "name": "Soul King Weapon Enhancement Level 25", "description": "", "type": "Enhancement", "element": "None", "file": "Enhancement.swf", "link": "Enhancement", "icon": "none", "equipment": "Weapon", "level": 25, "dps": 50, "range": 50, "rarity": "0196ba87-e69e-75ac-9885-5763facdf6ca", "quantity": 1, "stack": 1, "cost": 50000, "coins": 0, "diamonds": 0, "crystal": 0, "sell": true, "market": true, "temporary": false, "upgrade": false, "staff": false, "enh_id": "0196ba87-e691-7757-a7dd-c730c8a2f84b", "req_reputation": 0, "req_class_points": 0, "req_quests": "", "quest_string_index": -1, "quest_string_value": 0, "meta": "", "color": "ffffff", "pid": 494}, {"id": "0196ba87-e698-771d-b7fc-3b7f5c5a5514", "name": "Soul King Weapon Enhancement Level 30", "description": "", "type": "Enhancement", "element": "None", "file": "Enhancement.swf", "link": "Enhancement.", "icon": "none", "equipment": "Weapon", "level": 30, "dps": 50, "range": 50, "rarity": "0196ba87-e69e-75ac-9885-5763facdf6ca", "quantity": 1, "stack": 1, "cost": 50000, "coins": 0, "diamonds": 0, "crystal": 0, "sell": true, "market": true, "temporary": false, "upgrade": false, "staff": false, "enh_id": "0196ba87-e691-7757-a7dd-c97121b0cea7", "req_reputation": 0, "req_class_points": 0, "req_quests": "", "quest_string_index": -1, "quest_string_value": 0, "meta": "", "color": "ffffff", "pid": 495}, {"id": "0196ba87-e698-771d-b7fc-3c630ba7d26c", "name": "Soul King Weapon Enhancement Level 35", "description": "", "type": "Enhancement", "element": "None", "file": "Enhancement.swf", "link": "Enhancement", "icon": "none", "equipment": "Weapon", "level": 35, "dps": 25, "range": 50, "rarity": "0196ba87-e69e-75ac-9885-5763facdf6ca", "quantity": 1, "stack": 1, "cost": 50000, "coins": 0, "diamonds": 0, "crystal": 0, "sell": true, "market": true, "temporary": false, "upgrade": false, "staff": false, "enh_id": "0196ba87-e691-7757-a7dd-cd61044c3726", "req_reputation": 0, "req_class_points": 0, "req_quests": "", "quest_string_index": -1, "quest_string_value": 0, "meta": "", "color": "ffffff", "pid": 496}, {"id": "0196ba87-e698-771d-b7fc-429ed2e5c8c2", "name": "Windy Hair CC", "description": "This Hair Will Be Remembered by All!", "type": "<PERSON><PERSON>", "element": "None", "file": "items/helms/WindyHairCC.swf", "link": "WindyHairCC", "icon": "iihelm", "equipment": "he", "level": 1, "dps": 25, "range": 50, "rarity": "0196ba87-e69e-75ac-9885-af4e24f6923c", "quantity": 1, "stack": 1, "cost": 0, "coins": 0, "diamonds": 0, "crystal": 0, "sell": true, "market": true, "temporary": false, "upgrade": false, "staff": false, "enh_id": "0196ba87-e691-7757-a7dd-b3fcba14451c", "req_reputation": 0, "req_class_points": 0, "req_quests": "", "quest_string_index": -1, "quest_string_value": 0, "meta": "500000", "color": "ffffff", "pid": 497}, {"id": "0196ba87-e698-771d-b7fc-447192ff3cd0", "name": "Support Title", "description": "Special players who are dedicated to helping!", "type": "ServerUse", "element": "None", "file": "title::27", "link": "title::27", "icon": "iCheck", "equipment": "None", "level": 1, "dps": 25, "range": 50, "rarity": "0196ba87-e69e-75ac-9885-5763facdf6ca", "quantity": 1, "stack": 1, "cost": 0, "coins": 0, "diamonds": 0, "crystal": 0, "sell": true, "market": false, "temporary": false, "upgrade": false, "staff": false, "enh_id": "0196ba87-e691-7757-a7dd-b3fcba14451c", "req_reputation": 0, "req_class_points": 0, "req_quests": "", "quest_string_index": -1, "quest_string_value": 0, "meta": "", "color": "ffffff", "pid": 498}, {"id": "0196ba87-e698-771d-b7fc-4ac34ee589af", "name": "Job Manager", "description": "Only for Moderators/Administrators use.", "type": "ServerUse", "element": "None", "file": "title::2", "link": "title::2", "icon": "iCheck", "equipment": "None", "level": 1, "dps": 25, "range": 50, "rarity": "0196ba87-e69e-75ac-9885-5763facdf6ca", "quantity": 1, "stack": 1, "cost": 0, "coins": 0, "diamonds": 0, "crystal": 0, "sell": true, "market": false, "temporary": false, "upgrade": false, "staff": true, "enh_id": "0196ba87-e691-7757-a7dd-b3fcba14451c", "req_reputation": 0, "req_class_points": 0, "req_quests": "", "quest_string_index": -1, "quest_string_value": 0, "meta": "", "color": "ffffff", "pid": 499}, {"id": "0196ba87-e698-771d-b7fc-4c28844dc93b", "name": "Game Designers", "description": "Only for Moderators/Administrators use.", "type": "ServerUse", "element": "None", "file": "title::1", "link": "title::1", "icon": "iCheck", "equipment": "None", "level": 1, "dps": 25, "range": 50, "rarity": "0196ba87-e69e-75ac-9885-5763facdf6ca", "quantity": 1, "stack": 1, "cost": 0, "coins": 0, "diamonds": 0, "crystal": 0, "sell": true, "market": false, "temporary": false, "upgrade": false, "staff": true, "enh_id": "0196ba87-e691-7757-a7dd-b3fcba14451c", "req_reputation": 0, "req_class_points": 0, "req_quests": "", "quest_string_index": -1, "quest_string_value": 0, "meta": "", "color": "ffffff", "pid": 500}, {"id": "0196ba87-e698-771d-b7fc-5061697f3c53", "name": "Soul of a Sun God", "description": "The Soul of a Sun God;When you Defeat All of the Monster in Jungle Sun God Will Give You The Soul of Them", "type": "<PERSON><PERSON>", "element": "None", "file": "iibag", "link": "iibag", "icon": "iibag", "equipment": "<PERSON><PERSON>", "level": 1, "dps": 25, "range": 50, "rarity": "0196ba87-e69e-75ac-9885-5763facdf6ca", "quantity": 1, "stack": 1300, "cost": 0, "coins": 0, "diamonds": 0, "crystal": 0, "sell": true, "market": true, "temporary": false, "upgrade": false, "staff": false, "enh_id": "0196ba87-e691-7757-a7dd-b3fcba14451c", "req_reputation": 0, "req_class_points": 0, "req_quests": "", "quest_string_index": -1, "quest_string_value": 0, "meta": "", "color": "ffffff", "pid": 501}, {"id": "0196ba87-e698-771d-b7fc-547756165584", "name": "Essence of Warlord", "description": "", "type": "<PERSON><PERSON>", "element": "None", "file": "iibag", "link": "iibag", "icon": "iibag", "equipment": "<PERSON><PERSON>", "level": 1, "dps": 25, "range": 50, "rarity": "0196ba87-e69e-75ac-9885-5763facdf6ca", "quantity": 1, "stack": 5000, "cost": 0, "coins": 0, "diamonds": 0, "crystal": 0, "sell": true, "market": true, "temporary": false, "upgrade": false, "staff": false, "enh_id": "0196ba87-e691-7757-a7dd-b3fcba14451c", "req_reputation": 0, "req_class_points": 0, "req_quests": "", "quest_string_index": -1, "quest_string_value": 0, "meta": "", "color": "ffffff", "pid": 502}, {"id": "0196ba87-e698-771d-b7fc-5b388ddf37be", "class_id": "0196ba87-e68f-7738-8b2a-f7c841860f2b", "name": "Ancient Hero", "description": "Founder Package. Thank you for supporting PS Nythera.", "type": "Class", "element": "None", "file": "LegendArmor.swf", "link": "LegendArmor", "icon": "iiclass", "equipment": "ar", "level": 1, "dps": 25, "range": 50, "rarity": "0196ba87-e69e-75ac-9885-b58ec5b5bcbc", "quantity": 1, "stack": 1, "cost": 0, "coins": 1, "diamonds": 0, "crystal": 0, "sell": true, "market": true, "temporary": false, "upgrade": true, "staff": false, "enh_id": "0196ba87-e691-7757-a7dd-b3fcba14451c", "req_reputation": 0, "req_class_points": 0, "req_quests": "", "quest_string_index": -1, "quest_string_value": 0, "meta": "", "color": "ffffff", "pid": 503, "req_class_id": "0196ba87-e68f-7738-8b2a-f7c841860f2b"}, {"id": "0196ba87-e698-771d-b7fc-5c503f753375", "name": "Erebus Pet", "description": "", "type": "Pet", "element": "None", "file": "SoulPet.swf", "link": "SoulPet", "icon": "iipet", "equipment": "pe", "level": 1, "dps": 25, "range": 50, "rarity": "0196ba87-e69e-75ac-9885-5763facdf6ca", "quantity": 1, "stack": 1, "cost": 0, "coins": 0, "diamonds": 0, "crystal": 0, "sell": true, "market": true, "temporary": false, "upgrade": false, "staff": true, "enh_id": "0196ba87-e691-7757-a7dd-b3fcba14451c", "req_reputation": 0, "req_class_points": 0, "req_quests": "", "quest_string_index": -1, "quest_string_value": 0, "meta": "", "color": "ffffff", "pid": 504}, {"id": "0196ba87-e698-771d-b7fc-61d56c0094a8", "name": "Lost <PERSON><PERSON>", "description": "This can be Found in Yulgar and to farm in -Jo<PERSON>", "type": "<PERSON><PERSON>", "element": "None", "file": "iibag", "link": "iibag", "icon": "iibag", "equipment": "hi", "level": 1, "dps": 25, "range": 50, "rarity": "0196ba87-e69e-75ac-9885-5763facdf6ca", "quantity": 1, "stack": 300, "cost": 4500, "coins": 0, "diamonds": 0, "crystal": 0, "sell": true, "market": true, "temporary": false, "upgrade": false, "staff": false, "enh_id": "0196ba87-e691-7757-a7dd-b3fcba14451c", "req_reputation": 0, "req_class_points": 0, "req_quests": "", "quest_string_index": -1, "quest_string_value": 0, "meta": "", "color": "ffffff", "pid": 505}, {"id": "0196ba87-e698-771d-b7fc-65666b728b44", "name": "Master <PERSON>", "description": "Darkness Awakening", "type": "Armor", "element": "None", "file": "EmoxusTemplate.swf", "link": "EmoxusTemplate", "icon": "i<PERSON>or", "equipment": "co", "level": 1, "dps": 25, "range": 50, "rarity": "0196ba87-e69e-75ac-9885-be8d368e20ca", "quantity": 1, "stack": 1, "cost": 0, "coins": 1, "diamonds": 0, "crystal": 0, "sell": true, "market": false, "temporary": false, "upgrade": false, "staff": false, "enh_id": "0196ba87-e691-7757-a7dd-b3fcba14451c", "req_reputation": 0, "req_class_points": 0, "req_quests": "", "quest_string_index": -1, "quest_string_value": 0, "meta": "", "color": "ffffff", "pid": 506}, {"id": "0196ba87-e698-771d-b7fc-690cfe599035", "name": "Master <PERSON><PERSON>", "description": "Rare", "type": "<PERSON><PERSON>", "element": "None", "file": "items/helms/Aviatortest.swf", "link": "Aviatortest", "icon": "iihelm", "equipment": "he", "level": 1, "dps": 25, "range": 50, "rarity": "0196ba87-e69e-75ac-9885-be8d368e20ca", "quantity": 1, "stack": 1, "cost": 500, "coins": 1, "diamonds": 0, "crystal": 0, "sell": true, "market": false, "temporary": false, "upgrade": false, "staff": false, "enh_id": "0196ba87-e691-7757-a7dd-b3fcba14451c", "req_reputation": 0, "req_class_points": 0, "req_quests": "", "quest_string_index": -1, "quest_string_value": 0, "meta": "", "color": "ffffff", "pid": 507}, {"id": "0196ba87-e698-771d-b7fc-6e530f50d4d7", "name": "Master <PERSON><PERSON>", "description": "Rare", "type": "Sword", "element": "None", "file": "items/swords/StormFireErebus.swf", "link": "StormFireErebus", "icon": "iwsword", "equipment": "Weapon", "level": 1, "dps": 25, "range": 50, "rarity": "0196ba87-e69e-75ac-9885-be8d368e20ca", "quantity": 1, "stack": 1, "cost": 0, "coins": 1, "diamonds": 0, "crystal": 0, "sell": true, "market": false, "temporary": false, "upgrade": false, "staff": false, "enh_id": "0196ba87-e691-7757-a7dd-b3fcba14451c", "req_reputation": 0, "req_class_points": 0, "req_quests": "", "quest_string_index": -1, "quest_string_value": 0, "meta": "", "color": "ffffff", "pid": 508}, {"id": "0196ba87-e698-771d-b7fc-71bf4dc07ad4", "name": "Dual Master Crow<PERSON>", "description": "Rare", "type": "<PERSON>gger", "element": "None", "file": "items/daggers/StormFireErebus.swf", "link": "StormFireErebus", "icon": "iwda<PERSON>", "equipment": "Weapon", "level": 1, "dps": 25, "range": 50, "rarity": "0196ba87-e69e-75ac-9885-be8d368e20ca", "quantity": 1, "stack": 1, "cost": 0, "coins": 1, "diamonds": 0, "crystal": 0, "sell": true, "market": false, "temporary": false, "upgrade": false, "staff": false, "enh_id": "0196ba87-e691-7757-a7dd-b3fcba14451c", "req_reputation": 0, "req_class_points": 0, "req_quests": "", "quest_string_index": -1, "quest_string_value": 0, "meta": "", "color": "ffffff", "pid": 509}, {"id": "0196ba87-e698-771d-b7fc-772f8ec9d92c", "name": "Ichigo Blade", "description": "Ichigo Sword;Bleach", "type": "Sword", "element": "None", "file": "items/swords/Bleach2.swf", "link": "Bleach2", "icon": "iwsword", "equipment": "Weapon", "level": 1, "dps": 25, "range": 50, "rarity": "0196ba87-e69e-75ac-9885-af4e24f6923c", "quantity": 1, "stack": 1, "cost": 1500, "coins": 1, "diamonds": 0, "crystal": 0, "sell": true, "market": false, "temporary": false, "upgrade": false, "staff": false, "enh_id": "0196ba87-e691-7757-a7dd-b3fcba14451c", "req_reputation": 0, "req_class_points": 0, "req_quests": "", "quest_string_index": -1, "quest_string_value": 0, "meta": "", "color": "ffffff", "pid": 510}, {"id": "0196ba87-e698-771d-b7fc-799bf8133afe", "name": "Erebus Pirate Armor", "description": "", "type": "Armor", "element": "None", "file": "ArtlaSPirateArmor1.swf", "link": "ArtlaSPirateArmor", "icon": "i<PERSON>or", "equipment": "co", "level": 1, "dps": 25, "range": 50, "rarity": "0196ba87-e69e-75ac-9885-5763facdf6ca", "quantity": 1, "stack": 1, "cost": 0, "coins": 0, "diamonds": 0, "crystal": 0, "sell": true, "market": true, "temporary": false, "upgrade": false, "staff": false, "enh_id": "0196ba87-e691-7757-a7dd-b3fcba14451c", "req_reputation": 0, "req_class_points": 0, "req_quests": "", "quest_string_index": -1, "quest_string_value": 0, "meta": "", "color": "ffffff", "pid": 511}, {"id": "0196ba87-e698-771d-b7fc-7cd6e37083c2", "name": "Strength Potion", "description": "This potion increases your damage by 70% for 1 minute", "type": "<PERSON><PERSON>", "element": "None", "file": "icb3", "link": "icb3", "icon": "icb3", "equipment": "None", "level": 1, "dps": 25, "range": 50, "rarity": "0196ba87-e69e-75ac-9885-5b8b0c71fdb4", "quantity": 1, "stack": 5, "cost": 1000, "coins": 0, "diamonds": 0, "crystal": 0, "sell": true, "market": true, "temporary": false, "upgrade": false, "staff": false, "enh_id": "0196ba87-e691-7757-a7dd-b3fcba14451c", "req_reputation": 0, "req_class_points": 0, "req_quests": "", "quest_string_index": -1, "quest_string_value": 0, "meta": "8", "color": "e34f4f", "pid": 512}, {"id": "0196ba87-e698-771d-b7fc-806948e4ab2f", "name": "Soul King Token", "description": "this can be found in yulgar datapaw NPC", "type": "<PERSON><PERSON>", "element": "None", "file": "iibag", "link": "iibag", "icon": "iibag", "equipment": "<PERSON><PERSON>", "level": 1, "dps": 25, "range": 50, "rarity": "0196ba87-e69e-75ac-9885-5763facdf6ca", "quantity": 1, "stack": 5, "cost": 0, "coins": 0, "diamonds": 0, "crystal": 0, "sell": true, "market": true, "temporary": false, "upgrade": false, "staff": false, "enh_id": "0196ba87-e691-7757-a7dd-b3fcba14451c", "req_reputation": 0, "req_class_points": 0, "req_quests": "", "quest_string_index": -1, "quest_string_value": 0, "meta": "", "color": "ffffff", "pid": 513}, {"id": "0196ba87-e698-771d-b7fc-87e68a8d984e", "name": "Djin Defeated", "description": "this can found in djinboss", "type": "<PERSON><PERSON>", "element": "None", "file": "iibag", "link": "iibag", "icon": "iibag", "equipment": "None", "level": 1, "dps": 25, "range": 50, "rarity": "0196ba87-e69e-75ac-9885-5763facdf6ca", "quantity": 1, "stack": 1000, "cost": 0, "coins": 0, "diamonds": 0, "crystal": 0, "sell": true, "market": true, "temporary": true, "upgrade": false, "staff": false, "enh_id": "0196ba87-e691-7757-a7dd-b3fcba14451c", "req_reputation": 0, "req_class_points": 0, "req_quests": "", "quest_string_index": -1, "quest_string_value": 0, "meta": "", "color": "ffffff", "pid": 514}, {"id": "0196ba87-e698-771d-b7fc-88aa3c1f4d23", "class_id": "0196ba87-e68f-7738-8b2a-fb66c6598188", "name": "Void HighLord", "description": "Only the strongest;toughest;most dedicated (and insane) members of the Nulgath Nation can survive the trials required to unlock the Void Highlord Class!", "type": "Class", "element": "None", "file": "VoidArmor.swf", "link": "VoidArmor", "icon": "iiclass", "equipment": "ar", "level": 1, "dps": 25, "range": 50, "rarity": "0196ba87-e69e-75ac-9885-794081f589ef", "quantity": 1, "stack": 1, "cost": 0, "coins": 1, "diamonds": 0, "crystal": 0, "sell": true, "market": true, "temporary": false, "upgrade": false, "staff": false, "enh_id": "0196ba87-e691-7757-a7dd-b3fcba14451c", "req_reputation": 0, "req_class_points": 0, "req_quests": "", "quest_string_index": -1, "quest_string_value": 0, "meta": "", "color": "ffffff", "pid": 515, "req_class_id": "0196ba87-e68f-7738-8b2a-fb66c6598188"}, {"id": "0196ba87-e698-771d-b7fc-8c6a1fb36568", "name": "PvP Trophy", "description": "A mark signifying honorable player versus player (PvP) combat. It's not about winning;it's about making sure the other person loses.", "type": "<PERSON><PERSON>", "element": "None", "file": "iibag", "link": "iibag", "icon": "iibag", "equipment": "<PERSON><PERSON>", "level": 1, "dps": 25, "range": 50, "rarity": "0196ba87-e69e-75ac-9885-5763facdf6ca", "quantity": 1, "stack": 500, "cost": 0, "coins": 1, "diamonds": 0, "crystal": 0, "sell": true, "market": true, "temporary": false, "upgrade": false, "staff": false, "enh_id": "0196ba87-e691-7757-a7dd-b3fcba14451c", "req_reputation": 0, "req_class_points": 0, "req_quests": "", "quest_string_index": -1, "quest_string_value": 0, "meta": "", "color": "ffffff", "pid": 516}, {"id": "0196ba87-e698-771d-b7fc-902955c2e542", "name": "Urban Samurai Armor", "description": "Keeping the traditions of old doesn't mean you can't look fly.", "type": "Armor", "element": "None", "file": "UrbanSamurai.swf", "link": "UrbanSamurai", "icon": "i<PERSON>or", "equipment": "co", "level": 1, "dps": 25, "range": 50, "rarity": "0196ba87-e69e-75ac-9885-621e9f668f9c", "quantity": 1, "stack": 1, "cost": 0, "coins": 1, "diamonds": 0, "crystal": 0, "sell": true, "market": true, "temporary": false, "upgrade": false, "staff": false, "enh_id": "0196ba87-e691-7757-a7dd-b3fcba14451c", "req_reputation": 0, "req_class_points": 0, "req_quests": "", "quest_string_index": -1, "quest_string_value": 0, "meta": "", "color": "ffffff", "pid": 517}, {"id": "0196ba87-e698-771d-b7fc-9671d9709ea2", "name": "Urban Samurai Hat", "description": "Keeping the traditions of old doesn't mean you can't look fly.", "type": "<PERSON><PERSON>", "element": "None", "file": "items/helms/UrbanSamuraiHat.swf", "link": "UrbanSamuraiHat", "icon": "iihelm", "equipment": "he", "level": 1, "dps": 25, "range": 50, "rarity": "0196ba87-e69e-75ac-9885-621e9f668f9c", "quantity": 1, "stack": 1, "cost": 0, "coins": 1, "diamonds": 0, "crystal": 0, "sell": true, "market": true, "temporary": false, "upgrade": false, "staff": false, "enh_id": "0196ba87-e691-7757-a7dd-b3fcba14451c", "req_reputation": 0, "req_class_points": 0, "req_quests": "", "quest_string_index": -1, "quest_string_value": 0, "meta": "", "color": "ffffff", "pid": 518}, {"id": "0196ba87-e698-771d-b7fc-9b1deaec28c1", "name": "Urban Samurai Mask + Locks", "description": "Keeping the traditions of old doesn't mean you can't look fly.\\r\\n", "type": "<PERSON><PERSON>", "element": "None", "file": "items/helms/UrbanSamuraiMaskLocks.swf", "link": "UrbanSamuraiMaskLocks", "icon": "iihelm", "equipment": "he", "level": 1, "dps": 25, "range": 50, "rarity": "0196ba87-e69e-75ac-9885-5763facdf6ca", "quantity": 1, "stack": 1, "cost": 0, "coins": 1, "diamonds": 0, "crystal": 0, "sell": true, "market": true, "temporary": false, "upgrade": false, "staff": false, "enh_id": "0196ba87-e691-7757-a7dd-b3fcba14451c", "req_reputation": 0, "req_class_points": 0, "req_quests": "", "quest_string_index": -1, "quest_string_value": 0, "meta": "", "color": "ffffff", "pid": 519}, {"id": "0196ba87-e698-771d-b7fc-9c3a78f0c5b6", "name": "Urban Samurai Mask", "description": "Keeping the traditions of old doesn't mean you can't look fly.", "type": "<PERSON><PERSON>", "element": "None", "file": "items/helms/UrbanSamuraiMask.swf", "link": "UrbanSamuraiMask", "icon": "iihelm", "equipment": "he", "level": 1, "dps": 25, "range": 50, "rarity": "0196ba87-e69e-75ac-9885-621e9f668f9c", "quantity": 1, "stack": 1, "cost": 0, "coins": 1, "diamonds": 0, "crystal": 0, "sell": true, "market": true, "temporary": false, "upgrade": false, "staff": false, "enh_id": "0196ba87-e691-7757-a7dd-b3fcba14451c", "req_reputation": 0, "req_class_points": 0, "req_quests": "", "quest_string_index": -1, "quest_string_value": 0, "meta": "", "color": "ffffff", "pid": 520}, {"id": "0196ba87-e698-771d-b7fc-a3592bd7d0f6", "name": "Ichigo", "description": "", "type": "Armor", "element": "None", "file": "Ichigo.swf", "link": "Ichigo", "icon": "i<PERSON>or", "equipment": "co", "level": 1, "dps": 25, "range": 50, "rarity": "0196ba87-e69e-75ac-9885-91ec33cb57dc", "quantity": 1, "stack": 1, "cost": 0, "coins": 1, "diamonds": 0, "crystal": 0, "sell": true, "market": false, "temporary": false, "upgrade": false, "staff": false, "enh_id": "0196ba87-e691-7757-a7dd-b3fcba14451c", "req_reputation": 0, "req_class_points": 0, "req_quests": "", "quest_string_index": -1, "quest_string_value": 0, "meta": "", "color": "ffffff", "pid": 521}, {"id": "0196ba87-e698-771d-b7fc-a60546cae4ff", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "description": "Rare", "type": "Armor", "element": "None", "file": "Ulquorra.swf", "link": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "icon": "i<PERSON>or", "equipment": "co", "level": 1, "dps": 25, "range": 50, "rarity": "0196ba87-e69e-75ac-9885-be8d368e20ca", "quantity": 1, "stack": 1, "cost": 0, "coins": 1, "diamonds": 0, "crystal": 0, "sell": true, "market": false, "temporary": false, "upgrade": false, "staff": false, "enh_id": "0196ba87-e691-7757-a7dd-b3fcba14451c", "req_reputation": 0, "req_class_points": 0, "req_quests": "", "quest_string_index": -1, "quest_string_value": 0, "meta": "", "color": "ffffff", "pid": 522}, {"id": "0196ba87-e698-771d-b7fc-aa674cf70585", "name": "Straw Hat", "description": "Rare", "type": "<PERSON><PERSON>", "element": "None", "file": "items/helms/StrawHat.swf", "link": "StrawHat", "icon": "iihelm", "equipment": "he", "level": 1, "dps": 25, "range": 50, "rarity": "0196ba87-e69e-75ac-9885-be8d368e20ca", "quantity": 1, "stack": 1, "cost": 0, "coins": 1, "diamonds": 0, "crystal": 0, "sell": true, "market": false, "temporary": false, "upgrade": false, "staff": false, "enh_id": "0196ba87-e691-7757-a7dd-b3fcba14451c", "req_reputation": 0, "req_class_points": 0, "req_quests": "", "quest_string_index": -1, "quest_string_value": 0, "meta": "", "color": "ffffff", "pid": 523}, {"id": "0196ba87-e698-771d-b7fc-af7c9b700554", "class_id": "0196ba87-e68f-7738-8b2a-fedbde1b133c", "name": "Master <PERSON>", "description": "", "type": "Class", "element": "None", "file": "EmoxusTemplate.swf", "link": "EmoxusTemplate", "icon": "iiclass", "equipment": "ar", "level": 1, "dps": 25, "range": 50, "rarity": "0196ba87-e69e-75ac-9885-be8d368e20ca", "quantity": 1, "stack": 1, "cost": 0, "coins": 1, "diamonds": 0, "crystal": 0, "sell": true, "market": false, "temporary": false, "upgrade": false, "staff": true, "enh_id": "0196ba87-e691-7757-a7dd-eb49d94c0957", "req_reputation": 0, "req_class_points": 0, "req_quests": "", "quest_string_index": -1, "quest_string_value": 0, "meta": "", "color": "ffffff", "pid": 524, "req_class_id": "0196ba87-e68f-7738-8b2a-fedbde1b133c"}, {"id": "0196ba87-e698-771d-b7fc-b0ad94c26c8a", "name": "<PERSON><PERSON><PERSON><PERSON>", "description": "", "type": "Armor", "element": "None", "file": "Legion2018Calendar.swf", "link": "Legion2018Calendar", "icon": "i<PERSON>or", "equipment": "co", "level": 1, "dps": 25, "range": 50, "rarity": "0196ba87-e69e-75ac-9885-5763facdf6ca", "quantity": 1, "stack": 1, "cost": 0, "coins": 0, "diamonds": 0, "crystal": 0, "sell": true, "market": true, "temporary": false, "upgrade": false, "staff": false, "enh_id": "0196ba87-e691-7757-a7dd-b3fcba14451c", "req_reputation": 0, "req_class_points": 0, "req_quests": "", "quest_string_index": -1, "quest_string_value": 0, "meta": "", "color": "ffffff", "pid": 525}, {"id": "0196ba87-e698-771d-b7fc-b7d5276c7001", "name": "Fixing Armor", "description": "", "type": "Armor", "element": "None", "file": "datapaw11.swf", "link": "datapaw", "icon": "i<PERSON>or", "equipment": "co", "level": 1, "dps": 25, "range": 50, "rarity": "0196ba87-e69e-75ac-9885-5763facdf6ca", "quantity": 1, "stack": 1, "cost": 0, "coins": 0, "diamonds": 0, "crystal": 0, "sell": true, "market": true, "temporary": false, "upgrade": false, "staff": false, "enh_id": "0196ba87-e691-7757-a7dd-b3fcba14451c", "req_reputation": 0, "req_class_points": 0, "req_quests": "", "quest_string_index": -1, "quest_string_value": 0, "meta": "", "color": "ffffff", "pid": 526}, {"id": "0196ba87-e698-771d-b7fc-bb5fa431f912", "class_id": "0196ba87-e68f-7738-8b2b-063778d9f35f", "name": "Fallen Angel", "description": "Fallen Angels are the strategists of the Nation. Trained by Revontheus to wield Crystal Weaponry;they excel at fighting multiple enemies with arrows forged by <PERSON><PERSON><PERSON><PERSON>. Fallen Angels have various ways of taking their opponents down.", "type": "Class", "element": "None", "file": "AbyssalAngel2.swf", "link": "AbyssalAngel2", "icon": "iiclass", "equipment": "ar", "level": 1, "dps": 25, "range": 50, "rarity": "0196ba87-e69e-75ac-9885-c0ff0942e277", "quantity": 1, "stack": 1, "cost": 0, "coins": 1, "diamonds": 0, "crystal": 0, "sell": true, "market": true, "temporary": false, "upgrade": false, "staff": false, "enh_id": "0196ba87-e691-7757-a7dd-b3fcba14451c", "req_reputation": 0, "req_class_points": 0, "req_quests": "", "quest_string_index": -1, "quest_string_value": 0, "meta": "", "color": "ffffff", "pid": 527, "req_class_id": "0196ba87-e68f-7738-8b2b-063778d9f35f"}, {"id": "0196ba87-e698-771d-b7fc-be130c0304cb", "name": "Genots B", "description": "", "type": "Armor", "element": "None", "file": "Workbymylittlebrotherswf2.swf", "link": "FemaleTemplate", "icon": "i<PERSON>or", "equipment": "co", "level": 1, "dps": 25, "range": 50, "rarity": "0196ba87-e69e-75ac-9885-5763facdf6ca", "quantity": 1, "stack": 1, "cost": 0, "coins": 0, "diamonds": 0, "crystal": 0, "sell": true, "market": true, "temporary": false, "upgrade": false, "staff": false, "enh_id": "0196ba87-e691-7757-a7dd-b3fcba14451c", "req_reputation": 0, "req_class_points": 0, "req_quests": "", "quest_string_index": -1, "quest_string_value": 0, "meta": "", "color": "ffffff", "pid": 528}, {"id": "0196ba87-e698-771d-b7fc-c1b566fdc39b", "name": "Soul King Aura", "description": "soul king aura just black shadow fire", "type": "Cape", "element": "None", "file": "items/capes/fixAura2.swf", "link": "Aura2", "icon": "iicape", "equipment": "ba", "level": 1, "dps": 25, "range": 50, "rarity": "0196ba87-e69e-75ac-9885-af4e24f6923c", "quantity": 1, "stack": 1, "cost": 1230, "coins": 1, "diamonds": 0, "crystal": 0, "sell": true, "market": true, "temporary": false, "upgrade": false, "staff": false, "enh_id": "0196ba87-e691-7757-a7dd-b3fcba14451c", "req_reputation": 0, "req_class_points": 0, "req_quests": "", "quest_string_index": -1, "quest_string_value": 0, "meta": "", "color": "ffffff", "pid": 529}, {"id": "0196ba87-e698-771d-b7fc-c6afab26354e", "name": "Flaming Mask", "description": "10x Quantity", "type": "<PERSON><PERSON>", "element": "None", "file": "items/helms/SmokeMaskDatapaw1.swf", "link": "SmokeMaskDatapaw", "icon": "iihelm", "equipment": "he", "level": 1, "dps": 25, "range": 50, "rarity": "0196ba87-e69e-75ac-9885-af4e24f6923c", "quantity": 1, "stack": 1, "cost": 2500, "coins": 1, "diamonds": 0, "crystal": 0, "sell": true, "market": true, "temporary": false, "upgrade": false, "staff": false, "enh_id": "0196ba87-e691-7757-a7dd-b3fcba14451c", "req_reputation": 0, "req_class_points": 0, "req_quests": "", "quest_string_index": -1, "quest_string_value": 0, "meta": "", "color": "ffffff", "pid": 530}, {"id": "0196ba87-e698-771d-b7fc-c8b96ee9d014", "name": "Alter Miners Team", "description": "Alter Miners.. Finding a gold can be donated to poor players if you equipe this youre with us", "type": "Armor", "element": "None", "file": "AltMinersTeam2020.swf", "link": "AltMinersTeam2020", "icon": "i<PERSON>or", "equipment": "co", "level": 1, "dps": 25, "range": 50, "rarity": "0196ba87-e69e-75ac-9885-af4e24f6923c", "quantity": 1, "stack": 1, "cost": 800, "coins": 1, "diamonds": 0, "crystal": 0, "sell": true, "market": true, "temporary": false, "upgrade": false, "staff": false, "enh_id": "0196ba87-e691-7757-a7dd-b3fcba14451c", "req_reputation": 0, "req_class_points": 0, "req_quests": "", "quest_string_index": -1, "quest_string_value": 0, "meta": "", "color": "ffffff", "pid": 531}, {"id": "0196ba87-e698-771d-b7fc-cc5de4237742", "name": "Avatar of Death", "description": "Im in youre back while youre not dead well ill be in your back to make way to get you die WOAHAHA im your worst Nightmare", "type": "Cape", "element": "None", "file": "items/capes/AvatarofDeathv2.swf", "link": "AvatarofDeathv2", "icon": "iicape", "equipment": "ba", "level": 1, "dps": 25, "range": 50, "rarity": "0196ba87-e69e-75ac-9885-af4e24f6923c", "quantity": 1, "stack": 1, "cost": 4000, "coins": 1, "diamonds": 0, "crystal": 0, "sell": true, "market": true, "temporary": false, "upgrade": false, "staff": false, "enh_id": "0196ba87-e691-7757-a7dd-b3fcba14451c", "req_reputation": 0, "req_class_points": 0, "req_quests": "", "quest_string_index": -1, "quest_string_value": 0, "meta": "", "color": "ffffff", "pid": 532}, {"id": "0196ba87-e698-771d-b7fc-d32bbffafee8", "name": "Brutal Corn", "description": "Dont be frude this face an scammers face and fake friends wear this and youre frude as like me", "type": "<PERSON><PERSON>", "element": "None", "file": "items/helms/BrutalcornToInfinityAndBeyond.swf", "link": "BrutalcornToInfinityAndBeyond", "icon": "iihelm", "equipment": "he", "level": 1, "dps": 25, "range": 50, "rarity": "0196ba87-e69e-75ac-9885-af4e24f6923c", "quantity": 1, "stack": 1, "cost": 500, "coins": 1, "diamonds": 0, "crystal": 0, "sell": true, "market": true, "temporary": false, "upgrade": false, "staff": false, "enh_id": "0196ba87-e691-7757-a7dd-b3fcba14451c", "req_reputation": 0, "req_class_points": 0, "req_quests": "", "quest_string_index": -1, "quest_string_value": 0, "meta": "", "color": "ffffff", "pid": 533}, {"id": "0196ba87-e698-771d-b7fc-d4ef023bba7e", "name": "<PERSON>", "description": "just me and you 1v1 and you will see the power of tiger fang", "type": "Gauntlet", "element": "None", "file": "items/gauntlets/TigerFang.swf", "link": "TigerFang", "icon": "iwclaws", "equipment": "Weapon", "level": 1, "dps": 25, "range": 50, "rarity": "0196ba87-e69e-75ac-9885-af4e24f6923c", "quantity": 1, "stack": 1, "cost": 1600, "coins": 1, "diamonds": 0, "crystal": 0, "sell": true, "market": true, "temporary": false, "upgrade": false, "staff": false, "enh_id": "0196ba87-e691-7757-a7dd-b3fcba14451c", "req_reputation": 0, "req_class_points": 0, "req_quests": "", "quest_string_index": -1, "quest_string_value": 0, "meta": "", "color": "ffffff", "pid": 534}, {"id": "0196ba87-e698-771d-b7fc-d99b17ccf100", "name": "<PERSON> <PERSON><PERSON><PERSON>", "description": "Thanks for Supportings Us!", "type": "Sword", "element": "None", "file": "items/swords/DarkPaladinNavalSwordFixedD.swf", "link": "DarkPaladinNavalSwordFixed", "icon": "iwsword", "equipment": "Weapon", "level": 1, "dps": 25, "range": 50, "rarity": "0196ba87-e69e-75ac-9885-b58ec5b5bcbc", "quantity": 1, "stack": 1, "cost": 0, "coins": 1, "diamonds": 0, "crystal": 0, "sell": true, "market": false, "temporary": false, "upgrade": false, "staff": false, "enh_id": "0196ba87-e691-7757-a7dd-b3fcba14451c", "req_reputation": 0, "req_class_points": 0, "req_quests": "", "quest_string_index": -1, "quest_string_value": 0, "meta": "", "color": "ffffff", "pid": 535}, {"id": "0196ba87-e698-771d-b7fc-de7b725c512a", "name": "<PERSON> Paladin B<PERSON>o<PERSON>", "description": "Thanks for Supporting Us!", "type": "Axe", "element": "None", "file": "items/axes/DarkNavalBLoD.swf", "link": "DarkNavalBLoD", "icon": "iwaxe", "equipment": "Weapon", "level": 1, "dps": 25, "range": 50, "rarity": "0196ba87-e69e-75ac-9885-b58ec5b5bcbc", "quantity": 1, "stack": 1, "cost": 0, "coins": 1, "diamonds": 0, "crystal": 0, "sell": true, "market": false, "temporary": false, "upgrade": false, "staff": false, "enh_id": "0196ba87-e691-7757-a7dd-b3fcba14451c", "req_reputation": 0, "req_class_points": 0, "req_quests": "", "quest_string_index": -1, "quest_string_value": 0, "meta": "", "color": "ffffff", "pid": 536}, {"id": "0196ba87-e698-771d-b7fc-e3cc1be5435b", "name": "Dark Caladbolg Paladin", "description": "Thanks for Supporting Us!", "type": "Sword", "element": "None", "file": "items/swords/PaladinCaladbolg.swf", "link": "PaladinCaladbolg", "icon": "iwsword", "equipment": "Weapon", "level": 1, "dps": 25, "range": 50, "rarity": "0196ba87-e69e-75ac-9885-b58ec5b5bcbc", "quantity": 1, "stack": 1, "cost": 0, "coins": 1, "diamonds": 0, "crystal": 0, "sell": true, "market": false, "temporary": false, "upgrade": false, "staff": false, "enh_id": "0196ba87-e691-7757-a7dd-b3fcba14451c", "req_reputation": 0, "req_class_points": 0, "req_quests": "", "quest_string_index": -1, "quest_string_value": 0, "meta": "", "color": "ffffff", "pid": 537}, {"id": "0196ba87-e698-771d-b7fc-e765aed494c5", "name": "<PERSON>lad<PERSON>", "description": "Thanks for Supporting Us!", "type": "Armor", "element": "None", "file": "DarkPaladinNaval33.swf", "link": "Dark<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "icon": "i<PERSON>or", "equipment": "co", "level": 1, "dps": 25, "range": 50, "rarity": "0196ba87-e69e-75ac-9885-b58ec5b5bcbc", "quantity": 1, "stack": 1, "cost": 0, "coins": 1, "diamonds": 0, "crystal": 0, "sell": true, "market": false, "temporary": false, "upgrade": false, "staff": false, "enh_id": "0196ba87-e691-7757-a7dd-b3fcba14451c", "req_reputation": 0, "req_class_points": 0, "req_quests": "", "quest_string_index": -1, "quest_string_value": 0, "meta": "", "color": "ffffff", "pid": 538}, {"id": "0196ba87-e698-771d-b7fc-eac71e369707", "name": "<PERSON> Paladin <PERSON>", "description": "Thanks for Supporting Us!", "type": "<PERSON><PERSON>", "element": "None", "file": "items/helms/DarkPaladinHat.swf", "link": "DarkPaladinHat", "icon": "iihelm", "equipment": "he", "level": 1, "dps": 25, "range": 50, "rarity": "0196ba87-e69e-75ac-9885-b58ec5b5bcbc", "quantity": 1, "stack": 1, "cost": 0, "coins": 1, "diamonds": 0, "crystal": 0, "sell": true, "market": false, "temporary": false, "upgrade": false, "staff": false, "enh_id": "0196ba87-e691-7757-a7dd-b3fcba14451c", "req_reputation": 0, "req_class_points": 0, "req_quests": "", "quest_string_index": -1, "quest_string_value": 0, "meta": "", "color": "ffffff", "pid": 539}, {"id": "0196ba87-e698-771d-b7fc-ee7d8c477156", "name": "<PERSON> <PERSON><PERSON><PERSON>", "description": "Thanks for Supporting Us!", "type": "<PERSON><PERSON>", "element": "None", "file": "items/helms/DarkPaladinHelm.swf", "link": "DarkPaladinHelm", "icon": "iihelm", "equipment": "he", "level": 1, "dps": 25, "range": 50, "rarity": "0196ba87-e69e-75ac-9885-b58ec5b5bcbc", "quantity": 1, "stack": 1, "cost": 0, "coins": 1, "diamonds": 0, "crystal": 0, "sell": true, "market": false, "temporary": false, "upgrade": false, "staff": false, "enh_id": "0196ba87-e691-7757-a7dd-b3fcba14451c", "req_reputation": 0, "req_class_points": 0, "req_quests": "", "quest_string_index": -1, "quest_string_value": 0, "meta": "", "color": "ffffff", "pid": 540}, {"id": "0196ba87-e698-771d-b7fc-f2e90553d3ff", "name": "<PERSON><PERSON>", "description": "Thanks for Supporting Us!", "type": "<PERSON><PERSON>", "element": "None", "file": "items/helms/DarkPaladinNavalHelm2.swf", "link": "DarkPaladinNavalHelm2", "icon": "iihelm", "equipment": "he", "level": 1, "dps": 25, "range": 50, "rarity": "0196ba87-e69e-75ac-9885-b58ec5b5bcbc", "quantity": 1, "stack": 1, "cost": 0, "coins": 1, "diamonds": 0, "crystal": 0, "sell": true, "market": false, "temporary": false, "upgrade": false, "staff": false, "enh_id": "0196ba87-e691-7757-a7dd-b3fcba14451c", "req_reputation": 0, "req_class_points": 0, "req_quests": "", "quest_string_index": -1, "quest_string_value": 0, "meta": "", "color": "ffffff", "pid": 541}, {"id": "0196ba87-e698-771d-b7fc-f6f665c7e1a7", "name": "<PERSON> <PERSON><PERSON>", "description": "Thanks for Supporting Us!", "type": "ServerUse", "element": "None", "file": "iic2", "link": "bundle::1::true", "icon": "iic2", "equipment": "None", "level": 1, "dps": 25, "range": 50, "rarity": "0196ba87-e69e-75ac-9885-b58ec5b5bcbc", "quantity": 1, "stack": 1, "cost": 0, "coins": 1, "diamonds": 0, "crystal": 0, "sell": true, "market": true, "temporary": false, "upgrade": false, "staff": false, "enh_id": "0196ba87-e691-7757-a7dd-b3fcba14451c", "req_reputation": 0, "req_class_points": 0, "req_quests": "", "quest_string_index": -1, "quest_string_value": 0, "meta": "", "color": "ffffff", "pid": 542}, {"id": "0196ba87-e698-771d-b7fc-f87e778c6c8e", "name": "Ultranic Titan Killed", "description": "Can be found in roftitan", "type": "<PERSON><PERSON>", "element": "None", "file": "iibag", "link": "iibag", "icon": "iibag", "equipment": "<PERSON><PERSON>", "level": 1, "dps": 25, "range": 50, "rarity": "0196ba87-e69e-75ac-9885-5763facdf6ca", "quantity": 1, "stack": 20, "cost": 0, "coins": 0, "diamonds": 0, "crystal": 0, "sell": true, "market": true, "temporary": true, "upgrade": false, "staff": false, "enh_id": "0196ba87-e691-7757-a7dd-b3fcba14451c", "req_reputation": 0, "req_class_points": 0, "req_quests": "", "quest_string_index": -1, "quest_string_value": 0, "meta": "", "color": "ffffff", "pid": 543}, {"id": "0196ba87-e698-771d-b7fc-fe0e727b42f9", "name": "Golden Box Killed", "description": "Can be found in Roftitan", "type": "<PERSON><PERSON>", "element": "None", "file": "iibag", "link": "iibag", "icon": "iibag", "equipment": "<PERSON><PERSON>", "level": 1, "dps": 25, "range": 50, "rarity": "0196ba87-e69e-75ac-9885-5763facdf6ca", "quantity": 1, "stack": 20, "cost": 0, "coins": 0, "diamonds": 0, "crystal": 0, "sell": true, "market": true, "temporary": true, "upgrade": false, "staff": false, "enh_id": "0196ba87-e691-7757-a7dd-b3fcba14451c", "req_reputation": 0, "req_class_points": 0, "req_quests": "", "quest_string_index": -1, "quest_string_value": 0, "meta": "", "color": "ffffff", "pid": 544}, {"id": "0196ba87-e698-771d-b7fd-037ee6a045d1", "name": "<PERSON>", "description": "Just in Scarf and moving in wind", "type": "Cape", "element": "None", "file": "items/capes/TSScarfCC.swf", "link": "TSScarfCC", "icon": "iicape", "equipment": "ba", "level": 1, "dps": 25, "range": 50, "rarity": "0196ba87-e69e-75ac-9885-af4e24f6923c", "quantity": 1, "stack": 1, "cost": 6000, "coins": 1, "diamonds": 0, "crystal": 0, "sell": true, "market": true, "temporary": false, "upgrade": false, "staff": false, "enh_id": "0196ba87-e691-7757-a7dd-b3fcba14451c", "req_reputation": 0, "req_class_points": 0, "req_quests": "", "quest_string_index": -1, "quest_string_value": 0, "meta": "", "color": "ffffff", "pid": 545}, {"id": "0196ba87-e698-771d-b7fd-06e2561a9d18", "name": "Spirit Harvester", "description": "10x quantity", "type": "Sword", "element": "None", "file": "items/swords/SpiritHarvesterSwordAnimated.swf", "link": "SpiritHarvesterSwordAnimated", "icon": "iwsword", "equipment": "Weapon", "level": 1, "dps": 25, "range": 50, "rarity": "0196ba87-e69e-75ac-9885-af4e24f6923c", "quantity": 1, "stack": 1, "cost": 500000, "coins": 0, "diamonds": 0, "crystal": 0, "sell": true, "market": true, "temporary": false, "upgrade": false, "staff": false, "enh_id": "0196ba87-e691-7757-a7dd-b3fcba14451c", "req_reputation": 0, "req_class_points": 0, "req_quests": "", "quest_string_index": -1, "quest_string_value": 0, "meta": "", "color": "ffffff", "pid": 546}, {"id": "0196ba87-e698-771d-b7fd-0a7fe16e7580", "class_id": "0196ba87-e68f-7738-8b2b-0bf74dc22c8d", "name": "<PERSON> Paladin", "description": "Thank your for supporting PS Nythera", "type": "Class", "element": "None", "file": "DarkPaladinNaval33.swf", "link": "Dark<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "icon": "ied2", "equipment": "ar", "level": 1, "dps": 25, "range": 50, "rarity": "0196ba87-e69e-75ac-9885-b58ec5b5bcbc", "quantity": 1, "stack": 1, "cost": 0, "coins": 1, "diamonds": 0, "crystal": 0, "sell": true, "market": false, "temporary": false, "upgrade": false, "staff": false, "enh_id": "0196ba87-e691-7757-a7dd-b3fcba14451c", "req_reputation": 0, "req_class_points": 0, "req_quests": "", "quest_string_index": -1, "quest_string_value": 0, "meta": "", "color": "ffffff", "pid": 547, "req_class_id": "0196ba87-e68f-7738-8b2b-0bf74dc22c8d"}, {"id": "0196ba87-e698-771d-b7fd-0c8ec841e83c", "name": "Dragon Slayer", "description": "", "type": "Pet", "element": "None", "file": "items/pets/petest4.swf", "link": "BattleWyvern", "icon": "iipet", "equipment": "pe", "level": 1, "dps": 25, "range": 50, "rarity": "0196ba87-e69e-75ac-9885-5763facdf6ca", "quantity": 1, "stack": 1, "cost": 0, "coins": 0, "diamonds": 0, "crystal": 0, "sell": true, "market": true, "temporary": false, "upgrade": false, "staff": false, "enh_id": "0196ba87-e691-7757-a7dd-b3fcba14451c", "req_reputation": 0, "req_class_points": 0, "req_quests": "", "quest_string_index": -1, "quest_string_value": 0, "meta": "", "color": "ffffff", "pid": 548}, {"id": "0196ba87-e698-771d-b7fd-1121c0b86765", "name": "<PERSON><PERSON><PERSON>", "description": "", "type": "Armor", "element": "None", "file": "Shazam3.swf", "link": "TRY", "icon": "i<PERSON>or", "equipment": "co", "level": 1, "dps": 25, "range": 50, "rarity": "0196ba87-e69e-75ac-9885-87eb9358ada7", "quantity": 1, "stack": 1, "cost": 2000, "coins": 1, "diamonds": 0, "crystal": 0, "sell": true, "market": true, "temporary": false, "upgrade": true, "staff": false, "enh_id": "0196ba87-e691-7757-a7dd-b3fcba14451c", "req_reputation": 0, "req_class_points": 0, "req_quests": "", "quest_string_index": -1, "quest_string_value": 0, "meta": "", "color": "ffffff", "pid": 549}, {"id": "0196ba87-e698-771d-b7fd-144714105b28", "name": "Frost Sword", "description": "test item", "type": "Sword", "element": "None", "file": "items/swords/AncientDragonB2.swf", "link": "AncientDragonB2", "icon": "iwsword", "equipment": "Weapon", "level": 1, "dps": 25, "range": 50, "rarity": "0196ba87-e69e-75ac-9885-5763facdf6ca", "quantity": 1, "stack": 1, "cost": 0, "coins": 0, "diamonds": 0, "crystal": 0, "sell": true, "market": true, "temporary": false, "upgrade": false, "staff": false, "enh_id": "0196ba87-e691-7757-a7dd-b3fcba14451c", "req_reputation": 0, "req_class_points": 0, "req_quests": "", "quest_string_index": -1, "quest_string_value": 0, "meta": "", "color": "ffffff", "pid": 550}]