use reforged_domain::models::hair_shop_item::entity::HairShopItem;
use reforged_domain::models::hair_shop_item::value_object::HairShopItemId;
use reforged_domain::models::hair_shop::value_object::HairShopId;
use reforged_domain::models::hair::value_object::HairId;

#[derive(Debug)]
pub struct HairShopItemDbModelMapper(super::super::models::hair_shop_items::Model);

impl HairShopItemDbModelMapper {
    pub fn new(model: super::super::models::hair_shop_items::Model) -> Self {
        Self(model)
    }
}

impl From<HairShopItemDbModelMapper> for HairShopItem {
    fn from(value: HairShopItemDbModelMapper) -> Self {
        let model = value.0;

        HairShopItem::builder()
            .id(HairShopItemId::new(model.id))
            .gender(model.gender.into())
            .hair_shop_id(HairShopId::new(model.shop_id))
            .hair_id(HairId::new(model.hair_id))
            .build()
    }
}
