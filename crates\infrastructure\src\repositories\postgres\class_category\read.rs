use async_trait::async_trait;
use num_traits::ToPrimitive;
use reforged_domain::models::class_category::value_object::ClassCategoryId;
use reforged_domain::{
    error::RepositoryError, models::class_category::entity::ClassCategory,
    repository::class_category_repository::ClassCategoryReadRepository,
};
use reforged_shared::IdTrait;
use sea_orm::ColumnTrait;
use sea_orm::DatabaseConnection;
use sea_orm::EntityTrait;
use sea_orm::PaginatorTrait;
use sea_orm::QueryFilter;
use sea_orm::QueryOrder;

use crate::SeaORMErr;
use crate::mappers::class_category_mapper::ClassCategoryDbModelMapper;

use crate::models::class_categories::Column as ClassCategoriesModelColumn;
use crate::models::class_categories::Entity as ClassCategories;

#[allow(dead_code)]
pub struct PostgresClassCategoryReadRepository {
    pool: DatabaseConnection,
}

impl PostgresClassCategoryReadRepository {
    pub fn new(pool: DatabaseConnection) -> Self {
        Self { pool }
    }
}

#[async_trait]
impl ClassCategoryReadRepository for PostgresClassCategoryReadRepository {
    async fn find_by_id(
        &self,
        id: &ClassCategoryId,
    ) -> Result<Option<ClassCategory>, RepositoryError> {
        let category = ClassCategories::find()
            .filter(ClassCategoriesModelColumn::Id.eq(id.get_id()))
            .one(&self.pool)
            .await
            .map_err(SeaORMErr::from)?;

        category
            .ok_or(RepositoryError::NotFound(format!("with id {}", id)))
            .and_then(|cat| {
                let mapped_category = ClassCategoryDbModelMapper::new(cat);
                let category = ClassCategory::from(mapped_category);

                Ok(Some(category))
            })
    }

    async fn find_by_name(&self, name: &str) -> Result<Option<ClassCategory>, RepositoryError> {
        let category = ClassCategories::find()
            .filter(ClassCategoriesModelColumn::Name.eq(name))
            .one(&self.pool)
            .await
            .map_err(SeaORMErr::from)?;

        category
            .ok_or(RepositoryError::NotFound(format!("with name {}", name)))
            .and_then(|cat| {
                let mapped_category = ClassCategoryDbModelMapper::new(cat);
                let category = ClassCategory::from(mapped_category);

                Ok(Some(category))
            })
    }

    async fn find_all(&self) -> Result<Vec<ClassCategory>, RepositoryError> {
        let categories = ClassCategories::find()
            .all(&self.pool)
            .await
            .map_err(SeaORMErr::from)?;

        let mapped_categories = categories
            .into_iter()
            .map(ClassCategoryDbModelMapper::new)
            .collect::<Vec<_>>();

        let categories = mapped_categories
            .into_iter()
            .map(ClassCategory::from)
            .collect::<Vec<_>>();

        Ok(categories)
    }

    async fn find_paginate(
        &self,
        page: u32,
        limit: u32,
    ) -> Result<Vec<ClassCategory>, RepositoryError> {
        let categories = ClassCategories::find()
            .order_by_asc(ClassCategoriesModelColumn::Id)
            .paginate(&self.pool, limit.to_u64().unwrap_or(1));

        let categories = categories
            .fetch_page(page.to_u64().unwrap_or(1) - 1)
            .await
            .map_err(SeaORMErr::from)?;

        let mapped_categories = categories
            .into_iter()
            .map(ClassCategoryDbModelMapper::new)
            .collect::<Vec<_>>();

        let categories = mapped_categories
            .into_iter()
            .map(ClassCategory::from)
            .collect::<Vec<_>>();

        Ok(categories)
    }
}
