use reforged_domain::models::user_report::entity::UserReport;
use reforged_domain::models::user_report::value_object::UserReportId;
use reforged_domain::models::user::value_object::UserId;

#[derive(Debug)]
pub struct UserReportDbModelMapper(super::super::models::user_reports::Model);

impl UserReportDbModelMapper {
    pub fn new(model: super::super::models::user_reports::Model) -> Self {
        Self(model)
    }
}

impl From<UserReportDbModelMapper> for UserReport {
    fn from(value: UserReportDbModelMapper) -> Self {
        let model = value.0;
        
        UserReport::builder()
            .id(UserReportId::new(model.id))
            .user_id(UserId::new(model.user_id))
            .target_name(model.target_name.into())
            .category(model.category.into())
            .description(model.description.into())
            .date_submitted(model.date_submitted.and_utc().into())
            .build()
    }
}
