use reforged_shared::{UuidId, Value};

use super::entity::GuildHallBuilding;

pub type GuildHallBuildingId = UuidId<GuildHallBuilding>;

#[derive(Debug, PartialEq, Eq, PartialOrd, Ord, Clone, Default)]

pub struct Slot(i16);

impl Slot {
    pub fn new(value: i16) -> Self {
        Self(value)
    }
}

impl Value<i16> for Slot {
    fn value(&self) -> i16 {
        self.0
    }
}

impl From<i16> for Slot {
    fn from(value: i16) -> Self {
        Self(value)
    }
}

#[derive(Debug, PartialEq, Eq, PartialOrd, Ord, <PERSON>lone, <PERSON><PERSON>ult)]

pub struct Size(i16);

impl Size {
    pub fn new(value: i16) -> Self {
        Self(value)
    }
}

impl Value<i16> for Size {
    fn value(&self) -> i16 {
        self.0
    }
}

impl From<i16> for Size {
    fn from(value: i16) -> Self {
        Self(value)
    }
}
