use chrono::{DateTime, Utc};
use reforged_shared::{UuidId, Value};

use super::entity::UserLivedrop;

pub type UserLivedropId = UuidId<UserLivedrop>;

#[derive(Debug, PartialEq, Eq, PartialOrd, Ord, Clone, Default)]
pub struct LivedropQuantity(Option<i32>);

impl LivedropQuantity {
    pub fn new(value: Option<i32>) -> Self {
        Self(value)
    }
}

impl Value<Option<i32>> for LivedropQuantity {
    fn value(&self) -> Option<i32> {
        self.0
    }
}

impl From<Option<i32>> for LivedropQuantity {
    fn from(value: Option<i32>) -> Self {
        Self(value)
    }
}

#[derive(Debug, PartialEq, Eq, PartialOrd, Ord, Clone, Default)]
pub struct Sent(i16);

impl Sent {
    pub fn new(value: i16) -> Self {
        Self(value)
    }
}

impl Value<i16> for Sent {
    fn value(&self) -> i16 {
        self.0
    }
}

impl From<i16> for Sent {
    fn from(value: i16) -> Self {
        Self(value)
    }
}

#[derive(Debug, PartialEq, Eq, PartialOrd, Ord, Clone, Default)]
pub struct LivedropDate(DateTime<Utc>);

impl LivedropDate {
    pub fn new(date: DateTime<Utc>) -> Self {
        Self(date)
    }
}

impl Value<DateTime<Utc>> for LivedropDate {
    fn value(&self) -> DateTime<Utc> {
        self.0
    }
}

impl From<DateTime<Utc>> for LivedropDate {
    fn from(date: DateTime<Utc>) -> Self {
        Self(date)
    }
}

#[derive(Debug, PartialEq, Eq, PartialOrd, Ord, Clone, Default)]
pub struct Message(Option<String>);

impl Message {
    pub fn new(message: Option<String>) -> Self {
        Self(message)
    }
}

impl Value<Option<String>> for Message {
    fn value(&self) -> Option<String> {
        self.0.clone()
    }
}

impl From<Option<String>> for Message {
    fn from(value: Option<String>) -> Self {
        Self(value)
    }
}

#[derive(Debug, PartialEq, Eq, PartialOrd, Ord, Clone, Default)]
pub struct LivedropExperience(i64);

impl LivedropExperience {
    pub fn new(value: i64) -> Self {
        Self(value)
    }
}

impl Value<i64> for LivedropExperience {
    fn value(&self) -> i64 {
        self.0
    }
}

impl From<i64> for LivedropExperience {
    fn from(value: i64) -> Self {
        Self(value)
    }
}

#[derive(Debug, PartialEq, Eq, PartialOrd, Ord, Clone, Default)]
pub struct LivedropGold(i64);

impl LivedropGold {
    pub fn new(value: i64) -> Self {
        Self(value)
    }
}

impl Value<i64> for LivedropGold {
    fn value(&self) -> i64 {
        self.0
    }
}

impl From<i64> for LivedropGold {
    fn from(value: i64) -> Self {
        Self(value)
    }
}

#[derive(Debug, PartialEq, Eq, PartialOrd, Ord, Clone, Default)]
pub struct LivedropCoins(i32);

impl LivedropCoins {
    pub fn new(value: i32) -> Self {
        Self(value)
    }
}

impl Value<i32> for LivedropCoins {
    fn value(&self) -> i32 {
        self.0
    }
}

impl From<i32> for LivedropCoins {
    fn from(value: i32) -> Self {
        Self(value)
    }
}

#[derive(Debug, PartialEq, Eq, PartialOrd, Ord, Clone, Default)]
pub struct LivedropCrystal(i64);

impl LivedropCrystal {
    pub fn new(value: i64) -> Self {
        Self(value)
    }
}

impl Value<i64> for LivedropCrystal {
    fn value(&self) -> i64 {
        self.0
    }
}

impl From<i64> for LivedropCrystal {
    fn from(value: i64) -> Self {
        Self(value)
    }
}

#[derive(Debug, PartialEq, Eq, PartialOrd, Ord, Clone, Default)]
pub struct UpgradeDays(i32);

impl UpgradeDays {
    pub fn new(value: i32) -> Self {
        Self(value)
    }
}

impl Value<i32> for UpgradeDays {
    fn value(&self) -> i32 {
        self.0
    }
}

impl From<i32> for UpgradeDays {
    fn from(value: i32) -> Self {
        Self(value)
    }
}

#[derive(Debug, PartialEq, Eq, PartialOrd, Ord, Clone, Default)]
pub struct LivedropBagSlots(i32);

impl LivedropBagSlots {
    pub fn new(value: i32) -> Self {
        Self(value)
    }
}

impl Value<i32> for LivedropBagSlots {
    fn value(&self) -> i32 {
        self.0
    }
}

impl From<i32> for LivedropBagSlots {
    fn from(value: i32) -> Self {
        Self(value)
    }
}

#[derive(Debug, PartialEq, Eq, PartialOrd, Ord, Clone, Default)]
pub struct LivedropBankSlots(i32);

impl LivedropBankSlots {
    pub fn new(value: i32) -> Self {
        Self(value)
    }
}

impl Value<i32> for LivedropBankSlots {
    fn value(&self) -> i32 {
        self.0
    }
}

impl From<i32> for LivedropBankSlots {
    fn from(value: i32) -> Self {
        Self(value)
    }
}

#[derive(Debug, PartialEq, Eq, PartialOrd, Ord, Clone, Default)]
pub struct LivedropHouseSlots(i32);

impl LivedropHouseSlots {
    pub fn new(value: i32) -> Self {
        Self(value)
    }
}

impl Value<i32> for LivedropHouseSlots {
    fn value(&self) -> i32 {
        self.0
    }
}

impl From<i32> for LivedropHouseSlots {
    fn from(value: i32) -> Self {
        Self(value)
    }
}