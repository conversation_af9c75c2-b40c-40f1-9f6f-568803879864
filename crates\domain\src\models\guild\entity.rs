use super::value_object::*;
use crate::models::user::value_object::UserId;
use getset::{<PERSON><PERSON>, <PERSON><PERSON>};

#[derive(Debug, <PERSON><PERSON>, Default, bon::<PERSON><PERSON><PERSON>, <PERSON><PERSON>, Setters)]
#[getset(get = "pub", set = "pub")]
pub struct Guild {
    id: GuildId,
    name: <PERSON><PERSON><PERSON>,
    message_of_the_day: GuildMessageOfTheDay,
    max_members: GuildMaxMembers,
    hall_size: GuildHallSize,
    last_updated: GuildLastUpdated,
    wins: GuildWins,
    loss: GuildLoss,
    total_kills: GuildTotalKills,
    level: GuildLevel,
    experience: GuildExperience,
    guild_color: GuildColor,
    staff_g: UserId,
    color: GuildColorId,
}

impl Guild {
    #[allow(clippy::too_many_arguments)]
    pub fn new(
        id: GuildId,
        name: GuildName,
        message_of_the_day: GuildMessageOfTheDay,
        max_members: GuildMaxMembers,
        hall_size: GuildHallSize,
        last_updated: GuildLastUpdated,
        wins: GuildWins,
        loss: <PERSON><PERSON><PERSON>,
        total_kills: GuildTotalKills,
        level: GuildLevel,
        experience: GuildExperience,
        guild_color: GuildColor,
        staff_g: UserId,
        color: GuildColorId,
    ) -> Self {
        Self {
            id,
            name,
            message_of_the_day,
            max_members,
            hall_size,
            last_updated,
            wins,
            loss,
            total_kills,
            level,
            experience,
            guild_color,
            staff_g,
            color,
        }
    }
}

#[cfg(test)]
mod tests {
    use chrono::Utc;
    use reforged_shared::{IdTrait, Value};

    use super::*;

    #[test]
    fn test_guild_new() {
        let id = GuildId::new(uuid::Uuid::now_v7());
        let name = GuildName::new("Test Guild");
        let message_of_the_day = GuildMessageOfTheDay::new("Welcome to our guild!");
        let max_members = GuildMaxMembers::new(50);
        let hall_size = GuildHallSize::new(3);
        let last_updated = GuildLastUpdated::new(Utc::now());
        let wins = GuildWins::new(10);
        let loss = GuildLoss::new(5);
        let total_kills = GuildTotalKills::new(100);
        let level = GuildLevel::new(5);
        let experience = GuildExperience::new(1500);
        let guild_color = GuildColor::new("#FF0000");
        let staff_g = UserId::new(uuid::Uuid::now_v7());
        let color = GuildColorId::new(Some(2));

        let guild = Guild::new(
            id.clone(),
            name.clone(),
            message_of_the_day.clone(),
            max_members.clone(),
            hall_size.clone(),
            last_updated.clone(),
            wins.clone(),
            loss.clone(),
            total_kills.clone(),
            level.clone(),
            experience.clone(),
            guild_color.clone(),
            staff_g.clone(),
            color.clone(),
        );

        assert_eq!(guild.id().get_id(), id.get_id());
        assert_eq!(guild.name().value(), name.value());
        assert_eq!(
            guild.message_of_the_day().value(),
            message_of_the_day.value()
        );
        assert_eq!(guild.max_members().value(), max_members.value());
        assert_eq!(guild.hall_size().value(), hall_size.value());
        assert_eq!(guild.last_updated().value(), last_updated.value());
        assert_eq!(guild.wins().value(), wins.value());
        assert_eq!(guild.loss().value(), loss.value());
        assert_eq!(guild.total_kills().value(), total_kills.value());
        assert_eq!(guild.level().value(), level.value());
        assert_eq!(guild.experience().value(), experience.value());
        assert_eq!(guild.guild_color().value(), guild_color.value());
        assert_eq!(guild.staff_g().get_id(), staff_g.get_id());
        assert_eq!(guild.color().value(), color.value());
    }

    #[test]
    fn test_guild_builder() {
        let id = GuildId::new(uuid::Uuid::now_v7());
        let name = GuildName::new("Test Guild");
        let message_of_the_day = GuildMessageOfTheDay::new("Welcome to our guild!");
        let max_members = GuildMaxMembers::new(50);
        let hall_size = GuildHallSize::new(3);
        let last_updated = GuildLastUpdated::new(Utc::now());
        let wins = GuildWins::new(10);
        let loss = GuildLoss::new(5);
        let total_kills = GuildTotalKills::new(100);
        let level = GuildLevel::new(5);
        let experience = GuildExperience::new(1500);
        let guild_color = GuildColor::new("#FF0000");
        let staff_g = UserId::new(uuid::Uuid::now_v7());
        let color = GuildColorId::new(Some(2));

        let guild = Guild::builder()
            .id(id.clone())
            .name(name.clone())
            .message_of_the_day(message_of_the_day.clone())
            .max_members(max_members.clone())
            .hall_size(hall_size.clone())
            .last_updated(last_updated.clone())
            .wins(wins.clone())
            .loss(loss.clone())
            .total_kills(total_kills.clone())
            .level(level.clone())
            .experience(experience.clone())
            .guild_color(guild_color.clone())
            .staff_g(staff_g.clone())
            .color(color.clone())
            .build();

        assert_eq!(guild.id().get_id(), id.get_id());
        assert_eq!(guild.name().value(), name.value());
        assert_eq!(
            guild.message_of_the_day().value(),
            message_of_the_day.value()
        );
        assert_eq!(guild.max_members().value(), max_members.value());
        assert_eq!(guild.hall_size().value(), hall_size.value());
        assert_eq!(guild.last_updated().value(), last_updated.value());
        assert_eq!(guild.wins().value(), wins.value());
        assert_eq!(guild.loss().value(), loss.value());
        assert_eq!(guild.total_kills().value(), total_kills.value());
        assert_eq!(guild.level().value(), level.value());
        assert_eq!(guild.experience().value(), experience.value());
        assert_eq!(guild.guild_color().value(), guild_color.value());
        assert_eq!(guild.staff_g().get_id(), staff_g.get_id());
        assert_eq!(guild.color().value(), color.value());
    }

    #[test]
    fn test_getters_and_setters() {
        let mut guild = Guild::default();

        let id = GuildId::new(uuid::Uuid::now_v7());
        let name = GuildName::new("Test Guild");
        let message_of_the_day = GuildMessageOfTheDay::new("Welcome to our guild!");
        let max_members = GuildMaxMembers::new(50);
        let hall_size = GuildHallSize::new(3);
        let last_updated = GuildLastUpdated::new(Utc::now());
        let wins = GuildWins::new(10);
        let loss = GuildLoss::new(5);
        let total_kills = GuildTotalKills::new(100);
        let level = GuildLevel::new(5);
        let experience = GuildExperience::new(1500);
        let guild_color = GuildColor::new("#FF0000");
        let staff_g = UserId::new(uuid::Uuid::now_v7());
        let color = GuildColorId::new(Some(2));

        guild.set_id(id.clone());
        guild.set_name(name.clone());
        guild.set_message_of_the_day(message_of_the_day.clone());
        guild.set_max_members(max_members.clone());
        guild.set_hall_size(hall_size.clone());
        guild.set_last_updated(last_updated.clone());
        guild.set_wins(wins.clone());
        guild.set_loss(loss.clone());
        guild.set_total_kills(total_kills.clone());
        guild.set_level(level.clone());
        guild.set_experience(experience.clone());
        guild.set_guild_color(guild_color.clone());
        guild.set_staff_g(staff_g.clone());
        guild.set_color(color.clone());

        assert_eq!(guild.id().get_id(), id.get_id());
        assert_eq!(guild.name().value(), name.value());
        assert_eq!(
            guild.message_of_the_day().value(),
            message_of_the_day.value()
        );
        assert_eq!(guild.max_members().value(), max_members.value());
        assert_eq!(guild.hall_size().value(), hall_size.value());
        assert_eq!(guild.last_updated().value(), last_updated.value());
        assert_eq!(guild.wins().value(), wins.value());
        assert_eq!(guild.loss().value(), loss.value());
        assert_eq!(guild.total_kills().value(), total_kills.value());
        assert_eq!(guild.level().value(), level.value());
        assert_eq!(guild.experience().value(), experience.value());
        assert_eq!(guild.guild_color().value(), guild_color.value());
        assert_eq!(guild.staff_g().get_id(), staff_g.get_id());
        assert_eq!(guild.color().value(), color.value());
    }
}
