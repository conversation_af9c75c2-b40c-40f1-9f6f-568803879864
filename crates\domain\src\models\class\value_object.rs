use super::entity::Class;
use reforged_shared::{UuidId, Value};

pub type ClassId = UuidId<Class>;

#[derive(Debug, PartialEq, Eq, PartialOrd, Ord, Clone, Default)]

pub struct ClassName(String);

impl ClassName {
    pub fn new(name: impl Into<String>) -> Self {
        Self(name.into())
    }
}

impl Value<String> for ClassName {
    fn value(&self) -> String {
        self.0.clone()
    }
}

impl From<String> for ClassName {
    fn from(value: String) -> Self {
        Self(value)
    }
}

impl From<&str> for ClassName {
    fn from(value: &str) -> Self {
        Self(value.to_string())
    }
}

#[derive(Debug, PartialEq, Eq, PartialOrd, Ord, Clone, Default)]

pub struct ClassDescription(String);

impl ClassDescription {
    pub fn new(description: impl Into<String>) -> Self {
        Self(description.into())
    }
}

impl Value<String> for ClassDescription {
    fn value(&self) -> String {
        self.0.clone()
    }
}

impl From<String> for ClassDescription {
    fn from(value: String) -> Self {
        Self(value)
    }
}

impl From<&str> for ClassDescription {
    fn from(value: &str) -> Self {
        Self(value.to_string())
    }
}

#[derive(Debug, PartialEq, Eq, PartialOrd, Ord, Clone, Default)]

pub struct ManaRegenerationMethods(String);

impl ManaRegenerationMethods {
    pub fn new(methods: impl Into<String>) -> Self {
        Self(methods.into())
    }
}

impl Value<String> for ManaRegenerationMethods {
    fn value(&self) -> String {
        self.0.clone()
    }
}

impl From<String> for ManaRegenerationMethods {
    fn from(value: String) -> Self {
        Self(value)
    }
}

impl From<&str> for ManaRegenerationMethods {
    fn from(value: &str) -> Self {
        Self(value.to_string())
    }
}

#[derive(Debug, PartialEq, Eq, PartialOrd, Ord, Clone, Default)]

pub struct StatsDescription(String);

impl StatsDescription {
    pub fn new(description: impl Into<String>) -> Self {
        Self(description.into())
    }
}

impl Value<String> for StatsDescription {
    fn value(&self) -> String {
        self.0.clone()
    }
}

impl From<String> for StatsDescription {
    fn from(value: String) -> Self {
        Self(value)
    }
}

impl From<&str> for StatsDescription {
    fn from(value: &str) -> Self {
        Self(value.to_string())
    }
}
