use super::value_object::{
    <PERSON>B<PERSON>, MonsterB<PERSON>MinR<PERSON>, MonsterCoins, MonsterDP<PERSON>, MonsterElement, MonsterExperience,
    MonsterFile, MonsterGold, MonsterHealth, MonsterHide, MonsterId, MonsterLevel, MonsterLinkage,
    MonsterMana, MonsterName, MonsterRace, MonsterReputation, MonsterRespawnTime, MonsterSpeed,
    MonsterTeamId, MonsterWorldBoss,
};
use getset::{<PERSON><PERSON>, <PERSON><PERSON>};

#[derive(<PERSON>bu<PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, bon::<PERSON><PERSON><PERSON>, <PERSON><PERSON>, Setters)]
#[getset(get = "pub", set = "pub")]
pub struct Monster {
    id: MonsterId,
    name: <PERSON>Name,
    race: MonsterRace,
    file: MonsterFile,
    health: MonsterHealth,
    mana: MonsterMana,
    level: MonsterLevel,
    gold: MonsterGold,
    coins: MonsterCoins,
    experience: MonsterExperience,
    reputation: MonsterReputation,
    dps: MonsterDPS,
    speed: MonsterSpeed,
    element: MonsterElement,
    linkage: MonsterLinkage,
    team_id: <PERSON>TeamId,
    boss: <PERSON><PERSON><PERSON>,
    boss_min_res: MonsterBossMinRes,
    respawn_time: MonsterR<PERSON>pawnTime,
    hide: <PERSON><PERSON><PERSON>,
    world_boss: <PERSON><PERSON>or<PERSON><PERSON><PERSON>,
}

impl Monster {
    #[allow(clippy::too_many_arguments)]
    pub fn new(
        id: MonsterId,
        name: MonsterName,
        race: MonsterRace,
        file: MonsterFile,
        health: MonsterHealth,
        mana: MonsterMana,
        level: MonsterLevel,
        gold: MonsterGold,
        coins: MonsterCoins,
        experience: MonsterExperience,
        reputation: MonsterReputation,
        dps: MonsterDPS,
        speed: MonsterSpeed,
        element: MonsterElement,
        linkage: MonsterLinkage,
        team_id: MonsterTeamId,
        boss: MonsterBoss,
        boss_min_res: MonsterBossMinRes,
        respawn_time: MonsterRespawnTime,
        hide: MonsterHide,
        world_boss: MonsterWorldBoss,
    ) -> Self {
        Self {
            id,
            name,
            race,
            file,
            health,
            mana,
            level,
            gold,
            coins,
            experience,
            reputation,
            dps,
            speed,
            element,
            linkage,
            team_id,
            boss,
            boss_min_res,
            respawn_time,
            hide,
            world_boss,
        }
    }
}

#[cfg(test)]
mod tests {
    use reforged_shared::{IdTrait, Value};

    use super::*;

    fn create_test_values() -> (
        MonsterId,
        MonsterName,
        MonsterRace,
        MonsterFile,
        MonsterHealth,
        MonsterMana,
        MonsterLevel,
        MonsterGold,
        MonsterCoins,
        MonsterExperience,
        MonsterReputation,
        MonsterDPS,
        MonsterSpeed,
        MonsterElement,
        MonsterLinkage,
        MonsterTeamId,
        MonsterBoss,
        MonsterBossMinRes,
        MonsterRespawnTime,
        MonsterHide,
        MonsterWorldBoss,
    ) {
        (
            MonsterId::new(uuid::Uuid::now_v7()),
            MonsterName::new("Test Monster".to_string()),
            MonsterRace::new("Undead".to_string()),
            MonsterFile::new("monsters/test_monster.png".to_string()),
            MonsterHealth::new(1000),
            MonsterMana::new(500),
            MonsterLevel::new(10),
            MonsterGold::new(50),
            MonsterCoins::new(25),
            MonsterExperience::new(200),
            MonsterReputation::new(15),
            MonsterDPS::new(75),
            MonsterSpeed::new(3),
            MonsterElement::new("Fire".to_string()),
            MonsterLinkage::new("None".to_string()),
            MonsterTeamId::new(5),
            MonsterBoss::new(false),
            MonsterBossMinRes::new(0),
            MonsterRespawnTime::new(30),
            MonsterHide::new(false),
            MonsterWorldBoss::new(false),
        )
    }

    #[test]
    fn test_monster_new() {
        let (
            id,
            name,
            race,
            file,
            health,
            mana,
            level,
            gold,
            coins,
            experience,
            reputation,
            dps,
            speed,
            element,
            linkage,
            team_id,
            boss,
            boss_min_res,
            respawn_time,
            hide,
            world_boss,
        ) = create_test_values();

        let monster = Monster::new(
            id.clone(),
            name.clone(),
            race.clone(),
            file.clone(),
            health.clone(),
            mana.clone(),
            level.clone(),
            gold.clone(),
            coins.clone(),
            experience.clone(),
            reputation.clone(),
            dps.clone(),
            speed.clone(),
            element.clone(),
            linkage.clone(),
            team_id.clone(),
            boss.clone(),
            boss_min_res.clone(),
            respawn_time.clone(),
            hide.clone(),
            world_boss.clone(),
        );

        assert_eq!(monster.id().get_id(), id.get_id());
        assert_eq!(monster.name().value(), name.value());
        assert_eq!(monster.race().value(), race.value());
        assert_eq!(monster.file().value(), file.value());
        assert_eq!(monster.health().value(), health.value());
        assert_eq!(monster.mana().value(), mana.value());
        assert_eq!(monster.level().value(), level.value());
        assert_eq!(monster.gold().value(), gold.value());
        assert_eq!(monster.coins().value(), coins.value());
        assert_eq!(monster.experience().value(), experience.value());
        assert_eq!(monster.reputation().value(), reputation.value());
        assert_eq!(monster.dps().value(), dps.value());
        assert_eq!(monster.speed().value(), speed.value());
        assert_eq!(monster.element().value(), element.value());
        assert_eq!(monster.linkage().value(), linkage.value());
        assert_eq!(monster.team_id().value(), team_id.value());
        assert_eq!(monster.boss().value(), boss.value());
        assert_eq!(monster.boss_min_res().value(), boss_min_res.value());
        assert_eq!(monster.respawn_time().value(), respawn_time.value());
        assert_eq!(monster.hide().value(), hide.value());
        assert_eq!(monster.world_boss().value(), world_boss.value());
    }

    #[test]
    fn test_monster_builder() {
        let (
            id,
            name,
            race,
            file,
            health,
            mana,
            level,
            gold,
            coins,
            experience,
            reputation,
            dps,
            speed,
            element,
            linkage,
            team_id,
            boss,
            boss_min_res,
            respawn_time,
            hide,
            world_boss,
        ) = create_test_values();

        let monster = Monster::builder()
            .id(id.clone())
            .name(name.clone())
            .race(race.clone())
            .file(file.clone())
            .health(health.clone())
            .mana(mana.clone())
            .level(level.clone())
            .gold(gold.clone())
            .coins(coins.clone())
            .experience(experience.clone())
            .reputation(reputation.clone())
            .dps(dps.clone())
            .speed(speed.clone())
            .element(element.clone())
            .linkage(linkage.clone())
            .team_id(team_id.clone())
            .boss(boss.clone())
            .boss_min_res(boss_min_res.clone())
            .respawn_time(respawn_time.clone())
            .hide(hide.clone())
            .world_boss(world_boss.clone())
            .build();

        assert_eq!(monster.id().get_id(), id.get_id());
        assert_eq!(monster.name().value(), name.value());
        assert_eq!(monster.race().value(), race.value());
        assert_eq!(monster.file().value(), file.value());
        assert_eq!(monster.health().value(), health.value());
        assert_eq!(monster.mana().value(), mana.value());
        assert_eq!(monster.level().value(), level.value());
        assert_eq!(monster.gold().value(), gold.value());
        assert_eq!(monster.coins().value(), coins.value());
        assert_eq!(monster.experience().value(), experience.value());
        assert_eq!(monster.reputation().value(), reputation.value());
        assert_eq!(monster.dps().value(), dps.value());
        assert_eq!(monster.speed().value(), speed.value());
        assert_eq!(monster.element().value(), element.value());
        assert_eq!(monster.linkage().value(), linkage.value());
        assert_eq!(monster.team_id().value(), team_id.value());
        assert_eq!(monster.boss().value(), boss.value());
        assert_eq!(monster.boss_min_res().value(), boss_min_res.value());
        assert_eq!(monster.respawn_time().value(), respawn_time.value());
        assert_eq!(monster.hide().value(), hide.value());
        assert_eq!(monster.world_boss().value(), world_boss.value());
    }

    #[test]
    fn test_getters_and_setters() {
        let mut monster = Monster::default();

        let (
            id,
            name,
            race,
            file,
            health,
            mana,
            level,
            gold,
            coins,
            experience,
            reputation,
            dps,
            speed,
            element,
            linkage,
            team_id,
            boss,
            boss_min_res,
            respawn_time,
            hide,
            world_boss,
        ) = create_test_values();

        monster.set_id(id.clone());
        monster.set_name(name.clone());
        monster.set_race(race.clone());
        monster.set_file(file.clone());
        monster.set_health(health.clone());
        monster.set_mana(mana.clone());
        monster.set_level(level.clone());
        monster.set_gold(gold.clone());
        monster.set_coins(coins.clone());
        monster.set_experience(experience.clone());
        monster.set_reputation(reputation.clone());
        monster.set_dps(dps.clone());
        monster.set_speed(speed.clone());
        monster.set_element(element.clone());
        monster.set_linkage(linkage.clone());
        monster.set_team_id(team_id.clone());
        monster.set_boss(boss.clone());
        monster.set_boss_min_res(boss_min_res.clone());
        monster.set_respawn_time(respawn_time.clone());
        monster.set_hide(hide.clone());
        monster.set_world_boss(world_boss.clone());

        assert_eq!(monster.id().get_id(), id.get_id());
        assert_eq!(monster.name().value(), name.value());
        assert_eq!(monster.race().value(), race.value());
        assert_eq!(monster.file().value(), file.value());
        assert_eq!(monster.health().value(), health.value());
        assert_eq!(monster.mana().value(), mana.value());
        assert_eq!(monster.level().value(), level.value());
        assert_eq!(monster.gold().value(), gold.value());
        assert_eq!(monster.coins().value(), coins.value());
        assert_eq!(monster.experience().value(), experience.value());
        assert_eq!(monster.reputation().value(), reputation.value());
        assert_eq!(monster.dps().value(), dps.value());
        assert_eq!(monster.speed().value(), speed.value());
        assert_eq!(monster.element().value(), element.value());
        assert_eq!(monster.linkage().value(), linkage.value());
        assert_eq!(monster.team_id().value(), team_id.value());
        assert_eq!(monster.boss().value(), boss.value());
        assert_eq!(monster.boss_min_res().value(), boss_min_res.value());
        assert_eq!(monster.respawn_time().value(), respawn_time.value());
        assert_eq!(monster.hide().value(), hide.value());
        assert_eq!(monster.world_boss().value(), world_boss.value());
    }
}
