use crate::models;
use sea_orm::{ActiveModelTrait, IntoActiveModel};
use sea_orm_migration::prelude::*;

#[derive(DeriveMigrationName)]
pub struct Migration;

#[async_trait::async_trait]
impl MigrationTrait for Migration {
    async fn up(&self, manager: &SchemaManager) -> Result<(), DbErr> {
        let db = manager.get_connection();

        let quests = include_str!("../json/quests.json");
        let quests: Vec<models::quests::Model> = serde_json::from_str(quests).unwrap();

        for model in quests {
            let quest = model.into_active_model();
            quest.insert(db).await?;
        }

        let quest_locations = include_str!("../json/quests_locations.json");
        let quest_locations: Vec<models::quest_locations::Model> =
            serde_json::from_str(quest_locations).unwrap();

        for model in quest_locations {
            let quest_location = model.into_active_model();
            quest_location.insert(db).await?;
        }

        let quest_requirements = include_str!("../json/quests_requirements.json");
        let quest_requirements: Vec<models::quest_requirements::Model> =
            serde_json::from_str(quest_requirements).unwrap();

        for model in quest_requirements {
            let quest_requirement = model.into_active_model();
            quest_requirement.insert(db).await?;
        }

        let quest_rewards = include_str!("../json/quests_rewards.json");
        let quest_rewards: Vec<models::quest_rewards::Model> =
            serde_json::from_str(quest_rewards).unwrap();

        for model in quest_rewards {
            let quest_reward = model.into_active_model();
            quest_reward.insert(db).await?;
        }

        Ok(())
    }

    async fn down(&self, _manager: &SchemaManager) -> Result<(), DbErr> {
        Ok(())
    }
}
