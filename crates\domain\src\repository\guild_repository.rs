use async_trait::async_trait;
use mockall::automock;

use crate::error::RepositoryError;
use crate::models::guild::entity::Guild;
use crate::models::guild::value_object::GuildId;

#[automock]
#[async_trait]
pub trait GuildRepository: Send + Sync {
    async fn save(&self, entity: &Guild) -> Result<(), RepositoryError>;
    async fn update(&self, entity: &Guild) -> Result<(), RepositoryError>;
    async fn delete(&self, id: &GuildId) -> Result<(), RepositoryError>;
}

#[automock]
#[async_trait]
pub trait GuildReadRepository: Send + Sync {
    async fn find_by_id(&self, id: &GuildId) -> Result<Option<Guild>, RepositoryError>;
    async fn find_all(&self) -> Result<Vec<Guild>, RepositoryError>;
}
