use reforged_shared::{UuidId, Value};

use super::entity::Store;

pub type StoreId = UuidId<Store>;

#[derive(Debug, PartialEq, Eq, PartialOrd, Ord, Clone, Default)]
pub struct Available(i16);

impl Available {
    pub fn new(value: i16) -> Self {
        Self(value)
    }
}

impl Value<i16> for Available {
    fn value(&self) -> i16 {
        self.0
    }
}

impl From<i16> for Available {
    fn from(value: i16) -> Self {
        Self(value)
    }
}

#[derive(Debug, PartialEq, Eq, PartialOrd, Ord, Clone, Default)]
pub struct StoreName(String);

impl StoreName {
    pub fn new(name: impl Into<String>) -> Self {
        Self(name.into())
    }
}

impl Value<String> for StoreName {
    fn value(&self) -> String {
        self.0.clone()
    }
}

impl From<String> for StoreName {
    fn from(value: String) -> Self {
        Self(value)
    }
}

impl From<&str> for StoreName {
    fn from(value: &str) -> Self {
        Self(value.to_string())
    }
}

#[derive(Debug, PartialEq, PartialOrd, <PERSON><PERSON>, <PERSON><PERSON><PERSON>)]
pub struct Price(f64);

impl Price {
    pub fn new(value: f64) -> Self {
        Self(value)
    }
}

impl Value<f64> for Price {
    fn value(&self) -> f64 {
        self.0
    }
}

impl From<f64> for Price {
    fn from(value: f64) -> Self {
        Self(value)
    }
}

#[derive(Debug, PartialEq, Eq, PartialOrd, Ord, Clone, Default)]
pub struct Item(i32);

impl Item {
    pub fn new(value: i32) -> Self {
        Self(value)
    }
}

impl Value<i32> for Item {
    fn value(&self) -> i32 {
        self.0
    }
}

impl From<i32> for Item {
    fn from(value: i32) -> Self {
        Self(value)
    }
}

#[derive(Debug, PartialEq, Eq, PartialOrd, Ord, Clone, Default)]
pub struct Achievement(Option<i32>);

impl Achievement {
    pub fn new(value: Option<i32>) -> Self {
        Self(value)
    }
}

impl Value<Option<i32>> for Achievement {
    fn value(&self) -> Option<i32> {
        self.0
    }
}

impl From<Option<i32>> for Achievement {
    fn from(value: Option<i32>) -> Self {
        Self(value)
    }
}

#[derive(Debug, PartialEq, Eq, PartialOrd, Ord, Clone, Default)]
pub struct StoreGold(i64);

impl StoreGold {
    pub fn new(value: i64) -> Self {
        Self(value)
    }
}

impl Value<i64> for StoreGold {
    fn value(&self) -> i64 {
        self.0
    }
}

impl From<i64> for StoreGold {
    fn from(value: i64) -> Self {
        Self(value)
    }
}

#[derive(Debug, PartialEq, Eq, PartialOrd, Ord, Clone, Default)]
pub struct StoreCoins(i32);

impl StoreCoins {
    pub fn new(value: i32) -> Self {
        Self(value)
    }
}

impl Value<i32> for StoreCoins {
    fn value(&self) -> i32 {
        self.0
    }
}

impl From<i32> for StoreCoins {
    fn from(value: i32) -> Self {
        Self(value)
    }
}

#[derive(Debug, PartialEq, Eq, PartialOrd, Ord, Clone, Default)]
pub struct StoreCrystal(i32);

impl StoreCrystal {
    pub fn new(value: i32) -> Self {
        Self(value)
    }
}

impl Value<i32> for StoreCrystal {
    fn value(&self) -> i32 {
        self.0
    }
}

impl From<i32> for StoreCrystal {
    fn from(value: i32) -> Self {
        Self(value)
    }
}

#[derive(Debug, PartialEq, Eq, PartialOrd, Ord, Clone, Default)]
pub struct StoreDiamonds(i32);

impl StoreDiamonds {
    pub fn new(value: i32) -> Self {
        Self(value)
    }
}

impl Value<i32> for StoreDiamonds {
    fn value(&self) -> i32 {
        self.0
    }
}

impl From<i32> for StoreDiamonds {
    fn from(value: i32) -> Self {
        Self(value)
    }
}

#[derive(Debug, PartialEq, Eq, PartialOrd, Ord, Clone, Default)]
pub struct Upgrade(i32);

impl Upgrade {
    pub fn new(value: i32) -> Self {
        Self(value)
    }
}

impl Value<i32> for Upgrade {
    fn value(&self) -> i32 {
        self.0
    }
}

impl From<i32> for Upgrade {
    fn from(value: i32) -> Self {
        Self(value)
    }
}

#[derive(Debug, PartialEq, Eq, PartialOrd, Ord, Clone, Default)]
pub struct BagSlots(i32);

impl BagSlots {
    pub fn new(value: i32) -> Self {
        Self(value)
    }
}

impl Value<i32> for BagSlots {
    fn value(&self) -> i32 {
        self.0
    }
}

impl From<i32> for BagSlots {
    fn from(value: i32) -> Self {
        Self(value)
    }
}

#[derive(Debug, PartialEq, Eq, PartialOrd, Ord, Clone, Default)]
pub struct BankSlots(i32);

impl BankSlots {
    pub fn new(value: i32) -> Self {
        Self(value)
    }
}

impl Value<i32> for BankSlots {
    fn value(&self) -> i32 {
        self.0
    }
}

impl From<i32> for BankSlots {
    fn from(value: i32) -> Self {
        Self(value)
    }
}

#[derive(Debug, PartialEq, Eq, PartialOrd, Ord, Clone, Default)]
pub struct HouseSlots(i32);

impl HouseSlots {
    pub fn new(value: i32) -> Self {
        Self(value)
    }
}

impl Value<i32> for HouseSlots {
    fn value(&self) -> i32 {
        self.0
    }
}

impl From<i32> for HouseSlots {
    fn from(value: i32) -> Self {
        Self(value)
    }
}

#[derive(Debug, PartialEq, Eq, PartialOrd, Ord, Clone, Default)]
pub struct Quantity(i32);

impl Quantity {
    pub fn new(value: i32) -> Self {
        Self(value)
    }
}

impl Value<i32> for Quantity {
    fn value(&self) -> i32 {
        self.0
    }
}

impl From<i32> for Quantity {
    fn from(value: i32) -> Self {
        Self(value)
    }
}

#[derive(Debug, PartialEq, Eq, PartialOrd, Ord, Clone, Default)]
pub struct Img(Option<String>);

impl Img {
    pub fn new(value: Option<String>) -> Self {
        Self(value)
    }
}

impl Value<Option<String>> for Img {
    fn value(&self) -> Option<String> {
        self.0.clone()
    }
}

impl From<Option<String>> for Img {
    fn from(value: Option<String>) -> Self {
        Self(value)
    }
}

#[derive(Debug, PartialEq, Eq, PartialOrd, Ord, Clone, Default)]
pub enum StoreType {
    Package,
    VIP,
    FOUNDER,
    #[default]
    Coin,
}

impl StoreType {
    pub fn as_str(&self) -> &'static str {
        match self {
            StoreType::Package => "Package",
            StoreType::VIP => "VIP",
            StoreType::FOUNDER => "FOUNDER",
            StoreType::Coin => "Coin",
        }
    }
}