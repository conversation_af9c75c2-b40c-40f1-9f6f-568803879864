use super::value_object::*;
use crate::models::title::value_object::TitleId;
use crate::models::user::value_object::UserId;
use getset::{<PERSON><PERSON>, Set<PERSON>};

#[derive(<PERSON>bu<PERSON>, <PERSON><PERSON>, <PERSON><PERSON>ult, bon::<PERSON><PERSON><PERSON>, <PERSON><PERSON>, Set<PERSON>)]
#[getset(get = "pub", set = "pub")]
pub struct UserTitle {
    id: UserTitleId,
    user_id: UserId,
    title_id: TitleId,
    date: UserTitleDate,
}

impl UserTitle {
    pub fn new(id: UserTitleId, user_id: UserId, title_id: TitleId, date: UserTitleDate) -> Self {
        Self {
            id,
            user_id,
            title_id,
            date,
        }
    }
}

#[cfg(test)]
mod tests {
    use chrono::Utc;
    use reforged_shared::{IdTrait, Value};

    use super::*;

    #[test]
    fn test_user_title_new() {
        let id = UserTitleId::new(uuid::Uuid::now_v7());
        let user_id = UserId::new(uuid::Uuid::now_v7());
        let title_id = TitleId::new(uuid::Uuid::now_v7());
        let date = UserTitleDate::new(Utc::now());

        let user_title =
            UserTitle::new(id.clone(), user_id.clone(), title_id.clone(), date.clone());

        assert_eq!(user_title.id.get_id(), id.get_id());
        assert_eq!(user_title.user_id.get_id(), user_id.get_id());
        assert_eq!(user_title.title_id.get_id(), title_id.get_id());
        assert_eq!(user_title.date.value(), date.value());
    }

    #[test]
    fn test_user_title_builder() {
        let id = UserTitleId::new(uuid::Uuid::now_v7());
        let user_id = UserId::new(uuid::Uuid::now_v7());
        let title_id = TitleId::new(uuid::Uuid::now_v7());
        let date = UserTitleDate::new(Utc::now());

        let user_title = UserTitle::builder()
            .id(id.clone())
            .user_id(user_id.clone())
            .title_id(title_id.clone())
            .date(date.clone())
            .build();

        assert_eq!(user_title.id().get_id(), id.get_id());
        assert_eq!(user_title.user_id.get_id(), user_id.get_id());
        assert_eq!(user_title.title_id.get_id(), title_id.get_id());
        assert_eq!(user_title.date.value(), date.value());
    }

    #[test]
    fn test_getters_and_setters() {
        let mut user_title = UserTitle::default();

        let id = UserTitleId::new(uuid::Uuid::now_v7());
        let user_id = UserId::new(uuid::Uuid::now_v7());
        let title_id = TitleId::new(uuid::Uuid::now_v7());
        let date = UserTitleDate::new(Utc::now());

        user_title.set_id(id.clone());
        user_title.set_user_id(user_id.clone());
        user_title.set_title_id(title_id.clone());
        user_title.set_date(date.clone());

        assert_eq!(user_title.id().get_id(), id.get_id());
        assert_eq!(user_title.user_id().get_id(), user_id.get_id());
        assert_eq!(user_title.title_id().get_id(), title_id.get_id());
        assert_eq!(user_title.date().value(), date.value());
    }
}
