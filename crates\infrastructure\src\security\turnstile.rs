use std::env;

use async_trait::async_trait;
use cf_turnstile::{SiteVerifyRequest, TurnstileClient, error::TurnstileError};
use reforged_application::services::captcha_service::{Captcha, CaptchaError};

pub struct TurnstileCaptcha {
    client: TurnstileClient,
}

impl TurnstileCaptcha {
    pub fn new(secret_key: String) -> Self {
        let client = TurnstileClient::new(secret_key.into());
        Self { client }
    }
}

#[async_trait]
impl Captcha for TurnstileCaptcha {
    async fn validate(&self, captcha_response: String) -> Result<(), CaptchaError> {
        // TODO: move this check into top-level handler
        if let Ok(mode) = env::var("MODE") {
            if mode == "dev" {
                return Ok(());
            }
        }

        let client = &self.client;

        let validate_response = client
            .siteverify(SiteVerifyRequest {
                response: captcha_response,
                ..Default::default()
            })
            .await
            .map_err(|err| match err {
                TurnstileError::SiteVerifyError(_) => CaptchaError::InvalidCaptcha,
                _ => CaptchaError::InternalError,
            })?;

        match validate_response.success {
            true => Ok(()),
            false => Err(CaptchaError::InvalidCaptcha),
        }
    }
}
