//! `SeaORM` Entity, @generated by sea-orm-codegen 1.1.0

use sea_orm::entity::prelude::*;
use serde::Deserialize;

#[derive(Clone, Debug, PartialEq, DeriveEntityModel, Eq, Deserialize)]
#[sea_orm(table_name = "guilds")]
pub struct Model {
    #[sea_orm(primary_key, auto_increment = false)]
    pub id: Uuid,
    pub name: String,
    pub message_of_the_day: String,
    pub max_members: i16,
    pub hall_size: i16,
    pub last_updated: DateTime,
    pub wins: i32,
    pub loss: i32,
    pub total_kills: i32,
    pub level: i32,
    pub experience: i64,
    pub guild_color: String,
    pub staff_g: Uuid,
    pub color: Option<i32>,
}

#[derive(<PERSON><PERSON>, <PERSON><PERSON>, Debug, EnumIter, DeriveRelation)]
pub enum Relation {
    #[sea_orm(has_many = "super::guild_halls::Entity")]
    GuildHalls,
    #[sea_orm(has_many = "super::guild_items::Entity")]
    GuildItems,
    #[sea_orm(has_many = "super::guild_levels::Entity")]
    GuildLevels,
    #[sea_orm(has_many = "super::user_guilds::Entity")]
    UserGuilds,
    #[sea_orm(
        belongs_to = "super::users::Entity",
        from = "Column::StaffG",
        to = "super::users::Column::Id",
        on_update = "Cascade",
        on_delete = "Cascade"
    )]
    Users,
}

impl Related<super::guild_halls::Entity> for Entity {
    fn to() -> RelationDef {
        Relation::GuildHalls.def()
    }
}

impl Related<super::guild_items::Entity> for Entity {
    fn to() -> RelationDef {
        Relation::GuildItems.def()
    }
}

impl Related<super::guild_levels::Entity> for Entity {
    fn to() -> RelationDef {
        Relation::GuildLevels.def()
    }
}

impl Related<super::user_guilds::Entity> for Entity {
    fn to() -> RelationDef {
        Relation::UserGuilds.def()
    }
}

impl Related<super::users::Entity> for Entity {
    fn to() -> RelationDef {
        Relation::Users.def()
    }
}

impl ActiveModelBehavior for ActiveModel {}
