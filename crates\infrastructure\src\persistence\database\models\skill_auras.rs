//! `SeaORM` Entity, @generated by sea-orm-codegen 1.1.0

use sea_orm::entity::prelude::*;
use serde::Deserialize;

#[derive(<PERSON><PERSON>, Debug, PartialEq, DeriveEntityModel, Eq, Deserialize)]
#[sea_orm(table_name = "skill_auras")]
pub struct Model {
    #[sea_orm(primary_key, auto_increment = false)]
    pub id: Uuid,
    pub skill_id: Uuid,
    pub aura_id: Uuid,
}

#[derive(<PERSON><PERSON>, <PERSON><PERSON>, Debug, EnumIter, DeriveRelation)]
pub enum Relation {
    #[sea_orm(
        belongs_to = "super::auras::Entity",
        from = "Column::AuraId",
        to = "super::auras::Column::Id",
        on_update = "Cascade",
        on_delete = "Cascade"
    )]
    Auras,
    #[sea_orm(
        belongs_to = "super::skills::Entity",
        from = "Column::SkillId",
        to = "super::skills::Column::Id",
        on_update = "Cascade",
        on_delete = "Cascade"
    )]
    Skills,
}

impl Related<super::auras::Entity> for Entity {
    fn to() -> RelationDef {
        Relation::Auras.def()
    }
}

impl Related<super::skills::Entity> for Entity {
    fn to() -> RelationDef {
        Relation::Skills.def()
    }
}

impl ActiveModelBehavior for ActiveModel {}
