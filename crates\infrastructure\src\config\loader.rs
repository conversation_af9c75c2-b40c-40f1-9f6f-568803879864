use dotenvy::dotenv;

use super::{
    Config, ConfigError, captcha::CaptchaConfig, database::DatabaseConfig,
    eventstore::EventStoreConfig, http::HttpConfig,
};

pub struct ApiConfig {
    http: HttpConfig,
    database_config: DatabaseConfig,
    event_store_config: EventStoreConfig,
    captcha_config: CaptchaConfig,
}

impl ApiConfig {
    pub fn http(&self) -> &HttpConfig {
        &self.http
    }

    pub fn database(&self) -> &DatabaseConfig {
        &self.database_config
    }

    pub fn event_store(&self) -> &EventStoreConfig {
        &self.event_store_config
    }

    pub fn captcha(&self) -> &CaptchaConfig {
        &self.captcha_config
    }
}

pub fn load_api_config() -> Result<ApiConfig, ConfigError> {
    dotenv().ok();

    Ok(ApiConfig {
        http: HttpConfig::from_env()?,
        database_config: DatabaseConfig::from_env()?,
        event_store_config: EventStoreConfig::from_env()?,
        captcha_config: CaptchaConfig::from_env()?,
    })
}
