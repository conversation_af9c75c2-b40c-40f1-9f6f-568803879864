use super::value_object::ClassSkillId;
use crate::models::class::value_object::ClassId;
use crate::models::skill::value_object::SkillId;
use getset::{Get<PERSON>, Set<PERSON>};

#[derive(<PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, bon::<PERSON><PERSON><PERSON>, <PERSON><PERSON>, Set<PERSON>)]
#[getset(get = "pub", set = "pub")]
pub struct ClassSkill {
    id: ClassSkillId,
    class_id: ClassId,
    skill_id: SkillId,
}

impl ClassSkill {
    pub fn new(id: ClassSkillId, class_id: ClassId, skill_id: SkillId) -> Self {
        Self {
            id,
            class_id,
            skill_id,
        }
    }
}

#[cfg(test)]
mod tests {
    use reforged_shared::IdTrait;

    use super::*;

    #[test]
    fn test_class_skill_new() {
        let id = ClassSkillId::new(uuid::Uuid::now_v7());
        let class_id = ClassId::new(uuid::Uuid::now_v7());
        let skill_id = SkillId::new(uuid::Uuid::now_v7());

        let class_skill = ClassSkill::new(id.clone(), class_id.clone(), skill_id.clone());

        assert_eq!(class_skill.id().get_id(), id.get_id());
        assert_eq!(class_skill.class_id().get_id(), class_id.get_id());
        assert_eq!(class_skill.skill_id().get_id(), skill_id.get_id());
    }

    #[test]
    fn test_class_skill_builder() {
        let id = ClassSkillId::new(uuid::Uuid::now_v7());
        let class_id = ClassId::new(uuid::Uuid::now_v7());
        let skill_id = SkillId::new(uuid::Uuid::now_v7());

        let class_skill = ClassSkill::builder()
            .id(id.clone())
            .class_id(class_id.clone())
            .skill_id(skill_id.clone())
            .build();

        assert_eq!(class_skill.id().get_id(), id.get_id());
        assert_eq!(class_skill.class_id().get_id(), class_id.get_id());
        assert_eq!(class_skill.skill_id().get_id(), skill_id.get_id());
    }

    #[test]
    fn test_getters_and_setters() {
        let mut class_skill = ClassSkill::default();

        let id = ClassSkillId::new(uuid::Uuid::now_v7());
        let class_id = ClassId::new(uuid::Uuid::now_v7());
        let skill_id = SkillId::new(uuid::Uuid::now_v7());

        class_skill.set_id(id.clone());
        class_skill.set_class_id(class_id.clone());
        class_skill.set_skill_id(skill_id.clone());

        assert_eq!(class_skill.id().get_id(), id.get_id());
        assert_eq!(class_skill.class_id().get_id(), class_id.get_id());
        assert_eq!(class_skill.skill_id().get_id(), skill_id.get_id());
    }
}
