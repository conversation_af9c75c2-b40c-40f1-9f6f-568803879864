use getset::{<PERSON><PERSON>, <PERSON><PERSON>};

use super::value_object::*;
#[derive(<PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, bon::<PERSON><PERSON><PERSON>, <PERSON><PERSON>, Set<PERSON>)]
#[getset(get = "pub", set = "pub")]
pub struct ProfanityFilterSettings {
    id: ProfanityFilterSettingsId,
    swear: SwearWord,
    time_to_mute: TimeToMute,
}

impl ProfanityFilterSettings {
    pub fn new(id: ProfanityFilterSettingsId, swear: SwearWord, time_to_mute: TimeToMute) -> Self {
        Self {
            id,
            swear,
            time_to_mute,
        }
    }
}

#[cfg(test)]
mod tests {
    use reforged_shared::{IdTrait, Value};

    use super::*;

    #[test]
    fn test_profanity_filter_settings_new() {
        let id = ProfanityFilterSettingsId::new(uuid::Uuid::now_v7());
        let swear = SwearWord::new("badword".to_string());
        let time_to_mute = TimeToMute::new(300);

        let settings =
            ProfanityFilterSettings::new(id.clone(), swear.clone(), time_to_mute.clone());

        assert_eq!(settings.id().get_id(), id.get_id());
        assert_eq!(settings.swear().value(), swear.value());
        assert_eq!(settings.time_to_mute().value(), time_to_mute.value());
    }

    #[test]
    fn test_profanity_filter_settings_builder() {
        let id = ProfanityFilterSettingsId::new(uuid::Uuid::now_v7());
        let swear = SwearWord::new("badword".to_string());
        let time_to_mute = TimeToMute::new(300);

        let settings = ProfanityFilterSettings::builder()
            .id(id.clone())
            .swear(swear.clone())
            .time_to_mute(time_to_mute.clone())
            .build();

        assert_eq!(settings.id().get_id(), id.get_id());
        assert_eq!(settings.swear().value(), swear.value());
        assert_eq!(settings.time_to_mute().value(), time_to_mute.value());
    }

    #[test]
    fn test_getters_and_setters() {
        let mut settings = ProfanityFilterSettings::default();

        let id = ProfanityFilterSettingsId::new(uuid::Uuid::now_v7());
        let swear = SwearWord::new("badword".to_string());
        let time_to_mute = TimeToMute::new(300);

        settings.set_id(id.clone());
        settings.set_swear(swear.clone());
        settings.set_time_to_mute(time_to_mute.clone());

        assert_eq!(settings.id().get_id(), id.get_id());
        assert_eq!(settings.swear().value(), swear.value());
        assert_eq!(settings.time_to_mute().value(), time_to_mute.value());
    }
}
