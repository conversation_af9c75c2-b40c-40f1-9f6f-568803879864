use super::value_object::*;
use crate::models::user::value_object::UserId;
use getset::{<PERSON><PERSON>, Set<PERSON>};

#[derive(Debug, <PERSON><PERSON>, Default, bon::<PERSON><PERSON><PERSON>, <PERSON><PERSON>, Setters)]
#[getset(get = "pub", set = "pub")]
pub struct UserColor {
    id: UserColorId,
    user_id: UserId,
    color_chat: ColorChat,
    color_name: ColorName,
    color_hair: ColorHair,
    color_skin: ColorSkin,
    color_eye: ColorEye,
    color_base: ColorBase,
    color_trim: ColorTrim,
    color_accessory: ColorAccessory,
}

impl UserColor {
    pub fn new(
        id: UserColorId,
        user_id: UserId,
        color_chat: ColorChat,
        color_name: ColorName,
        color_hair: ColorHair,
        color_skin: ColorSkin,
        color_eye: ColorEye,
        color_base: ColorBase,
        color_trim: ColorTrim,
        color_accessory: ColorAccessory,
    ) -> Self {
        Self {
            id,
            user_id,
            color_chat,
            color_name,
            color_hair,
            color_skin,
            color_eye,
            color_base,
            color_trim,
            color_accessory,
        }
    }
}

#[cfg(test)]
mod tests {
    use reforged_shared::{IdTrait, Value};

    use super::*;

    #[test]
    fn test_user_color_new() {
        let id = UserColorId::new(uuid::Uuid::now_v7());
        let user_id = UserId::new(uuid::Uuid::now_v7());
        let color_chat = ColorChat::new("#FF0000");
        let color_name = ColorName::new("#00FF00");
        let color_hair = ColorHair::new("#0000FF");
        let color_skin = ColorSkin::new("#FFFF00");
        let color_eye = ColorEye::new("#00FFFF");
        let color_base = ColorBase::new("#FF00FF");
        let color_trim = ColorTrim::new("#FFFFFF");
        let color_accessory = ColorAccessory::new("#000000");

        let user_color = UserColor::new(
            id.clone(),
            user_id.clone(),
            color_chat.clone(),
            color_name.clone(),
            color_hair.clone(),
            color_skin.clone(),
            color_eye.clone(),
            color_base.clone(),
            color_trim.clone(),
            color_accessory.clone(),
        );

        assert_eq!(user_color.user_id.get_id(), user_id.get_id());
        assert_eq!(user_color.color_chat.value(), color_chat.value());
        assert_eq!(user_color.color_name.value(), color_name.value());
        assert_eq!(user_color.color_hair.value(), color_hair.value());
        assert_eq!(user_color.color_skin.value(), color_skin.value());
        assert_eq!(user_color.color_eye.value(), color_eye.value());
        assert_eq!(user_color.color_base.value(), color_base.value());
        assert_eq!(user_color.color_trim.value(), color_trim.value());
        assert_eq!(user_color.color_accessory.value(), color_accessory.value());
    }

    #[test]
    fn test_user_color_builder() {
        let id = UserColorId::new(uuid::Uuid::now_v7());
        let user_id = UserId::new(uuid::Uuid::now_v7());
        let color_chat = ColorChat::new("#FF0000");
        let color_name = ColorName::new("#00FF00");
        let color_hair = ColorHair::new("#0000FF");
        let color_skin = ColorSkin::new("#FFFF00");
        let color_eye = ColorEye::new("#00FFFF");
        let color_base = ColorBase::new("#FF00FF");
        let color_trim = ColorTrim::new("#FFFFFF");
        let color_accessory = ColorAccessory::new("#000000");

        let user_color = UserColor::builder()
            .id(id.clone())
            .user_id(user_id.clone())
            .color_chat(color_chat.clone())
            .color_name(color_name.clone())
            .color_hair(color_hair.clone())
            .color_skin(color_skin.clone())
            .color_eye(color_eye.clone())
            .color_base(color_base.clone())
            .color_trim(color_trim.clone())
            .color_accessory(color_accessory.clone())
            .build();

        assert_eq!(user_color.user_id.get_id(), user_id.get_id());
        assert_eq!(user_color.color_chat.value(), color_chat.value());
        assert_eq!(user_color.color_name.value(), color_name.value());
        assert_eq!(user_color.color_hair.value(), color_hair.value());
        assert_eq!(user_color.color_skin.value(), color_skin.value());
        assert_eq!(user_color.color_eye.value(), color_eye.value());
        assert_eq!(user_color.color_base.value(), color_base.value());
        assert_eq!(user_color.color_trim.value(), color_trim.value());
        assert_eq!(user_color.color_accessory.value(), color_accessory.value());
    }

    #[test]
    fn test_getters_and_setters() {
        let mut user_color = UserColor::default();

        let id = UserColorId::new(uuid::Uuid::now_v7());
        let user_id = UserId::new(uuid::Uuid::now_v7());
        let color_chat = ColorChat::new("#FF0000");
        let color_name = ColorName::new("#00FF00");
        let color_hair = ColorHair::new("#0000FF");
        let color_skin = ColorSkin::new("#FFFF00");
        let color_eye = ColorEye::new("#00FFFF");
        let color_base = ColorBase::new("#FF00FF");
        let color_trim = ColorTrim::new("#FFFFFF");
        let color_accessory = ColorAccessory::new("#000000");

        user_color.set_id(id.clone());
        user_color.set_user_id(user_id.clone());
        user_color.set_color_chat(color_chat.clone());
        user_color.set_color_name(color_name.clone());
        user_color.set_color_hair(color_hair.clone());
        user_color.set_color_skin(color_skin.clone());
        user_color.set_color_eye(color_eye.clone());
        user_color.set_color_base(color_base.clone());
        user_color.set_color_trim(color_trim.clone());
        user_color.set_color_accessory(color_accessory.clone());

        assert_eq!(user_color.user_id().get_id(), user_id.get_id());
        assert_eq!(user_color.color_chat().value(), color_chat.value());
        assert_eq!(user_color.color_name().value(), color_name.value());
        assert_eq!(user_color.color_hair().value(), color_hair.value());
        assert_eq!(user_color.color_skin().value(), color_skin.value());
        assert_eq!(user_color.color_eye().value(), color_eye.value());
        assert_eq!(user_color.color_base().value(), color_base.value());
        assert_eq!(user_color.color_trim().value(), color_trim.value());
        assert_eq!(
            user_color.color_accessory().value(),
            color_accessory.value()
        );
    }
}
