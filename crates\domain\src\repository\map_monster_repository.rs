use async_trait::async_trait;
use mockall::automock;

use crate::error::RepositoryError;
use crate::models::map_monster::entity::MapMonster;
use crate::models::map_monster::value_object::MapMonsterId;

#[automock]
#[async_trait]
pub trait MapMonsterRepository: Send + Sync {
    async fn save(&self, entity: &MapMonster) -> Result<(), RepositoryError>;
    async fn update(&self, entity: &MapMonster) -> Result<(), RepositoryError>;
    async fn delete(&self, id: &MapMonsterId) -> Result<(), RepositoryError>;
}

#[automock]
#[async_trait]
pub trait MapMonsterReadRepository: Send + Sync {
    async fn find_by_id(&self, id: &MapMonsterId) -> Result<Option<MapMonster>, RepositoryError>;
    async fn find_all(&self) -> Result<Vec<MapMonster>, RepositoryError>;
}
