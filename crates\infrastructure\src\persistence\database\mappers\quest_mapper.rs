use reforged_domain::models::achievement::value_object::AchievementId;
use reforged_domain::models::class::value_object::ClassId;
use reforged_domain::models::faction::value_object::FactionId;
use reforged_domain::models::quest::{entity::Quest, value_object::QuestId};
use reforged_domain::models::war::value_object::WarId;

#[derive(Debug)]
pub struct QuestDbModelMapper(super::super::models::quests::Model);

impl QuestDbModelMapper {
    pub fn new(quest: super::super::models::quests::Model) -> Self {
        Self(quest)
    }
}

impl From<QuestDbModelMapper> for Quest {
    fn from(value: QuestDbModelMapper) -> Self {
        let quest_model = value.0;

        let id = QuestId::new(quest_model.id);

        Quest::builder()
            .id(id)
            .faction_id(FactionId::new(quest_model.faction_id))
            .req_achievement(
                quest_model
                    .req_achievement
                    .map(AchievementId::new)
                    .unwrap_or_default(),
            )
            .req_reputation(quest_model.req_reputation.into())
            .req_class_id(
                quest_model
                    .req_class_id
                    .map(ClassId::new)
                    .unwrap_or_default(),
            )
            .req_class_points(quest_model.req_class_points.into())
            .name(quest_model.name.into())
            .description(quest_model.description.into())
            .end_text(
                quest_model
                    .end_text
                    .map(|text| text.into())
                    .unwrap_or_default(),
            )
            .experience(quest_model.experience.into())
            .g_experience(quest_model.g_experience.into())
            .gold(quest_model.gold.into())
            .coins(quest_model.coins.into())
            .reputation(quest_model.reputation.into())
            .class_points(quest_model.class_points.into())
            .check(quest_model.check.into())
            .field(quest_model.field.into())
            .index(quest_model.index.into())
            .level(quest_model.level.into())
            .value(quest_model.value.into())
            .slot(quest_model.slot.into())
            .once(quest_model.once.into())
            .badges(quest_model.badges.map(|b| b.into()).unwrap_or_default())
            .give_membership(
                quest_model
                    .give_membership
                    .map(|m| m.into())
                    .unwrap_or_default(),
            )
            .war_id(quest_model.war_id.map(WarId::new).unwrap_or_default())
            .reward_type(quest_model.reward_type.into())
            .achievement_id(
                quest_model
                    .achievement_id
                    .map(AchievementId::new)
                    .unwrap_or_default(),
            )
            .staff(quest_model.staff.into())
            .upgrade(quest_model.upgrade.into())
            .war_mega(quest_model.war_mega.into())
            .build()
    }
}
