use crate::models::{redeem_code::value_object::RedeemCodeId, user::value_object::UserId};
use getset::{<PERSON><PERSON>, <PERSON><PERSON>};

use super::value_object::*;

#[derive(<PERSON>bu<PERSON>, <PERSON><PERSON>, De<PERSON>ult, bon::<PERSON><PERSON><PERSON>, <PERSON><PERSON>, Set<PERSON>)]
#[getset(get = "pub", set = "pub")]
pub struct UserRedeem {
    id: UserRedeemId,
    user_id: UserId,
    redeem_id: RedeemCodeId,
    date: RedeemDate,
}

impl UserRedeem {
    pub fn new(
        id: UserRedeemId,
        user_id: UserId,
        redeem_id: RedeemCodeId,
        date: RedeemDate,
    ) -> Self {
        Self {
            id,
            user_id,
            redeem_id,
            date,
        }
    }
}

#[cfg(test)]
mod tests {
    use chrono::Utc;
    use reforged_shared::{IdTrait, Value};

    use super::*;

    #[test]
    fn test_user_redeem_new() {
        let id = UserRedeemId::new(uuid::Uuid::now_v7());
        let user_id = UserId::new(uuid::Uuid::now_v7());
        let redeem_id = RedeemCodeId::new(uuid::Uuid::now_v7());
        let date = RedeemDate::new(Utc::now());

        let user_redeem =
            UserRedeem::new(id.clone(), user_id.clone(), redeem_id.clone(), date.clone());

        assert_eq!(user_redeem.id.get_id(), id.get_id());
        assert_eq!(user_redeem.user_id.get_id(), user_id.get_id());
        assert_eq!(user_redeem.redeem_id.get_id(), redeem_id.get_id());
        assert_eq!(user_redeem.date.value(), date.value());
    }

    #[test]
    fn test_user_redeem_builder() {
        let id = UserRedeemId::new(uuid::Uuid::now_v7());
        let user_id = UserId::new(uuid::Uuid::now_v7());
        let redeem_id = RedeemCodeId::new(uuid::Uuid::now_v7());
        let date = RedeemDate::new(Utc::now());

        let user_redeem = UserRedeem::builder()
            .id(id.clone())
            .user_id(user_id.clone())
            .redeem_id(redeem_id.clone())
            .date(date.clone())
            .build();

        assert_eq!(user_redeem.id.get_id(), id.get_id());
        assert_eq!(user_redeem.user_id.get_id(), user_id.get_id());
        assert_eq!(user_redeem.redeem_id.get_id(), redeem_id.get_id());
        assert_eq!(user_redeem.date.value(), date.value());
    }

    #[test]
    fn test_getters_and_setters() {
        let mut user_redeem = UserRedeem::default();

        let id = UserRedeemId::new(uuid::Uuid::now_v7());
        let user_id = UserId::new(uuid::Uuid::now_v7());
        let redeem_id = RedeemCodeId::new(uuid::Uuid::now_v7());
        let date = RedeemDate::new(Utc::now());

        user_redeem.set_id(id.clone());
        user_redeem.set_user_id(user_id.clone());
        user_redeem.set_redeem_id(redeem_id.clone());
        user_redeem.set_date(date.clone());

        assert_eq!(user_redeem.id().get_id(), id.get_id());
        assert_eq!(user_redeem.user_id().get_id(), user_id.get_id());
        assert_eq!(user_redeem.redeem_id().get_id(), redeem_id.get_id());
        assert_eq!(user_redeem.date().value(), date.value());
    }
}
