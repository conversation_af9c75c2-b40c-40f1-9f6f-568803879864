use getset::{<PERSON><PERSON>, <PERSON><PERSON>};

use crate::models::user::value_object::UserId;

use super::value_object::UserFriendId;

#[derive(<PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, bon::<PERSON><PERSON><PERSON>, <PERSON><PERSON>, Set<PERSON>)]
#[getset(get = "pub", set = "pub")]
pub struct UserFriend {
    id: UserFriendId,
    user_id: UserId,
    friend_id: UserId,
}

impl UserFriend {
    pub fn new(id: UserFriendId, user_id: UserId, friend_id: UserId) -> Self {
        Self {
            id,
            user_id,
            friend_id,
        }
    }
}

#[cfg(test)]
mod tests {
    use reforged_shared::IdTrait;

    use super::*;

    #[test]
    fn test_user_friend_new() {
        let id = UserFriendId::new(uuid::Uuid::now_v7());
        let user_id = UserId::new(uuid::Uuid::now_v7());
        let friend_id = UserId::new(uuid::Uuid::now_v7());

        let user_friend = UserFriend::new(id.clone(), user_id.clone(), friend_id.clone());

        assert_eq!(user_friend.id().get_id(), id.get_id());
        assert_eq!(user_friend.user_id().get_id(), user_id.get_id());
        assert_eq!(user_friend.friend_id().get_id(), friend_id.get_id());
    }

    #[test]
    fn test_user_friend_builder() {
        let id = UserFriendId::new(uuid::Uuid::now_v7());
        let user_id = UserId::new(uuid::Uuid::now_v7());
        let friend_id = UserId::new(uuid::Uuid::now_v7());

        let user_friend = UserFriend::builder()
            .id(id.clone())
            .user_id(user_id.clone())
            .friend_id(friend_id.clone())
            .build();

        assert_eq!(user_friend.id().get_id(), id.get_id());
        assert_eq!(user_friend.user_id().get_id(), user_id.get_id());
        assert_eq!(user_friend.friend_id().get_id(), friend_id.get_id());
    }
}