use chrono::{DateTime, Utc};
use reforged_shared::{UuidId, Value};

use super::entity::PasswordResetToken;

pub type PasswordResetTokenId = UuidId<PasswordResetToken>;

#[derive(Debug, PartialEq, Eq, PartialOrd, Ord, Clone, De<PERSON>ult)]
pub struct Token(String);

impl Token {
    pub fn new(token: String) -> Self {
        Self(token)
    }
}

impl Value<String> for Token {
    fn value(&self) -> String {
        self.0.clone()
    }
}

#[derive(Debug, PartialEq, Eq, PartialOrd, Ord, Clone, Default)]
pub struct PasswordResetTokenCreatedAt(DateTime<Utc>);

impl PasswordResetTokenCreatedAt {
    pub fn new(created_at: DateTime<Utc>) -> Self {
        Self(created_at)
    }
}

impl Value<DateTime<Utc>> for PasswordResetTokenCreatedAt {
    fn value(&self) -> DateTime<Utc> {
        self.0
    }
}

#[derive(Debug, <PERSON>ialEq, Eq, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>)]
pub struct PasswordResetTokenExpiresAt(DateTime<Utc>);

impl PasswordResetTokenExpiresAt {
    pub fn new(expires_at: DateTime<Utc>) -> Self {
        Self(expires_at)
    }

    pub fn is_expired(&self) -> bool {
        self.0 < Utc::now()
    }
}

impl Value<DateTime<Utc>> for PasswordResetTokenExpiresAt {
    fn value(&self) -> DateTime<Utc> {
        self.0
    }
}
