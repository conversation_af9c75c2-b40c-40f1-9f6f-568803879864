//! `SeaORM` Entity, @generated by sea-orm-codegen 1.1.0

use sea_orm::entity::prelude::*;
use serde::Deserialize;

#[derive(Clone, Debug, PartialEq, DeriveEntityModel, Eq, Deserialize)]
#[sea_orm(table_name = "user_livedrops")]
pub struct Model {
    #[sea_orm(primary_key, auto_increment = false)]
    pub id: Uuid,
    pub user_id: Option<Uuid>,
    pub item_id: Option<Uuid>,
    pub quantity: Option<i32>,
    pub sent: i16,
    pub date: DateTime,
    #[sea_orm(column_type = "Text", nullable)]
    pub message: Option<String>,
    pub achievement_id: Option<Uuid>,
    pub title_id: Option<Uuid>,
    pub experience: i64,
    pub gold: i64,
    pub coins: i32,
    pub crystal: i64,
    pub upgrade_days: i32,
    pub bag_slots: i32,
    pub bank_slots: i32,
    pub house_slots: i32,
}

#[derive(<PERSON><PERSON>, <PERSON><PERSON>, Debug, EnumI<PERSON>, DeriveRelation)]
pub enum Relation {
    #[sea_orm(
        belongs_to = "super::achievements::Entity",
        from = "Column::AchievementId",
        to = "super::achievements::Column::Id",
        on_update = "Cascade",
        on_delete = "SetNull"
    )]
    Achievements,
    #[sea_orm(
        belongs_to = "super::items::Entity",
        from = "Column::ItemId",
        to = "super::items::Column::Id",
        on_update = "Cascade",
        on_delete = "SetNull"
    )]
    Items,
    #[sea_orm(
        belongs_to = "super::titles::Entity",
        from = "Column::TitleId",
        to = "super::titles::Column::Id",
        on_update = "Cascade",
        on_delete = "SetNull"
    )]
    Titles,
    #[sea_orm(
        belongs_to = "super::users::Entity",
        from = "Column::UserId",
        to = "super::users::Column::Id",
        on_update = "Cascade",
        on_delete = "Cascade"
    )]
    Users,
}

impl Related<super::achievements::Entity> for Entity {
    fn to() -> RelationDef {
        Relation::Achievements.def()
    }
}

impl Related<super::items::Entity> for Entity {
    fn to() -> RelationDef {
        Relation::Items.def()
    }
}

impl Related<super::titles::Entity> for Entity {
    fn to() -> RelationDef {
        Relation::Titles.def()
    }
}

impl Related<super::users::Entity> for Entity {
    fn to() -> RelationDef {
        Relation::Users.def()
    }
}

impl ActiveModelBehavior for ActiveModel {}
