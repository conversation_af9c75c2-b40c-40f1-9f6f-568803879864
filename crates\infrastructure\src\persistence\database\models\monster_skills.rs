//! `SeaORM` Entity, @generated by sea-orm-codegen 1.1.0

use sea_orm::entity::prelude::*;
use serde::Deserialize;

#[derive(<PERSON><PERSON>, Debug, PartialEq, DeriveEntityModel, Eq, Deserialize)]
#[sea_orm(table_name = "monster_skills")]
pub struct Model {
    #[sea_orm(primary_key, auto_increment = false)]
    pub id: Uuid,
    pub monster_id: Uuid,
    pub skills: i32,
    pub skill_id: Uuid,
}

#[derive(<PERSON><PERSON>, <PERSON><PERSON>, Debug, EnumIter, DeriveRelation)]
pub enum Relation {
    #[sea_orm(
        belongs_to = "super::monsters::Entity",
        from = "Column::MonsterId",
        to = "super::monsters::Column::Id",
        on_update = "Cascade",
        on_delete = "Cascade"
    )]
    Monsters,
    #[sea_orm(
        belongs_to = "super::skills::Entity",
        from = "Column::SkillId",
        to = "super::skills::Column::Id",
        on_update = "Cascade",
        on_delete = "Cascade"
    )]
    Skills,
}

impl Related<super::monsters::Entity> for Entity {
    fn to() -> RelationDef {
        Relation::Monsters.def()
    }
}

impl Related<super::skills::Entity> for Entity {
    fn to() -> RelationDef {
        Relation::Skills.def()
    }
}

impl ActiveModelBehavior for ActiveModel {}
