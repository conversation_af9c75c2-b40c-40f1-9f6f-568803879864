use reforged_shared::{UuidId, Value};

use super::entity::AuraEffect;

pub type AuraEffectId = UuidId<AuraEffect>;

#[derive(Debug, PartialEq, Eq, PartialOrd, Ord, Clone, Default)]

pub struct AuraEffectStat(String);

impl AuraEffectStat {
    pub fn new(stat: impl Into<String>) -> Self {
        Self(stat.into())
    }
}

impl Value<String> for AuraEffectStat {
    fn value(&self) -> String {
        self.0.clone()
    }
}

impl From<String> for AuraEffectStat {
    fn from(value: String) -> Self {
        Self(value)
    }
}

impl From<&str> for AuraEffectStat {
    fn from(value: &str) -> Self {
        Self(value.to_string())
    }
}

#[derive(Debug, PartialEq, PartialOrd, Clone, Default)]

pub struct AuraEffectValue(f64);

impl AuraEffectValue {
    pub fn new(value: f64) -> Self {
        Self(value)
    }
}

impl Value<f64> for AuraEffectValue {
    fn value(&self) -> f64 {
        self.0
    }
}

impl From<f64> for AuraEffectValue {
    fn from(value: f64) -> Self {
        Self(value)
    }
}

#[derive(Debug, PartialEq, Eq, PartialOrd, Ord, Clone, Default)]

pub struct AuraEffectType(String);

impl AuraEffectType {
    pub fn new(effect_type: impl Into<String>) -> Self {
        Self(effect_type.into())
    }
}

impl Value<String> for AuraEffectType {
    fn value(&self) -> String {
        self.0.clone()
    }
}

impl From<String> for AuraEffectType {
    fn from(value: String) -> Self {
        Self(value)
    }
}

impl From<&str> for AuraEffectType {
    fn from(value: &str) -> Self {
        Self(value.to_string())
    }
}
