use num_traits::ToPrimitive;
use reforged_domain::models::class_category::{
    entity::ClassCategory,
    value_object::{
        ClassCategoryEnum, ClassCategoryId, Dexterity, Endurance, Intellect, Luck, Strength, Wisdom,
    },
};

#[derive(Debug)]
pub struct ClassCategoryDbModelMapper(super::super::models::class_categories::Model);

impl ClassCategoryDbModelMapper {
    pub fn new(category: super::super::models::class_categories::Model) -> Self {
        Self(category)
    }
}

impl From<ClassCategoryDbModelMapper> for ClassCategory {
    fn from(value: ClassCategoryDbModelMapper) -> Self {
        let category_model = value.0;
        let category = ClassCategoryEnum::try_from(category_model.category).unwrap_or_default();

        let id = ClassCategoryId::new(category_model.id);

        ClassCategory::builder()
            .id(id)
            .name(category_model.name.into())
            .category(category)
            .strength(Strength::new(
                category_model.strength.to_f64().unwrap_or(0.),
            ))
            .endurance(Endurance::new(
                category_model.endurance.to_f64().unwrap_or(0.),
            ))
            .dexterity(Dexterity::new(
                category_model.dexterity.to_f64().unwrap_or(0.),
            ))
            .intellect(Intellect::new(
                category_model.intellect.to_f64().unwrap_or(0.),
            ))
            .wisdom(Wisdom::new(category_model.wisdom.to_f64().unwrap_or(0.)))
            .luck(Luck::new(category_model.luck.to_f64().unwrap_or(0.)))
            .build()
    }
}
