use reforged_domain::models::class::value_object::ClassId;
use reforged_domain::models::enhancement::value_object::EnhancementId;
use reforged_domain::models::faction::value_object::FactionId;
use reforged_domain::models::item::{entity::Item, value_object::ItemId};
use reforged_domain::models::item_rarity::value_object::ItemRarityId;

#[derive(Debug)]
pub struct ItemDbModelMapper(super::super::models::items::Model);

impl ItemDbModelMapper {
    pub fn new(item: super::super::models::items::Model) -> Self {
        Self(item)
    }
}

impl From<ItemDbModelMapper> for Item {
    fn from(value: ItemDbModelMapper) -> Self {
        let item_model = value.0;

        let id = ItemId::new(item_model.id);

        Item::builder()
            .id(id)
            .class_id(item_model.class_id.map(ClassId::new).unwrap_or_default())
            .name(item_model.name.into())
            .description(item_model.description.into())
            .item_type(item_model.r#type.into())
            .element(item_model.element.into())
            .file(item_model.file.into())
            .link(item_model.link.into())
            .icon(item_model.icon.into())
            .equipment(item_model.equipment.into())
            .level(item_model.level.into())
            .dps(item_model.dps.into())
            .item_range(item_model.range.into())
            .rarity_id(ItemRarityId::new(item_model.rarity))
            .quantity(item_model.quantity.into())
            .stack(item_model.stack.into())
            .cost(item_model.cost.into())
            .coins(item_model.coins.into())
            .diamonds(item_model.diamonds.into())
            .crystal(item_model.crystal.into())
            .sell(item_model.sell.into())
            .market(item_model.market.into())
            .temporary(item_model.temporary.into())
            .upgrade(item_model.upgrade.into())
            .staff(item_model.staff.into())
            .enh_id(
                item_model
                    .enh_id
                    .map(EnhancementId::new)
                    .unwrap_or_default(),
            )
            .faction_id(
                item_model
                    .faction_id
                    .map(FactionId::new)
                    .unwrap_or_default(),
            )
            .req_reputation(item_model.req_reputation.into())
            .req_class_id(
                item_model
                    .req_class_id
                    .map(ClassId::new)
                    .unwrap_or_default(),
            )
            .req_class_points(item_model.req_class_points.into())
            .req_quests(item_model.req_quests.into())
            .quest_string_index(item_model.quest_string_index.into())
            .quest_string_value(item_model.quest_string_value.into())
            .meta(item_model.meta.into())
            .color(item_model.color.into())
            .build()
    }
}
