use crate::models::{monster::value_object::MonsterId, skill::value_object::SkillId};

use super::value_object::*;
use getset::{<PERSON><PERSON>, <PERSON><PERSON>};
#[derive(<PERSON>bu<PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, bon::<PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>)]
#[getset(get = "pub", set = "pub")]
pub struct MonsterSkill {
    id: MonsterSkillId,
    monster_id: MonsterId,
    skills: MonsterSkills,
    skill_id: SkillId,
}

impl MonsterSkill {
    pub fn new(
        id: MonsterSkillId,
        monster_id: MonsterId,
        skills: MonsterSkills,
        skill_id: SkillId,
    ) -> Self {
        Self {
            id,
            monster_id,
            skills,
            skill_id,
        }
    }
}

#[cfg(test)]
mod tests {
    use reforged_shared::{IdTrait, Value};

    use super::*;

    #[test]
    fn test_monster_skill_new() {
        let id = MonsterSkillId::new(uuid::Uuid::now_v7());
        let monster_id = MonsterId::new(uuid::Uuid::now_v7());
        let skills = MonsterSkills::new(2);
        let skill_id = SkillId::new(uuid::Uuid::now_v7());

        let monster_skill = MonsterSkill::new(
            id.clone(),
            monster_id.clone(),
            skills.clone(),
            skill_id.clone(),
        );

        assert_eq!(monster_skill.id().get_id(), id.get_id());
        assert_eq!(monster_skill.monster_id().get_id(), monster_id.get_id());
        assert_eq!(monster_skill.skills().value(), skills.value());
        assert_eq!(monster_skill.skill_id().get_id(), skill_id.get_id());
    }

    #[test]
    fn test_monster_skill_builder() {
        let id = MonsterSkillId::new(uuid::Uuid::now_v7());
        let monster_id = MonsterId::new(uuid::Uuid::now_v7());
        let skills = MonsterSkills::new(1);
        let skill_id = SkillId::new(uuid::Uuid::now_v7());

        let monster_skill = MonsterSkill::builder()
            .id(id.clone())
            .monster_id(monster_id.clone())
            .skills(skills.clone())
            .skill_id(skill_id.clone())
            .build();

        assert_eq!(monster_skill.id().get_id(), id.get_id());
        assert_eq!(monster_skill.monster_id().get_id(), monster_id.get_id());
        assert_eq!(monster_skill.skills().value(), skills.value());
        assert_eq!(monster_skill.skill_id().get_id(), skill_id.get_id());
    }

    #[test]
    fn test_getters_and_setters() {
        let mut monster_skill = MonsterSkill::default();

        let id = MonsterSkillId::new(uuid::Uuid::now_v7());
        let monster_id = MonsterId::new(uuid::Uuid::now_v7());
        let skills = MonsterSkills::new(1);
        let skill_id = SkillId::new(uuid::Uuid::now_v7());

        monster_skill.set_id(id.clone());
        monster_skill.set_monster_id(monster_id.clone());
        monster_skill.set_skills(skills.clone());
        monster_skill.set_skill_id(skill_id.clone());

        assert_eq!(monster_skill.id().get_id(), id.get_id());
        assert_eq!(monster_skill.monster_id().get_id(), monster_id.get_id());
        assert_eq!(monster_skill.skills().value(), skills.value());
        assert_eq!(monster_skill.skill_id().get_id(), skill_id.get_id());
    }
}
