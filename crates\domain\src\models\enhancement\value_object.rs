use super::entity::Enhancement;
use reforged_shared::{UuidId, Value};
use uuid::Uuid;

pub type EnhancementId = UuidId<Enhancement>;

#[derive(Debug, PartialEq, Eq, PartialOrd, Ord, Clone, Default)]

pub struct EnhancementName(String);

impl EnhancementName {
    pub fn new(name: impl Into<String>) -> Self {
        Self(name.into())
    }
}

impl Value<String> for EnhancementName {
    fn value(&self) -> String {
        self.0.clone()
    }
}

impl From<String> for EnhancementName {
    fn from(value: String) -> Self {
        Self(value)
    }
}

impl From<&str> for EnhancementName {
    fn from(value: &str) -> Self {
        Self(value.to_string())
    }
}

#[derive(Debug, PartialEq, Eq, PartialOrd, Ord, Clone, Default)]

pub struct Rarity(Uuid);

impl Rarity {
    pub fn new(value: Uuid) -> Self {
        Self(value)
    }
}

impl Value<Uuid> for Rarity {
    fn value(&self) -> Uuid {
        self.0
    }
}

impl From<Uuid> for Rarity {
    fn from(value: Uuid) -> Self {
        Self(value)
    }
}

#[derive(Debug, PartialEq, Eq, PartialOrd, Ord, Clone, Default)]

pub struct Dps(i16);

impl Dps {
    pub fn new(value: i16) -> Self {
        Self(value)
    }
}

impl Value<i16> for Dps {
    fn value(&self) -> i16 {
        self.0
    }
}

impl From<i16> for Dps {
    fn from(value: i16) -> Self {
        Self(value)
    }
}

#[derive(Debug, PartialEq, Eq, PartialOrd, Ord, Clone, Default)]

pub struct Level(i16);

impl Level {
    pub fn new(value: i16) -> Self {
        Self(value)
    }
}

impl Value<i16> for Level {
    fn value(&self) -> i16 {
        self.0
    }
}

impl From<i16> for Level {
    fn from(value: i16) -> Self {
        Self(value)
    }
}
