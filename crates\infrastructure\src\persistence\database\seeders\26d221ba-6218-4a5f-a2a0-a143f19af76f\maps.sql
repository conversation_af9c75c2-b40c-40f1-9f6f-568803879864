INSERT INTO "maps" ("id", "name", "file", "max_players", "req_level", "upgrade", "staff", "pvp", "world_boss") VALUES (1, 'buyhouse', 'town-buyhouse-24mar20_r5.swf', 6, 1, false, false, false, false);
INSERT INTO "maps" ("id", "name", "file", "max_players", "req_level", "upgrade", "staff", "pvp", "world_boss") VALUES (3, 'limbo', 'Limbo.swf', 15, 1, false, true, false, false);
INSERT INTO "maps" ("id", "name", "file", "max_players", "req_level", "upgrade", "staff", "pvp", "world_boss") VALUES (15, 'roftitan', 'RemembranceOfTitan.swf', 20, 0, false, false, false, false);
INSERT INTO "maps" ("id", "name", "file", "max_players", "req_level", "upgrade", "staff", "pvp", "world_boss") VALUES (21, 'deadlock', '1v1.swf', 2, 5, false, false, true, false);
INSERT INTO "maps" ("id", "name", "file", "max_players", "req_level", "upgrade", "staff", "pvp", "world_boss") VALUES (22, 'theater', 'Nythera-TheaterMap07-06-2022R2.swf', 8, 10, false, false, false, false);
INSERT INTO "maps" ("id", "name", "file", "max_players", "req_level", "upgrade", "staff", "pvp", "world_boss") VALUES (27, 'wheel', 'Nythera-WheelTest4.swf', 6, 5, false, true, false, false);
INSERT INTO "maps" ("id", "name", "file", "max_players", "req_level", "upgrade", "staff", "pvp", "world_boss") VALUES (33, 'wrong', 'town-prison.swf', 15, 10, false, true, false, false);
INSERT INTO "maps" ("id", "name", "file", "max_players", "req_level", "upgrade", "staff", "pvp", "world_boss") VALUES (36, 'SoulWell', 'NytheralSoulWell1.swf', 20, 0, false, false, false, false);
INSERT INTO "maps" ("id", "name", "file", "max_players", "req_level", "upgrade", "staff", "pvp", "world_boss") VALUES (37, 'lovemarsh', 'Nythera-LoveMarshR2.swf', 8, 35, false, true, false, false);
INSERT INTO "maps" ("id", "name", "file", "max_players", "req_level", "upgrade", "staff", "pvp", "world_boss") VALUES (38, 'BadgeTest', 'LimboDeezES.swf', 6, 0, false, true, false, false);
INSERT INTO "maps" ("id", "name", "file", "max_players", "req_level", "upgrade", "staff", "pvp", "world_boss") VALUES (10, 'forest', 'NytheraForest30-06-22.swf', 10, 1, false, false, false, false);
INSERT INTO "maps" ("id", "name", "file", "max_players", "req_level", "upgrade", "staff", "pvp", "world_boss") VALUES (23, 'forestlove', 'Nythera-ForestLove07-04-2022.swf', 10, 30, false, false, false, false);
INSERT INTO "maps" ("id", "name", "file", "max_players", "req_level", "upgrade", "staff", "pvp", "world_boss") VALUES (6, 'yulgar', 'NytheraYulgar08-07-22.swf', 12, 2, false, false, false, false);
INSERT INTO "maps" ("id", "name", "file", "max_players", "req_level", "upgrade", "staff", "pvp", "world_boss") VALUES (7, 'lostcave', 'NytheraCoinsMap.swf', 7, 0, false, false, false, false);
INSERT INTO "maps" ("id", "name", "file", "max_players", "req_level", "upgrade", "staff", "pvp", "world_boss") VALUES (8, 'nasari', 'AuraDimensionNasari06-21-22-1.swf', 12, 15, false, true, false, false);
INSERT INTO "maps" ("id", "name", "file", "max_players", "req_level", "upgrade", "staff", "pvp", "world_boss") VALUES (9, 'drakath', 'AuraDimensionDrakath20-06.swf', 9, 5, false, false, false, false);
INSERT INTO "maps" ("id", "name", "file", "max_players", "req_level", "upgrade", "staff", "pvp", "world_boss") VALUES (11, 'legioncave', 'NytheraLegioncave29-06-22-2.swf', 10, 0, false, true, false, false);
INSERT INTO "maps" ("id", "name", "file", "max_players", "req_level", "upgrade", "staff", "pvp", "world_boss") VALUES (5, 'trainers', 'NytheraTrainers26-06-22.swf', 10, 0, false, false, false, false);
INSERT INTO "maps" ("id", "name", "file", "max_players", "req_level", "upgrade", "staff", "pvp", "world_boss") VALUES (12, 'pirates', 'NytheraPirates28-06-22-6.swf', 12, 25, false, false, false, false);
INSERT INTO "maps" ("id", "name", "file", "max_players", "req_level", "upgrade", "staff", "pvp", "world_boss") VALUES (13, 'nulgathcave', 'NytheraNulgacave03-07-22-1.swf', 6, 0, false, true, false, false);
INSERT INTO "maps" ("id", "name", "file", "max_players", "req_level", "upgrade", "staff", "pvp", "world_boss") VALUES (14, 'worldboss3', 'NytheraWorldBoss3-1.swf', 6, 0, false, true, false, false);
INSERT INTO "maps" ("id", "name", "file", "max_players", "req_level", "upgrade", "staff", "pvp", "world_boss") VALUES (16, 'sketch', 'PaperMap-NytheraFix.swf', 8, 7, false, false, false, false);
INSERT INTO "maps" ("id", "name", "file", "max_players", "req_level", "upgrade", "staff", "pvp", "world_boss") VALUES (17, 'worldboss', 'NytheraWorldBoss1.swf', 100, 1, false, false, false, true);
INSERT INTO "maps" ("id", "name", "file", "max_players", "req_level", "upgrade", "staff", "pvp", "world_boss") VALUES (20, 'djinboss', 'Elemental.swf', 20, 0, false, false, false, false);
INSERT INTO "maps" ("id", "name", "file", "max_players", "req_level", "upgrade", "staff", "pvp", "world_boss") VALUES (24, 'meliodaschallenge', 'MeliodasMap05-07-22-1.swf', 25, 0, false, false, false, false);
INSERT INTO "maps" ("id", "name", "file", "max_players", "req_level", "upgrade", "staff", "pvp", "world_boss") VALUES (19, 'worldboss2', 'NytheraWorldBoss2-1.swf', 100, 0, false, true, false, false);
INSERT INTO "maps" ("id", "name", "file", "max_players", "req_level", "upgrade", "staff", "pvp", "world_boss") VALUES (18, 'nibbleon', 'NytheraNibbleon05-07-22.swf', 15, 10, false, false, false, false);
INSERT INTO "maps" ("id", "name", "file", "max_players", "req_level", "upgrade", "staff", "pvp", "world_boss") VALUES (25, 'party', 'NytheraDisco.swf', 50, 0, false, false, false, false);
INSERT INTO "maps" ("id", "name", "file", "max_players", "req_level", "upgrade", "staff", "pvp", "world_boss") VALUES (26, 'test', '.', 6, 0, false, true, false, false);
INSERT INTO "maps" ("id", "name", "file", "max_players", "req_level", "upgrade", "staff", "pvp", "world_boss") VALUES (28, 'hollowrealm', 'Nytherahbchallenge03-07-22-1.swf', 15, 0, false, false, false, false);
INSERT INTO "maps" ("id", "name", "file", "max_players", "req_level", "upgrade", "staff", "pvp", "world_boss") VALUES (29, 'destiny', 'DestinyNight.swf', 10, 5, false, true, false, false);
INSERT INTO "maps" ("id", "name", "file", "max_players", "req_level", "upgrade", "staff", "pvp", "world_boss") VALUES (30, 'factory', 'Town-Darkfactory.swf', 10, 30, false, true, false, false);
INSERT INTO "maps" ("id", "name", "file", "max_players", "req_level", "upgrade", "staff", "pvp", "world_boss") VALUES (31, 'pantano', 'Nythera-Pantano.swf', 15, 0, false, false, false, false);
INSERT INTO "maps" ("id", "name", "file", "max_players", "req_level", "upgrade", "staff", "pvp", "world_boss") VALUES (32, 'prison', 'town-prison.swf', 100, 0, false, true, false, false);
INSERT INTO "maps" ("id", "name", "file", "max_players", "req_level", "upgrade", "staff", "pvp", "world_boss") VALUES (34, 'soulyard', 'SoulYardNythera07-07-22-2.swf', 20, 0, false, false, false, false);
INSERT INTO "maps" ("id", "name", "file", "max_players", "req_level", "upgrade", "staff", "pvp", "world_boss") VALUES (35, 'hbchallenge', 'NytheraHBChallenge07-07-22-2.swf', 10, 5, false, false, false, false);
INSERT INTO "maps" ("id", "name", "file", "max_players", "req_level", "upgrade", "staff", "pvp", "world_boss") VALUES (2, 'faroff', 'NytheraBattleonFurao09-07-22.swf', 30, 1, false, false, false, false);
INSERT INTO "maps" ("id", "name", "file", "max_players", "req_level", "upgrade", "staff", "pvp", "world_boss") VALUES (4, 'newbie', 'NewbieNythera27-06-22-1.swf', 16, 1, false, false, false, false);
