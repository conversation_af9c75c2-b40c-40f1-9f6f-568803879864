use reforged_domain::models::enhancement::{
    entity::Enhancement,
    value_object::{Dps, EnhancementId, EnhancementName, Level, Rarity},
};
use reforged_domain::models::enhancement_pattern::value_object::EnhancementPatternId;

#[derive(Debug)]
pub struct EnhancementDbModelMapper(super::super::models::enhancements::Model);

impl EnhancementDbModelMapper {
    pub fn new(enhancement: super::super::models::enhancements::Model) -> Self {
        Self(enhancement)
    }
}

impl From<EnhancementDbModelMapper> for Enhancement {
    fn from(value: EnhancementDbModelMapper) -> Self {
        let enhancement_model = value.0;

        let id = EnhancementId::new(enhancement_model.id);

        Enhancement::builder()
            .id(id)
            .name(EnhancementName::new(enhancement_model.name))
            .pattern_id(EnhancementPatternId::new(enhancement_model.pattern_id))
            .rarity(Rarity::new(enhancement_model.rarity))
            .dps(Dps::new(enhancement_model.dps))
            .level(Level::new(enhancement_model.level))
            .build()
    }
}
