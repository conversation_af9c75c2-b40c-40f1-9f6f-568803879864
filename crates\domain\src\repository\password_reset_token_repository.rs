use async_trait::async_trait;

use crate::{
    error::RepositoryError,
    models::password_reset_token::{
        entity::PasswordResetToken,
        value_object::{PasswordResetTokenId, Token},
    },
};

#[mockall::automock]
#[async_trait]
pub trait PasswordResetTokenRepository: Send + Sync {
    async fn save(&self, data: &PasswordResetToken) -> Result<(), RepositoryError>;
    async fn delete(&self, id: &PasswordResetTokenId) -> Result<(), RepositoryError>;
}

#[mockall::automock]
#[async_trait]
pub trait PasswordResetTokensReadRepository: Send + Sync {
    async fn find_by_id(
        &self,
        id: &PasswordResetTokenId,
    ) -> Result<Option<PasswordResetToken>, RepositoryError>;
    async fn find_by_token(
        &self,
        token: &Token,
    ) -> Result<Option<PasswordResetToken>, RepositoryError>;
}
