use crate::models::{map::value_object::MapId, monster::value_object::MonsterId};

use super::value_object::*;
use getset::{<PERSON><PERSON>, <PERSON><PERSON>};
#[derive(Debug, <PERSON><PERSON>, De<PERSON>ult, bon::<PERSON><PERSON><PERSON>, <PERSON><PERSON>, Set<PERSON>)]
#[getset(get = "pub", set = "pub")]
pub struct MonsterBoss {
    id: MonsterBossId,
    monster_id: MonsterId,
    map_id: MapId,
    spawn_interval: MonsterBossSpawnInterval,
    time_limit: MonsterBossTimeLimit,
    kills: MonsterBossKills,
    deaths: MonsterBossDeaths,
    death_time: MonsterBossDeathTime,
    spawn_time: MonsterBossSpawnTime,
    description: MonsterBossDescription,
}

impl MonsterBoss {
    pub fn new(
        id: MonsterBossId,
        monster_id: MonsterId,
        map_id: MapId,
        spawn_interval: MonsterBossSpawnInterval,
        time_limit: MonsterBossTimeLimit,
        kills: MonsterBossKills,
        deaths: MonsterBossDeaths,
        death_time: MonsterBossDeathTime,
        spawn_time: MonsterBossSpawnTime,
        description: MonsterBossDescription,
    ) -> Self {
        Self {
            id,
            monster_id,
            map_id,
            spawn_interval,
            time_limit,
            kills,
            deaths,
            death_time,
            spawn_time,
            description,
        }
    }
}

#[cfg(test)]
mod tests {
    use reforged_shared::{IdTrait, Value};

    use super::*;

    fn create_test_values() -> (
        MonsterBossId,
        MonsterId,
        MapId,
        MonsterBossSpawnInterval,
        MonsterBossTimeLimit,
        MonsterBossKills,
        MonsterBossDeaths,
        MonsterBossDeathTime,
        MonsterBossSpawnTime,
        MonsterBossDescription,
    ) {
        (
            MonsterBossId::new(uuid::Uuid::now_v7()),
            MonsterId::new(uuid::Uuid::now_v7()),
            MapId::new(uuid::Uuid::now_v7()),
            MonsterBossSpawnInterval::new(3600),
            MonsterBossTimeLimit::new(1800),
            MonsterBossKills::new(0),
            MonsterBossDeaths::new(0),
            MonsterBossDeathTime::new(0),
            MonsterBossSpawnTime::new(0),
            MonsterBossDescription::new("Fearsome dragon boss".to_string()),
        )
    }

    #[test]
    fn test_monster_boss_new() {
        let (
            id,
            monster_id,
            map_id,
            spawn_interval,
            time_limit,
            kills,
            deaths,
            death_time,
            spawn_time,
            description,
        ) = create_test_values();

        let monster_boss = MonsterBoss::new(
            id.clone(),
            monster_id.clone(),
            map_id.clone(),
            spawn_interval.clone(),
            time_limit.clone(),
            kills.clone(),
            deaths.clone(),
            death_time.clone(),
            spawn_time.clone(),
            description.clone(),
        );

        assert_eq!(monster_boss.id().get_id(), id.get_id());
        assert_eq!(monster_boss.monster_id().get_id(), monster_id.get_id());
        assert_eq!(monster_boss.map_id().get_id(), map_id.get_id());
        assert_eq!(
            monster_boss.spawn_interval().value(),
            spawn_interval.value()
        );
        assert_eq!(monster_boss.time_limit().value(), time_limit.value());
        assert_eq!(monster_boss.kills().value(), kills.value());
        assert_eq!(monster_boss.deaths().value(), deaths.value());
        assert_eq!(monster_boss.death_time().value(), death_time.value());
        assert_eq!(monster_boss.spawn_time().value(), spawn_time.value());
        assert_eq!(monster_boss.description().value(), description.value());
    }

    #[test]
    fn test_monster_boss_builder() {
        let (
            id,
            monster_id,
            map_id,
            spawn_interval,
            time_limit,
            kills,
            deaths,
            death_time,
            spawn_time,
            description,
        ) = create_test_values();

        let monster_boss = MonsterBoss::builder()
            .id(id.clone())
            .monster_id(monster_id.clone())
            .map_id(map_id.clone())
            .spawn_interval(spawn_interval.clone())
            .time_limit(time_limit.clone())
            .kills(kills.clone())
            .deaths(deaths.clone())
            .death_time(death_time.clone())
            .spawn_time(spawn_time.clone())
            .description(description.clone())
            .build();

        assert_eq!(monster_boss.id().get_id(), id.get_id());
        assert_eq!(monster_boss.monster_id().get_id(), monster_id.get_id());
        assert_eq!(monster_boss.map_id().get_id(), map_id.get_id());
        assert_eq!(
            monster_boss.spawn_interval().value(),
            spawn_interval.value()
        );
        assert_eq!(monster_boss.time_limit().value(), time_limit.value());
        assert_eq!(monster_boss.kills().value(), kills.value());
        assert_eq!(monster_boss.deaths().value(), deaths.value());
        assert_eq!(monster_boss.death_time().value(), death_time.value());
        assert_eq!(monster_boss.spawn_time().value(), spawn_time.value());
        assert_eq!(monster_boss.description().value(), description.value());
    }

    #[test]
    fn test_getters_and_setters() {
        let mut monster_boss = MonsterBoss::default();

        let (
            id,
            monster_id,
            map_id,
            spawn_interval,
            time_limit,
            kills,
            deaths,
            death_time,
            spawn_time,
            description,
        ) = create_test_values();

        monster_boss.set_id(id.clone());
        monster_boss.set_monster_id(monster_id.clone());
        monster_boss.set_map_id(map_id.clone());
        monster_boss.set_spawn_interval(spawn_interval.clone());
        monster_boss.set_time_limit(time_limit.clone());
        monster_boss.set_kills(kills.clone());
        monster_boss.set_deaths(deaths.clone());
        monster_boss.set_death_time(death_time.clone());
        monster_boss.set_spawn_time(spawn_time.clone());
        monster_boss.set_description(description.clone());

        assert_eq!(monster_boss.id().get_id(), id.get_id());
        assert_eq!(monster_boss.monster_id().get_id(), monster_id.get_id());
        assert_eq!(monster_boss.map_id().get_id(), map_id.get_id());
        assert_eq!(
            monster_boss.spawn_interval().value(),
            spawn_interval.value()
        );
        assert_eq!(monster_boss.time_limit().value(), time_limit.value());
        assert_eq!(monster_boss.kills().value(), kills.value());
        assert_eq!(monster_boss.deaths().value(), deaths.value());
        assert_eq!(monster_boss.death_time().value(), death_time.value());
        assert_eq!(monster_boss.spawn_time().value(), spawn_time.value());
        assert_eq!(monster_boss.description().value(), description.value());
    }
}
