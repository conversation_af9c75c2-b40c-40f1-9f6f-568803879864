use async_trait::async_trait;
use mockall::automock;

use crate::error::RepositoryError;
use crate::models::item_bundle::entity::ItemBundle;
use crate::models::item_bundle::value_object::ItemBundleId;

#[automock]
#[async_trait]
pub trait ItemBundleRepository: Send + Sync {
    async fn save(&self, entity: &ItemBundle) -> Result<(), RepositoryError>;
    async fn update(&self, entity: &ItemBundle) -> Result<(), RepositoryError>;
    async fn delete(&self, id: &ItemBundleId) -> Result<(), RepositoryError>;
}

#[automock]
#[async_trait]
pub trait ItemBundleReadRepository: Send + Sync {
    async fn find_by_id(&self, id: &ItemBundleId) -> Result<Option<ItemBundle>, RepositoryError>;
    async fn find_all(&self) -> Result<Vec<ItemBundle>, RepositoryError>;
}
