use getset::{<PERSON><PERSON>, <PERSON><PERSON>};
use reforged_shared::UuidId;

use crate::models::user::value_object::UserId;

use super::value_object::{Age, Avatar, Country, Gender};

pub type ProfileId = UuidId<Profile>;

#[allow(unused)]
#[derive(<PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, bon::<PERSON><PERSON><PERSON>, Get<PERSON>, Setters)]
#[getset(get = "pub", set = "pub")]
pub struct Profile {
    id: ProfileId,
    user_id: UserId,
    country: Country,
    age: Age,
    gender: Gender,
    avatar: Avatar,
}

impl Profile {
    pub fn new(
        id: ProfileId,
        user_id: UserId,
        country: Country,
        age: Age,
        gender: Gender,
        avatar: Avatar,
    ) -> Self {
        Self {
            id,
            user_id,
            country,
            age,
            gender,
            avatar,
        }
    }
}

#[cfg(test)]
mod tests {
    use reforged_shared::{IdTrait, Value};

    use super::*;

    #[test]
    fn test_profile_new() {
        let id = ProfileId::new(uuid::Uuid::now_v7());
        let user_id = UserId::new(uuid::Uuid::now_v7());
        let country = Country::new("United States".to_string());
        let age = Age::new(25);
        let gender = Gender::Male;
        let avatar = Avatar::new("avatars/default.png".to_string());

        let profile = Profile::new(
            id.clone(),
            user_id.clone(),
            country.clone(),
            age.clone(),
            gender.clone(),
            avatar.clone(),
        );

        assert_eq!(profile.id().get_id(), id.get_id());
        assert_eq!(profile.user_id().get_id(), user_id.get_id());
        assert_eq!(profile.country().value(), country.value());
        assert_eq!(profile.age().value(), age.value());
        assert_eq!(profile.gender(), &gender);
        assert_eq!(profile.avatar().value(), avatar.value());
    }

    #[test]
    fn test_profile_builder() {
        let id = ProfileId::new(uuid::Uuid::now_v7());
        let user_id = UserId::new(uuid::Uuid::now_v7());
        let country = Country::new("United States".to_string());
        let age = Age::new(25);
        let gender = Gender::Male;
        let avatar = Avatar::new("avatars/default.png".to_string());

        let profile = Profile::builder()
            .id(id.clone())
            .user_id(user_id.clone())
            .country(country.clone())
            .age(age.clone())
            .gender(gender.clone())
            .avatar(avatar.clone())
            .build();

        assert_eq!(profile.id().get_id(), id.get_id());
        assert_eq!(profile.user_id().get_id(), user_id.get_id());
        assert_eq!(profile.country().value(), country.value());
        assert_eq!(profile.age().value(), age.value());
        assert_eq!(profile.gender(), &gender);
        assert_eq!(profile.avatar().value(), avatar.value());
    }

    #[test]
    fn test_getters_and_setters() {
        let mut profile = Profile::default();

        let id = ProfileId::new(uuid::Uuid::now_v7());
        let user_id = UserId::new(uuid::Uuid::now_v7());
        let country = Country::new("United States".to_string());
        let age = Age::new(25);
        let gender = Gender::Male;
        let avatar = Avatar::new("avatars/default.png".to_string());

        profile.set_id(id.clone());
        profile.set_user_id(user_id.clone());
        profile.set_country(country.clone());
        profile.set_age(age.clone());
        profile.set_gender(gender.clone());
        profile.set_avatar(avatar.clone());

        assert_eq!(profile.id().get_id(), id.get_id());
        assert_eq!(profile.user_id().get_id(), user_id.get_id());
        assert_eq!(profile.country().value(), country.value());
        assert_eq!(profile.age().value(), age.value());
        assert_eq!(profile.gender(), &gender);
        assert_eq!(profile.avatar().value(), avatar.value());
    }
}
