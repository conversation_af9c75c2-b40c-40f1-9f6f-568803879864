use reforged_domain::models::user_boost::entity::UserBoost;
use reforged_domain::models::user_boost::value_object::UserBoostId;
use reforged_domain::models::user::value_object::UserId;

#[derive(Debug)]
pub struct UserBoostDbModelMapper(super::super::models::user_boosts::Model);

impl UserBoostDbModelMapper {
    pub fn new(model: super::super::models::user_boosts::Model) -> Self {
        Self(model)
    }
}

impl From<UserBoostDbModelMapper> for UserBoost {
    fn from(value: UserBoostDbModelMapper) -> Self {
        let model = value.0;
        
        UserBoost::builder()
            .id(UserBoostId::new(model.id))
            .user_id(UserId::new(model.user_id))
            .cp_boost_expire(model.cp_boost_expire.into())
            .rep_boost_expire(model.rep_boost_expire.into())
            .gold_boost_expire(model.gold_boost_expire.into())
            .exp_boost_expire(model.exp_boost_expire.into())
            .upgrade_expire(model.upgrade_expire.into())
            .upgrade_days(model.upgrade_days.into())
            .upgraded(model.upgraded.into())
            .build()
    }
}
