use reforged_domain::models::shop_item::entity::ShopItem;
use reforged_domain::models::shop_item::value_object::ShopItemId;
use reforged_domain::models::shop::value_object::ShopId;
use reforged_domain::models::item::value_object::ItemId;

#[derive(Debug)]
pub struct ShopItemDbModelMapper(super::super::models::shop_items::Model);

impl ShopItemDbModelMapper {
    pub fn new(model: super::super::models::shop_items::Model) -> Self {
        Self(model)
    }
}

impl From<ShopItemDbModelMapper> for ShopItem {
    fn from(value: ShopItemDbModelMapper) -> Self {
        let model = value.0;
        
        ShopItem::builder()
            .id(ShopItemId::new(model.id))
            .shop_id(ShopId::new(model.shop_id))
            .item_id(ItemId::new(model.item_id))
            .quantity(model.quantity.into())
            .gold(model.gold.into())
            .coins(model.coins.into())
            .build()
    }
}
