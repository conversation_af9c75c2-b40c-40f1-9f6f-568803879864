use std::fmt::Debug;

use async_trait::async_trait;
use chrono::{DateTime, Duration, Utc};
use serde::{Deserialize, Serialize};

use crate::error::ApplicationError;

#[async_trait]
pub trait QueryHandler<Q, R> {
    async fn handle(&self, query: Q) -> Result<R, ApplicationError>;
}

#[derive(Debug, Serialize, Deserialize, Clone, PartialEq)]
#[serde(rename_all = "snake_case")]
pub enum PasetoClaimPurpose {
    AccessToken,
    RefreshToken,
    PasswordReset,
    EmailVerification,
}

impl TryFrom<&str> for PasetoClaimPurpose {
    type Error = ApplicationError;

    fn try_from(value: &str) -> Result<Self, Self::Error> {
        match value {
            "access_token" => Ok(Self::AccessToken),
            "refresh_token" => Ok(Self::RefreshToken),
            "password_reset" => Ok(Self::PasswordReset),
            "email_verification" => Ok(Self::EmailVerification),
            _ => Err(ApplicationError::TokenInvalid),
        }
    }
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PasetoClaims {
    pub id: uuid::Uuid,
    pub email: String,
    pub role: String,
    pub exp: Duration,
    pub purpose: PasetoClaimPurpose,
}

impl PasetoClaims {
    pub fn new(
        id: uuid::Uuid,
        email: String,
        role: String,
        exp: Duration,
        purpose: PasetoClaimPurpose,
    ) -> Self {
        Self {
            id,
            email,
            role,
            exp,
            purpose,
        }
    }
}

pub trait TokenService: Send + Sync + 'static {
    fn generate_token(
        &self,
        claims: PasetoClaims,
        expiration: Duration,
    ) -> Result<(String, DateTime<Utc>), ApplicationError>;
    fn validate_token(
        &self,
        token: String,
        purpose: PasetoClaimPurpose,
    ) -> Result<PasetoClaims, ApplicationError>;
}

#[async_trait]
pub trait BrokerServicePublisher: Send + Sync + 'static {
    async fn publish(
        &self,
        event_type: &str,
        message: serde_json::Value,
    ) -> Result<(), ApplicationError>;
}

#[async_trait]
pub trait BrokerServiceConsumer: Send + Sync + 'static {
    async fn consume(&self) -> Result<(), ApplicationError>;
}
