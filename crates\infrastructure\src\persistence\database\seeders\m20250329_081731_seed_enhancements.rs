use std::collections::HashMap;

use crate::models::{self};
use sea_orm::{ActiveModelTrait, ActiveValue::Set, IntoActiveModel, prelude::Uuid};
use sea_orm_migration::prelude::*;

#[derive(DeriveMigrationName)]
pub struct Migration;

#[async_trait::async_trait]
impl MigrationTrait for Migration {
    async fn up(&self, manager: &SchemaManager) -> Result<(), DbErr> {
        let db = manager.get_connection();

        let enhancement_patterns = include_str!("../json/enhancements_patterns.json");
        let enhancement_patterns: Vec<models::enhancement_patterns::Model> =
            serde_json::from_str(enhancement_patterns).unwrap();

        let mut pattern_ids: HashMap<u64, Uuid> = HashMap::new();

        for model in enhancement_patterns {
            let pattern_id = model.id;

            let timestamp = pattern_id.get_timestamp().unwrap();
            let (sec, _) = timestamp.to_unix();

            pattern_ids.insert(sec, pattern_id);

            let enhancement_pattern = model.into_active_model();
            enhancement_pattern.insert(db).await?;
        }

        let enhancements = include_str!("../json/enhancements.json");
        let enhancements: Vec<models::enhancements::Model> =
            serde_json::from_str(enhancements).unwrap();

        for model in enhancements {
            let id = model.pattern_id;
            let timestamp = id.get_timestamp().unwrap();
            let (sec, _) = timestamp.to_unix();

            let pattern_id = pattern_ids.get(&sec).unwrap().clone();

            let mut enhancement = model.into_active_model();
            enhancement.pattern_id = Set(pattern_id);

            enhancement.insert(db).await?;
        }

        Ok(())
    }

    async fn down(&self, _manager: &SchemaManager) -> Result<(), DbErr> {
        Ok(())
    }
}
