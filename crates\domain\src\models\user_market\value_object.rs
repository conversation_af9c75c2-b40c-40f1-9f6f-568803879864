use chrono::{DateTime, Utc};
use reforged_shared::{UuidId, Value};

use super::entity::UserMarket;

pub type UserMarketId = UuidId<UserMarket>;

#[derive(Debug, PartialEq, Eq, PartialOrd, Ord, Clone, Default)]
pub struct MarketDateTime(DateTime<Utc>);

impl MarketDateTime {
    pub fn new(datetime: DateTime<Utc>) -> Self {
        Self(datetime)
    }
}

impl Value<DateTime<Utc>> for MarketDateTime {
    fn value(&self) -> DateTime<Utc> {
        self.0
    }
}

impl From<DateTime<Utc>> for MarketDateTime {
    fn from(datetime: DateTime<Utc>) -> Self {
        Self(datetime)
    }
}

#[derive(Debug, PartialEq, Eq, PartialOrd, Ord, Clone, Default)]
pub struct MarketCoins(i32);

impl MarketCoins {
    pub fn new(value: i32) -> Self {
        Self(value)
    }
}

impl Value<i32> for MarketCoins {
    fn value(&self) -> i32 {
        self.0
    }
}

impl From<i32> for MarketCoins {
    fn from(value: i32) -> Self {
        Self(value)
    }
}

#[derive(Debug, PartialEq, Eq, PartialOrd, Ord, Clone, Default)]
pub struct MarketGold(i32);

impl MarketGold {
    pub fn new(value: i32) -> Self {
        Self(value)
    }
}

impl Value<i32> for MarketGold {
    fn value(&self) -> i32 {
        self.0
    }
}

impl From<i32> for MarketGold {
    fn from(value: i32) -> Self {
        Self(value)
    }
}

#[derive(Debug, PartialEq, Eq, PartialOrd, Ord, Clone, Default)]
pub struct MarketQuantity(i32);

impl MarketQuantity {
    pub fn new(value: i32) -> Self {
        Self(value)
    }
}

impl Value<i32> for MarketQuantity {
    fn value(&self) -> i32 {
        self.0
    }
}

impl From<i32> for MarketQuantity {
    fn from(value: i32) -> Self {
        Self(value)
    }
}

#[derive(Debug, PartialEq, Eq, PartialOrd, Ord, Clone, Default)]
pub struct MarketStatus(i16);

impl MarketStatus {
    pub fn new(value: i16) -> Self {
        Self(value)
    }
}

impl Value<i16> for MarketStatus {
    fn value(&self) -> i16 {
        self.0
    }
}

impl From<i16> for MarketStatus {
    fn from(value: i16) -> Self {
        Self(value)
    }
}

#[derive(Debug, PartialEq, Eq, PartialOrd, Ord, Clone, Default)]
pub enum MarketType {
    #[default]
    Auction,
    Vending,
}

impl MarketType {
    pub fn as_str(&self) -> &'static str {
        match self {
            MarketType::Auction => "Auction",
            MarketType::Vending => "Vending",
        }
    }
}