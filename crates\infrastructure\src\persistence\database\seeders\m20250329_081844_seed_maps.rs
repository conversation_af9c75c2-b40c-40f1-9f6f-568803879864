use crate::models;
use sea_orm::{ActiveModelTrait, IntoActiveModel};
use sea_orm_migration::prelude::*;

#[derive(DeriveMigrationName)]
pub struct Migration;

#[async_trait::async_trait]
impl MigrationTrait for Migration {
    async fn up(&self, manager: &SchemaManager) -> Result<(), DbErr> {
        let db = manager.get_connection();

        let maps = include_str!("../json/maps.json");
        let maps: Vec<models::maps::Model> = serde_json::from_str(maps).unwrap();

        for model in maps {
            let hair = model.into_active_model();
            hair.insert(db).await?;
        }

        let map_items = include_str!("../json/maps_items.json");
        let map_items: Vec<models::map_items::Model> = serde_json::from_str(map_items).unwrap();

        for model in map_items {
            let map_item = model.into_active_model();
            map_item.insert(db).await?;
        }

        Ok(())
    }

    async fn down(&self, _manager: &SchemaManager) -> Result<(), DbErr> {
        Ok(())
    }
}
