use crate::models::prelude::*;
use sea_orm::Schema;
use sea_orm_migration::prelude::*;

#[derive(DeriveMigrationName)]
pub struct Migration;

#[async_trait::async_trait]
impl MigrationTrait for Migration {
    async fn up(&self, manager: &SchemaManager) -> Result<(), DbErr> {
        let backend = manager.get_database_backend();
        let schema = Schema::new(backend);

        // MARK: enhancements tables and junction tables
        manager
            .create_table(schema.create_table_from_entity(EnhancementPatterns))
            .await?;

        manager
            .create_table(schema.create_table_from_entity(Enhancements))
            .await?;

        Ok(())
    }

    async fn down(&self, manager: &SchemaManager) -> Result<(), DbErr> {
        manager
            .drop_table(Table::drop().table(Enhancements).to_owned())
            .await?;

        manager
            .drop_table(Table::drop().table(EnhancementPatterns).to_owned())
            .await
    }
}
