use async_trait::async_trait;
use mockall::automock;

use crate::error::RepositoryError;
use crate::models::book::entity::Book;
use crate::models::book::value_object::BookId;

#[automock]
#[async_trait]
pub trait BookRepository: Send + Sync {
    async fn save(&self, entity: &Book) -> Result<(), RepositoryError>;
    async fn update(&self, entity: &Book) -> Result<(), RepositoryError>;
    async fn delete(&self, id: &BookId) -> Result<(), RepositoryError>;
}

#[automock]
#[async_trait]
pub trait BookReadRepository: Send + Sync {
    async fn find_by_id(&self, id: &BookId) -> Result<Option<Book>, RepositoryError>;
    async fn find_all(&self) -> Result<Vec<Book>, RepositoryError>;
}
