use crate::models::item::value_object::ItemId;
use crate::models::item_skill::value_object::*;
use crate::models::skill::value_object::SkillId;
use getset::{<PERSON><PERSON>, Set<PERSON>};

#[derive(<PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, bon::<PERSON><PERSON><PERSON>, <PERSON><PERSON>, Set<PERSON>)]
#[getset(get = "pub", set = "pub")]
pub struct ItemSkill {
    pub id: ItemSkillId,
    pub item_id: ItemId,
    pub skill_id: SkillId,
}

impl ItemSkill {
    pub fn new(id: ItemSkillId, item_id: ItemId, skill_id: SkillId) -> Self {
        Self {
            id,
            item_id,
            skill_id,
        }
    }
}

#[cfg(test)]
mod tests {
    use reforged_shared::IdTrait;

    use super::*;

    #[test]
    fn test_item_skill_new() {
        let id = ItemSkillId::new(uuid::Uuid::now_v7());
        let item_id = ItemId::new(uuid::Uuid::now_v7());
        let skill_id = SkillId::new(uuid::Uuid::now_v7());

        let item_skill = ItemSkill::new(id.clone(), item_id.clone(), skill_id.clone());

        assert_eq!(item_skill.id.get_id(), id.get_id());
        assert_eq!(item_skill.item_id.get_id(), item_id.get_id());
        assert_eq!(item_skill.skill_id.get_id(), skill_id.get_id());
    }

    #[test]
    fn test_item_skill_builder() {
        let id = ItemSkillId::new(uuid::Uuid::now_v7());
        let item_id = ItemId::new(uuid::Uuid::now_v7());
        let skill_id = SkillId::new(uuid::Uuid::now_v7());

        let item_skill = ItemSkill::builder()
            .id(id.clone())
            .item_id(item_id.clone())
            .skill_id(skill_id.clone())
            .build();

        assert_eq!(item_skill.id.get_id(), id.get_id());
        assert_eq!(item_skill.item_id.get_id(), item_id.get_id());
        assert_eq!(item_skill.skill_id.get_id(), skill_id.get_id());
    }

    #[test]
    fn test_getters_and_setters() {
        let mut item_skill = ItemSkill::default();

        let id = ItemSkillId::new(uuid::Uuid::now_v7());
        let item_id = ItemId::new(uuid::Uuid::now_v7());
        let skill_id = SkillId::new(uuid::Uuid::now_v7());

        item_skill.set_id(id.clone());
        item_skill.set_item_id(item_id.clone());
        item_skill.set_skill_id(skill_id.clone());

        assert_eq!(item_skill.id().get_id(), id.get_id());
        assert_eq!(item_skill.item_id().get_id(), item_id.get_id());
        assert_eq!(item_skill.skill_id().get_id(), skill_id.get_id());
    }
}
