use chrono::{DateTime, Utc};
use uuid::Uuid;

#[derive(<PERSON>bu<PERSON>, <PERSON><PERSON>)]
pub struct CreateUserCommand {
    pub id: Uuid,
    pub username: String,
    pub email: String,
    pub role: String,
    pub gender: String,
    pub password: String,
}

#[derive(<PERSON>bu<PERSON>, <PERSON><PERSON>)]
pub struct RequestPasswordResetCommand {
    pub id: Uuid,
    pub email: String,
    pub reset_token: String,
    pub created_at: DateTime<Utc>,
    pub expires_at: DateTime<Utc>,
}

#[derive(Debug, Clone)]
pub struct ChangeUserPasswordCommand {
    pub user_id: Uuid,
    pub email: String,
    pub new_password: String,
    pub token: String,
    pub token_id: Uuid,
}

#[derive(Debug, <PERSON>lone)]
pub enum UserCommands {
    CreateUser(CreateUserCommand),
    RequestPasswordReset(RequestPasswordResetCommand),
    ChangeUserPassword(ChangeUserPasswordCommand),
}
