use reforged_domain::models::user_login::entity::UserLogin;
use reforged_domain::models::user_login::value_object::UserLoginId;
use reforged_domain::models::user::value_object::UserId;
use chrono::{DateTime, Utc};

#[derive(Debug)]
pub struct UserLoginDbModelMapper(super::super::models::user_logins::Model);

impl UserLoginDbModelMapper {
    pub fn new(model: super::super::models::user_logins::Model) -> Self {
        Self(model)
    }
}

impl From<UserLoginDbModelMapper> for UserLogin {
    fn from(value: UserLoginDbModelMapper) -> Self {
        let model = value.0;

        UserLogin::builder()
            .id(UserLoginId::new(model.id))
            .user_id(UserId::new(model.user_id))
            .location(model.location.into())
            .status(model.status.into())
            .address(model.address.into())
            .date(DateTime::<Utc>::from_naive_utc_and_offset(model.date, Utc).into())
            .build()
    }
}
