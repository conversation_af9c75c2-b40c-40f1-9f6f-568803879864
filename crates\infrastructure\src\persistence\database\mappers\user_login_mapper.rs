use reforged_domain::models::user_login::entity::{UserLogin, UserLoginId};
use reforged_domain::models::user::value_object::UserId;

#[derive(Debug)]
pub struct UserLoginDbModelMapper(super::super::models::user_logins::Model);

impl UserLoginDbModelMapper {
    pub fn new(model: super::super::models::user_logins::Model) -> Self {
        Self(model)
    }
}

impl From<UserLoginDbModelMapper> for UserLogin {
    fn from(value: UserLoginDbModelMapper) -> Self {
        let model = value.0;
        
        UserLogin::builder()
            .id(UserLoginId::new(model.id))
            .user_id(UserId::new(model.user_id))
            .location(model.location.into())
            .address(model.address.into())
            .date(model.date.into())
            .build()
    }
}
