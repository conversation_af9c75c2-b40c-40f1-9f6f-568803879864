use super::entity::Skill;
use reforged_shared::{UuidId, Value};

pub type SkillId = UuidId<Skill>;

#[derive(Debug, PartialEq, PartialOrd, Clone, Default)]

pub struct SkillName(String);

impl SkillName {
    pub fn new(name: impl Into<String>) -> Self {
        Self(name.into())
    }
}

impl Value<String> for SkillName {
    fn value(&self) -> String {
        self.0.clone()
    }
}

impl From<String> for SkillName {
    fn from(value: String) -> Self {
        Self(value)
    }
}

impl From<&str> for SkillName {
    fn from(value: &str) -> Self {
        Self(value.to_string())
    }
}

#[derive(Debug, PartialEq, PartialOrd, Clone, Default)]

pub struct Animation(String);

impl Animation {
    pub fn new(animation: impl Into<String>) -> Self {
        Self(animation.into())
    }
}

impl Value<String> for Animation {
    fn value(&self) -> String {
        self.0.clone()
    }
}

impl From<String> for Animation {
    fn from(value: String) -> Self {
        Self(value)
    }
}

impl From<&str> for Animation {
    fn from(value: &str) -> Self {
        Self(value.to_string())
    }
}

#[derive(Debug, PartialEq, PartialOrd, Clone, Default)]

pub struct SkillDescription(String);

impl SkillDescription {
    pub fn new(description: impl Into<String>) -> Self {
        Self(description.into())
    }
}

impl Value<String> for SkillDescription {
    fn value(&self) -> String {
        self.0.clone()
    }
}

impl From<String> for SkillDescription {
    fn from(value: String) -> Self {
        Self(value)
    }
}

impl From<&str> for SkillDescription {
    fn from(value: &str) -> Self {
        Self(value.to_string())
    }
}

#[derive(Debug, PartialEq, PartialOrd, Clone, Default)]

pub struct Damage(f64);

impl Damage {
    pub fn new(value: f64) -> Self {
        Self(value)
    }
}

impl Value<f64> for Damage {
    fn value(&self) -> f64 {
        self.0
    }
}

impl From<f64> for Damage {
    fn from(value: f64) -> Self {
        Self(value)
    }
}

#[derive(Debug, PartialEq, PartialOrd, Clone, Default)]

pub struct Mana(i16);

impl Mana {
    pub fn new(value: i16) -> Self {
        Self(value)
    }
}

impl Value<i16> for Mana {
    fn value(&self) -> i16 {
        self.0
    }
}

impl From<i16> for Mana {
    fn from(value: i16) -> Self {
        Self(value)
    }
}

#[derive(Debug, PartialEq, PartialOrd, Clone, Default)]

pub struct ManaBack(i16);

impl ManaBack {
    pub fn new(value: i16) -> Self {
        Self(value)
    }
}

impl Value<i16> for ManaBack {
    fn value(&self) -> i16 {
        self.0
    }
}

impl From<i16> for ManaBack {
    fn from(value: i16) -> Self {
        Self(value)
    }
}

#[derive(Debug, PartialEq, PartialOrd, Clone, Default)]

pub struct LifeSteal(f64);

impl LifeSteal {
    pub fn new(value: f64) -> Self {
        Self(value)
    }
}

impl Value<f64> for LifeSteal {
    fn value(&self) -> f64 {
        self.0
    }
}

impl From<f64> for LifeSteal {
    fn from(value: f64) -> Self {
        Self(value)
    }
}

#[derive(Debug, PartialEq, PartialOrd, Clone, Default)]

pub struct Icon(String);

impl Icon {
    pub fn new(icon: impl Into<String>) -> Self {
        Self(icon.into())
    }
}

impl Value<String> for Icon {
    fn value(&self) -> String {
        self.0.clone()
    }
}

impl From<String> for Icon {
    fn from(value: String) -> Self {
        Self(value)
    }
}

impl From<&str> for Icon {
    fn from(value: &str) -> Self {
        Self(value.to_string())
    }
}

#[derive(Debug, PartialEq, PartialOrd, Clone, Default)]

pub struct Range(i32);

impl Range {
    pub fn new(value: i32) -> Self {
        Self(value)
    }
}

impl Value<i32> for Range {
    fn value(&self) -> i32 {
        self.0
    }
}

impl From<i32> for Range {
    fn from(value: i32) -> Self {
        Self(value)
    }
}

#[derive(Debug, PartialEq, PartialOrd, Clone, Default)]

pub struct Dsrc(String);

impl Dsrc {
    pub fn new(dsrc: impl Into<String>) -> Self {
        Self(dsrc.into())
    }
}

impl Value<String> for Dsrc {
    fn value(&self) -> String {
        self.0.clone()
    }
}

impl From<String> for Dsrc {
    fn from(value: String) -> Self {
        Self(value)
    }
}

impl From<&str> for Dsrc {
    fn from(value: &str) -> Self {
        Self(value.to_string())
    }
}

#[derive(Debug, PartialEq, PartialOrd, Clone, Default)]

pub struct Reference(String);

impl Reference {
    pub fn new(reference: impl Into<String>) -> Self {
        Self(reference.into())
    }
}

impl Value<String> for Reference {
    fn value(&self) -> String {
        self.0.clone()
    }
}

impl From<String> for Reference {
    fn from(value: String) -> Self {
        Self(value)
    }
}

impl From<&str> for Reference {
    fn from(value: &str) -> Self {
        Self(value.to_string())
    }
}

#[derive(Debug, PartialEq, PartialOrd, Clone, Default)]

pub struct Target(String);

impl Target {
    pub fn new(target: impl Into<String>) -> Self {
        Self(target.into())
    }
}

impl Value<String> for Target {
    fn value(&self) -> String {
        self.0.clone()
    }
}

impl From<String> for Target {
    fn from(value: String) -> Self {
        Self(value)
    }
}

impl From<&str> for Target {
    fn from(value: &str) -> Self {
        Self(value.to_string())
    }
}

#[derive(Debug, PartialEq, PartialOrd, Clone, Default)]

pub struct Effects(String);

impl Effects {
    pub fn new(effects: impl Into<String>) -> Self {
        Self(effects.into())
    }
}

impl Value<String> for Effects {
    fn value(&self) -> String {
        self.0.clone()
    }
}

impl From<String> for Effects {
    fn from(value: String) -> Self {
        Self(value)
    }
}

impl From<&str> for Effects {
    fn from(value: &str) -> Self {
        Self(value.to_string())
    }
}

#[derive(Debug, PartialEq, PartialOrd, Clone, Default)]

pub struct SkillType(String);

impl SkillType {
    pub fn new(skill_type: impl Into<String>) -> Self {
        Self(skill_type.into())
    }
}

impl Value<String> for SkillType {
    fn value(&self) -> String {
        self.0.clone()
    }
}

impl From<String> for SkillType {
    fn from(value: String) -> Self {
        Self(value)
    }
}

impl From<&str> for SkillType {
    fn from(value: &str) -> Self {
        Self(value.to_string())
    }
}

#[derive(Debug, PartialEq, PartialOrd, Clone, Default)]

pub struct Strl(String);

impl Strl {
    pub fn new(strl: impl Into<String>) -> Self {
        Self(strl.into())
    }
}

impl Value<String> for Strl {
    fn value(&self) -> String {
        self.0.clone()
    }
}

impl From<String> for Strl {
    fn from(value: String) -> Self {
        Self(value)
    }
}

impl From<&str> for Strl {
    fn from(value: &str) -> Self {
        Self(value.to_string())
    }
}

#[derive(Debug, PartialEq, PartialOrd, Clone, Default)]

pub struct Cooldown(i32);

impl Cooldown {
    pub fn new(value: i32) -> Self {
        Self(value)
    }
}

impl Value<i32> for Cooldown {
    fn value(&self) -> i32 {
        self.0
    }
}

impl From<i32> for Cooldown {
    fn from(value: i32) -> Self {
        Self(value)
    }
}

#[derive(Debug, PartialEq, PartialOrd, Clone, Default)]

pub struct HitTargets(i16);

impl HitTargets {
    pub fn new(value: i16) -> Self {
        Self(value)
    }
}

impl Value<i16> for HitTargets {
    fn value(&self) -> i16 {
        self.0
    }
}

impl From<i16> for HitTargets {
    fn from(value: i16) -> Self {
        Self(value)
    }
}

#[derive(Debug, PartialEq, PartialOrd, Clone, Default)]

pub struct Pet(bool);

impl Pet {
    pub fn new(value: bool) -> Self {
        Self(value)
    }
}

impl Value<bool> for Pet {
    fn value(&self) -> bool {
        self.0
    }
}

impl From<bool> for Pet {
    fn from(value: bool) -> Self {
        Self(value)
    }
}

impl From<i32> for Pet {
    fn from(value: i32) -> Self {
        Self(value != 0)
    }
}

#[derive(Debug, PartialEq, PartialOrd, Clone, Default)]

pub struct Chance(Option<f64>);

impl Chance {
    pub fn new(value: Option<f64>) -> Self {
        Self(value)
    }
}

impl Value<Option<f64>> for Chance {
    fn value(&self) -> Option<f64> {
        self.0
    }
}

impl From<Option<f64>> for Chance {
    fn from(value: Option<f64>) -> Self {
        Self(value)
    }
}

impl From<f64> for Chance {
    fn from(value: f64) -> Self {
        Self(Some(value))
    }
}

#[derive(Debug, PartialEq, PartialOrd, Clone, Default)]

pub struct ShowDamage(bool);

impl ShowDamage {
    pub fn new(value: bool) -> Self {
        Self(value)
    }
}

impl Value<bool> for ShowDamage {
    fn value(&self) -> bool {
        self.0
    }
}

impl From<bool> for ShowDamage {
    fn from(value: bool) -> Self {
        Self(value)
    }
}
