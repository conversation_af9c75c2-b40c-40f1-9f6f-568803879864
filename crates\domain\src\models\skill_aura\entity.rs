use crate::models::aura::value_object::AuraId;
use crate::models::skill::value_object::SkillId;
use crate::models::skill_aura::value_object::*;
use getset::{<PERSON><PERSON>, Set<PERSON>};

#[derive(<PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, bon::<PERSON><PERSON><PERSON>, <PERSON><PERSON>, Set<PERSON>)]
#[getset(get = "pub", set = "pub")]
pub struct SkillAura {
    pub id: SkillAuraId,
    pub skill_id: SkillId,
    pub aura_id: AuraId,
}

impl SkillAura {
    pub fn new(id: SkillAuraId, skill_id: SkillId, aura_id: AuraId) -> Self {
        Self {
            id,
            skill_id,
            aura_id,
        }
    }
}

#[cfg(test)]
mod tests {
    use reforged_shared::IdTrait;

    use super::*;

    #[test]
    fn test_skill_aura_new() {
        let id = SkillAuraId::new(uuid::Uuid::now_v7());
        let skill_id = SkillId::new(uuid::Uuid::now_v7());
        let aura_id = AuraId::new(uuid::Uuid::now_v7());

        let skill_aura = SkillAura::new(id.clone(), skill_id.clone(), aura_id.clone());

        assert_eq!(skill_aura.id.get_id(), id.get_id());
        assert_eq!(skill_aura.skill_id.get_id(), skill_id.get_id());
        assert_eq!(skill_aura.aura_id.get_id(), aura_id.get_id());
    }

    #[test]
    fn test_skill_aura_builder() {
        let id = SkillAuraId::new(uuid::Uuid::now_v7());
        let skill_id = SkillId::new(uuid::Uuid::now_v7());
        let aura_id = AuraId::new(uuid::Uuid::now_v7());

        let skill_aura = SkillAura::builder()
            .id(id.clone())
            .skill_id(skill_id.clone())
            .aura_id(aura_id.clone())
            .build();

        assert_eq!(skill_aura.id.get_id(), id.get_id());
        assert_eq!(skill_aura.skill_id.get_id(), skill_id.get_id());
        assert_eq!(skill_aura.aura_id.get_id(), aura_id.get_id());
    }

    #[test]
    fn test_getters_and_setters() {
        let mut skill_aura = SkillAura::default();

        let id = SkillAuraId::new(uuid::Uuid::now_v7());
        let skill_id = SkillId::new(uuid::Uuid::now_v7());
        let aura_id = AuraId::new(uuid::Uuid::now_v7());

        skill_aura.set_id(id.clone());
        skill_aura.set_skill_id(skill_id.clone());
        skill_aura.set_aura_id(aura_id.clone());

        assert_eq!(skill_aura.id().get_id(), id.get_id());
        assert_eq!(skill_aura.skill_id().get_id(), skill_id.get_id());
        assert_eq!(skill_aura.aura_id().get_id(), aura_id.get_id());
    }
}
