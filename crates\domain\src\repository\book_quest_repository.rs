use async_trait::async_trait;
use mockall::automock;

use crate::error::RepositoryError;
use crate::models::book_quest::entity::BookQuest;
use crate::models::book_quest::value_object::BookQuestId;

#[automock]
#[async_trait]
pub trait BookQuestRepository: Send + Sync {
    async fn save(&self, entity: &BookQuest) -> Result<(), RepositoryError>;
    async fn update(&self, entity: &BookQuest) -> Result<(), RepositoryError>;
    async fn delete(&self, id: &BookQuestId) -> Result<(), RepositoryError>;
}

#[automock]
#[async_trait]
pub trait BookQuestReadRepository: Send + Sync {
    async fn find_by_id(&self, id: &BookQuestId) -> Result<Option<BookQuest>, RepositoryError>;
    async fn find_all(&self) -> Result<Vec<BookQuest>, RepositoryError>;
}
