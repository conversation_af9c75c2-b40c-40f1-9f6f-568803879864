//! `SeaORM` Entity, @generated by sea-orm-codegen 1.1.0

use sea_orm::entity::prelude::*;
use serde::Deserialize;

#[derive(<PERSON><PERSON>, Debug, PartialEq, DeriveEntityModel, Eq, Deserialize)]
#[sea_orm(table_name = "auras")]
pub struct Model {
    #[sea_orm(primary_key, auto_increment = false)]
    pub id: Uuid,
    pub name: String,
    pub duration: i16,
    pub category: String,
    pub damage_increase: Decimal,
    pub damage_taken_decrease: Decimal,
    pub chance: Decimal,
    pub self_: bool,
}

#[derive(Co<PERSON>, Clone, Debug, EnumIter, DeriveRelation)]
pub enum Relation {
    #[sea_orm(has_many = "super::aura_effects::Entity")]
    AuraEffects,
    #[sea_orm(has_many = "super::skill_auras::Entity")]
    SkillAuras,
}

impl Related<super::aura_effects::Entity> for Entity {
    fn to() -> RelationDef {
        Relation::AuraEffects.def()
    }
}

impl Related<super::skill_auras::Entity> for Entity {
    fn to() -> RelationDef {
        Relation::SkillAuras.def()
    }
}

impl ActiveModelBehavior for ActiveModel {}
