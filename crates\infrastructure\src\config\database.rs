use std::net::Ipv4Addr;

use super::{Config, ConfigError};

pub struct DatabaseConfig {
    pub protocol: String,
    pub host: Ipv4Addr,
    pub port: u16,
    pub username: String,
    pub password: String,
    pub database: String,
}

impl DatabaseConfig {
    pub fn get_connection_string(&self) -> String {
        let protocol = &self.protocol;
        let host = &self.host.to_string();
        let port = &self.port;
        let username = &self.username;
        let password = &self.password;
        let database = &self.database;

        format!("{protocol}://{username}:{password}@{host}:{port}/{database}")
    }
}

impl Config for DatabaseConfig {
    fn from_env() -> Result<Self, ConfigError> {
        let protocol = std::env::var("DATABASE_PROTOCOL")
            .map_err(|_| ConfigError::EnvVarNotFound("DATABASE_PROTOCOL".to_string()))?;
        let host = std::env::var("DATABASE_HOST")
            .map_err(|_| ConfigError::EnvVarNotFound("DATABASE_HOST".to_string()))?
            .parse::<Ipv4Addr>()
            .map_err(|_| ConfigError::EnvVarNotValid("DATABASE_HOST".to_string()))?;
        let port = std::env::var("DATABASE_PORT")
            .map_err(|_| ConfigError::EnvVarNotFound("DATABASE_PORT".to_string()))?
            .parse::<u16>()
            .map_err(|_| ConfigError::EnvVarNotValid("DATABASE_PORT".to_string()))?;
        let username = std::env::var("DATABASE_USERNAME")
            .map_err(|_| ConfigError::EnvVarNotFound("DATABASE_USERNAME".to_string()))?;
        let password = std::env::var("DATABASE_PASSWORD")
            .map_err(|_| ConfigError::EnvVarNotFound("DATABASE_PASSWORD".to_string()))?;
        let database = std::env::var("DATABASE_NAME")
            .map_err(|_| ConfigError::EnvVarNotFound("DATABASE_NAME".to_string()))?;

        Ok(Self {
            protocol,
            host,
            port,
            username,
            password,
            database,
        })
    }
}
