use getset::{<PERSON><PERSON>, <PERSON><PERSON>};

use crate::models::{map::value_object::MapId, shop::value_object::ShopId};

use super::value_object::*;

#[derive(<PERSON>bu<PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, bon::<PERSON><PERSON><PERSON>, <PERSON><PERSON>, Set<PERSON>)]
#[getset(get = "pub", set = "pub")]
pub struct ShopLocation {
    id: ShopLocationId,
    shop_id: ShopId,
    map_id: MapId,
}

impl ShopLocation {
    pub fn new(id: ShopLocationId, shop_id: ShopId, map_id: MapId) -> Self {
        Self {
            id,
            shop_id,
            map_id,
        }
    }
}

#[cfg(test)]
mod tests {
    use reforged_shared::IdTrait;

    use super::*;

    #[test]
    fn test_shop_location_new() {
        let id = ShopLocationId::new(uuid::Uuid::now_v7());
        let shop_id = ShopId::new(uuid::Uuid::now_v7());
        let map_id = MapId::new(uuid::Uuid::now_v7());

        let shop_location = ShopLocation::new(id.clone(), shop_id.clone(), map_id.clone());

        assert_eq!(shop_location.id.get_id(), id.get_id());
        assert_eq!(shop_location.shop_id.get_id(), shop_id.get_id());
        assert_eq!(shop_location.map_id.get_id(), map_id.get_id());
    }

    #[test]
    fn test_shop_location_builder() {
        let id = ShopLocationId::new(uuid::Uuid::now_v7());
        let shop_id = ShopId::new(uuid::Uuid::now_v7());
        let map_id = MapId::new(uuid::Uuid::now_v7());

        let shop_location = ShopLocation::builder()
            .id(id.clone())
            .shop_id(shop_id.clone())
            .map_id(map_id.clone())
            .build();

        assert_eq!(shop_location.id.get_id(), id.get_id());
        assert_eq!(shop_location.shop_id.get_id(), shop_id.get_id());
        assert_eq!(shop_location.map_id.get_id(), map_id.get_id());
    }

    #[test]
    fn test_getters_and_setters() {
        let mut shop_location = ShopLocation::default();

        let id = ShopLocationId::new(uuid::Uuid::now_v7());
        let shop_id = ShopId::new(uuid::Uuid::now_v7());
        let map_id = MapId::new(uuid::Uuid::now_v7());

        shop_location.set_id(id.clone());
        shop_location.set_shop_id(shop_id.clone());
        shop_location.set_map_id(map_id.clone());

        assert_eq!(shop_location.id().get_id(), id.get_id());
        assert_eq!(shop_location.shop_id().get_id(), shop_id.get_id());
        assert_eq!(shop_location.map_id().get_id(), map_id.get_id());
    }
}
