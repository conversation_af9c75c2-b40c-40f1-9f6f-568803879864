INSERT INTO "hairs" ("id", "gender", "name", "file") VALUES (1, 'M', 'Bald', 'hair/M/Bald.swf');
INSERT INTO "hairs" ("id", "gender", "name", "file") VALUES (2, 'F', 'Bald', 'hair/F/Bald.swf');
INSERT INTO "hairs" ("id", "gender", "name", "file") VALUES (3, 'M', 'Default', 'hair/M/Default.swf');
INSERT INTO "hairs" ("id", "gender", "name", "file") VALUES (4, 'F', 'Pig1Bangs1', 'hair/F/Pig1Bangs1.swf');
INSERT INTO "hairs" ("id", "gender", "name", "file") VALUES (5, 'M', 'Blank', 'hair/M/Blank.swf');
INSERT INTO "hairs" ("id", "gender", "name", "file") VALUES (6, 'M', 'BaldBeard', 'hair/M/BaldBeard.swf');
INSERT INTO "hairs" ("id", "gender", "name", "file") VALUES (7, 'M', 'BaldStache', 'hair/M/BaldStache.swf');
INSERT INTO "hairs" ("id", "gender", "name", "file") VALUES (8, 'M', 'Bangs22', 'hair/M/Bangs22.swf');
INSERT INTO "hairs" ("id", "gender", "name", "file") VALUES (9, 'M', 'Bangs22Beard', 'hair/M/Bangs22Beard.swf');
INSERT INTO "hairs" ("id", "gender", "name", "file") VALUES (10, 'M', 'Bangs22Stache', 'hair/M/Bangs22Stache.swf');
INSERT INTO "hairs" ("id", "gender", "name", "file") VALUES (11, 'M', 'Bob', 'hair/M/Bob.swf');
INSERT INTO "hairs" ("id", "gender", "name", "file") VALUES (12, 'M', 'BobBeard', 'hair/M/BobBeard.swf');
INSERT INTO "hairs" ("id", "gender", "name", "file") VALUES (13, 'M', 'BobStache', 'hair/M/BobStache.swf');
INSERT INTO "hairs" ("id", "gender", "name", "file") VALUES (14, 'M', 'Braid1', 'hair/m/Braid1.swf');
INSERT INTO "hairs" ("id", "gender", "name", "file") VALUES (15, 'M', 'Braid1Beard', 'hair/M/Braid1Beard.swf');
INSERT INTO "hairs" ("id", "gender", "name", "file") VALUES (16, 'M', 'Braid1Stache', 'hair/M/Braid1Stache.swf');
INSERT INTO "hairs" ("id", "gender", "name", "file") VALUES (17, 'M', 'Curl', 'hair/M/Curl.swf');
INSERT INTO "hairs" ("id", "gender", "name", "file") VALUES (18, 'M', 'CurlBeard', 'hair/M/CurlBeard.swf');
INSERT INTO "hairs" ("id", "gender", "name", "file") VALUES (19, 'M', 'CurlStache', 'hair/M/CurlStache.swf');
INSERT INTO "hairs" ("id", "gender", "name", "file") VALUES (20, 'M', 'DefaultBeard', 'hair/M/DefaultBeard.swf');
INSERT INTO "hairs" ("id", "gender", "name", "file") VALUES (21, 'M', 'DefaultStache', 'hair/M/DefaultStache.swf');
INSERT INTO "hairs" ("id", "gender", "name", "file") VALUES (22, 'M', 'Goku1', 'hair/M/Goku1.swf');
INSERT INTO "hairs" ("id", "gender", "name", "file") VALUES (23, 'M', 'Goku1Beard', 'hair/M/Goku1Beard.swf');
INSERT INTO "hairs" ("id", "gender", "name", "file") VALUES (24, 'M', 'Goku1Stache', 'hair/M/Goku1Stache.swf');
INSERT INTO "hairs" ("id", "gender", "name", "file") VALUES (25, 'M', 'Goku2', 'hair/M/Goku2.swf');
INSERT INTO "hairs" ("id", "gender", "name", "file") VALUES (26, 'M', 'Goku2Beard', 'hair/M/Goku2Beard.swf');
INSERT INTO "hairs" ("id", "gender", "name", "file") VALUES (27, 'M', 'Goku2Stache', 'hair/M/Goku2Stache.swf');
INSERT INTO "hairs" ("id", "gender", "name", "file") VALUES (28, 'M', 'Normal', 'hair/M/Normal.swf');
INSERT INTO "hairs" ("id", "gender", "name", "file") VALUES (29, 'M', 'NormalBeard', 'hair/M/NormalBeard.swf');
INSERT INTO "hairs" ("id", "gender", "name", "file") VALUES (30, 'M', 'NormalStache', 'hair/M/NormalStache.swf');
INSERT INTO "hairs" ("id", "gender", "name", "file") VALUES (31, 'M', 'Normal2', 'hair/M/Normal2.swf');
INSERT INTO "hairs" ("id", "gender", "name", "file") VALUES (32, 'M', 'Normal2Beard', 'hair/M/Normal2Beard.swf');
INSERT INTO "hairs" ("id", "gender", "name", "file") VALUES (33, 'M', 'Normal2Stache', 'hair/M/Normal2Stache.swf');
INSERT INTO "hairs" ("id", "gender", "name", "file") VALUES (34, 'M', 'Pompadour', 'hair/M/Pompadour.swf');
INSERT INTO "hairs" ("id", "gender", "name", "file") VALUES (35, 'M', 'PompadourBeard', 'hair/M/PompadourBeard.swf');
INSERT INTO "hairs" ("id", "gender", "name", "file") VALUES (36, 'M', 'PompadourStache', 'hair/M/PompadourStache.swf');
INSERT INTO "hairs" ("id", "gender", "name", "file") VALUES (37, 'M', 'Spikedown', 'hair/M/Spikedown.swf');
INSERT INTO "hairs" ("id", "gender", "name", "file") VALUES (38, 'M', 'SpikedownBeard', 'hair/M/SpikedownBeard.swf');
INSERT INTO "hairs" ("id", "gender", "name", "file") VALUES (39, 'M', 'SpikedownStache', 'hair/M/SpikedownStache.swf');
INSERT INTO "hairs" ("id", "gender", "name", "file") VALUES (40, 'M', 'Tonsure', 'hair/M/Tonsure.swf');
INSERT INTO "hairs" ("id", "gender", "name", "file") VALUES (41, 'M', 'TonsureBeard', 'hair/M/TonsureBeard.swf');
INSERT INTO "hairs" ("id", "gender", "name", "file") VALUES (42, 'M', 'TonsureStache', 'hair/M/TonsureStache.swf');
INSERT INTO "hairs" ("id", "gender", "name", "file") VALUES (43, 'M', 'Wavy', 'hair/M/Wavy.swf');
INSERT INTO "hairs" ("id", "gender", "name", "file") VALUES (44, 'M', 'WavyBeard', 'hair/M/WavyBeard.swf');
INSERT INTO "hairs" ("id", "gender", "name", "file") VALUES (45, 'M', 'WavyStache', 'hair/M/WavyStache.swf');
INSERT INTO "hairs" ("id", "gender", "name", "file") VALUES (46, 'M', 'Zhoom', 'hair/M/Zhoom.swf');
INSERT INTO "hairs" ("id", "gender", "name", "file") VALUES (47, 'M', 'ZhoomBeard', 'hair/M/ZhoomBeard.swf');
INSERT INTO "hairs" ("id", "gender", "name", "file") VALUES (48, 'M', 'ZhoomStache', 'hair/M/ZhoomStache.swf');
INSERT INTO "hairs" ("id", "gender", "name", "file") VALUES (49, 'M', 'Ponytail8', 'hair/M/Ponytail8.swf');
INSERT INTO "hairs" ("id", "gender", "name", "file") VALUES (50, 'M', 'Ponytail7', 'hair/M/Ponytail7.swf');
INSERT INTO "hairs" ("id", "gender", "name", "file") VALUES (51, 'M', 'Ponytail6', 'hair/M/Ponytail6.swf');
INSERT INTO "hairs" ("id", "gender", "name", "file") VALUES (52, 'M', 'Ponytail5', 'hair/M/Ponytail5-1Oct13.swf');
INSERT INTO "hairs" ("id", "gender", "name", "file") VALUES (53, 'M', 'Ponytail4', 'hair/M/Ponytail4.swf');
INSERT INTO "hairs" ("id", "gender", "name", "file") VALUES (54, 'M', 'Slickback1', 'hair/M/Slickback1.swf');
INSERT INTO "hairs" ("id", "gender", "name", "file") VALUES (55, 'M', 'Mohawk1', 'hair/M/Mohawk1.swf');
INSERT INTO "hairs" ("id", "gender", "name", "file") VALUES (56, 'M', 'Mohawk2', 'hair/M/Mohawk2.swf');
INSERT INTO "hairs" ("id", "gender", "name", "file") VALUES (57, 'M', 'Mohawk3', 'hair/M/Mohawk3.swf');
INSERT INTO "hairs" ("id", "gender", "name", "file") VALUES (58, 'M', 'Conan1', 'hair/M/Conan1.swf');
INSERT INTO "hairs" ("id", "gender", "name", "file") VALUES (59, 'M', 'Ponytail3', 'hair/M/Ponytail3.swf');
INSERT INTO "hairs" ("id", "gender", "name", "file") VALUES (60, 'M', 'Conan2', 'hair/M/Conan2.swf');
INSERT INTO "hairs" ("id", "gender", "name", "file") VALUES (61, 'M', 'Conan3', 'hair/M/Conan3.swf');
INSERT INTO "hairs" ("id", "gender", "name", "file") VALUES (62, 'M', 'Ponytail', 'hair/M/Ponytail.swf');
INSERT INTO "hairs" ("id", "gender", "name", "file") VALUES (63, 'M', 'Slickback2', 'hair/M/Slickback2.swf');
INSERT INTO "hairs" ("id", "gender", "name", "file") VALUES (64, 'M', 'Feathered1', 'hair/M/Feathered1.swf');
INSERT INTO "hairs" ("id", "gender", "name", "file") VALUES (65, 'M', 'Feathered2', 'hair/M/Feathered2.swf');
INSERT INTO "hairs" ("id", "gender", "name", "file") VALUES (66, 'M', 'Halo1', 'hair/M/Halo1.swf');
INSERT INTO "hairs" ("id", "gender", "name", "file") VALUES (67, 'M', 'Fro1', 'hair/M/Fro1.swf');
INSERT INTO "hairs" ("id", "gender", "name", "file") VALUES (68, 'M', 'Fro2', 'hair/M/Fro2.swf');
INSERT INTO "hairs" ("id", "gender", "name", "file") VALUES (69, 'M', 'Mohawk4', 'hair/M/Mohawk4.swf');
INSERT INTO "hairs" ("id", "gender", "name", "file") VALUES (70, 'M', 'Glowingeyes1', 'hair/M/Glowingeyes1.swf');
INSERT INTO "hairs" ("id", "gender", "name", "file") VALUES (71, 'M', 'Glowingeyes2', 'hair/M/Glowingeyes2.swf');
INSERT INTO "hairs" ("id", "gender", "name", "file") VALUES (72, 'M', 'Miltonius', 'hair/M/Miltonius.swf');
INSERT INTO "hairs" ("id", "gender", "name", "file") VALUES (73, 'M', 'Ponytail9', 'hair/M/Ponytail9.swf');
INSERT INTO "hairs" ("id", "gender", "name", "file") VALUES (74, 'M', 'Beard1', 'hair/M/Beard1.swf');
INSERT INTO "hairs" ("id", "gender", "name", "file") VALUES (75, 'M', 'Beard2', 'hair/M/Beard2.swf');
INSERT INTO "hairs" ("id", "gender", "name", "file") VALUES (76, 'M', 'Beard3', 'hair/M/Beard3.swf');
INSERT INTO "hairs" ("id", "gender", "name", "file") VALUES (77, 'M', 'Widowspeak1', 'hair/M/Widowspeak1.swf');
INSERT INTO "hairs" ("id", "gender", "name", "file") VALUES (78, 'M', 'Wildbeard1', 'hair/M/Wildbeard1.swf');
INSERT INTO "hairs" ("id", "gender", "name", "file") VALUES (79, 'M', 'Wildbeard2', 'hair/M/Wildbeard2.swf');
INSERT INTO "hairs" ("id", "gender", "name", "file") VALUES (80, 'M', 'Long1', 'hair/M/Long1.swf');
INSERT INTO "hairs" ("id", "gender", "name", "file") VALUES (81, 'M', 'Braided1', 'hair/M/Braided1.swf');
INSERT INTO "hairs" ("id", "gender", "name", "file") VALUES (82, 'M', 'Rennhair1', 'hair/M/Rennhair1.swf');
INSERT INTO "hairs" ("id", "gender", "name", "file") VALUES (83, 'M', 'Ziohair1', 'hair/M/Ziohair1.swf');
INSERT INTO "hairs" ("id", "gender", "name", "file") VALUES (84, 'M', 'Ryuuji', 'hair/M/Ryuuji.swf');
INSERT INTO "hairs" ("id", "gender", "name", "file") VALUES (85, 'M', 'SummerHairShort', 'hair/M/BidoofSummerHair.swf');
INSERT INTO "hairs" ("id", "gender", "name", "file") VALUES (86, 'F', 'Blank', 'hair/F/Blank.swf');
INSERT INTO "hairs" ("id", "gender", "name", "file") VALUES (87, 'F', 'Bangs1', 'hair/F/Bangs1.swf');
INSERT INTO "hairs" ("id", "gender", "name", "file") VALUES (88, 'F', 'Bangs2', 'hair/F/Bangs2.swf');
INSERT INTO "hairs" ("id", "gender", "name", "file") VALUES (89, 'F', 'Bangs3', 'hair/F/Bangs3.swf');
INSERT INTO "hairs" ("id", "gender", "name", "file") VALUES (90, 'F', 'Braid2', 'hair/F/Braid2.swf');
INSERT INTO "hairs" ("id", "gender", "name", "file") VALUES (91, 'F', 'Braid2Bangs1', 'hair/F/Braid2Bangs1.swf');
INSERT INTO "hairs" ("id", "gender", "name", "file") VALUES (92, 'F', 'Braid3', 'hair/F/Braid3.swf');
INSERT INTO "hairs" ("id", "gender", "name", "file") VALUES (93, 'F', 'Braid3Bangs1', 'hair/F/Braid3Bangs1.swf');
INSERT INTO "hairs" ("id", "gender", "name", "file") VALUES (94, 'F', 'Braid3Bangs2', 'hair/F/Braid3Bangs2.swf');
INSERT INTO "hairs" ("id", "gender", "name", "file") VALUES (95, 'F', 'Pig1Bangs2', 'hair/F/Pig1Bangs2.swf');
INSERT INTO "hairs" ("id", "gender", "name", "file") VALUES (96, 'F', 'Pig2', 'hair/F/Pig2.swf');
INSERT INTO "hairs" ("id", "gender", "name", "file") VALUES (97, 'F', 'Pig2Bangs1', 'hair/F/Pig2Bangs1.swf');
INSERT INTO "hairs" ("id", "gender", "name", "file") VALUES (98, 'F', 'Pig2Bangs2', 'hair/F/Pig2Bangs2.swf');
INSERT INTO "hairs" ("id", "gender", "name", "file") VALUES (99, 'F', 'Pob', 'hair/F/Pob.swf');
INSERT INTO "hairs" ("id", "gender", "name", "file") VALUES (100, 'F', 'Pony1Bangs1', 'hair/F/Pony1Bangs1.swf');
INSERT INTO "hairs" ("id", "gender", "name", "file") VALUES (101, 'F', 'Pony1Bangs2', 'hair/F/Pony1Bangs2.swf');
INSERT INTO "hairs" ("id", "gender", "name", "file") VALUES (102, 'F', 'Pony1Bangs2Long', 'hair/F/Pony1Bangs2Long.swf');
INSERT INTO "hairs" ("id", "gender", "name", "file") VALUES (103, 'F', 'Pony1Long', 'hair/F/Pony1Long.swf');
INSERT INTO "hairs" ("id", "gender", "name", "file") VALUES (104, 'F', 'Pony2', 'hair/F/Pony2.swf');
INSERT INTO "hairs" ("id", "gender", "name", "file") VALUES (105, 'F', 'Pony2Bangs1', 'hair/F/Pony2Bangs1.swf');
INSERT INTO "hairs" ("id", "gender", "name", "file") VALUES (106, 'F', 'Pony2Bangs2', 'hair/F/Pony2Bangs2.swf');
INSERT INTO "hairs" ("id", "gender", "name", "file") VALUES (107, 'F', 'Pony3', 'hair/F/Pony3.swf');
INSERT INTO "hairs" ("id", "gender", "name", "file") VALUES (108, 'F', 'Pony3Bangs1', 'hair/F/Pony3Bangs1.swf');
INSERT INTO "hairs" ("id", "gender", "name", "file") VALUES (109, 'F', 'Pony3Bangs2', 'hair/F/Pony3Bangs2.swf');
INSERT INTO "hairs" ("id", "gender", "name", "file") VALUES (110, 'F', 'Pony4', 'hair/F/Pony4.swf');
INSERT INTO "hairs" ("id", "gender", "name", "file") VALUES (111, 'F', 'Pony4Bangs1', 'hair/F/Pony4Bangs1.swf');
INSERT INTO "hairs" ("id", "gender", "name", "file") VALUES (112, 'F', 'Pony4Bangs2', 'hair/F/Pony4Bangs2.swf');
INSERT INTO "hairs" ("id", "gender", "name", "file") VALUES (113, 'F', 'Pony5', 'hair/F/Pony5.swf');
INSERT INTO "hairs" ("id", "gender", "name", "file") VALUES (114, 'F', 'Pony5Bangs2', 'hair/F/Pony5Bangs2.swf');
INSERT INTO "hairs" ("id", "gender", "name", "file") VALUES (115, 'F', 'Spike1', 'hair/F/Spike1.swf');
INSERT INTO "hairs" ("id", "gender", "name", "file") VALUES (116, 'F', 'Spike2', 'hair/F/Spike2.swf');
INSERT INTO "hairs" ("id", "gender", "name", "file") VALUES (117, 'F', 'Bangs1Long', 'hair/F/Bangs1Long.swf');
INSERT INTO "hairs" ("id", "gender", "name", "file") VALUES (118, 'F', 'Bangs2Long', 'hair/F/Bangs2Long.swf');
INSERT INTO "hairs" ("id", "gender", "name", "file") VALUES (119, 'F', 'Bangs3Long', 'hair/F/Bangs3Long.swf');
INSERT INTO "hairs" ("id", "gender", "name", "file") VALUES (120, 'F', 'Ponytail1', 'hair/F/Ponytail1.swf');
INSERT INTO "hairs" ("id", "gender", "name", "file") VALUES (121, 'F', 'Halo1', 'hair/F/Halo1.swf');
INSERT INTO "hairs" ("id", "gender", "name", "file") VALUES (122, 'F', 'Halo2', 'hair/F/Halo2.swf');
INSERT INTO "hairs" ("id", "gender", "name", "file") VALUES (123, 'F', 'Halo3', 'hair/F/Halo3.swf');
INSERT INTO "hairs" ("id", "gender", "name", "file") VALUES (124, 'F', 'Ponytail2', 'hair/F/Ponytail2.swf');
INSERT INTO "hairs" ("id", "gender", "name", "file") VALUES (125, 'F', 'Ponytail3', 'hair/F/Ponytail3.swf');
INSERT INTO "hairs" ("id", "gender", "name", "file") VALUES (126, 'F', 'Ponytail4', 'hair/F/Ponytail4.swf');
INSERT INTO "hairs" ("id", "gender", "name", "file") VALUES (127, 'F', 'Braid1', 'hair/F/Braid1.swf');
INSERT INTO "hairs" ("id", "gender", "name", "file") VALUES (128, 'F', 'Ponytail5', 'hair/F/Ponytail5.swf');
INSERT INTO "hairs" ("id", "gender", "name", "file") VALUES (129, 'F', 'Ponytail6', 'hair/F/Ponytail6.swf');
INSERT INTO "hairs" ("id", "gender", "name", "file") VALUES (130, 'F', 'Ponytail7', 'hair/F/Ponytail7.swf');
INSERT INTO "hairs" ("id", "gender", "name", "file") VALUES (131, 'F', 'Fro1', 'hair/F/Fro1.swf');
INSERT INTO "hairs" ("id", "gender", "name", "file") VALUES (132, 'F', 'Fro2', 'hair/F/Fro2.swf');
INSERT INTO "hairs" ("id", "gender", "name", "file") VALUES (133, 'F', 'Glowingeyes1', 'hair/F/Glowingeyes1.swf');
INSERT INTO "hairs" ("id", "gender", "name", "file") VALUES (134, 'F', 'cat1', 'hair/F/cat1.swf');
INSERT INTO "hairs" ("id", "gender", "name", "file") VALUES (135, 'F', 'Glowingeyes2', 'hair/F/Glowingeyes2.swf');
INSERT INTO "hairs" ("id", "gender", "name", "file") VALUES (136, 'F', 'Curls', 'hair/F/Curls.swf');
INSERT INTO "hairs" ("id", "gender", "name", "file") VALUES (137, 'F', 'Bang22', 'hair/F/Bang22.swf');
INSERT INTO "hairs" ("id", "gender", "name", "file") VALUES (138, 'F', 'Formal', 'hair/F/formal.swf');
INSERT INTO "hairs" ("id", "gender", "name", "file") VALUES (139, 'F', 'Saf1', 'hair/F/saf1.swf');
INSERT INTO "hairs" ("id", "gender", "name", "file") VALUES (140, 'F', 'Betty1', 'hair/F/betty1.swf');
INSERT INTO "hairs" ("id", "gender", "name", "file") VALUES (141, 'F', 'Saf2', 'hair/F/saf2.swf');
INSERT INTO "hairs" ("id", "gender", "name", "file") VALUES (142, 'F', 'Long1', 'hair/F/Long1.swf');
INSERT INTO "hairs" ("id", "gender", "name", "file") VALUES (143, 'F', 'Braided3', 'hair/F/Braided3.swf');
INSERT INTO "hairs" ("id", "gender", "name", "file") VALUES (144, 'F', 'Braided2', 'hair/F/Braided2.swf');
INSERT INTO "hairs" ("id", "gender", "name", "file") VALUES (145, 'F', 'Braided1', 'hair/F/Braided1.swf');
INSERT INTO "hairs" ("id", "gender", "name", "file") VALUES (146, 'F', 'Rennhair1', 'hair/F/Rennhair1.swf');
INSERT INTO "hairs" ("id", "gender", "name", "file") VALUES (147, 'F', 'Ziohair1', 'hair/F/Ziohair1.swf');
INSERT INTO "hairs" ("id", "gender", "name", "file") VALUES (148, 'F', 'Cute1', 'hair/F/Cute1.swf');
INSERT INTO "hairs" ("id", "gender", "name", "file") VALUES (149, 'F', 'Ryuuji', 'hair/F/Ryuuji.swf');
INSERT INTO "hairs" ("id", "gender", "name", "file") VALUES (150, 'F', 'SummerHairLong', 'hair/F/BidoofSummerHairLong.swf');
INSERT INTO "hairs" ("id", "gender", "name", "file") VALUES (151, 'F', 'Bob', 'hair/F/Bob.swf');
INSERT INTO "hairs" ("id", "gender", "name", "file") VALUES (152, 'M', 'DageScene', 'hair/M/DageScene.swf');
