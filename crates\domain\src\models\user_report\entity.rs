use super::value_object::*;
use crate::models::user::value_object::UserId;
use getset::{<PERSON><PERSON>, <PERSON><PERSON>};

#[derive(<PERSON>bu<PERSON>, <PERSON><PERSON>, <PERSON><PERSON>ult, bon::<PERSON><PERSON><PERSON>, <PERSON><PERSON>, Setters)]
#[getset(get = "pub", set = "pub")]
pub struct UserReport {
    id: UserReportId,
    user_id: UserId,
    target_name: TargetName,
    category: ReportCategory,
    description: ReportDescription,
    date_submitted: ReportDate,
}

impl UserReport {
    pub fn new(
        id: UserReportId,
        user_id: UserId,
        target_name: TargetName,
        category: ReportCategory,
        description: ReportDescription,
        date_submitted: ReportDate,
    ) -> Self {
        Self {
            id,
            user_id,
            target_name,
            category,
            description,
            date_submitted,
        }
    }
}

#[cfg(test)]
mod tests {
    use chrono::Utc;
    use reforged_shared::{IdTrait, Value};

    use super::*;

    #[test]
    fn test_user_report_new() {
        let id = UserReportId::new(uuid::Uuid::now_v7());
        let user_id = UserId::new(uuid::Uuid::now_v7());
        let target_name = TargetName::new("target_user".to_string());
        let category = ReportCategory::new("Harassment".to_string());
        let description = ReportDescription::new("Test description".to_string());
        let date_submitted = ReportDate::new(Utc::now());

        let user_report = UserReport::new(
            id.clone(),
            user_id.clone(),
            target_name.clone(),
            category.clone(),
            description.clone(),
            date_submitted.clone(),
        );

        assert_eq!(user_report.id.get_id(), id.get_id());
        assert_eq!(user_report.user_id.get_id(), user_id.get_id());
        assert_eq!(user_report.target_name.value(), target_name.value());
        assert_eq!(user_report.category.value(), category.value());
        assert_eq!(user_report.description.value(), description.value());
        assert_eq!(user_report.date_submitted.value(), date_submitted.value());
    }

    #[test]
    fn test_user_report_builder() {
        let id = UserReportId::new(uuid::Uuid::now_v7());
        let user_id = UserId::new(uuid::Uuid::now_v7());
        let target_name = TargetName::new("target_user".to_string());
        let category = ReportCategory::new("Harassment".to_string());
        let description = ReportDescription::new("Test description".to_string());
        let date_submitted = ReportDate::new(Utc::now());

        let user_report = UserReport::builder()
            .id(id.clone())
            .user_id(user_id.clone())
            .target_name(target_name.clone())
            .category(category.clone())
            .description(description.clone())
            .date_submitted(date_submitted.clone())
            .build();

        assert_eq!(user_report.id.get_id(), id.get_id());
        assert_eq!(user_report.user_id.get_id(), user_id.get_id());
        assert_eq!(user_report.target_name.value(), target_name.value());
        assert_eq!(user_report.category.value(), category.value());
        assert_eq!(user_report.description.value(), description.value());
        assert_eq!(user_report.date_submitted.value(), date_submitted.value());
    }

    #[test]
    fn test_getters_and_setters() {
        let mut user_report = UserReport::default();

        let id = UserReportId::new(uuid::Uuid::now_v7());
        let user_id = UserId::new(uuid::Uuid::now_v7());
        let target_name = TargetName::new("target_user".to_string());
        let category = ReportCategory::new("Harassment".to_string());
        let description = ReportDescription::new("Test description".to_string());
        let date_submitted = ReportDate::new(Utc::now());

        user_report.set_id(id.clone());
        user_report.set_user_id(user_id.clone());
        user_report.set_target_name(target_name.clone());
        user_report.set_category(category.clone());
        user_report.set_description(description.clone());
        user_report.set_date_submitted(date_submitted.clone());

        assert_eq!(user_report.id().get_id(), id.get_id());
        assert_eq!(user_report.user_id().get_id(), user_id.get_id());
        assert_eq!(user_report.target_name().value(), target_name.value());
        assert_eq!(user_report.category().value(), category.value());
        assert_eq!(user_report.description().value(), description.value());
        assert_eq!(user_report.date_submitted().value(), date_submitted.value());
    }
}
