use async_trait::async_trait;
use mockall::automock;

use crate::{
    error::RepositoryError,
    models::skill::{entity::Skill, value_object::SkillId},
};

#[automock]
#[async_trait]
pub trait SkillRepository: Send + Sync {
    async fn save(&self, skill: &Skill) -> Result<Skill, RepositoryError>;
    async fn update(&self, skill: &Skill) -> Result<(), RepositoryError>;
    async fn delete(&self, id: &SkillId) -> Result<(), RepositoryError>;
}

#[automock]
#[async_trait]
pub trait SkillReadRepository: Send + Sync {
    async fn find_by_id(&self, id: &SkillId) -> Result<Option<Skill>, RepositoryError>;
    async fn find_by_name(&self, name: &str) -> Result<Option<Skill>, RepositoryError>;
    async fn find_all(&self) -> Result<Vec<Skill>, RepositoryError>;
    async fn find_paginate(&self, page: u32, limit: u32) -> Result<Vec<Skill>, RepositoryError>;
}
