use getset::{<PERSON><PERSON>, <PERSON><PERSON>};

use crate::models::{store::value_object::StoreId, user::value_object::UserId};

use super::value_object::{
    Broadcast, Currency, Hash, Method, PaymentId, PurchaseDate, PurchaseEmail, PurchaseItem,
    PurchasePrice, Purchased, TransactionId, UserPurchaseId,
};

#[derive(Debug, <PERSON><PERSON>, Default, bon::<PERSON><PERSON>er, Getters, Setters)]
#[getset(get = "pub", set = "pub")]
pub struct UserPurchase {
    id: UserPurchaseId,
    user_id: UserId,
    payment_id: PaymentId,
    transaction_id: TransactionId,
    email: PurchaseEmail,
    hash: Hash,
    item: PurchaseItem,
    item_id: StoreId,
    price: PurchasePrice,
    method: Method,
    currency: Currency,
    purchased: Purchased,
    date: PurchaseDate,
    broadcast: Broadcast,
}

impl UserPurchase {
    #[allow(clippy::too_many_arguments)]
    pub fn new(
        id: UserPurchaseId,
        user_id: UserId,
        payment_id: PaymentId,
        transaction_id: TransactionId,
        email: PurchaseEmail,
        hash: Hash,
        item: PurchaseItem,
        item_id: StoreId,
        price: PurchasePrice,
        method: Method,
        currency: Currency,
        purchased: Purchased,
        date: PurchaseDate,
        broadcast: Broadcast,
    ) -> Self {
        Self {
            id,
            user_id,
            payment_id,
            transaction_id,
            email,
            hash,
            item,
            item_id,
            price,
            method,
            currency,
            purchased,
            date,
            broadcast,
        }
    }
}

#[cfg(test)]
mod tests {
    use chrono::Utc;
    use reforged_shared::{IdTrait, Value};

    use super::*;

    #[test]
    fn test_user_purchase_new() {
        let id = UserPurchaseId::new(uuid::Uuid::now_v7());
        let user_id = UserId::new(uuid::Uuid::now_v7());
        let payment_id = PaymentId::new("PAY123");
        let transaction_id = TransactionId::new(Some("TXN456".to_string()));
        let email = PurchaseEmail::new(Some("<EMAIL>".to_string()));
        let hash = Hash::new("hash123");
        let item = PurchaseItem::new("Test Item");
        let item_id = StoreId::new(uuid::Uuid::now_v7());
        let price = PurchasePrice::new("9.99");
        let method = Method::new("PayPal");
        let currency = Currency::new("USD");
        let purchased = Purchased::new(1);
        let date = PurchaseDate::new(Utc::now());
        let broadcast = Broadcast::new(true);

        let user_purchase = UserPurchase::new(
            id.clone(),
            user_id.clone(),
            payment_id.clone(),
            transaction_id.clone(),
            email.clone(),
            hash.clone(),
            item.clone(),
            item_id.clone(),
            price.clone(),
            method.clone(),
            currency.clone(),
            purchased.clone(),
            date.clone(),
            broadcast.clone(),
        );

        assert_eq!(user_purchase.id().get_id(), id.get_id());
        assert_eq!(user_purchase.user_id().get_id(), user_id.get_id());
        assert_eq!(user_purchase.payment_id().value(), "PAY123");
        assert_eq!(user_purchase.item().value(), "Test Item");
        assert_eq!(user_purchase.price().value(), "9.99");
        assert_eq!(user_purchase.method().value(), "PayPal");
        assert_eq!(user_purchase.currency().value(), "USD");
        assert_eq!(user_purchase.purchased().value(), 1);
        assert_eq!(user_purchase.broadcast().value(), true);
    }
}