use chrono::{DateTime, Utc};
use reforged_domain::models::admin_upload::entity::AdminUpload;
use reforged_domain::models::admin_upload::value_object::{
    AdminUploadDate, AdminUploadId, AdminUploadName, AdminUploadType,
};
use reforged_domain::models::user::value_object::UserId;

#[derive(Debug)]
pub struct AdminUploadDbModelMapper(super::super::models::admin_uploads::Model);

impl AdminUploadDbModelMapper {
    pub fn new(admin_upload: super::super::models::admin_uploads::Model) -> Self {
        Self(admin_upload)
    }
}

impl From<AdminUploadDbModelMapper> for AdminUpload {
    fn from(value: AdminUploadDbModelMapper) -> Self {
        let admin_upload_model = value.0;

        let id = AdminUploadId::new(admin_upload_model.id);
        let user_id = UserId::new(admin_upload_model.user_id);
        let file_name = AdminUploadName::new(admin_upload_model.file_name);
        let r#type = AdminUploadType::new(admin_upload_model.r#type);
        let date = AdminUploadDate::new(DateTime::<Utc>::from_naive_utc_and_offset(
            admin_upload_model.date.and_hms_opt(0, 0, 0).unwrap(),
            Utc,
        ));

        AdminUpload::builder()
            .id(id)
            .user_id(user_id)
            .file_name(file_name)
            .r#type(r#type)
            .date(date)
            .build()
    }
}
