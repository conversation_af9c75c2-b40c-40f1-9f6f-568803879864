use crate::models::item::value_object::File;

use super::value_object::{Command, DiscordCommandId};
use getset::{<PERSON><PERSON>, Set<PERSON>};

#[derive(<PERSON>bu<PERSON>, <PERSON><PERSON>, <PERSON><PERSON>ult, bon::<PERSON><PERSON><PERSON>, <PERSON><PERSON>, Setters)]
#[getset(get = "pub", set = "pub")]
pub struct DiscordCommand {
    id: DiscordCommandId,
    command: Command,
    file: File,
}

impl DiscordCommand {
    pub fn new(id: DiscordCommandId, command: Command, file: File) -> Self {
        Self { id, command, file }
    }
}

#[cfg(test)]
mod tests {
    use reforged_shared::{IdTrait, Value};

    use super::*;

    #[test]
    fn test_discord_command_new() {
        let id = DiscordCommandId::new(uuid::Uuid::now_v7());
        let command = Command::new("!help");
        let file = File::new("help_image.png");

        let discord_command = DiscordCommand::new(id.clone(), command.clone(), file.clone());

        assert_eq!(discord_command.id().get_id(), id.get_id());
        assert_eq!(discord_command.command().value(), command.value());
        assert_eq!(discord_command.file().value(), file.value());
    }

    #[test]
    fn test_discord_command_builder() {
        let id = DiscordCommandId::new(uuid::Uuid::now_v7());
        let command = Command::new("!help");
        let file = File::new("help_image.png");

        let discord_command = DiscordCommand::builder()
            .id(id.clone())
            .command(command.clone())
            .file(file.clone())
            .build();

        assert_eq!(discord_command.id().get_id(), id.get_id());
        assert_eq!(discord_command.command().value(), command.value());
        assert_eq!(discord_command.file().value(), file.value());
    }

    #[test]
    fn test_getters_and_setters() {
        let mut discord_command = DiscordCommand::default();

        let id = DiscordCommandId::new(uuid::Uuid::now_v7());
        let command = Command::new("!help");
        let file = File::new("help_image.png");

        discord_command.set_id(id.clone());
        discord_command.set_command(command.clone());
        discord_command.set_file(file.clone());

        assert_eq!(discord_command.id().get_id(), id.get_id());
        assert_eq!(discord_command.command().value(), command.value());
        assert_eq!(discord_command.file().value(), file.value());
    }
}
