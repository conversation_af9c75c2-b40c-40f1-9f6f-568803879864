use async_trait::async_trait;
use mockall::automock;

use crate::error::RepositoryError;
use crate::models::item_effect::entity::ItemEffect;
use crate::models::item_effect::value_object::ItemEffectId;

#[automock]
#[async_trait]
pub trait ItemEffectRepository: Send + Sync {
    async fn save(&self, entity: &ItemEffect) -> Result<(), RepositoryError>;
    async fn update(&self, entity: &ItemEffect) -> Result<(), RepositoryError>;
    async fn delete(&self, id: &ItemEffectId) -> Result<(), RepositoryError>;
}

#[automock]
#[async_trait]
pub trait ItemEffectReadRepository: Send + Sync {
    async fn find_by_id(&self, id: &ItemEffectId) -> Result<Option<ItemEffect>, RepositoryError>;
    async fn find_all(&self) -> Result<Vec<ItemEffect>, RepositoryError>;
}
