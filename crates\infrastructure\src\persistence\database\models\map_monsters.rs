//! `SeaORM` Entity, @generated by sea-orm-codegen 1.1.0

use sea_orm::entity::prelude::*;
use serde::Deserialize;

#[derive(<PERSON><PERSON>, Debug, PartialEq, DeriveEntityModel, Eq, Deserialize)]
#[sea_orm(table_name = "map_monsters")]
pub struct Model {
    #[sea_orm(primary_key, auto_increment = false)]
    pub id: Uuid,
    pub map_id: Uuid,
    pub monster_id: Uuid,
    pub mon_map_id: i32,
    pub frame: String,
}

#[derive(<PERSON><PERSON>, <PERSON><PERSON>, Debug, EnumIter, DeriveRelation)]
pub enum Relation {
    #[sea_orm(
        belongs_to = "super::maps::Entity",
        from = "Column::MapId",
        to = "super::maps::Column::Id",
        on_update = "Cascade",
        on_delete = "Cascade"
    )]
    Maps,
    #[sea_orm(
        belongs_to = "super::monsters::Entity",
        from = "Column::MonsterId",
        to = "super::monsters::Column::Id",
        on_update = "Cascade",
        on_delete = "Cascade"
    )]
    Monsters,
}

impl Related<super::maps::Entity> for Entity {
    fn to() -> RelationDef {
        Relation::Maps.def()
    }
}

impl Related<super::monsters::Entity> for Entity {
    fn to() -> RelationDef {
        Relation::Monsters.def()
    }
}

impl ActiveModelBehavior for ActiveModel {}
