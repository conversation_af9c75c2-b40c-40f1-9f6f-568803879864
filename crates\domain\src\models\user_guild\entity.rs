use super::value_object::*;
use crate::models::guild::value_object::GuildId;
use crate::models::user::value_object::UserId;
use getset::{Get<PERSON>, Setters};

#[derive(<PERSON>bu<PERSON>, <PERSON><PERSON>, Default, bon::<PERSON><PERSON><PERSON>, <PERSON><PERSON>, Set<PERSON>)]
#[getset(get = "pub", set = "pub")]
pub struct UserGuild {
    id: UserGuildId,
    guild_id: GuildId,
    user_id: UserId,
    rank: UserGuildRank,
}

impl UserGuild {
    pub fn new(id: UserGuildId, guild_id: GuildId, user_id: UserId, rank: UserGuildRank) -> Self {
        Self {
            id,
            guild_id,
            user_id,
            rank,
        }
    }
}

#[cfg(test)]
mod tests {
    use reforged_shared::{IdTrait, Value};

    use super::*;

    #[test]
    fn test_user_guild_new() {
        let id = UserGuildId::new(uuid::Uuid::now_v7());
        let guild_id = GuildId::new(uuid::Uuid::now_v7());
        let user_id = UserId::new(uuid::Uuid::now_v7());
        let rank = UserGuildRank::new(4);

        let user_guild =
            UserGuild::new(id.clone(), guild_id.clone(), user_id.clone(), rank.clone());

        assert_eq!(user_guild.id.get_id(), id.get_id());
        assert_eq!(user_guild.guild_id.get_id(), guild_id.get_id());
        assert_eq!(user_guild.user_id.get_id(), user_id.get_id());
        assert_eq!(user_guild.rank.value(), rank.value());
    }

    #[test]
    fn test_user_guild_builder() {
        let id = UserGuildId::new(uuid::Uuid::now_v7());
        let guild_id = GuildId::new(uuid::Uuid::now_v7());
        let user_id = UserId::new(uuid::Uuid::now_v7());
        let rank = UserGuildRank::new(4);

        let user_guild = UserGuild::builder()
            .id(id.clone())
            .guild_id(guild_id.clone())
            .user_id(user_id.clone())
            .rank(rank.clone())
            .build();

        assert_eq!(user_guild.id.get_id(), id.get_id());
        assert_eq!(user_guild.guild_id.get_id(), guild_id.get_id());
        assert_eq!(user_guild.user_id.get_id(), user_id.get_id());
        assert_eq!(user_guild.rank.value(), rank.value());
    }

    #[test]
    fn test_getters_and_setters() {
        let mut user_guild = UserGuild::default();

        let id = UserGuildId::new(uuid::Uuid::now_v7());
        let guild_id = GuildId::new(uuid::Uuid::now_v7());
        let user_id = UserId::new(uuid::Uuid::now_v7());
        let rank = UserGuildRank::new(4);

        user_guild.set_id(id.clone());
        user_guild.set_guild_id(guild_id.clone());
        user_guild.set_user_id(user_id.clone());
        user_guild.set_rank(rank.clone());

        assert_eq!(user_guild.id().get_id(), id.get_id());
        assert_eq!(user_guild.guild_id().get_id(), guild_id.get_id());
        assert_eq!(user_guild.user_id().get_id(), user_id.get_id());
        assert_eq!(user_guild.rank().value(), rank.value());
    }
}
