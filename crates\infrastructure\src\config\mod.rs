pub mod broker;
pub(crate) mod captcha;
pub mod database;
pub mod email_config;
pub(crate) mod emulator;
pub(crate) mod env;
pub mod eventstore;
pub(crate) mod http;
pub mod loader;
pub(crate) mod logging;
pub(crate) mod storage;
pub mod token;

#[derive(Debug, thiserror::Error)]
pub enum ConfigError {
    #[error("Env var {0} not found")]
    EnvVarNotFound(String),
    #[error("Env var {0} not valid")]
    EnvVarNotValid(String),
}

pub trait Config {
    fn from_env() -> Result<Self, ConfigError>
    where
        Self: Sized;
}
