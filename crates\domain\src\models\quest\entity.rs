use crate::models::achievement::value_object::AchievementId;
use crate::models::class::value_object::ClassId;
use crate::models::faction::value_object::FactionId;
use crate::models::quest::value_object::*;
use crate::models::war::value_object::WarId;
use getset::{<PERSON><PERSON>, <PERSON><PERSON>};

#[derive(<PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, bon::<PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>)]
#[getset(get = "pub", set = "pub")]
pub struct Quest {
    pub id: QuestId,
    pub faction_id: FactionId,
    pub req_achievement: Option<AchievementId>,
    pub req_reputation: QuestReqReputation,
    pub req_class_id: Option<ClassId>,
    pub req_class_points: QuestReqClassPoints,
    pub name: QuestName,
    pub description: QuestDescription,
    pub end_text: Option<QuestEndText>,
    pub experience: QuestExperience,
    pub g_experience: QuestGExperience,
    pub gold: QuestGold,
    pub coins: Quest<PERSON>oins,
    pub reputation: QuestReputation,
    pub class_points: QuestClassPoints,
    pub reward_type: QuestRewardType,
    pub level: QuestLevel,
    pub upgrade: QuestUpgrade,
    pub once: QuestOnce,
    pub slot: QuestSlot,
    pub value: QuestValue,
    pub field: QuestField,
    pub index: QuestIndex,
    pub badges: Option<QuestBadges>,
    pub give_membership: Option<QuestGiveMembership>,
    pub war_id: Option<WarId>,
    pub achievement_id: Option<AchievementId>,
    pub war_mega: QuestWarMega,
    pub req_guild_level: Option<QuestReqGuildLevel>,
    pub staff: QuestStaff,
    pub check: QuestCheck,
}

impl Quest {
    #[allow(clippy::too_many_arguments)]
    pub fn new(
        id: QuestId,
        faction_id: FactionId,
        req_achievement: Option<AchievementId>,
        req_reputation: QuestReqReputation,
        req_class_id: Option<ClassId>,
        req_class_points: QuestReqClassPoints,
        name: QuestName,
        description: QuestDescription,
        end_text: Option<QuestEndText>,
        experience: QuestExperience,
        g_experience: QuestGExperience,
        gold: QuestGold,
        coins: QuestCoins,
        reputation: QuestReputation,
        class_points: QuestClassPoints,
        reward_type: QuestRewardType,
        level: QuestLevel,
        upgrade: QuestUpgrade,
        once: QuestOnce,
        slot: QuestSlot,
        value: QuestValue,
        field: QuestField,
        index: QuestIndex,
        badges: Option<QuestBadges>,
        give_membership: Option<QuestGiveMembership>,
        war_id: Option<WarId>,
        achievement_id: Option<AchievementId>,
        war_mega: QuestWarMega,
        req_guild_level: Option<QuestReqGuildLevel>,
        staff: QuestStaff,
        check: QuestCheck,
    ) -> Self {
        Self {
            id,
            faction_id,
            req_achievement,
            req_reputation,
            req_class_id,
            req_class_points,
            name,
            description,
            end_text,
            experience,
            g_experience,
            gold,
            coins,
            reputation,
            class_points,
            reward_type,
            level,
            upgrade,
            once,
            slot,
            value,
            field,
            index,
            badges,
            give_membership,
            war_id,
            achievement_id,
            war_mega,
            req_guild_level,
            staff,
            check,
        }
    }
}

#[cfg(test)]
mod tests {
    use reforged_shared::{IdTrait, Value};
    use uuid::Uuid;

    use super::*;

    #[test]
    fn test_quest_new() {
        let id = QuestId::new(uuid::Uuid::now_v7());
        let faction_id = FactionId::new(uuid::Uuid::now_v7());
        let req_achievement = Some(AchievementId::new(uuid::Uuid::now_v7()));
        let req_reputation = QuestReqReputation::new(4);
        let req_class_id = Some(ClassId::new(uuid::Uuid::now_v7()));
        let req_class_points = QuestReqClassPoints::new(6);
        let name = QuestName::new("Test Quest".to_string());
        let description = QuestDescription::new("Test Description".to_string());
        let end_text = Some(QuestEndText::new("Test End Text".to_string()));
        let experience = QuestExperience::new(100);
        let g_experience = QuestGExperience::new(200);
        let gold = QuestGold::new(300);
        let coins = QuestCoins::new(400);
        let reputation = QuestReputation::new(500);
        let class_points = QuestClassPoints::new(600);
        let reward_type = QuestRewardType::default();
        let level = QuestLevel::new(10);
        let upgrade = QuestUpgrade::new(false);
        let once = QuestOnce::new(true);
        let slot = QuestSlot::new(2);
        let value = QuestValue::new(700);
        let field = QuestField::new("Test Field".to_string());
        let index = QuestIndex::new(3);
        let badges = Some(QuestBadges::new("Test Badges".to_string()));
        let give_membership = Some(QuestGiveMembership::new(true));
        let war_id = Some(WarId::new(Uuid::now_v7()));
        let achievement_id = Some(AchievementId::new(uuid::Uuid::now_v7()));
        let war_mega = QuestWarMega::new(false);
        let req_guild_level = Some(QuestReqGuildLevel::new(7));
        let staff = QuestStaff::new(false);
        let check = QuestCheck::new(true);

        let quest = Quest::new(
            id.clone(),
            faction_id.clone(),
            req_achievement.clone(),
            req_reputation.clone(),
            req_class_id.clone(),
            req_class_points.clone(),
            name.clone(),
            description.clone(),
            end_text.clone(),
            experience.clone(),
            g_experience.clone(),
            gold.clone(),
            coins.clone(),
            reputation.clone(),
            class_points.clone(),
            reward_type.clone(),
            level.clone(),
            upgrade.clone(),
            once.clone(),
            slot.clone(),
            value.clone(),
            field.clone(),
            index.clone(),
            badges.clone(),
            give_membership.clone(),
            war_id.clone(),
            achievement_id.clone(),
            war_mega.clone(),
            req_guild_level.clone(),
            staff.clone(),
            check.clone(),
        );

        assert_eq!(quest.id.get_id(), id.get_id());
        assert_eq!(quest.faction_id.get_id(), faction_id.get_id());
        assert_eq!(
            quest.req_achievement.as_ref().unwrap().get_id(),
            req_achievement.unwrap().get_id()
        );
        assert_eq!(quest.req_reputation.value(), req_reputation.value());
        assert_eq!(
            quest.req_class_id.as_ref().unwrap().get_id(),
            req_class_id.unwrap().get_id()
        );
        assert_eq!(quest.req_class_points.value(), req_class_points.value());
        assert_eq!(quest.name.value(), name.value());
        assert_eq!(quest.description.value(), description.value());
        assert_eq!(
            quest.end_text.as_ref().unwrap().value(),
            end_text.unwrap().value()
        );
        assert_eq!(quest.experience.value(), experience.value());
        assert_eq!(quest.g_experience.value(), g_experience.value());
        assert_eq!(quest.gold.value(), gold.value());
        assert_eq!(quest.coins.value(), coins.value());
        assert_eq!(quest.reputation.value(), reputation.value());
        assert_eq!(quest.class_points.value(), class_points.value());
        assert_eq!(quest.reward_type.value(), reward_type.value());
        assert_eq!(quest.level.value(), level.value());
        assert_eq!(quest.upgrade.value(), upgrade.value());
        assert_eq!(quest.once.value(), once.value());
        assert_eq!(quest.slot.value(), slot.value());
        assert_eq!(quest.value.value(), value.value());
        assert_eq!(quest.field.value(), field.value());
        assert_eq!(quest.index.value(), index.value());
        assert_eq!(
            quest.badges.as_ref().unwrap().value(),
            badges.unwrap().value()
        );
        assert_eq!(
            quest.give_membership.as_ref().unwrap().value(),
            give_membership.unwrap().value()
        );
        assert_eq!(
            quest.war_id.as_ref().unwrap().get_id(),
            war_id.unwrap().get_id()
        );
        assert_eq!(
            quest.achievement_id.as_ref().unwrap().get_id(),
            achievement_id.unwrap().get_id()
        );
        assert_eq!(quest.war_mega.value(), war_mega.value());
        assert_eq!(
            quest.req_guild_level.as_ref().unwrap().value(),
            req_guild_level.unwrap().value()
        );
        assert_eq!(quest.staff.value(), staff.value());
        assert_eq!(quest.check.value(), check.value());
    }

    #[test]
    fn test_quest_builder() {
        let id = QuestId::new(uuid::Uuid::now_v7());
        let faction_id = FactionId::new(uuid::Uuid::now_v7());
        let req_achievement = Some(AchievementId::new(uuid::Uuid::now_v7()));
        let req_reputation = QuestReqReputation::new(4);
        let req_class_id = Some(ClassId::new(uuid::Uuid::now_v7()));
        let req_class_points = QuestReqClassPoints::new(6);
        let name = QuestName::new("Test Quest".to_string());
        let description = QuestDescription::new("Test Description".to_string());
        let end_text = Some(QuestEndText::new("Test End Text".to_string()));
        let experience = QuestExperience::new(100);
        let g_experience = QuestGExperience::new(200);
        let gold = QuestGold::new(300);
        let coins = QuestCoins::new(400);
        let reputation = QuestReputation::new(500);
        let class_points = QuestClassPoints::new(600);
        let reward_type = QuestRewardType::default();
        let level = QuestLevel::new(10);
        let upgrade = QuestUpgrade::new(false);
        let once = QuestOnce::new(true);
        let slot = QuestSlot::new(2);
        let value = QuestValue::new(700);
        let field = QuestField::new("Test Field".to_string());
        let index = QuestIndex::new(3);
        let badges = Some(QuestBadges::new("Test Badges".to_string()));
        let give_membership = Some(QuestGiveMembership::new(true));
        let war_id = Some(WarId::new(Uuid::now_v7()));
        let achievement_id = Some(AchievementId::new(uuid::Uuid::now_v7()));
        let war_mega = QuestWarMega::new(false);
        let req_guild_level = Some(QuestReqGuildLevel::new(7));
        let staff = QuestStaff::new(false);
        let check = QuestCheck::new(true);

        let quest = Quest::builder()
            .id(id.clone())
            .faction_id(faction_id.clone())
            .req_achievement(req_achievement.clone().unwrap())
            .req_reputation(req_reputation.clone())
            .req_class_id(req_class_id.clone().unwrap())
            .req_class_points(req_class_points.clone())
            .name(name.clone())
            .description(description.clone())
            .end_text(end_text.clone().unwrap())
            .experience(experience.clone())
            .g_experience(g_experience.clone())
            .gold(gold.clone())
            .coins(coins.clone())
            .reputation(reputation.clone())
            .class_points(class_points.clone())
            .reward_type(reward_type.clone())
            .level(level.clone())
            .upgrade(upgrade.clone())
            .once(once.clone())
            .slot(slot.clone())
            .value(value.clone())
            .field(field.clone())
            .index(index.clone())
            .badges(badges.clone().unwrap())
            .give_membership(give_membership.clone().unwrap())
            .war_id(war_id.clone().unwrap())
            .achievement_id(achievement_id.clone().unwrap())
            .war_mega(war_mega.clone())
            .req_guild_level(req_guild_level.clone().unwrap())
            .staff(staff.clone())
            .check(check.clone())
            .build();

        assert_eq!(quest.id.get_id(), id.get_id());
        assert_eq!(quest.faction_id.get_id(), faction_id.get_id());
        assert_eq!(
            quest.req_achievement.as_ref().unwrap().get_id(),
            req_achievement.unwrap().get_id()
        );
        assert_eq!(quest.req_reputation.value(), req_reputation.value());
        assert_eq!(
            quest.req_class_id.as_ref().unwrap().get_id(),
            req_class_id.unwrap().get_id()
        );
        assert_eq!(quest.req_class_points.value(), req_class_points.value());
        assert_eq!(quest.name.value(), name.value());
        assert_eq!(quest.description.value(), description.value());
        assert_eq!(
            quest.end_text.as_ref().unwrap().value(),
            end_text.unwrap().value()
        );
        assert_eq!(quest.experience.value(), experience.value());
        assert_eq!(quest.g_experience.value(), g_experience.value());
        assert_eq!(quest.gold.value(), gold.value());
        assert_eq!(quest.coins.value(), coins.value());
        assert_eq!(quest.reputation.value(), reputation.value());
        assert_eq!(quest.class_points.value(), class_points.value());
        assert_eq!(quest.reward_type.value(), reward_type.value());
        assert_eq!(quest.level.value(), level.value());
        assert_eq!(quest.upgrade.value(), upgrade.value());
        assert_eq!(quest.once.value(), once.value());
        assert_eq!(quest.slot.value(), slot.value());
        assert_eq!(quest.value.value(), value.value());
        assert_eq!(quest.field.value(), field.value());
        assert_eq!(quest.index.value(), index.value());
        assert_eq!(
            quest.badges.as_ref().unwrap().value(),
            badges.unwrap().value()
        );
        assert_eq!(
            quest.give_membership.as_ref().unwrap().value(),
            give_membership.unwrap().value()
        );
        assert_eq!(
            quest.war_id.as_ref().unwrap().get_id(),
            war_id.unwrap().get_id()
        );
        assert_eq!(
            quest.achievement_id.as_ref().unwrap().get_id(),
            achievement_id.unwrap().get_id()
        );
        assert_eq!(quest.war_mega.value(), war_mega.value());
        assert_eq!(
            quest.req_guild_level.as_ref().unwrap().value(),
            req_guild_level.unwrap().value()
        );
        assert_eq!(quest.staff.value(), staff.value());
        assert_eq!(quest.check.value(), check.value());
    }

    #[test]
    fn test_getters_and_setters() {
        let mut quest = Quest::default();

        let id = QuestId::new(uuid::Uuid::now_v7());
        let faction_id = FactionId::new(uuid::Uuid::now_v7());
        let req_achievement = Some(AchievementId::new(uuid::Uuid::now_v7()));
        let req_reputation = QuestReqReputation::new(4);
        let req_class_id = Some(ClassId::new(uuid::Uuid::now_v7()));
        let req_class_points = QuestReqClassPoints::new(6);
        let name = QuestName::new("Test Quest".to_string());
        let description = QuestDescription::new("Test Description".to_string());
        let end_text = Some(QuestEndText::new("Test End Text".to_string()));
        let experience = QuestExperience::new(100);
        let g_experience = QuestGExperience::new(200);
        let gold = QuestGold::new(300);
        let coins = QuestCoins::new(400);
        let reputation = QuestReputation::new(500);
        let class_points = QuestClassPoints::new(600);
        let reward_type = QuestRewardType::R;
        let level = QuestLevel::new(10);
        let upgrade = QuestUpgrade::new(false);
        let once = QuestOnce::new(true);
        let slot = QuestSlot::new(2);
        let value = QuestValue::new(700);
        let field = QuestField::new("Test Field".to_string());
        let index = QuestIndex::new(3);
        let badges = Some(QuestBadges::new("Test Badges".to_string()));
        let give_membership = Some(QuestGiveMembership::new(true));
        let war_id = Some(WarId::new(Uuid::now_v7()));
        let achievement_id = Some(AchievementId::new(uuid::Uuid::now_v7()));
        let war_mega = QuestWarMega::new(false);
        let req_guild_level = Some(QuestReqGuildLevel::new(7));
        let staff = QuestStaff::new(false);
        let check = QuestCheck::new(true);

        quest.set_id(id.clone());
        quest.set_faction_id(faction_id.clone());
        quest.set_req_achievement(req_achievement.clone());
        quest.set_req_reputation(req_reputation.clone());
        quest.set_req_class_id(req_class_id.clone());
        quest.set_req_class_points(req_class_points.clone());
        quest.set_name(name.clone());
        quest.set_description(description.clone());
        quest.set_end_text(end_text.clone());
        quest.set_experience(experience.clone());
        quest.set_g_experience(g_experience.clone());
        quest.set_gold(gold.clone());
        quest.set_coins(coins.clone());
        quest.set_reputation(reputation.clone());
        quest.set_class_points(class_points.clone());
        quest.set_reward_type(reward_type.clone());
        quest.set_level(level.clone());
        quest.set_upgrade(upgrade.clone());
        quest.set_once(once.clone());
        quest.set_slot(slot.clone());
        quest.set_value(value.clone());
        quest.set_field(field.clone());
        quest.set_index(index.clone());
        quest.set_badges(badges.clone());
        quest.set_give_membership(give_membership.clone());
        quest.set_war_id(war_id.clone());
        quest.set_achievement_id(achievement_id.clone());
        quest.set_war_mega(war_mega.clone());
        quest.set_req_guild_level(req_guild_level.clone());
        quest.set_staff(staff.clone());
        quest.set_check(check.clone());

        assert_eq!(quest.id().get_id(), id.get_id());
        assert_eq!(quest.faction_id().get_id(), faction_id.get_id());
        assert_eq!(
            quest.req_achievement().as_ref().unwrap().get_id(),
            req_achievement.unwrap().get_id()
        );
        assert_eq!(quest.req_reputation().value(), req_reputation.value());
        assert_eq!(
            quest.req_class_id().as_ref().unwrap().get_id(),
            req_class_id.unwrap().get_id()
        );
        assert_eq!(quest.req_class_points().value(), req_class_points.value());
        assert_eq!(quest.name().value(), name.value());
        assert_eq!(quest.description().value(), description.value());
        assert_eq!(
            quest.end_text().as_ref().unwrap().value(),
            end_text.unwrap().value()
        );
        assert_eq!(quest.experience().value(), experience.value());
        assert_eq!(quest.g_experience().value(), g_experience.value());
        assert_eq!(quest.gold().value(), gold.value());
        assert_eq!(quest.coins().value(), coins.value());
        assert_eq!(quest.reputation().value(), reputation.value());
        assert_eq!(quest.class_points().value(), class_points.value());
        assert_eq!(quest.reward_type().value(), reward_type.value());
        assert_eq!(quest.level().value(), level.value());
        assert_eq!(quest.upgrade().value(), upgrade.value());
        assert_eq!(quest.once().value(), once.value());
        assert_eq!(quest.slot().value(), slot.value());
        assert_eq!(quest.value().value(), value.value());
        assert_eq!(quest.field().value(), field.value());
        assert_eq!(quest.index().value(), index.value());
        assert_eq!(
            quest.badges().as_ref().unwrap().value(),
            badges.unwrap().value()
        );
        assert_eq!(
            quest.give_membership().as_ref().unwrap().value(),
            give_membership.unwrap().value()
        );
        assert_eq!(
            quest.war_id().as_ref().unwrap().get_id(),
            war_id.unwrap().get_id()
        );
        assert_eq!(
            quest.achievement_id().as_ref().unwrap().get_id(),
            achievement_id.unwrap().get_id()
        );
        assert_eq!(quest.war_mega().value(), war_mega.value());
        assert_eq!(
            quest.req_guild_level().as_ref().unwrap().value(),
            req_guild_level.unwrap().value()
        );
        assert_eq!(quest.staff().value(), staff.value());
        assert_eq!(quest.check().value(), check.value());
    }
}
