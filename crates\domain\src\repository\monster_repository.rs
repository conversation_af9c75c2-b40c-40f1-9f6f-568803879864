use async_trait::async_trait;
use mockall::automock;

use crate::error::RepositoryError;
use crate::models::monster::entity::Monster;
use crate::models::monster::value_object::MonsterId;

#[automock]
#[async_trait]
pub trait MonsterRepository: Send + Sync {
    async fn save(&self, entity: &Monster) -> Result<(), RepositoryError>;
    async fn update(&self, entity: &Monster) -> Result<(), RepositoryError>;
    async fn delete(&self, id: &MonsterId) -> Result<(), RepositoryError>;
}

#[automock]
#[async_trait]
pub trait MonsterReadRepository: Send + Sync {
    async fn find_by_id(&self, id: &MonsterId) -> Result<Option<Monster>, RepositoryError>;
    async fn find_all(&self) -> Result<Vec<Monster>, RepositoryError>;
}
