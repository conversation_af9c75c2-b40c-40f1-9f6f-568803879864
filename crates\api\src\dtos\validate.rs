use actix_web::FromRequest;
use futures_util::{FutureExt, future::LocalBoxFuture};
use serde::de::DeserializeOwned;
use validator::Validate;

use crate::error::ApiError;

#[derive(Debug, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>)]
pub struct ValidateJson<T>(pub T);

impl<T> FromRequest for ValidateJson<T>
where
    T: DeserializeOwned + Validate + 'static,
{
    type Error = ApiError;
    type Future = LocalBoxFuture<'static, Result<Self, Self::Error>>;

    fn from_request(
        req: &actix_web::HttpRequest,
        payload: &mut actix_web::dev::Payload,
    ) -> Self::Future {
        let future = actix_web::web::Json::<T>::from_request(req, payload);

        async move {
            let json = future.await.map_err(|e| ApiError::JsonRequest(e))?;
            json.validate().map_err(|e| ApiError::Validation(e))?;
            Ok(Self(json.into_inner()))
        }
        .boxed_local()
    }
}

// impl<T, S> FromRequest<S> for ValidateJson<T>
// where
//     T: DeserializeOwned + Validate,
//     S: Send + Sync,
//     Json<T>: FromRequest<S, Rejection = JsonRejection>,
// {
//     type Rejection = ApiError;

//     async fn from_request(req: axum::extract::Request, state: &S) -> Result<Self, Self::Rejection> {
//         let Json(data) = Json::<T>::from_request(req, state).await?;
//         data.validate()?;

//         Ok(ValidateJson(data))
//     }
// }
