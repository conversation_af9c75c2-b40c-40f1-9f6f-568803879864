use actix::<PERSON><PERSON>;
use futures::{FutureExt, future::LocalBoxFuture};
use reforged_domain::{
    events::user_events::{ResetPasswordRequested, UserCreated, UserPasswordChanged},
    models::{
        password_reset_token::{
            // Added imports for PasswordResetToken
            entity::PasswordResetToken,
            value_object::{
                PasswordResetTokenCreatedAt, PasswordResetTokenExpiresAt, PasswordResetTokenId,
                Token as PasswordResetTokenValue,
            },
        },
        profile::value_object::Gender,
        user::{
            entity::User,
            value_object::{Email, UserId, UserRole, Username},
        },
    },
};
use tracing_log::log::{info, warn};

use crate::actors::message_broker::message::Publish;

use super::actor::ProjectionActor;

impl Handler<Publish<UserCreated>> for ProjectionActor {
    type Result = LocalBoxFuture<'static, ()>;

    fn handle(&mut self, msg: Publish<UserCreated>, _ctx: &mut Self::Context) -> Self::Result {
        let user_repository = self.user_repository();
        let password_hasher = self.password_hasher();

        let payload = msg.message();
        info!("Saving user {} to database", payload.username);

        let payload = payload.clone();

        let user_repository = user_repository.clone();
        let password_hasher = password_hasher.clone();

        Box::pin(async move {
            let id = UserId::new(payload.id);
            let username = Username::new(payload.username.to_owned());
            let email = Email::new(payload.email.to_owned());
            let hashed_password = password_hasher.hash(&payload.password).await.unwrap();
            let role = UserRole::try_from(payload.role.to_owned()).unwrap();
            let gender = Gender::try_from(payload.gender.to_owned()).unwrap();

            let new_user = User::builder()
                .id(id)
                .username(username)
                .email(email)
                .hashed_password(hashed_password)
                .role(role)
                .build();

            let user_repository = user_repository.clone();
            if let Err(e) = user_repository.save(&new_user, &gender).await {
                warn!("Failed to save user: {}", e);
            }
        })
        .boxed_local()
    }
}

impl Handler<Publish<ResetPasswordRequested>> for ProjectionActor {
    type Result = LocalBoxFuture<'static, ()>;

    fn handle(
        &mut self,
        msg: Publish<ResetPasswordRequested>,
        _ctx: &mut Self::Context,
    ) -> Self::Result {
        let payload = msg.message().clone();
        info!(
            "ProjectionActor received ResetPasswordRequested event for email: {}, token: {}, expires_at: {}",
            payload.email, payload.reset_token, payload.expires_at
        );

        let prt_repository = self.password_reset_token_repository().clone();

        Box::pin(async move {
            // user_id is now available in the event payload.
            let user_id = UserId::new(payload.user_id);

            let password_reset_token_entity = PasswordResetToken::builder()
                .id(PasswordResetTokenId::new(payload.id)) // payload.id is the token's ID
                .user_id(user_id) // Use user_id from the event
                .token(PasswordResetTokenValue::new(payload.reset_token.clone()))
                .created_at(PasswordResetTokenCreatedAt::new(payload.created_at))
                .expires_at(PasswordResetTokenExpiresAt::new(payload.expires_at))
                .build();

            match prt_repository.save(&password_reset_token_entity).await {
                Ok(_) => info!(
                    "Successfully saved PasswordResetToken with id: {} to projection.",
                    payload.id
                ),
                Err(e) => warn!(
                    "Failed to save PasswordResetToken with id: {}: {}",
                    payload.id, e
                ),
            }
        })
        .boxed_local()
    }
}

impl Handler<Publish<UserPasswordChanged>> for ProjectionActor {
    type Result = LocalBoxFuture<'static, ()>;

    fn handle(
        &mut self,
        msg: Publish<UserPasswordChanged>,
        _ctx: &mut Self::Context,
    ) -> Self::Result {
        let payload = msg.message().clone();
        info!(
            "ProjectionActor received UserPasswordChanged event for user_id: {}, email: {}, changed_at: {}",
            payload.id,
            payload.email,
            payload.password_changed_at // payload.id is the user_id
        );

        let user_repository = self.user_repository().clone();
        let password_reset_token_repository = self.password_reset_token_repository().clone();
        let password_hasher = self.password_hasher().clone();

        Box::pin(async move {
            let user_id_from_event = payload.id;
            let email_from_event = payload.email.clone(); // Email is in UserPasswordChanged event

            match password_hasher.hash(&payload.new_password).await {
                Ok(hashed_password_vo) => {
                    let mut user_for_update = User::default();
                    let user_id = UserId::new(user_id_from_event);
                    user_for_update.set_id(user_id);
                    user_for_update.set_email(email_from_event.into());
                    user_for_update.set_hashed_password(hashed_password_vo);

                    info!(
                        "Attempting to update password for user_id: {}. User details for update (excluding some fields): id={}, email={:?}",
                        user_id_from_event, user_for_update.id(), user_for_update.email()
                    );

                    match user_repository.update(&user_for_update).await {
                        Ok(_) => {
                            info!(
                                "Successfully updated password (and potentially other fields to default) for user_id: {}",
                                user_id_from_event
                            );

                            let token_id = PasswordResetTokenId::new(payload.reset_token_id);

                           _ = password_reset_token_repository
                                .delete(&token_id)
                                .await;
                        }
                        Err(e) => {
                            warn!(
                                "Failed to update user (password and other fields) for user_id: {}: {:?}",
                                user_id_from_event, e
                            );
                        }
                    }
                }
                Err(e) => {
                    warn!(
                        "Failed to hash new password for user_id: {}: {:?}",
                        user_id_from_event, e
                    );
                }
            }
        })
        .boxed_local()
    }
}
