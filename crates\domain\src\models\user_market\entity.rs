use getset::{<PERSON><PERSON>, <PERSON><PERSON>};

use crate::models::{
    enhancement::value_object::EnhancementId, item::value_object::ItemId,
    user::value_object::UserId,
};

use super::value_object::{
    MarketCoins, MarketDateTime, MarketGold, MarketQuantity, MarketStatus, MarketType,
    UserMarketId,
};

#[derive(<PERSON>bu<PERSON>, <PERSON><PERSON>, <PERSON><PERSON>ult, bon::<PERSON><PERSON>er, Getters, Setters)]
#[getset(get = "pub", set = "pub")]
pub struct UserMarket {
    id: UserMarketId,
    user_id: UserId,
    item_id: ItemId,
    enh_id: EnhancementId,
    datetime: MarketDateTime,
    buyer_id: Option<UserId>,
    coins: MarketCoins,
    gold: MarketGold,
    quantity: MarketQuantity,
    status: MarketStatus,
    market_type: MarketType,
}

impl UserMarket {
    #[allow(clippy::too_many_arguments)]
    pub fn new(
        id: UserMarketId,
        user_id: UserId,
        item_id: ItemId,
        enh_id: EnhancementId,
        datetime: MarketDateTime,
        buyer_id: Option<UserId>,
        coins: MarketCoins,
        gold: MarketGold,
        quantity: MarketQuantity,
        status: MarketStatus,
        market_type: MarketType,
    ) -> Self {
        Self {
            id,
            user_id,
            item_id,
            enh_id,
            datetime,
            buyer_id,
            coins,
            gold,
            quantity,
            status,
            market_type,
        }
    }
}

#[cfg(test)]
mod tests {
    use chrono::Utc;
    use reforged_shared::{IdTrait, Value};

    use super::*;

    #[test]
    fn test_user_market_new() {
        let id = UserMarketId::new(uuid::Uuid::now_v7());
        let user_id = UserId::new(uuid::Uuid::now_v7());
        let item_id = ItemId::new(uuid::Uuid::now_v7());
        let enh_id = EnhancementId::new(uuid::Uuid::now_v7());
        let datetime = MarketDateTime::new(Utc::now());
        let buyer_id = Some(UserId::new(uuid::Uuid::now_v7()));
        let coins = MarketCoins::new(100);
        let gold = MarketGold::new(500);
        let quantity = MarketQuantity::new(1);
        let status = MarketStatus::new(1);
        let market_type = MarketType::Auction;

        let user_market = UserMarket::new(
            id.clone(),
            user_id.clone(),
            item_id.clone(),
            enh_id.clone(),
            datetime.clone(),
            buyer_id.clone(),
            coins.clone(),
            gold.clone(),
            quantity.clone(),
            status.clone(),
            market_type.clone(),
        );

        assert_eq!(user_market.id().get_id(), id.get_id());
        assert_eq!(user_market.user_id().get_id(), user_id.get_id());
        assert_eq!(user_market.item_id().get_id(), item_id.get_id());
        assert_eq!(user_market.coins().value(), 100);
        assert_eq!(user_market.gold().value(), 500);
        assert_eq!(user_market.quantity().value(), 1);
        assert_eq!(user_market.status().value(), 1);
        assert_eq!(user_market.market_type().as_str(), "Auction");
    }
}