use crate::models::user::value_object::UserId;

use super::value_object::*;
use getset::{<PERSON><PERSON>, Set<PERSON>};

#[derive(<PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>ult, bon::<PERSON><PERSON><PERSON>, <PERSON><PERSON>, Set<PERSON>)]
#[getset(get = "pub", set = "pub")]
pub struct CMSArticle {
    id: CMSArticleId,
    title: CMSArticleTitle,
    content: CMSArticleContent,
    created_at: CMSArticleCreatedAt,
    image: CMSArticleImage,
    author_id: UserId,
}

impl CMSArticle {
    pub fn new(
        id: CMSArticleId,
        title: CMSArticleTitle,
        content: CMSArticleContent,
        created_at: CMSArticleCreatedAt,
        image: CMSArticleImage,
        author_id: UserId,
    ) -> Self {
        Self {
            id,
            title,
            content,
            created_at,
            image,
            author_id,
        }
    }
}

#[cfg(test)]
mod tests {
    use chrono::Utc;
    use reforged_shared::{IdTrait, Value};

    use super::*;

    #[test]
    fn test_cms_article_new() {
        let id = CMSArticleId::new(uuid::Uuid::now_v7());
        let title = CMSArticleTitle::new("Test CMS Article");
        let content = CMSArticleContent::new("This is the content of the test article");
        let created_at = CMSArticleCreatedAt::new(Utc::now());
        let image = CMSArticleImage::new(Some("test_image.png"));
        let author_id = UserId::new(uuid::Uuid::now_v7());

        let cms_article = CMSArticle::new(
            id.clone(),
            title.clone(),
            content.clone(),
            created_at.clone(),
            image.clone(),
            author_id.clone(),
        );

        assert_eq!(cms_article.id().get_id(), id.get_id());
        assert_eq!(cms_article.title().value(), title.value());
        assert_eq!(cms_article.content().value(), content.value());
        assert_eq!(cms_article.created_at().value(), created_at.value());
        assert_eq!(cms_article.image().value(), image.value());
        assert_eq!(cms_article.author_id().get_id(), author_id.get_id());
    }

    #[test]
    fn test_cms_article_builder() {
        let id = CMSArticleId::new(uuid::Uuid::now_v7());
        let title = CMSArticleTitle::new("Test CMS Article");
        let content = CMSArticleContent::new("This is the content of the test article");
        let created_at = CMSArticleCreatedAt::new(Utc::now());
        let image = CMSArticleImage::new(Some("test_image.png"));
        let author_id = UserId::new(uuid::Uuid::now_v7());

        let cms_article = CMSArticle::builder()
            .id(id.clone())
            .title(title.clone())
            .content(content.clone())
            .created_at(created_at.clone())
            .image(image.clone())
            .author_id(author_id.clone())
            .build();

        assert_eq!(cms_article.id().get_id(), id.get_id());
        assert_eq!(cms_article.title().value(), title.value());
        assert_eq!(cms_article.content().value(), content.value());
        assert_eq!(cms_article.created_at().value(), created_at.value());
        assert_eq!(cms_article.image().value(), image.value());
        assert_eq!(cms_article.author_id().get_id(), author_id.get_id());
    }

    #[test]
    fn test_getters_and_setters() {
        let mut cms_article = CMSArticle::default();

        let id = CMSArticleId::new(uuid::Uuid::now_v7());
        let title = CMSArticleTitle::new("Test CMS Article");
        let content = CMSArticleContent::new("This is the content of the test article");
        let created_at = CMSArticleCreatedAt::new(Utc::now());
        let image = CMSArticleImage::new(Some("test_image.png"));
        let author_id = UserId::new(uuid::Uuid::now_v7());

        cms_article.set_id(id.clone());
        cms_article.set_title(title.clone());
        cms_article.set_content(content.clone());
        cms_article.set_created_at(created_at.clone());
        cms_article.set_image(image.clone());
        cms_article.set_author_id(author_id.clone());

        assert_eq!(cms_article.id().get_id(), id.get_id());
        assert_eq!(cms_article.title().value(), title.value());
        assert_eq!(cms_article.content().value(), content.value());
        assert_eq!(cms_article.created_at().value(), created_at.value());
        assert_eq!(cms_article.image().value(), image.value());
        assert_eq!(cms_article.author_id().get_id(), author_id.get_id());
    }
}
