use async_trait::async_trait;
use mockall::automock;

use crate::error::RepositoryError;
use crate::models::hair_shop_item::entity::HairShopItem;
use crate::models::hair_shop_item::value_object::HairShopItemId;

#[automock]
#[async_trait]
pub trait HairShopItemRepository: Send + Sync {
    async fn save(&self, entity: &HairShopItem) -> Result<(), RepositoryError>;
    async fn update(&self, entity: &HairShopItem) -> Result<(), RepositoryError>;
    async fn delete(&self, id: &HairShopItemId) -> Result<(), RepositoryError>;
}

#[automock]
#[async_trait]
pub trait HairShopItemReadRepository: Send + Sync {
    async fn find_by_id(
        &self,
        id: &HairShopItemId,
    ) -> Result<Option<HairShopItem>, RepositoryError>;
    async fn find_all(&self) -> Result<Vec<HairShopItem>, RepositoryError>;
}
