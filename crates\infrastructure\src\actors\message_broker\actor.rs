use actix::{Actor, Addr};
use tracing::info;

pub type MessageBrokerActorAddr = Addr<MessageBrokerActor>;

pub struct MessageBrokerActor {
    name: String,
}

impl MessageBrokerActor {
    pub fn new(name: String) -> Self {
        Self { name }
    }
}

impl Actor for MessageBrokerActor {
    type Context = actix::Context<Self>;

    fn started(&mut self, _ctx: &mut Self::Context) {
        info!("[{}] MessageBrokerActor has started", self.name);
    }

    fn stopped(&mut self, _ctx: &mut Self::Context) {
        info!("[{}] MessageBrokerActor has stopped", self.name);
    }
}
