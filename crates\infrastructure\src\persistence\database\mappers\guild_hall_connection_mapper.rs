use reforged_domain::models::guild_hall::value_object::GuildHallId;
use reforged_domain::models::guild_hall_connection::value_object::{Cell, Pad, PadPosition};
use reforged_domain::models::guild_hall_connection::{
    entity::GuildHallConnection, value_object::GuildHallConnectionId,
};

#[derive(Debug)]
pub struct GuildHallConnectionDbModelMapper(super::super::models::guild_hall_connections::Model);

impl GuildHallConnectionDbModelMapper {
    pub fn new(guild_hall_connection: super::super::models::guild_hall_connections::Model) -> Self {
        Self(guild_hall_connection)
    }
}

impl From<GuildHallConnectionDbModelMapper> for GuildHallConnection {
    fn from(value: GuildHallConnectionDbModelMapper) -> Self {
        let guild_hall_connection_model = value.0;

        let id = GuildHallConnectionId::new(guild_hall_connection_model.id);

        GuildHallConnection::builder()
            .id(id)
            .hall_id(GuildHallId::new(guild_hall_connection_model.hall_id))
            .pad(Pad::new(guild_hall_connection_model.pad))
            .cell(Cell::new(guild_hall_connection_model.cell))
            .pad_position(PadPosition::new(guild_hall_connection_model.pad_position))
            .build()
    }
}
