use std::sync::Arc;

use serde::Serialize;

use crate::{
    error::ApplicationError,
    traits::{PasetoClaimPurpose, TokenService},
};

#[derive(Serialize)]
pub struct TokenVerificationResponse {
    is_valid: bool,
}

pub struct TokenVerificationUsecase {
    token_service: Arc<dyn TokenService>,
}

impl TokenVerificationUsecase {
    pub fn new(token_service: Arc<dyn TokenService>) -> Self {
        Self { token_service }
    }
}

impl TokenVerificationUsecase {
    pub async fn execute(
        &self,
        token: String,
    ) -> Result<TokenVerificationResponse, ApplicationError> {
        let token_service = self.token_service.clone();
        let _ = token_service.validate_token(token, PasetoClaimPurpose::AccessToken)?;

        let response = TokenVerificationResponse { is_valid: true };
        Ok(response)
    }
}
