# API config
HOST=127.0.0.1
PORT=5000

# READ Database configuration
DATABASE_PROTOCOL=postgres
DATABASE_HOST=127.0.0.1
DATABASE_USERNAME=postgres
DATABASE_PASSWORD=secret
DATABASE_NAME=reforged_db
DATABASE_PORT=5432
DATABASE_URL=${DATABASE_PROTOCOL}://${DATABASE_USERNAME}:${DATABASE_PASSWORD}@${DATABASE_HOST}:${DATABASE_PORT}/${DATABASE_NAME}

# Event Store configuration
EVENT_STORE_PROTOCOL=postgres
EVENT_STORE_HOST=127.0.0.1
EVENT_STORE_USERNAME=postgres
EVENT_STORE_PASSWORD=secret
EVENT_STORE_NAME=reforged-event-store
EVENT_STORE_PORT=5442
EVENT_STORE_URL=${EVENT_STORE_PROTOCOL}://${EVENT_STORE_USERNAME}:${EVENT_STORE_PASSWORD}@${EVENT_STORE_HOST}:${EVENT_STORE_PORT}/${EVENT_STORE_NAME}

# Captcha config
CAPTCHA_SECRET_KEY=

# Message Broker config
MESSAGE_BROKER_EXCHANGE=amq.direct
MESSAGE_BROKER_URL=amqp://user:secret@127.0.0.1:5672/%2F

# Logger config
RUST_LOG=info,server=info,reforged_api=info,reforged_application=info,reforged_infrastructure=info,tower_http=info,axum::rejection=trace,tokio=trace,runtime=trace
RUST_BACKTRACE=1

# Email config
EMAIL_HOST=smtp.gmail.com
EMAIL_PORT=587
EMAIL_USERNAME=
EMAIL_PASSWORD=

# Token config
SYMMETRIC_KEY=thisisaverysecretpasetosymmetric

# Project config
MODE=dev
