use reforged_domain::models::redeem_code::entity::RedeemCode;
use reforged_domain::models::redeem_code::value_object::*;
use reforged_domain::models::item::value_object::ItemId;

#[derive(Debug)]
pub struct RedeemCodeDbModelMapper(super::super::models::redeem_codes::Model);

impl RedeemCodeDbModelMapper {
    pub fn new(model: super::super::models::redeem_codes::Model) -> Self {
        Self(model)
    }
}

impl From<RedeemCodeDbModelMapper> for RedeemCode {
    fn from(value: RedeemCodeDbModelMapper) -> Self {
        let model = value.0;

        RedeemCode::builder()
            .id(RedeemCodeId::new(model.id))
            .code(RedeemCodeCode::new(model.code))
            .coins(RedeemCodeCoins::new(model.coins.try_into().unwrap_or(0)))
            .gold(RedeemCodeGold::new(model.gold.try_into().unwrap_or(0)))
            .exp(RedeemCodeExp::new(model.exp.try_into().unwrap_or(0)))
            .class_points(RedeemCodeClassPoints::new(model.class_points.try_into().unwrap_or(0)))
            .item_id(ItemId::new(model.item_id.unwrap_or_default()))
            .upgrade_days(RedeemCodeUpgradeDays::new(model.upgrade_days.try_into().unwrap_or(0)))
            .date_expiry(RedeemCodeDateExpiry::new(model.date_expiry.and_utc()))
            .limit(RedeemCodeLimit::new(model.limit.try_into().unwrap_or(0)))
            .build()
    }
}
