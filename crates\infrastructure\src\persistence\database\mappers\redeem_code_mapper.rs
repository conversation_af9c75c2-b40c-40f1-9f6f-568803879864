use reforged_domain::models::redeem_code::entity::RedeemCode;
use reforged_domain::models::redeem_code::value_object::RedeemCodeId;
use chrono::{DateTime, Utc};

#[derive(Debug)]
pub struct RedeemCodeDbModelMapper(super::super::models::redeem_codes::Model);

impl RedeemCodeDbModelMapper {
    pub fn new(model: super::super::models::redeem_codes::Model) -> Self {
        Self(model)
    }
}

impl From<RedeemCodeDbModelMapper> for RedeemCode {
    fn from(value: RedeemCodeDbModelMapper) -> Self {
        let model = value.0;
        
        RedeemCode::builder()
            .id(RedeemCodeId::new(model.id))
            .code(model.code.into())
            .gold(model.gold.into())
            .coins(model.coins.into())
            .crystal(model.crystal.into())
            .upgrade_days(model.upgrade_days.into())
            .bag_slots(model.bag_slots.into())
            .bank_slots(model.bank_slots.into())
            .house_slots(model.house_slots.into())
            .uses(model.uses.into())
            .max_uses(model.max_uses.into())
            .expires_at(DateTime::<Utc>::from_naive_utc_and_offset(model.expires_at, Utc).into())
            .build()
    }
}
