use reforged_shared::{UuidId, Value};

use super::entity::UserSlot;

pub type UserSlotId = UuidId<UserSlot>;

#[derive(Debug, PartialEq, Eq, PartialOrd, Ord, Clone, Default)]

pub struct UserSlotBag(i16);

impl UserSlotBag {
    pub fn new(slots_bag: i16) -> Self {
        Self(slots_bag)
    }
}

impl Value<i16> for UserSlotBag {
    fn value(&self) -> i16 {
        self.0
    }
}

impl From<i16> for UserSlotBag {
    fn from(value: i16) -> Self {
        Self(value)
    }
}

#[derive(Debug, PartialEq, Eq, PartialOrd, Ord, Clone, Default)]

pub struct UserSlotBank(i16);

impl UserSlotBank {
    pub fn new(slots_bank: i16) -> Self {
        Self(slots_bank)
    }
}

impl Value<i16> for UserSlotBank {
    fn value(&self) -> i16 {
        self.0
    }
}

impl From<i16> for UserSlotBank {
    fn from(value: i16) -> Self {
        Self(value)
    }
}

#[derive(Debug, PartialEq, Eq, PartialOrd, Ord, <PERSON>lone, Default)]

pub struct UserSlotHouse(i16);

impl UserSlotHouse {
    pub fn new(slots_house: i16) -> Self {
        Self(slots_house)
    }
}

impl Value<i16> for UserSlotHouse {
    fn value(&self) -> i16 {
        self.0
    }
}

impl From<i16> for UserSlotHouse {
    fn from(value: i16) -> Self {
        Self(value)
    }
}

#[derive(Debug, PartialEq, Eq, PartialOrd, Ord, Clone, Default)]

pub struct UserSlotAuction(i16);

impl UserSlotAuction {
    pub fn new(slots_auction: i16) -> Self {
        Self(slots_auction)
    }
}

impl Value<i16> for UserSlotAuction {
    fn value(&self) -> i16 {
        self.0
    }
}

impl From<i16> for UserSlotAuction {
    fn from(value: i16) -> Self {
        Self(value)
    }
}
