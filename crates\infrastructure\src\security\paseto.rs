use chrono::{DateTime, Duration, Utc};
use pasetors::Local;
use pasetors::claims::{C<PERSON><PERSON>, ClaimsValidationRules};
use pasetors::keys::SymmetricKey;
use pasetors::token::UntrustedToken;
use pasetors::version4::V4;
use reforged_application::{error::ApplicationError, traits::PasetoClaims};

use reforged_application::traits::{PasetoClaimPurpose, TokenService};

#[allow(dead_code)]
pub struct PasetoAuthenticationTokenService {
    symmetric_key: SymmetricKey<V4>,
}

impl PasetoAuthenticationTokenService {
    pub fn new(symmetric_key: String) -> Result<Self, ApplicationError> {
        let symmetric_key = SymmetricKey::<V4>::from(&symmetric_key.as_bytes())
            .map_err(|_| ApplicationError::TokenGenerationFailed)?;

        Ok(Self { symmetric_key })
    }
}

impl TokenService for PasetoAuthenticationTokenService {
    fn generate_token(
        &self,
        claims: PasetoClaims,
        expiration: Duration,
    ) -> Result<(String, DateTime<Utc>), ApplicationError> {
        let additional_data =
            serde_json::to_value(&claims).map_err(|_| ApplicationError::JsonSerializationError)?;
        let now = chrono::Utc::now();
        let expiration = now + expiration;

        let mut paseto_claims =
            Claims::new().map_err(|_| ApplicationError::TokenGenerationFailed)?;
        paseto_claims
            .add_additional("data", additional_data)
            .map_err(|_| ApplicationError::TokenGenerationFailed)?;
        paseto_claims
            .expiration(expiration.to_rfc3339().as_str())
            .map_err(|_| ApplicationError::TokenGenerationFailed)?;
        paseto_claims
            .not_before(now.to_rfc3339().as_str())
            .map_err(|_| ApplicationError::TokenGenerationFailed)?;
        paseto_claims
            .issued_at(now.to_rfc3339().as_str())
            .map_err(|_| ApplicationError::TokenGenerationFailed)?;
        paseto_claims
            .issuer("reforged")
            .map_err(|_| ApplicationError::TokenGenerationFailed)?;
        paseto_claims
            .audience("reforged")
            .map_err(|_| ApplicationError::TokenGenerationFailed)?;

        let token = pasetors::local::encrypt(&self.symmetric_key, &paseto_claims, None, None)
            .map_err(|_| ApplicationError::TokenGenerationFailed)?;

        Ok((token, expiration))
    }

    fn validate_token(
        &self,
        token: String,
        purpose: PasetoClaimPurpose,
    ) -> Result<PasetoClaims, ApplicationError> {
        let mut validation_rules = ClaimsValidationRules::new();
        validation_rules.validate_issuer_with("reforged");
        validation_rules.validate_audience_with("reforged");

        let untrustued_token = UntrustedToken::<Local, V4>::try_from(&token)
            .map_err(|_| ApplicationError::TokenInvalid)?;

        let trusted_token = pasetors::local::decrypt(
            &self.symmetric_key,
            &untrustued_token,
            &validation_rules,
            None,
            None,
        )
        .map_err(|_| ApplicationError::TokenInvalid)?;

        let claims = trusted_token.payload_claims();

        match claims {
            Some(claims) => {
                let additional_data = claims.get_claim("data");
                if additional_data.is_none() {
                    return Err(ApplicationError::TokenExpired);
                }

                let additional_data = additional_data.unwrap();

                let paste_claims: PasetoClaims = serde_json::from_value(additional_data.clone())
                    .map_err(|e| {
                        eprintln!("Error deserializing user claims: {}", e);
                        ApplicationError::JsonDeserializationError
                    })?;

                if paste_claims.purpose != purpose {
                    return Err(ApplicationError::TokenInvalid);
                }

                let expiration = paste_claims.exp;

                let issued_at = claims
                    .get_claim("iat")
                    .ok_or(ApplicationError::TokenInvalid)?
                    .as_str()
                    .ok_or(ApplicationError::TokenInvalid)?;

                let issued_at = chrono::DateTime::parse_from_rfc3339(issued_at)
                    .map_err(|_| ApplicationError::TokenInvalid)?
                    .with_timezone(&chrono::Utc);

                if chrono::Utc::now() > issued_at + expiration {
                    return Err(ApplicationError::TokenExpired);
                }

                Ok(paste_claims)
            }
            None => Err(ApplicationError::TokenInvalid),
        }
    }
}

#[cfg(test)]
mod tests {
    use reforged_application::traits::PasetoClaimPurpose;
    use reforged_shared::uuid_generator::uuid_now;

    use super::*;

    #[test]
    fn test_generate_token() {
        let paseto_auth =
            PasetoAuthenticationTokenService::new("thisisaverysecretpasetosymmetric".to_string())
                .unwrap();

        let paste_claims = PasetoClaims::new(
            uuid_now(),
            "<EMAIL>".to_string(),
            "admin".to_string(),
            Duration::days(1),
            PasetoClaimPurpose::AccessToken,
        );

        let (token, _) = paseto_auth
            .generate_token(paste_claims, Duration::days(1))
            .unwrap();

        assert_ne!(token, "");
        assert!(token.len() > 0);
    }

    #[test]
    fn test_validate_token() {
        let paseto_auth =
            PasetoAuthenticationTokenService::new("thisisaverysecretpasetosymmetric".to_string())
                .unwrap();

        let id = uuid_now();

        let paste_claims = PasetoClaims::new(
            id,
            "<EMAIL>".to_string(),
            "admin".to_string(),
            Duration::days(1),
            PasetoClaimPurpose::AccessToken,
        );

        let (token, _) = paseto_auth
            .generate_token(paste_claims, Duration::days(1))
            .unwrap();

        let paste_claims = paseto_auth
            .validate_token(token, PasetoClaimPurpose::AccessToken)
            .unwrap();

        assert_eq!(paste_claims.id, id);
        assert_eq!(paste_claims.email, "<EMAIL>");
        assert_eq!(paste_claims.role, "admin");
        assert_eq!(paste_claims.exp, Duration::days(1));
        assert_eq!(paste_claims.purpose, PasetoClaimPurpose::AccessToken);
    }
}
