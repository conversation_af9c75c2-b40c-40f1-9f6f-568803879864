use crate::models::prelude::*;
use sea_orm::Schema;
use sea_orm_migration::prelude::*;

#[derive(DeriveMigrationName)]
pub struct Migration;

#[async_trait::async_trait]
impl MigrationTrait for Migration {
    async fn up(&self, manager: &SchemaManager) -> Result<(), DbErr> {
        let backend = manager.get_database_backend();
        let schema = Schema::new(backend);

        // MARK: monsters tables and junction tables

        // MARK: wars tables
        manager
            .create_table(schema.create_table_from_entity(Monsters))
            .await?;
        manager
            .create_table(schema.create_table_from_entity(Wars))
            .await?;
        manager
            .create_table(schema.create_table_from_entity(MonsterSkills))
            .await?;
        manager
            .create_table(schema.create_table_from_entity(MonsterDrops))
            .await?;

        Ok(())
    }

    async fn down(&self, manager: &SchemaManager) -> Result<(), DbErr> {
        manager
            .drop_table(Table::drop().table(MonsterDrops).to_owned())
            .await?;
        manager
            .drop_table(Table::drop().table(MonsterSkills).to_owned())
            .await?;
        manager
            .drop_table(Table::drop().table(Monsters).to_owned())
            .await?;
        manager
            .drop_table(Table::drop().table(Wars).to_owned())
            .await
    }
}
