use reforged_domain::models::guild::value_object::GuildId;
use reforged_domain::models::guild_inventory::{
    entity::GuildInventory, value_object::GuildInventoryId,
};
use reforged_domain::models::item::value_object::ItemId;
use reforged_domain::models::user::value_object::UserId;

#[derive(Debug)]
pub struct GuildInventoryDbModelMapper(super::super::models::guild_items::Model);

impl GuildInventoryDbModelMapper {
    pub fn new(guild_inventory: super::super::models::guild_items::Model) -> Self {
        Self(guild_inventory)
    }
}

impl From<GuildInventoryDbModelMapper> for GuildInventory {
    fn from(value: GuildInventoryDbModelMapper) -> Self {
        let guild_inventory_model = value.0;

        let id = GuildInventoryId::new(guild_inventory_model.id);

        GuildInventory::builder()
            .id(id)
            .guild_id(GuildId::new(guild_inventory_model.guild_id))
            .item_id(ItemId::new(guild_inventory_model.item_id))
            .user_id(UserId::new(guild_inventory_model.user_id))
            .build()
    }
}
