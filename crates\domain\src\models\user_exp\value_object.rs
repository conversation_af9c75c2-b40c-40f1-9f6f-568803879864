use reforged_shared::{UuidId, Value};

use super::entity::UserExperience;

pub type UserExperienceId = UuidId<UserExperience>;

#[derive(Debug, PartialEq, Eq, PartialOrd, Ord, <PERSON>lone, <PERSON><PERSON>ult)]
pub struct Experience(u32);

#[derive(Debug, PartialEq, Eq, PartialOrd, Ord, Clone, De<PERSON>ult)]
pub struct Level(u16);

impl Experience {
    pub fn new(experience: u32) -> Self {
        Self(experience)
    }
}

impl Value<u32> for Experience {
    fn value(&self) -> u32 {
        self.0
    }
}

impl From<u32> for Experience {
    fn from(experience: u32) -> Self {
        Self(experience)
    }
}

impl Level {
    pub fn new(value: u16) -> Self {
        Self(value)
    }
}

impl Value<u16> for Level {
    fn value(&self) -> u16 {
        self.0
    }
}

impl From<u16> for Level {
    fn from(value: u16) -> Self {
        Self(value)
    }
}
