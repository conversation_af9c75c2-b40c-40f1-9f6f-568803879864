use reforged_domain::models::profile::entity::{Profile, ProfileId};
use reforged_domain::models::user::value_object::UserId;

#[derive(Debug)]
pub struct ProfileDbModelMapper(super::super::models::profiles::Model);

impl ProfileDbModelMapper {
    pub fn new(profile: super::super::models::profiles::Model) -> Self {
        Self(profile)
    }
}

impl From<ProfileDbModelMapper> for Profile {
    fn from(value: ProfileDbModelMapper) -> Self {
        let profile_model = value.0;

        let id = ProfileId::new(profile_model.id);

        Profile::builder()
            .id(id)
            .user_id(UserId::new(profile_model.user_id))
            .country(profile_model.country.into())
            .age(profile_model.age.into())
            .gender(profile_model.gender.into())
            .avatar(profile_model.avatar.into())
            .build()
    }
}
