use crate::actors::message_broker::{
    actor::MessageBrokerActorAddr,
    message::{Event, Publish},
};
use async_trait::async_trait;
use futures_util::StreamExt;
use lapin::{
    Channel, Connection,
    options::{BasicAckOptions, BasicConsumeOptions, BasicPublishOptions},
    types::FieldTable,
};
use reforged_application::{
    error::ApplicationError,
    traits::{BrokerServiceConsumer, BrokerServicePublisher},
};
use reforged_domain::events::user_events::{
    ResetPasswordRequested, UserCreated, UserPasswordChanged,
};
use serde::{Serialize, de::DeserializeOwned};
use tracing::{error, info, warn}; // Added error and warn

use crate::broker::MessageBrokerConfig;

pub struct RabbitMQ {
    config: MessageBrokerConfig,
    connection: Connection,
    channel: Channel,
}

impl RabbitMQ {
    pub fn new(config: MessageBrokerConfig, connection: Connection, channel: Channel) -> Self {
        Self {
            config,
            connection,
            channel,
        }
    }

    pub fn connection(&self) -> &Connection {
        &self.connection
    }

    pub fn channel(&self) -> &Channel {
        &self.channel
    }

    pub fn config(&self) -> &MessageBrokerConfig {
        &self.config
    }
}

#[async_trait]
impl BrokerServicePublisher for RabbitMQ {
    async fn publish(
        &self,
        event_type: &str,
        message: serde_json::Value,
    ) -> Result<(), ApplicationError> {
        let channel = self.channel();
        let config = self.config();

        let envelope = Event::new(event_type.to_string(), message);
        let message = serde_json::to_string(&envelope).map_err(|_| {
            ApplicationError::InternalError("Failed to serialize message".to_string())
        })?;

        let confirm = channel
            .basic_publish(
                &config.exchange,
                "",
                BasicPublishOptions::default(),
                message.as_bytes(),
                Default::default(),
            )
            .await
            .map_err(|_| {
                ApplicationError::InternalError("Failed to publish message".to_string())
            })?;

        confirm.await.map_err(|_| {
            ApplicationError::InternalError("Failed to confirm message".to_string())
        })?;

        Ok(())
    }
}

/// Consumes messages from the queue, <br>
/// and pass it to other services to handle the message. <br>
/// Like Emulator service which is responsible for game server emulation
pub struct RabbitMQConsumer {
    consumer_name: String,
    queue_name: String,
    channel: Channel,
    broker_addr: MessageBrokerActorAddr,
}

impl RabbitMQConsumer {
    pub fn new(
        consumer_name: String,
        queue_name: String,
        channel: Channel,
        broker_addr: MessageBrokerActorAddr,
    ) -> Self {
        Self {
            consumer_name,
            queue_name,
            channel,
            broker_addr,
        }
    }

    fn try_deserialize_and_dispatch<E>(
        &self,
        payload_value: serde_json::Value,
        event_name_for_log: &str,
    ) where
        E: Serialize + DeserializeOwned + Clone + Send + std::fmt::Debug + 'static,
    {
        match serde_json::from_value::<E>(payload_value.clone()) {
            Ok(deserialized_event) => {
                info!(
                    "Successfully deserialized {}. Dispatching: {:?}", // Updated log message
                    event_name_for_log, deserialized_event
                );
                self.broker_addr.do_send(Publish::new(deserialized_event));
            }
            Err(e) => {
                error!(
                    "Failed to deserialize {} payload: {:?}. Raw payload: {}", // Log raw payload as string
                    event_name_for_log,
                    e,
                    serde_json::to_string(&payload_value)
                        .unwrap_or_else(|_| "Invalid JSON".to_string())
                );
            }
        }
    }
}

#[async_trait]
impl BrokerServiceConsumer for RabbitMQConsumer {
    async fn consume(&self) -> Result<(), ApplicationError> {
        let mut consumer = self
            .channel
            .basic_consume(
                &self.queue_name,
                &self.consumer_name,
                BasicConsumeOptions::default(),
                FieldTable::default(),
            )
            .await
            .map_err(|e| {
                // Added error context
                error!("Failed to create RabbitMQ consumer: {:?}", e);
                ApplicationError::InternalError("Failed to create consumer".to_string())
            })?;

        info!("Consumer created, waiting for messages...");

        while let Some(delivery_result) = consumer.next().await {
            match delivery_result {
                Ok(delivery) => {
                    let message_data = delivery.data.clone(); // Clone for potential error logging
                    let message_str = String::from_utf8_lossy(&message_data);

                    info!("Message str: {}", message_str);

                    match serde_json::from_str::<Event>(&message_str) {
                        Ok(event_envelope) => {
                            info!("Received event type: {}", event_envelope.event_type());
                            let payload_value = event_envelope.payload().clone();

                            match event_envelope.event_type() {
                                "UserCreated" => {
                                    self.try_deserialize_and_dispatch::<UserCreated>(
                                        payload_value,
                                        "UserCreated",
                                    );
                                }
                                "ResetPasswordRequested" => {
                                    self.try_deserialize_and_dispatch::<ResetPasswordRequested>(
                                        payload_value,
                                        "ResetPasswordRequested",
                                    );
                                }
                                "UserPasswordChanged" => {
                                    self.try_deserialize_and_dispatch::<UserPasswordChanged>(
                                        payload_value,
                                        "UserPasswordChanged",
                                    );
                                }
                                unknown_event_type => {
                                    warn!("Received unknown event type: {}", unknown_event_type);
                                    // Consider if this should be ACKed or NACKed/DLQed
                                }
                            }
                        }
                        Err(e) => {
                            error!(
                                "Failed to deserialize message into Event envelope: {:?}. Raw message: {}",
                                e, message_str
                            );
                            // Consider NACK/DLQ for malformed Event envelopes
                        }
                    };

                    // Acknowledge the message after attempting to process it.
                    // This placement means messages are ACKed even if deserialization/dispatch fails.
                    if let Err(e) = delivery.ack(BasicAckOptions::default()).await {
                        error!("Failed to ACK message: {:?}", e);
                    }
                }
                Err(e) => {
                    error!(
                        "Error in message delivery from RabbitMQ consumer stream: {:?}",
                        e
                    );
                    // This indicates a stream error, might need to re-establish consumer or connection.
                }
            }
        }
        warn!("RabbitMQ consumer stream ended."); // Log if loop exits
        Ok(())
    }
}
