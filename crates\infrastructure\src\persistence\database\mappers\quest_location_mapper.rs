use reforged_domain::models::quest_location::entity::QuestLocation;
use reforged_domain::models::quest_location::value_object::QuestLocationId;
use reforged_domain::models::quest::value_object::QuestId;
use reforged_domain::models::map::value_object::MapId;

#[derive(Debug)]
pub struct QuestLocationDbModelMapper(super::super::models::quest_locations::Model);

impl QuestLocationDbModelMapper {
    pub fn new(model: super::super::models::quest_locations::Model) -> Self {
        Self(model)
    }
}

impl From<QuestLocationDbModelMapper> for QuestLocation {
    fn from(value: QuestLocationDbModelMapper) -> Self {
        let model = value.0;

        QuestLocation::builder()
            .id(QuestLocationId::new(model.id))
            .quest_id(QuestId::new(model.quest_id))
            .map_id(MapId::new(model.map_id))
            .build()
    }
}
