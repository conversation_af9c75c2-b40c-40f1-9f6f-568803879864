use reforged_domain::models::shop_location::entity::ShopLocation;
use reforged_domain::models::shop_location::value_object::ShopLocationId;
use reforged_domain::models::shop::value_object::ShopId;
use reforged_domain::models::map::value_object::MapId;

#[derive(Debug)]
pub struct ShopLocationDbModelMapper(super::super::models::shop_locations::Model);

impl ShopLocationDbModelMapper {
    pub fn new(model: super::super::models::shop_locations::Model) -> Self {
        Self(model)
    }
}

impl From<ShopLocationDbModelMapper> for ShopLocation {
    fn from(value: ShopLocationDbModelMapper) -> Self {
        let model = value.0;

        ShopLocation::builder()
            .id(ShopLocationId::new(model.id))
            .shop_id(ShopId::new(model.shop_id))
            .map_id(MapId::new(model.map_id))
            .build()
    }
}
