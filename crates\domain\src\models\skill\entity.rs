use super::value_object::{
    Animation, Chance, Cooldown, Damage, Dsrc, Effects, HitTargets, Icon, LifeSteal, Mana,
    ManaBack, Pet, Range, Reference, ShowDamage, SkillDescription, SkillId, SkillName, SkillType,
    Strl, Target,
};
use getset::{Get<PERSON>, Setters};

#[derive(<PERSON>bu<PERSON>, <PERSON><PERSON>, <PERSON><PERSON>ult, bon::<PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>)]
#[getset(get = "pub", set = "pub")]
pub struct Skill {
    id: SkillId,
    name: SkillName,
    animation: Animation,
    description: SkillDescription,
    damage: Damage,
    mana: Mana,
    mana_back: ManaBack,
    life_steal: LifeSteal,
    icon: Icon,
    range: Range,
    dsrc: Dsrc,
    reference: Reference,
    target: Target,
    effects: Effects,
    skill_type: SkillType,
    strl: Strl,
    cooldown: Cooldown,
    hit_targets: HitTargets,
    pet: Pet,
    chance: Chance,
    show_damage: ShowDamage,
}

impl Skill {
    #[allow(clippy::too_many_arguments)]
    pub fn new(
        id: SkillId,
        name: <PERSON><PERSON><PERSON><PERSON>,
        animation: Animation,
        description: SkillDescription,
        damage: Damage,
        mana: Mana,
        mana_back: ManaBack,
        life_steal: LifeSteal,
        icon: Icon,
        range: Range,
        dsrc: Dsrc,
        reference: Reference,
        target: Target,
        effects: Effects,
        skill_type: SkillType,
        strl: Strl,
        cooldown: Cooldown,
        hit_targets: HitTargets,
        pet: Pet,
        chance: Chance,
        show_damage: ShowDamage,
    ) -> Self {
        Self {
            id,
            name,
            animation,
            description,
            damage,
            mana,
            mana_back,
            life_steal,
            icon,
            range,
            dsrc,
            reference,
            target,
            effects,
            skill_type,
            strl,
            cooldown,
            hit_targets,
            pet,
            chance,
            show_damage,
        }
    }
}

#[cfg(test)]
mod tests {
    use reforged_shared::{IdTrait, Value};

    use super::*;

    #[test]
    fn test_skill_new() {
        let id = SkillId::new(uuid::Uuid::now_v7());
        let name = SkillName::new("Test Skill");
        let animation = Animation::new("test_animation");
        let description = SkillDescription::new("Test Description");
        let damage = Damage::new(100.0);
        let mana = Mana::new(50);
        let mana_back = ManaBack::new(10);
        let life_steal = LifeSteal::new(5.0);
        let icon = Icon::new("test_icon.png");
        let range = Range::new(10);
        let dsrc = Dsrc::new("test_dsrc");
        let reference = Reference::new("test_reference");
        let target = Target::new("test_target");
        let effects = Effects::new("test_effects");
        let skill_type = SkillType::new("test_type");
        let strl = Strl::new("test_strl");
        let cooldown = Cooldown::new(30);
        let hit_targets = HitTargets::new(3);
        let pet = Pet::new(false);
        let chance = Chance::new(Some(75.0));
        let show_damage = ShowDamage::new(true);

        let skill = Skill::new(
            id.clone(),
            name.clone(),
            animation.clone(),
            description.clone(),
            damage.clone(),
            mana.clone(),
            mana_back.clone(),
            life_steal.clone(),
            icon.clone(),
            range.clone(),
            dsrc.clone(),
            reference.clone(),
            target.clone(),
            effects.clone(),
            skill_type.clone(),
            strl.clone(),
            cooldown.clone(),
            hit_targets.clone(),
            pet.clone(),
            chance.clone(),
            show_damage.clone(),
        );

        assert_eq!(skill.id.get_id(), id.get_id());
        assert_eq!(skill.name.value(), name.value());
        assert_eq!(skill.animation.value(), animation.value());
        assert_eq!(skill.description.value(), description.value());
        assert_eq!(skill.damage.value(), damage.value());
        assert_eq!(skill.mana.value(), mana.value());
        assert_eq!(skill.mana_back.value(), mana_back.value());
        assert_eq!(skill.life_steal.value(), life_steal.value());
        assert_eq!(skill.icon.value(), icon.value());
        assert_eq!(skill.range.value(), range.value());
        assert_eq!(skill.dsrc.value(), dsrc.value());
        assert_eq!(skill.reference.value(), reference.value());
        assert_eq!(skill.target.value(), target.value());
        assert_eq!(skill.effects.value(), effects.value());
        assert_eq!(skill.skill_type.value(), skill_type.value());
        assert_eq!(skill.strl.value(), strl.value());
        assert_eq!(skill.cooldown.value(), cooldown.value());
        assert_eq!(skill.hit_targets.value(), hit_targets.value());
        assert_eq!(skill.pet.value(), pet.value());
        assert_eq!(skill.chance.value(), chance.value());
        assert_eq!(skill.show_damage.value(), show_damage.value());
    }

    #[test]
    fn test_skill_builder() {
        let id = SkillId::new(uuid::Uuid::now_v7());
        let name = SkillName::new("Test Skill");
        let animation = Animation::new("test_animation");
        let description = SkillDescription::new("Test Description");
        let damage = Damage::new(100.0);
        let mana = Mana::new(50);
        let mana_back = ManaBack::new(10);
        let life_steal = LifeSteal::new(5.0);
        let icon = Icon::new("test_icon.png");
        let range = Range::new(10);
        let dsrc = Dsrc::new("test_dsrc");
        let reference = Reference::new("test_reference");
        let target = Target::new("test_target");
        let effects = Effects::new("test_effects");
        let skill_type = SkillType::new("test_type");
        let strl = Strl::new("test_strl");
        let cooldown = Cooldown::new(30);
        let hit_targets = HitTargets::new(3);
        let pet = Pet::new(false);
        let chance = Chance::new(Some(75.0));
        let show_damage = ShowDamage::new(true);

        let skill = Skill::builder()
            .id(id.clone())
            .name(name.clone())
            .animation(animation.clone())
            .description(description.clone())
            .damage(damage.clone())
            .mana(mana.clone())
            .mana_back(mana_back.clone())
            .life_steal(life_steal.clone())
            .icon(icon.clone())
            .range(range.clone())
            .dsrc(dsrc.clone())
            .reference(reference.clone())
            .target(target.clone())
            .effects(effects.clone())
            .skill_type(skill_type.clone())
            .strl(strl.clone())
            .cooldown(cooldown.clone())
            .hit_targets(hit_targets.clone())
            .pet(pet.clone())
            .chance(chance.clone())
            .show_damage(show_damage.clone())
            .build();

        assert_eq!(skill.id.get_id(), id.get_id());
        assert_eq!(skill.name.value(), name.value());
        assert_eq!(skill.animation.value(), animation.value());
        assert_eq!(skill.description.value(), description.value());
        assert_eq!(skill.damage.value(), damage.value());
        assert_eq!(skill.mana.value(), mana.value());
        assert_eq!(skill.mana_back.value(), mana_back.value());
        assert_eq!(skill.life_steal.value(), life_steal.value());
        assert_eq!(skill.icon.value(), icon.value());
        assert_eq!(skill.range.value(), range.value());
        assert_eq!(skill.dsrc.value(), dsrc.value());
        assert_eq!(skill.reference.value(), reference.value());
        assert_eq!(skill.target.value(), target.value());
        assert_eq!(skill.effects.value(), effects.value());
        assert_eq!(skill.skill_type.value(), skill_type.value());
        assert_eq!(skill.strl.value(), strl.value());
        assert_eq!(skill.cooldown.value(), cooldown.value());
        assert_eq!(skill.hit_targets.value(), hit_targets.value());
        assert_eq!(skill.pet.value(), pet.value());
        assert_eq!(skill.chance.value(), chance.value());
        assert_eq!(skill.show_damage.value(), show_damage.value());
    }

    #[test]
    fn test_getters_and_setters() {
        let mut skill = Skill::default();

        let id = SkillId::new(uuid::Uuid::now_v7());
        let name = SkillName::new("Test Skill");
        let animation = Animation::new("test_animation");
        let description = SkillDescription::new("Test Description");
        let damage = Damage::new(100.0);
        let mana = Mana::new(50);
        let mana_back = ManaBack::new(10);
        let life_steal = LifeSteal::new(5.0);
        let icon = Icon::new("test_icon.png");
        let range = Range::new(10);
        let dsrc = Dsrc::new("test_dsrc");
        let reference = Reference::new("test_reference");
        let target = Target::new("test_target");
        let effects = Effects::new("test_effects");
        let skill_type = SkillType::new("test_type");
        let strl = Strl::new("test_strl");
        let cooldown = Cooldown::new(30);
        let hit_targets = HitTargets::new(3);
        let pet = Pet::new(false);
        let chance = Chance::new(Some(75.0));
        let show_damage = ShowDamage::new(true);

        skill.set_id(id.clone());
        skill.set_name(name.clone());
        skill.set_animation(animation.clone());
        skill.set_description(description.clone());
        skill.set_damage(damage.clone());
        skill.set_mana(mana.clone());
        skill.set_mana_back(mana_back.clone());
        skill.set_life_steal(life_steal.clone());
        skill.set_icon(icon.clone());
        skill.set_range(range.clone());
        skill.set_dsrc(dsrc.clone());
        skill.set_reference(reference.clone());
        skill.set_target(target.clone());
        skill.set_effects(effects.clone());
        skill.set_skill_type(skill_type.clone());
        skill.set_strl(strl.clone());
        skill.set_cooldown(cooldown.clone());
        skill.set_hit_targets(hit_targets.clone());
        skill.set_pet(pet.clone());
        skill.set_chance(chance.clone());
        skill.set_show_damage(show_damage.clone());

        assert_eq!(skill.id().get_id(), id.get_id());
        assert_eq!(skill.name().value(), name.value());
        assert_eq!(skill.animation().value(), animation.value());
        assert_eq!(skill.description().value(), description.value());
        assert_eq!(skill.damage().value(), damage.value());
        assert_eq!(skill.mana().value(), mana.value());
        assert_eq!(skill.mana_back().value(), mana_back.value());
        assert_eq!(skill.life_steal().value(), life_steal.value());
        assert_eq!(skill.icon().value(), icon.value());
        assert_eq!(skill.range().value(), range.value());
        assert_eq!(skill.dsrc().value(), dsrc.value());
        assert_eq!(skill.reference().value(), reference.value());
        assert_eq!(skill.target().value(), target.value());
        assert_eq!(skill.effects().value(), effects.value());
        assert_eq!(skill.skill_type().value(), skill_type.value());
        assert_eq!(skill.strl().value(), strl.value());
        assert_eq!(skill.cooldown().value(), cooldown.value());
        assert_eq!(skill.hit_targets().value(), hit_targets.value());
        assert_eq!(skill.pet().value(), pet.value());
        assert_eq!(skill.chance().value(), chance.value());
        assert_eq!(skill.show_damage().value(), show_damage.value());
    }
}
