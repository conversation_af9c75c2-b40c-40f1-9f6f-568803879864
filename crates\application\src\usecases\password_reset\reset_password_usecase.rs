use std::{str::FromStr, sync::Arc};

use chrono::{DateTime, Utc};
use esrs::AggregateState; // Added
use reforged_domain::commands::user_commands::{ChangeUserPasswordCommand, UserCommands};
use reforged_domain::{
    models::{password_reset_token::value_object::Token, user::entity::User},
    traits::password_hasher::PasswordHasher,
};
use reforged_shared::{IdTrait, Value};
use serde::Serialize;
use uuid::Uuid;

use crate::{
    error::ApplicationError,
    // events::user::UserPasswordChanged, // Removed
    eventsource::user::{UserAggregateState, UserStoreManager}, // Added
    queries::{
        password_reset_token_queries::GetPasswordResetToken,
        password_reset_token_query_handlers::PasswordResetTokenQueryHandler,
        user_queries::GetUserByIdQuery, user_query_handlers::User<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
    },
    services::captcha_service::Captcha,
    traits::{Pa<PERSON>o<PERSON><PERSON><PERSON><PERSON>ur<PERSON>, QueryHandler, TokenService}, // Removed BrokerServicePublisher
};

#[derive(Serialize)]
pub struct ResetPasswordResponse {
    password_reset_at: DateTime<Utc>,
}

pub struct ResetPasswordUsecase {
    captcha_service: Arc<dyn Captcha>,
    token_service: Arc<dyn TokenService>,
    user_query_handler: UserQueryHandler,
    password_hasher: Arc<dyn PasswordHasher>,
    password_reset_token_query_handler: PasswordResetTokenQueryHandler,
    user_store_manager: Arc<UserStoreManager>, // Added
}

impl ResetPasswordUsecase {
    pub fn new(
        captcha_service: Arc<dyn Captcha>,
        token_service: Arc<dyn TokenService>,
        user_query_handler: UserQueryHandler,
        password_hasher: Arc<dyn PasswordHasher>,
        password_reset_token_query_handler: PasswordResetTokenQueryHandler,
        user_store_manager: Arc<UserStoreManager>, // Added
    ) -> Self {
        Self {
            captcha_service,
            token_service,
            user_query_handler,
            password_hasher,
            password_reset_token_query_handler,
            user_store_manager, // Added
        }
    }
}

impl ResetPasswordUsecase {
    pub async fn execute(
        &self,
        token: String,
        new_password: String,
        captcha: String,
    ) -> Result<ResetPasswordResponse, ApplicationError> {
        self.captcha_service.validate(captcha).await?;

        let query = GetPasswordResetToken {
            token: Token::new(token.to_owned()),
        };

        // only have to check if the token still exists
        let password_reset_token = self
            .password_reset_token_query_handler
            .handle(query)
            .await?
            .ok_or(ApplicationError::TokenExpired)?;

        let token_service = self.token_service.clone();
        let claims =
            token_service.validate_token(token.to_owned(), PasetoClaimPurpose::PasswordReset)?;

        let user_id = claims.id;
        let query = GetUserByIdQuery::new(user_id);

        let user = self
            .user_query_handler
            .handle(query)
            .await?
            .ok_or(ApplicationError::UserNotFound)?;

        let is_password_matched = self
            .password_hasher
            .verify(&new_password, &user.hashed_password)
            .await;

        if is_password_matched.is_ok() {
            return Err(ApplicationError::PasswordNotChanged);
        }

        let user = User::from(user);
        let password_reset_token_id = Uuid::from_str(&password_reset_token.id).map_err(|_| {
            ApplicationError::InternalError("Unable to parse password reset token id".to_string())
        })?;

        let cmd = ChangeUserPasswordCommand {
            user_id: user.id().get_id(), // user.id() -> UserId, UserId.value() -> Uuid
            email: user.email().value(), // user.email() -> Email, Email.value() -> String
            new_password: new_password.clone(),
            token: token.clone(),
            token_id: password_reset_token_id.clone(),
        };

        let id = Uuid::now_v7();
        let state: AggregateState<UserAggregateState> = AggregateState::with_id(id);
        if let Err(e) = self
            .user_store_manager
            .handle_command(state, UserCommands::ChangeUserPassword(cmd))
            .await
        {
            println!("Failed to change user password: {}", e);
        }

        let now = Utc::now();
        let response = ResetPasswordResponse {
            password_reset_at: now,
        };

        Ok(response)
    }
}
