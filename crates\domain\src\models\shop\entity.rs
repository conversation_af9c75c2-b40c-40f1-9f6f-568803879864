use crate::models::{achievement::value_object::AchievementId, item::value_object::ItemId};
use getset::{<PERSON><PERSON>, <PERSON><PERSON>};

use super::value_object::*;

#[derive(<PERSON>bu<PERSON>, <PERSON><PERSON>, De<PERSON>ult, bon::<PERSON><PERSON><PERSON>, <PERSON><PERSON>, Setters)]
#[getset(get = "pub", set = "pub")]
pub struct Shop {
    id: ShopId,
    name: ShopName,
    house: ShopHouse,
    upgrade: ShopUpgrade,
    staff: ShopStaff,
    limited: ShopLimited,
    field: ShopField,
    achievement_id: AchievementId,
    item_id: ItemId,
}

impl Shop {
    pub fn new(
        id: ShopId,
        name: ShopName,
        house: ShopHouse,
        upgrade: ShopUpgrade,
        staff: ShopStaff,
        limited: ShopLimited,
        field: ShopField,
        achievement_id: AchievementId,
        item_id: ItemId,
    ) -> Self {
        Self {
            id,
            name,
            house,
            upgrade,
            staff,
            limited,
            field,
            achievement_id,
            item_id,
        }
    }
}

#[cfg(test)]
mod tests {
    use reforged_shared::{IdTrait, Value};

    use super::*;

    #[test]
    fn test_shop_new() {
        let id = ShopId::new(uuid::Uuid::now_v7());
        let name = ShopName::new("Test Shop".to_string());
        let house = ShopHouse::new(true);
        let upgrade = ShopUpgrade::new(false);
        let staff = ShopStaff::new(true);
        let limited = ShopLimited::new(false);
        let field = ShopField::new("Test Field".to_string());
        let achievement_id = AchievementId::new(uuid::Uuid::now_v7());
        let item_id = ItemId::new(uuid::Uuid::now_v7());

        let shop = Shop::new(
            id.clone(),
            name.clone(),
            house.clone(),
            upgrade.clone(),
            staff.clone(),
            limited.clone(),
            field.clone(),
            achievement_id.clone(),
            item_id.clone(),
        );

        assert_eq!(shop.id.get_id(), id.get_id());
        assert_eq!(shop.name.value(), name.value());
        assert_eq!(shop.house.value(), house.value());
        assert_eq!(shop.upgrade.value(), upgrade.value());
        assert_eq!(shop.staff.value(), staff.value());
        assert_eq!(shop.limited.value(), limited.value());
        assert_eq!(shop.field.value(), field.value());
        assert_eq!(shop.achievement_id.get_id(), achievement_id.get_id());
        assert_eq!(shop.item_id.get_id(), item_id.get_id());
    }

    #[test]
    fn test_shop_builder() {
        let id = ShopId::new(uuid::Uuid::now_v7());
        let name = ShopName::new("Test Shop".to_string());
        let house = ShopHouse::new(true);
        let upgrade = ShopUpgrade::new(false);
        let staff = ShopStaff::new(true);
        let limited = ShopLimited::new(false);
        let field = ShopField::new("Test Field".to_string());
        let achievement_id = AchievementId::new(uuid::Uuid::now_v7());
        let item_id = ItemId::new(uuid::Uuid::now_v7());

        let shop = Shop::builder()
            .id(id.clone())
            .name(name.clone())
            .house(house.clone())
            .upgrade(upgrade.clone())
            .staff(staff.clone())
            .limited(limited.clone())
            .field(field.clone())
            .achievement_id(achievement_id.clone())
            .item_id(item_id.clone())
            .build();

        assert_eq!(shop.id.get_id(), id.get_id());
        assert_eq!(shop.name.value(), name.value());
        assert_eq!(shop.house.value(), house.value());
        assert_eq!(shop.upgrade.value(), upgrade.value());
        assert_eq!(shop.staff.value(), staff.value());
        assert_eq!(shop.limited.value(), limited.value());
        assert_eq!(shop.field.value(), field.value());
        assert_eq!(shop.achievement_id.get_id(), achievement_id.get_id());
        assert_eq!(shop.item_id.get_id(), item_id.get_id());
    }

    #[test]
    fn test_getters_and_setters() {
        let mut shop = Shop::default();

        let id = ShopId::new(uuid::Uuid::now_v7());
        let name = ShopName::new("Test Shop".to_string());
        let house = ShopHouse::new(true);
        let upgrade = ShopUpgrade::new(false);
        let staff = ShopStaff::new(true);
        let limited = ShopLimited::new(false);
        let field = ShopField::new("Test Field".to_string());
        let achievement_id = AchievementId::new(uuid::Uuid::now_v7());
        let item_id = ItemId::new(uuid::Uuid::now_v7());

        shop.set_id(id.clone());
        shop.set_name(name.clone());
        shop.set_house(house.clone());
        shop.set_upgrade(upgrade.clone());
        shop.set_staff(staff.clone());
        shop.set_limited(limited.clone());
        shop.set_field(field.clone());
        shop.set_achievement_id(achievement_id.clone());
        shop.set_item_id(item_id.clone());

        assert_eq!(shop.id().get_id(), id.get_id());
        assert_eq!(shop.name().value(), name.value());
        assert_eq!(shop.house().value(), house.value());
        assert_eq!(shop.upgrade().value(), upgrade.value());
        assert_eq!(shop.staff().value(), staff.value());
        assert_eq!(shop.limited().value(), limited.value());
        assert_eq!(shop.field().value(), field.value());
        assert_eq!(shop.achievement_id().get_id(), achievement_id.get_id());
        assert_eq!(shop.item_id().get_id(), item_id.get_id());
    }
}
