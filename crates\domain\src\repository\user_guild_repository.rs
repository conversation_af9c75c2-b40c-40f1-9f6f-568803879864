use async_trait::async_trait;
use mockall::automock;

use crate::error::RepositoryError;
use crate::models::user_guild::entity::UserGuild;
use crate::models::user_guild::value_object::UserGuildId;

#[automock]
#[async_trait]
pub trait UserGuildRepository: Send + Sync {
    async fn save(&self, entity: &UserGuild) -> Result<(), RepositoryError>;
    async fn update(&self, entity: &UserGuild) -> Result<(), RepositoryError>;
    async fn delete(&self, id: &UserGuildId) -> Result<(), RepositoryError>;
}

#[automock]
#[async_trait]
pub trait UserGuildReadRepository: Send + Sync {
    async fn find_by_id(&self, id: &UserGuildId) -> Result<Option<UserGuild>, RepositoryError>;
    async fn find_all(&self) -> Result<Vec<UserGuild>, RepositoryError>;
}
