use async_trait::async_trait;
use mockall::automock;

use crate::error::RepositoryError;
use crate::models::monster_boss::entity::<PERSON>B<PERSON>;
use crate::models::monster_boss::value_object::MonsterBossId;

#[automock]
#[async_trait]
pub trait MonsterBossRepository: Send + Sync {
    async fn save(&self, entity: &MonsterBoss) -> Result<(), RepositoryError>;
    async fn update(&self, entity: &MonsterBoss) -> Result<(), RepositoryError>;
    async fn delete(&self, id: &MonsterBossId) -> Result<(), RepositoryError>;
}

#[automock]
#[async_trait]
pub trait MonsterBossReadRepository: Send + Sync {
    async fn find_by_id(&self, id: &MonsterBossId) -> Result<Option<MonsterBoss>, RepositoryError>;
    async fn find_all(&self) -> Result<Vec<MonsterBoss>, RepositoryError>;
}
