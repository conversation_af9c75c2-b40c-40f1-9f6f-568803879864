use super::value_object::*;
use getset::{<PERSON><PERSON>, <PERSON><PERSON>};

#[derive(<PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, bon::<PERSON><PERSON><PERSON>, <PERSON><PERSON>, Set<PERSON>)]
#[getset(get = "pub", set = "pub")]
pub struct Role {
    id: RoleId,
    tag: RoleTag,
    user_color: RoleUserColor,
}

impl Role {
    pub fn new(id: RoleId, tag: RoleTag, user_color: RoleUserColor) -> Self {
        Self {
            id,
            tag,
            user_color,
        }
    }
}

#[cfg(test)]
mod tests {
    use reforged_shared::{IdTrait, Value};

    use super::*;

    #[test]
    fn test_role_new() {
        let id = RoleId::new(uuid::Uuid::now_v7());
        let tag = RoleTag::new(UserRoleTag::Administrator);
        let user_color = RoleUserColor::new("#FF0000");

        let role = Role::new(id.clone(), tag.clone(), user_color.clone());

        assert_eq!(role.id().get_id(), id.get_id());
        assert_eq!(role.tag().value(), tag.value());
        assert_eq!(role.user_color().value(), user_color.value());
    }

    #[test]
    fn test_role_builder() {
        let id = RoleId::new(uuid::Uuid::now_v7());
        let tag = RoleTag::new(UserRoleTag::Administrator);
        let user_color = RoleUserColor::new("#FF0000");

        let role = Role::builder()
            .id(id.clone())
            .tag(tag.clone())
            .user_color(user_color.clone())
            .build();

        assert_eq!(role.id().get_id(), id.get_id());
        assert_eq!(role.tag().value(), tag.value());
        assert_eq!(role.user_color().value(), user_color.value());
    }

    #[test]
    fn test_getters_and_setters() {
        let mut role = Role::default();

        let id = RoleId::new(uuid::Uuid::now_v7());
        let tag = RoleTag::new(UserRoleTag::Administrator);
        let user_color = RoleUserColor::new("#FF0000");

        role.set_id(id.clone());
        role.set_tag(tag.clone());
        role.set_user_color(user_color.clone());

        assert_eq!(role.id().get_id(), id.get_id());
        assert_eq!(role.tag().value(), tag.value());
        assert_eq!(role.user_color().value(), user_color.value());
    }
}
