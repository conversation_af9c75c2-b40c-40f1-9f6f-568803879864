use reforged_domain::models::monster_skill::entity::MonsterSkill;
use reforged_domain::models::monster_skill::value_object::{MonsterSkillId, MonsterSkills};
use reforged_domain::models::monster::value_object::MonsterId;
use reforged_domain::models::skill::value_object::SkillId;

#[derive(Debug)]
pub struct MonsterSkillDbModelMapper(super::super::models::monster_skills::Model);

impl MonsterSkillDbModelMapper {
    pub fn new(model: super::super::models::monster_skills::Model) -> Self {
        Self(model)
    }
}

impl From<MonsterSkillDbModelMapper> for MonsterSkill {
    fn from(value: MonsterSkillDbModelMapper) -> Self {
        let model = value.0;

        MonsterSkill::builder()
            .id(MonsterSkillId::new(model.id))
            .monster_id(MonsterId::new(model.monster_id))
            .skills(MonsterSkills::new(model.skills))
            .skill_id(SkillId::new(model.skill_id))
            .build()
    }
}
