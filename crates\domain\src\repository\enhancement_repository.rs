use async_trait::async_trait;
use mockall::automock;

use crate::error::RepositoryError;
use crate::models::enhancement::entity::Enhancement;
use crate::models::enhancement::value_object::EnhancementId;

#[automock]
#[async_trait]
pub trait EnhancementRepository: Send + Sync {
    async fn save(&self, entity: &Enhancement) -> Result<(), RepositoryError>;
    async fn update(&self, entity: &Enhancement) -> Result<(), RepositoryError>;
    async fn delete(&self, id: &EnhancementId) -> Result<(), RepositoryError>;
}

#[automock]
#[async_trait]
pub trait EnhancementReadRepository: Send + Sync {
    async fn find_by_id(&self, id: &EnhancementId) -> Result<Option<Enhancement>, RepositoryError>;
    async fn find_all(&self) -> Result<Vec<Enhancement>, RepositoryError>;
}
