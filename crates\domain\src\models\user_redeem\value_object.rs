use chrono::{DateTime, Utc};
use reforged_shared::{UuidId, Value};

use super::entity::UserRedeem;

pub type UserRedeemId = UuidId<UserRedeem>;

#[derive(Debug, PartialEq, Eq, PartialOrd, Ord, Clone, Default)]

pub struct RedeemDate(DateTime<Utc>);

impl RedeemDate {
    pub fn new(date: DateTime<Utc>) -> Self {
        Self(date)
    }
}

impl Value<DateTime<Utc>> for RedeemDate {
    fn value(&self) -> DateTime<Utc> {
        self.0
    }
}

impl From<DateTime<Utc>> for RedeemDate {
    fn from(date: DateTime<Utc>) -> Self {
        Self(date)
    }
}
