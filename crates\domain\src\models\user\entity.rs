use getset::{<PERSON><PERSON>, <PERSON><PERSON>};

use super::value_object::{<PERSON><PERSON>, <PERSON><PERSON><PERSON>assword, User<PERSON>d, User<PERSON><PERSON>, <PERSON>rna<PERSON>};

#[allow(unused)]
#[derive(<PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, bon::<PERSON><PERSON><PERSON>, <PERSON><PERSON>, Set<PERSON>)]
#[getset(get = "pub", set = "pub")]
pub struct User {
    id: UserId,
    username: <PERSON><PERSON><PERSON>,
    email: Email,
    role: User<PERSON><PERSON>,
    hashed_password: HashedPassword,
}

#[cfg(test)]
mod tests {
    use reforged_shared::Value;

    use super::*;

    #[test]
    fn test_user() {
        let user = User::builder()
            .id(UserId::new(uuid::Uuid::now_v7()))
            .email("<EMAIL>".into())
            .username("username".into())
            .hashed_password(HashedPassword::new("hash".into(), "salt".into()))
            .role(UserRole::Player)
            .build();

        assert_eq!(user.username().value(), "username");
        assert_eq!(user.email().value(), "<EMAIL>");
        assert_eq!(user.hashed_password().hash(), "hash");
        assert_eq!(user.hashed_password().salt(), "salt");
    }
}
