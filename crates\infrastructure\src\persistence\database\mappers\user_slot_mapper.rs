use reforged_domain::models::user_slot::entity::UserSlot;
use reforged_domain::models::user_slot::value_object::UserSlotId;
use reforged_domain::models::user::value_object::UserId;

#[derive(Debug)]
pub struct UserSlotDbModelMapper(super::super::models::user_slots::Model);

impl UserSlotDbModelMapper {
    pub fn new(model: super::super::models::user_slots::Model) -> Self {
        Self(model)
    }
}

impl From<UserSlotDbModelMapper> for UserSlot {
    fn from(value: UserSlotDbModelMapper) -> Self {
        let model = value.0;
        
        UserSlot::builder()
            .id(UserSlotId::new(model.id))
            .user_id(UserId::new(model.user_id))
            .slots_bag(model.slots_bag.into())
            .slots_bank(model.slots_bank.into())
            .slots_house(model.slots_house.into())
            .slots_auction(model.slots_auction.into())
            .build()
    }
}
