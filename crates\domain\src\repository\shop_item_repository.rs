use async_trait::async_trait;
use mockall::automock;

use crate::error::RepositoryError;
use crate::models::shop_item::entity::ShopItem;
use crate::models::shop_item::value_object::ShopItemId;

#[automock]
#[async_trait]
pub trait ShopItemRepository: Send + Sync {
    async fn save(&self, entity: &ShopItem) -> Result<(), RepositoryError>;
    async fn update(&self, entity: &ShopItem) -> Result<(), RepositoryError>;
    async fn delete(&self, id: &ShopItemId) -> Result<(), RepositoryError>;
}

#[automock]
#[async_trait]
pub trait ShopItemReadRepository: Send + Sync {
    async fn find_by_id(&self, id: &ShopItemId) -> Result<Option<ShopItem>, RepositoryError>;
    async fn find_all(&self) -> Result<Vec<ShopItem>, RepositoryError>;
}
