use crate::models::prelude::*;
use sea_orm::Schema;
use sea_orm_migration::prelude::*;

#[derive(DeriveMigrationName)]
pub struct Migration;

#[async_trait::async_trait]
impl MigrationTrait for Migration {
    async fn up(&self, manager: &SchemaManager) -> Result<(), DbErr> {
        let backend = manager.get_database_backend();
        let schema = Schema::new(backend);

        // MARK: admins tables, junction tables and related tables
        manager
            .create_table(schema.create_table_from_entity(AdminUploads))
            .await?;
        manager
            .create_table(schema.create_table_from_entity(Servers))
            .await?;
        manager
            .create_table(schema.create_table_from_entity(DiscordCommands))
            .await?;
        manager
            .create_table(schema.create_table_from_entity(ProfanityFilterSettings))
            .await?;
        manager
            .create_table(schema.create_table_from_entity(GameSettings))
            .await?;
        manager
            .create_table(schema.create_table_from_entity(GameRatesSettings))
            .await?;

        Ok(())
    }

    async fn down(&self, manager: &SchemaManager) -> Result<(), DbErr> {
        manager
            .drop_table(Table::drop().table(RedeemCodes).to_owned())
            .await?;
        manager
            .drop_table(Table::drop().table(GameRatesSettings).to_owned())
            .await?;
        manager
            .drop_table(Table::drop().table(GameSettings).to_owned())
            .await?;
        manager
            .drop_table(Table::drop().table(ProfanityFilterSettings).to_owned())
            .await?;
        manager
            .drop_table(Table::drop().table(DiscordCommands).to_owned())
            .await?;
        manager
            .drop_table(Table::drop().table(Servers).to_owned())
            .await?;
        manager
            .drop_table(Table::drop().table(AdminUploads).to_owned())
            .await
    }
}
