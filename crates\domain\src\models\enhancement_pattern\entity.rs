use super::value_object::{
    Description, Dexterity, Endurance, EnhancementPatternId, EnhancementPatternName, Intelligence,
    Luck, Strength, Wisdom,
};

use getset::{<PERSON><PERSON>, Set<PERSON>};

#[derive(Debug, <PERSON><PERSON>, Default, bon::<PERSON><PERSON><PERSON>, Get<PERSON>, Set<PERSON>)]
#[getset(get = "pub", set = "pub")]
pub struct EnhancementPattern {
    id: EnhancementPatternId,
    name: EnhancementPatternName,
    description: Description,
    wisdom: Wisdom,
    strength: Strength,
    luck: Luck,
    dexterity: Dexterity,
    endurance: Endurance,
    intelligence: Intelligence,
}

impl EnhancementPattern {
    pub fn new(
        id: EnhancementPatternId,
        name: EnhancementPatternName,
        description: Description,
        wisdom: Wisdom,
        strength: Strength,
        luck: Luck,
        dexterity: Dexterity,
        endurance: Endurance,
        intelligence: Intelligence,
    ) -> Self {
        Self {
            id,
            name,
            description,
            wisdom,
            strength,
            luck,
            dexterity,
            endurance,
            intelligence,
        }
    }
}

#[cfg(test)]
mod tests {
    use reforged_shared::{IdTrait, Value};

    use super::*;

    #[test]
    fn test_enhancement_pattern_new() {
        let id = EnhancementPatternId::new(uuid::Uuid::now_v7());
        let name = EnhancementPatternName::new("Warrior Pattern");
        let description = Description::new("Enhances strength and endurance");
        let wisdom = Wisdom::new(10);
        let strength = Strength::new(20);
        let luck = Luck::new(5);
        let dexterity = Dexterity::new(15);
        let endurance = Endurance::new(25);
        let intelligence = Intelligence::new(8);

        let enhancement_pattern = EnhancementPattern::new(
            id.clone(),
            name.clone(),
            description.clone(),
            wisdom.clone(),
            strength.clone(),
            luck.clone(),
            dexterity.clone(),
            endurance.clone(),
            intelligence.clone(),
        );

        assert_eq!(enhancement_pattern.id().get_id(), id.get_id());
        assert_eq!(enhancement_pattern.name().value(), name.value());
        assert_eq!(
            enhancement_pattern.description().value(),
            description.value()
        );
        assert_eq!(enhancement_pattern.wisdom().value(), wisdom.value());
        assert_eq!(enhancement_pattern.strength().value(), strength.value());
        assert_eq!(enhancement_pattern.luck().value(), luck.value());
        assert_eq!(enhancement_pattern.dexterity().value(), dexterity.value());
        assert_eq!(enhancement_pattern.endurance().value(), endurance.value());
        assert_eq!(
            enhancement_pattern.intelligence().value(),
            intelligence.value()
        );
    }

    #[test]
    fn test_enhancement_pattern_builder() {
        let id = EnhancementPatternId::new(uuid::Uuid::now_v7());
        let name = EnhancementPatternName::new("Warrior Pattern");
        let description = Description::new("Enhances strength and endurance");
        let wisdom = Wisdom::new(10);
        let strength = Strength::new(20);
        let luck = Luck::new(5);
        let dexterity = Dexterity::new(15);
        let endurance = Endurance::new(25);
        let intelligence = Intelligence::new(8);

        let enhancement_pattern = EnhancementPattern::builder()
            .id(id.clone())
            .name(name.clone())
            .description(description.clone())
            .wisdom(wisdom.clone())
            .strength(strength.clone())
            .luck(luck.clone())
            .dexterity(dexterity.clone())
            .endurance(endurance.clone())
            .intelligence(intelligence.clone())
            .build();

        assert_eq!(enhancement_pattern.id().get_id(), id.get_id());
        assert_eq!(enhancement_pattern.name().value(), name.value());
        assert_eq!(
            enhancement_pattern.description().value(),
            description.value()
        );
        assert_eq!(enhancement_pattern.wisdom().value(), wisdom.value());
        assert_eq!(enhancement_pattern.strength().value(), strength.value());
        assert_eq!(enhancement_pattern.luck().value(), luck.value());
        assert_eq!(enhancement_pattern.dexterity().value(), dexterity.value());
        assert_eq!(enhancement_pattern.endurance().value(), endurance.value());
        assert_eq!(
            enhancement_pattern.intelligence().value(),
            intelligence.value()
        );
    }

    #[test]
    fn test_getters_and_setters() {
        let mut enhancement_pattern = EnhancementPattern::default();

        let id = EnhancementPatternId::new(uuid::Uuid::now_v7());
        let name = EnhancementPatternName::new("Warrior Pattern");
        let description = Description::new("Enhances strength and endurance");
        let wisdom = Wisdom::new(10);
        let strength = Strength::new(20);
        let luck = Luck::new(5);
        let dexterity = Dexterity::new(15);
        let endurance = Endurance::new(25);
        let intelligence = Intelligence::new(8);

        enhancement_pattern.set_id(id.clone());
        enhancement_pattern.set_name(name.clone());
        enhancement_pattern.set_description(description.clone());
        enhancement_pattern.set_wisdom(wisdom.clone());
        enhancement_pattern.set_strength(strength.clone());
        enhancement_pattern.set_luck(luck.clone());
        enhancement_pattern.set_dexterity(dexterity.clone());
        enhancement_pattern.set_endurance(endurance.clone());
        enhancement_pattern.set_intelligence(intelligence.clone());

        assert_eq!(enhancement_pattern.id().get_id(), id.get_id());
        assert_eq!(enhancement_pattern.name().value(), name.value());
        assert_eq!(
            enhancement_pattern.description().value(),
            description.value()
        );
        assert_eq!(enhancement_pattern.wisdom().value(), wisdom.value());
        assert_eq!(enhancement_pattern.strength().value(), strength.value());
        assert_eq!(enhancement_pattern.luck().value(), luck.value());
        assert_eq!(enhancement_pattern.dexterity().value(), dexterity.value());
        assert_eq!(enhancement_pattern.endurance().value(), endurance.value());
        assert_eq!(
            enhancement_pattern.intelligence().value(),
            intelligence.value()
        );
    }
}
