use chrono::{DateTime, Utc};
use reforged_shared::{UuidId, Value};

use super::entity::UserGuild;

pub type UserGuildId = UuidId<UserGuild>;

#[derive(Debug, PartialEq, Eq, PartialOrd, Ord, Clone, Default)]

pub struct UserGuildRank(u32);

impl UserGuildRank {
    pub fn new(rank: u32) -> Self {
        Self(rank)
    }
}

impl Value<u32> for UserGuildRank {
    fn value(&self) -> u32 {
        self.0
    }
}

impl From<u32> for UserGuildRank {
    fn from(value: u32) -> Self {
        Self(value)
    }
}

#[derive(Debug, PartialEq, Eq, PartialOrd, Ord, Clone, Default)]

pub struct UserGuildJoinedAt(DateTime<Utc>);

impl UserGuildJoinedAt {
    pub fn new(joined_at: DateTime<Utc>) -> Self {
        Self(joined_at)
    }
}

impl Value<DateTime<Utc>> for UserGuildJoinedAt {
    fn value(&self) -> DateTime<Utc> {
        self.0
    }
}

impl From<DateTime<Utc>> for UserGuildJoinedAt {
    fn from(value: DateTime<Utc>) -> Self {
        Self(value)
    }
}
