use actix::prelude::*;
use futures_util::{FutureExt, future::LocalBoxFuture};
use tracing::{error, info};

use crate::actors::message_broker::message::Publish;
use reforged_domain::events::user_events::{
    ResetPasswordRequested, UserCreated, UserPasswordChanged,
};

use super::actor::NotificationActor;

impl Handler<Publish<UserCreated>> for NotificationActor {
    type Result = LocalBoxFuture<'static, ()>;

    fn handle(&mut self, msg: Publish<UserCreated>, _ctx: &mut Self::Context) -> Self::Result {
        let body = format!(
            r#"
        The Reforged team welcomes you to the Reforged community! <br>
        We are excited to have you join us on this journey. <br><br>

        Start playing now <a href="{}">here</a>. <br>
        If you have any questions or need assistance, please don't hesitate to reach out to our support team. <br><br>

        Best regards, <br>
        The Reforged Team
        "#,
            self.client_url()
        );
        let message = msg.message();
        let email = message.email.clone();
        let subject = "Welcome to Reforged";
        let email_service = self.email_service();

        Box::pin(async move {
            match email_service
                .send_email(email.to_owned(), subject.to_string(), body.to_string())
                .await
            {
                Err(e) => error!("Failed to send email: {}", e),
                Ok(()) => {
                    info!("Notification sent to {}; msg={}", email, subject);
                }
            }
        })
        .boxed_local()
    }
}

impl Handler<Publish<ResetPasswordRequested>> for NotificationActor {
    type Result = LocalBoxFuture<'static, ()>;

    fn handle(
        &mut self,
        msg: Publish<ResetPasswordRequested>,
        _ctx: &mut Self::Context,
    ) -> Self::Result {
        let message = msg.message();
        let url = self.client_url();
        let token = message.reset_token.as_str();

        let body = format!(
            r#"
        You have requested to reset your password. <br>

        If you did not request this, please ignore this email. <br><br>

        If you did request this, please click the link
        below to reset your password. <br><br>

        <a href="{url}/reset-password?token={token}">Reset Password</a> <br><br>
        or copy and paste the link below into your browser: {url}/reset-password?token={token} <br><br>

        If you have any questions or need assistance, please don't hesitate to reach out to our support team. <br><br>

        Best regards, <br>
        The Reforged Team
        "#,
        );

        let email = message.email.clone();
        let subject = "Password Reset Request";
        let email_service = self.email_service();

        async move {
            match email_service
                .send_email(email.to_owned(), subject.to_string(), body.to_string())
                .await
            {
                Err(e) => error!("Failed to send email: {}", e),
                Ok(()) => {
                    info!("Notification sent to {}; msg={}", email, subject);
                }
            }
        }
        .boxed_local()
    }
}

impl Handler<Publish<UserPasswordChanged>> for NotificationActor {
    type Result = LocalBoxFuture<'static, ()>;

    fn handle(
        &mut self,
        msg: Publish<UserPasswordChanged>,
        _ctx: &mut Self::Context,
    ) -> Self::Result {
        let message = msg.message();
        let url = self.client_url();

        let body = format!(
            r#"
        Your password has been changed. <br>

        If you did not change your password, please contact our support team immediately. <br><br>

        If you did change your password, please click the link to login. <br><br>

        <a href="{url}/login">Login</a> <br><br>
        or copy and paste the link below into your browser: {url}/login <br><br>

        If you have any questions or need assistance, please don't hesitate to reach out to our support team. <br><br>

        Best regards, <br>
        The Reforged Team
        "#
        );

        let email = message.email.clone();
        let subject = "Password Changed";
        let email_service = self.email_service();

        async move {
            match email_service
                .send_email(email.to_owned(), subject.to_string(), body.to_string())
                .await
            {
                Err(e) => error!("Failed to send email: {}", e),
                Ok(()) => {
                    info!("Notification sent to {}; msg={}", email, subject);
                }
            }
        }
        .boxed_local()
    }
}
