use chrono::{DateTime, Utc};
use reforged_shared::{UuidId, Value};

use super::entity::UserItem;

pub type UserItemId = UuidId<UserItem>;

#[derive(Debug, PartialEq, PartialOrd, Clone, Default)]
pub struct UserItemQuantity(u64);

impl UserItemQuantity {
    pub fn new(quantity: impl Into<u64>) -> Self {
        Self(quantity.into())
    }
}

impl Value<u64> for UserItemQuantity {
    fn value(&self) -> u64 {
        self.0.clone()
    }
}

#[derive(Debug, PartialEq, Eq, PartialOrd, Ord, Clone, Default)]
pub struct UserItemDate(DateTime<Utc>);

impl UserItemDate {
    pub fn new(created_at: DateTime<Utc>) -> Self {
        Self(created_at)
    }
}

impl Value<DateTime<Utc>> for UserItemDate {
    fn value(&self) -> DateTime<Utc> {
        self.0
    }
}

#[derive(Debug, PartialEq, Eq, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>)]
pub struct UserItemEquipped(bool);

impl UserItemEquipped {
    pub fn new(equipped: bool) -> Self {
        Self(equipped)
    }
}

impl Value<bool> for UserItemEquipped {
    fn value(&self) -> bool {
        self.0
    }
}

impl From<i16> for UserItemEquipped {
    fn from(equipped: i16) -> Self {
        Self(equipped != 0)
    }
}

#[derive(Debug, PartialEq, Eq, PartialOrd, Ord, Clone, Default)]
pub struct UserItemBank(bool);

impl UserItemBank {
    pub fn new(bank: bool) -> Self {
        Self(bank)
    }
}

impl Value<bool> for UserItemBank {
    fn value(&self) -> bool {
        self.0
    }
}

impl From<i16> for UserItemBank {
    fn from(bank: i16) -> Self {
        Self(bank != 0)
    }
}

#[derive(Debug, PartialEq, Eq, PartialOrd, Ord, Clone, Default)]
pub struct UserItemBind(Option<bool>);

impl UserItemBind {
    pub fn new(bind: Option<bool>) -> Self {
        Self(bind)
    }
}

impl Value<Option<bool>> for UserItemBind {
    fn value(&self) -> Option<bool> {
        self.0
    }
}

impl From<Option<i16>> for UserItemBind {
    fn from(bind: Option<i16>) -> Self {
        Self(bind.map(|b| b != 0))
    }
}
