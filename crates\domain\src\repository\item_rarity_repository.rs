use async_trait::async_trait;

use crate::{
    error::RepositoryError,
    models::item_rarity::{entity::ItemRarity, value_object::ItemRarityEnum},
};

#[mockall::automock]
#[async_trait]
pub trait ItemRarityRepository: Send + Sync {
    async fn save(&self, item_rarity: &ItemRarity) -> Result<ItemRarity, RepositoryError>;
    async fn delete(&self, id: i32) -> Result<(), RepositoryError>;
}

#[mockall::automock]
#[async_trait]
pub trait ItemRarityReadRepository: Send + Sync {
    async fn find_by_id(&self, id: i32) -> Result<Option<ItemRarity>, RepositoryError>;
    async fn find_by_name(
        &self,
        name: ItemRarityEnum,
    ) -> Result<Option<ItemRarity>, RepositoryError>;
    async fn find_all(&self) -> Result<Vec<ItemRarity>, RepositoryError>;
    async fn find_paginate(
        &self,
        page: u32,
        limit: u32,
    ) -> Result<Vec<ItemRarity>, RepositoryError>;
}
