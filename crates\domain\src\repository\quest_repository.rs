use async_trait::async_trait;
use mockall::automock;

use crate::error::RepositoryError;
use crate::models::quest::entity::Quest;
use crate::models::quest::value_object::QuestId;

#[automock]
#[async_trait]
pub trait QuestRepository: Send + Sync {
    async fn save(&self, entity: &Quest) -> Result<(), RepositoryError>;
    async fn update(&self, entity: &Quest) -> Result<(), RepositoryError>;
    async fn delete(&self, id: &QuestId) -> Result<(), RepositoryError>;
}

#[automock]
#[async_trait]
pub trait QuestReadRepository: Send + Sync {
    async fn find_by_id(&self, id: &QuestId) -> Result<Option<Quest>, RepositoryError>;
    async fn find_all(&self) -> Result<Vec<Quest>, RepositoryError>;
}
