use crate::models::item::value_object::ItemId;
use crate::models::item_requirement::value_object::*;
use getset::{<PERSON><PERSON>, <PERSON><PERSON>};

#[derive(<PERSON>bu<PERSON>, <PERSON><PERSON>, <PERSON><PERSON>ult, bon::<PERSON><PERSON><PERSON>, <PERSON><PERSON>, Set<PERSON>)]
#[getset(get = "pub", set = "pub")]
pub struct ItemRequirement {
    pub id: ItemRequirementId,
    pub item_id: ItemId,
    pub req_item_id: ItemId,
    pub quantity: ItemRequirementQuantity,
}

impl ItemRequirement {
    pub fn new(
        id: ItemRequirementId,
        item_id: ItemId,
        req_item_id: ItemId,
        quantity: ItemRequirementQuantity,
    ) -> Self {
        Self {
            id,
            item_id,
            req_item_id,
            quantity,
        }
    }
}

#[cfg(test)]
mod tests {
    use reforged_shared::{IdTrait, Value};

    use super::*;

    #[test]
    fn test_item_requirement_new() {
        let id = ItemRequirementId::new(uuid::Uuid::now_v7());
        let item_id = ItemId::new(uuid::Uuid::now_v7());
        let req_item_id = ItemId::new(uuid::Uuid::now_v7());
        let quantity = ItemRequirementQuantity::new(5);

        let requirement = ItemRequirement::new(
            id.clone(),
            item_id.clone(),
            req_item_id.clone(),
            quantity.clone(),
        );

        assert_eq!(requirement.id.get_id(), id.get_id());
        assert_eq!(requirement.item_id.get_id(), item_id.get_id());
        assert_eq!(requirement.req_item_id.get_id(), req_item_id.get_id());
        assert_eq!(requirement.quantity.value(), quantity.value());
    }

    #[test]
    fn test_item_requirement_builder() {
        let id = ItemRequirementId::new(uuid::Uuid::now_v7());
        let item_id = ItemId::new(uuid::Uuid::now_v7());
        let req_item_id = ItemId::new(uuid::Uuid::now_v7());
        let quantity = ItemRequirementQuantity::new(5);

        let requirement = ItemRequirement::builder()
            .id(id.clone())
            .item_id(item_id.clone())
            .req_item_id(req_item_id.clone())
            .quantity(quantity.clone())
            .build();

        assert_eq!(requirement.id.get_id(), id.get_id());
        assert_eq!(requirement.item_id.get_id(), item_id.get_id());
        assert_eq!(requirement.req_item_id.get_id(), req_item_id.get_id());
        assert_eq!(requirement.quantity.value(), quantity.value());
    }

    #[test]
    fn test_getters_and_setters() {
        let mut requirement = ItemRequirement::default();

        let id = ItemRequirementId::new(uuid::Uuid::now_v7());
        let item_id = ItemId::new(uuid::Uuid::now_v7());
        let req_item_id = ItemId::new(uuid::Uuid::now_v7());
        let quantity = ItemRequirementQuantity::new(5);

        requirement.set_id(id.clone());
        requirement.set_item_id(item_id.clone());
        requirement.set_req_item_id(req_item_id.clone());
        requirement.set_quantity(quantity.clone());

        assert_eq!(requirement.id().get_id(), id.get_id());
        assert_eq!(requirement.item_id().get_id(), item_id.get_id());
        assert_eq!(requirement.req_item_id().get_id(), req_item_id.get_id());
        assert_eq!(requirement.quantity().value(), quantity.value());
    }
}
