use crate::models;
use sea_orm::{ActiveModelTrait, IntoActiveModel};
use sea_orm_migration::prelude::*;

#[derive(DeriveMigrationName)]
pub struct Migration;

#[async_trait::async_trait]
impl MigrationTrait for Migration {
    async fn up(&self, manager: &SchemaManager) -> Result<(), DbErr> {
        let db = manager.get_connection();

        let monsters = include_str!("../json/monsters.json");
        let monsters: Vec<models::monsters::Model> = serde_json::from_str(monsters).unwrap();

        for model in monsters {
            let monster = model.into_active_model();
            monster.insert(db).await?;
        }

        let monster_skills = include_str!("../json/monsters_skills.json");
        let monster_skills: Vec<models::monster_skills::Model> =
            serde_json::from_str(monster_skills).unwrap();

        for model in monster_skills {
            let monster_skill = model.into_active_model();
            monster_skill.insert(db).await?;
        }

        let monster_drops = include_str!("../json/monsters_drops.json");
        let monster_drops: Vec<models::monster_drops::Model> =
            serde_json::from_str(monster_drops).unwrap();

        for model in monster_drops {
            let monster_drop = model.into_active_model();
            monster_drop.insert(db).await?;
        }

        let map_monsters = include_str!("../json/maps_monsters.json");
        let map_monsters: Vec<models::map_monsters::Model> =
            serde_json::from_str(map_monsters).unwrap();

        for model in map_monsters {
            let map_monster = model.into_active_model();
            map_monster.insert(db).await?;
        }

        let monster_bosses = include_str!("../json/monsters_bosses.json");
        let monster_bosses: Vec<models::monster_bosses::Model> =
            serde_json::from_str(monster_bosses).unwrap();

        for model in monster_bosses {
            let monster_boss = model.into_active_model();
            monster_boss.insert(db).await?;
        }

        Ok(())
    }

    async fn down(&self, _manager: &SchemaManager) -> Result<(), DbErr> {
        Ok(())
    }
}
