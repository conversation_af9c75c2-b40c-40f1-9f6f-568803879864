INSERT INTO "user_boosts" ("user_id", "cp_boost_expire", "rep_boost_expire", "gold_boost_expire", "exp_boost_expire", "upgrade_expire", "upgrade_days", "upgraded") VALUES (5, '1970-01-01', '1970-01-01', '1970-01-01', '1970-01-01', '1970-01-01', 0, false);
INSERT INTO "user_boosts" ("user_id", "cp_boost_expire", "rep_boost_expire", "gold_boost_expire", "exp_boost_expire", "upgrade_expire", "upgrade_days", "upgraded") VALUES (6, '1970-01-01', '1970-01-01', '1970-01-01', '1970-01-01', '1970-01-01', 0, false);
INSERT INTO "user_boosts" ("user_id", "cp_boost_expire", "rep_boost_expire", "gold_boost_expire", "exp_boost_expire", "upgrade_expire", "upgrade_days", "upgraded") VALUES (1, '1970-01-01', '1970-01-01', '1970-01-01', '1970-01-01', '1970-01-01', 0, true);
INSERT INTO "user_boosts" ("user_id", "cp_boost_expire", "rep_boost_expire", "gold_boost_expire", "exp_boost_expire", "upgrade_expire", "upgrade_days", "upgraded") VALUES (16, '1970-01-01', '1970-01-01', '1970-01-01', '1970-01-01', '1970-01-01', 0, false);
INSERT INTO "user_boosts" ("user_id", "cp_boost_expire", "rep_boost_expire", "gold_boost_expire", "exp_boost_expire", "upgrade_expire", "upgrade_days", "upgraded") VALUES (17, '1970-01-01', '1970-01-01', '1970-01-01', '1970-01-01', '1970-01-01', 0, false);
INSERT INTO "user_boosts" ("user_id", "cp_boost_expire", "rep_boost_expire", "gold_boost_expire", "exp_boost_expire", "upgrade_expire", "upgrade_days", "upgraded") VALUES (18, '1970-01-01', '1970-01-01', '1970-01-01', '1970-01-01', '1970-01-01', 0, false);
INSERT INTO "user_boosts" ("user_id", "cp_boost_expire", "rep_boost_expire", "gold_boost_expire", "exp_boost_expire", "upgrade_expire", "upgrade_days", "upgraded") VALUES (19, '1970-01-01', '1970-01-01', '1970-01-01', '1970-01-01', '1970-01-01', 0, false);
INSERT INTO "user_boosts" ("user_id", "cp_boost_expire", "rep_boost_expire", "gold_boost_expire", "exp_boost_expire", "upgrade_expire", "upgrade_days", "upgraded") VALUES (20, '1970-01-01', '1970-01-01', '1970-01-01', '1970-01-01', '1970-01-01', 0, false);
INSERT INTO "user_boosts" ("user_id", "cp_boost_expire", "rep_boost_expire", "gold_boost_expire", "exp_boost_expire", "upgrade_expire", "upgrade_days", "upgraded") VALUES (21, '1970-01-01', '1970-01-01', '1970-01-01', '1970-01-01', '1970-01-01', 0, false);
INSERT INTO "user_boosts" ("user_id", "cp_boost_expire", "rep_boost_expire", "gold_boost_expire", "exp_boost_expire", "upgrade_expire", "upgrade_days", "upgraded") VALUES (22, '1970-01-01', '1970-01-01', '1970-01-01', '1970-01-01', '1970-01-01', 0, false);
INSERT INTO "user_boosts" ("user_id", "cp_boost_expire", "rep_boost_expire", "gold_boost_expire", "exp_boost_expire", "upgrade_expire", "upgrade_days", "upgraded") VALUES (24, '1970-01-01', '1970-01-01', '1970-01-01', '1970-01-01', '1970-01-01', 0, false);
INSERT INTO "user_boosts" ("user_id", "cp_boost_expire", "rep_boost_expire", "gold_boost_expire", "exp_boost_expire", "upgrade_expire", "upgrade_days", "upgraded") VALUES (57, '1970-01-01', '1970-01-01', '1970-01-01', '1970-01-01', '1970-01-01', 0, false);
INSERT INTO "user_boosts" ("user_id", "cp_boost_expire", "rep_boost_expire", "gold_boost_expire", "exp_boost_expire", "upgrade_expire", "upgrade_days", "upgraded") VALUES (58, '1970-01-01', '1970-01-01', '1970-01-01', '1970-01-01', '1970-01-01', 0, false);
INSERT INTO "user_boosts" ("user_id", "cp_boost_expire", "rep_boost_expire", "gold_boost_expire", "exp_boost_expire", "upgrade_expire", "upgrade_days", "upgraded") VALUES (59, '1970-01-01', '1970-01-01', '1970-01-01', '1970-01-01', '1970-01-01', 0, false);
INSERT INTO "user_boosts" ("user_id", "cp_boost_expire", "rep_boost_expire", "gold_boost_expire", "exp_boost_expire", "upgrade_expire", "upgrade_days", "upgraded") VALUES (60, '1970-01-01', '1970-01-01', '1970-01-01', '1970-01-01', '1970-01-01', 0, false);
INSERT INTO "user_boosts" ("user_id", "cp_boost_expire", "rep_boost_expire", "gold_boost_expire", "exp_boost_expire", "upgrade_expire", "upgrade_days", "upgraded") VALUES (61, '1970-01-01', '1970-01-01', '1970-01-01', '1970-01-01', '1970-01-01', 0, false);
INSERT INTO "user_boosts" ("user_id", "cp_boost_expire", "rep_boost_expire", "gold_boost_expire", "exp_boost_expire", "upgrade_expire", "upgrade_days", "upgraded") VALUES (62, '1970-01-01', '1970-01-01', '1970-01-01', '1970-01-01', '1970-01-01', 0, false);
INSERT INTO "user_boosts" ("user_id", "cp_boost_expire", "rep_boost_expire", "gold_boost_expire", "exp_boost_expire", "upgrade_expire", "upgrade_days", "upgraded") VALUES (63, '1970-01-01', '1970-01-01', '1970-01-01', '1970-01-01', '1970-01-01', 0, false);
INSERT INTO "user_boosts" ("user_id", "cp_boost_expire", "rep_boost_expire", "gold_boost_expire", "exp_boost_expire", "upgrade_expire", "upgrade_days", "upgraded") VALUES (64, '1970-01-01', '1970-01-01', '1970-01-01', '1970-01-01', '1970-01-01', 0, false);
