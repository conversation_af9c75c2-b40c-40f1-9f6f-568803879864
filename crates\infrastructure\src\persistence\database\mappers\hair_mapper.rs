use reforged_domain::models::hair::{entity::Hair, value_object::HairId};

#[derive(Debug)]
pub struct HairDbModelMapper(super::super::models::hairs::Model);

impl HairDbModelMapper {
    pub fn new(hair: super::super::models::hairs::Model) -> Self {
        Self(hair)
    }
}

impl From<HairDbModelMapper> for Hair {
    fn from(value: HairDbModelMapper) -> Self {
        let hair_model = value.0;

        let id = HairId::new(hair_model.id);

        Hair::builder()
            .id(id)
            .name(hair_model.name.into())
            .gender(hair_model.gender.into())
            .file(hair_model.file.into())
            .build()
    }
}
