//! `SeaORM` Entity, @generated by sea-orm-codegen 1.1.0

use sea_orm::entity::prelude::*;
use serde::Deserialize;

#[derive(Clone, Debug, PartialEq, DeriveEntityModel, Eq, Deserialize)]
#[sea_orm(table_name = "shops")]
pub struct Model {
    #[sea_orm(primary_key, auto_increment = false)]
    pub id: Uuid,
    pub name: String,
    pub house: bool,
    pub upgrade: bool,
    pub staff: bool,
    pub limited: bool,
    pub field: String,
    pub achievement_id: Option<Uuid>,
    pub item_id: Option<Uuid>,
}

#[derive(Copy, Clone, Debug, EnumIter, DeriveRelation)]
pub enum Relation {
    #[sea_orm(
        belongs_to = "super::items::Entity",
        from = "Column::ItemId",
        to = "super::items::Column::Id",
        on_update = "Cascade",
        on_delete = "SetNull"
    )]
    Items,
    #[sea_orm(has_many = "super::shop_items::Entity")]
    ShopItems,
    #[sea_orm(has_many = "super::shop_locations::Entity")]
    ShopLocations,
}

impl Related<super::items::Entity> for Entity {
    fn to() -> RelationDef {
        Relation::Items.def()
    }
}

impl Related<super::shop_items::Entity> for Entity {
    fn to() -> RelationDef {
        Relation::ShopItems.def()
    }
}

impl Related<super::shop_locations::Entity> for Entity {
    fn to() -> RelationDef {
        Relation::ShopLocations.def()
    }
}

impl ActiveModelBehavior for ActiveModel {}
