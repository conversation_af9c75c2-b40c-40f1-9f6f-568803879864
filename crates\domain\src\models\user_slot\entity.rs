use super::value_object::*;
use crate::models::user::value_object::UserId;
use getset::{<PERSON><PERSON>, <PERSON><PERSON>};

#[derive(<PERSON>bu<PERSON>, <PERSON><PERSON>, Default, bon::<PERSON><PERSON><PERSON>, <PERSON><PERSON>, Setters)]
#[getset(get = "pub", set = "pub")]
pub struct UserSlot {
    id: UserSlotId,
    user_id: UserId,
    slots_bag: UserSlotBag,
    slots_bank: UserSlotBank,
    slots_house: UserSlotHouse,
    slots_auction: UserSlotAuction,
}

impl UserSlot {
    pub fn new(
        id: UserSlotId,
        user_id: UserId,
        slots_bag: UserSlotBag,
        slots_bank: UserSlotBank,
        slots_house: UserSlotHouse,
        slots_auction: UserSlotAuction,
    ) -> Self {
        Self {
            id,
            user_id,
            slots_bag,
            slots_bank,
            slots_house,
            slots_auction,
        }
    }
}

#[cfg(test)]
mod tests {
    use reforged_shared::{IdTrait, Value};

    use super::*;

    #[test]
    fn test_user_slot_new() {
        let id = UserSlotId::new(uuid::Uuid::now_v7());
        let user_id = UserId::new(uuid::Uuid::now_v7());
        let slots_bag = UserSlotBag::new(100);
        let slots_bank = UserSlotBank::new(200);
        let slots_house = UserSlotHouse::new(300);
        let slots_auction = UserSlotAuction::new(50);

        let user_slot = UserSlot::new(
            id.clone(),
            user_id.clone(),
            slots_bag.clone(),
            slots_bank.clone(),
            slots_house.clone(),
            slots_auction.clone(),
        );

        assert_eq!(user_slot.id.get_id(), id.get_id());
        assert_eq!(user_slot.user_id.get_id(), user_id.get_id());
        assert_eq!(user_slot.slots_bag.value(), slots_bag.value());
        assert_eq!(user_slot.slots_bank.value(), slots_bank.value());
        assert_eq!(user_slot.slots_house.value(), slots_house.value());
        assert_eq!(user_slot.slots_auction.value(), slots_auction.value());
    }

    #[test]
    fn test_user_slot_builder() {
        let id = UserSlotId::new(uuid::Uuid::now_v7());
        let user_id = UserId::new(uuid::Uuid::now_v7());
        let slots_bag = UserSlotBag::new(100);
        let slots_bank = UserSlotBank::new(200);
        let slots_house = UserSlotHouse::new(300);
        let slots_auction = UserSlotAuction::new(50);

        let user_slot = UserSlot::builder()
            .id(id.clone())
            .user_id(user_id.clone())
            .slots_bag(slots_bag.clone())
            .slots_bank(slots_bank.clone())
            .slots_house(slots_house.clone())
            .slots_auction(slots_auction.clone())
            .build();

        assert_eq!(user_slot.id.get_id(), id.get_id());
        assert_eq!(user_slot.user_id.get_id(), user_id.get_id());
        assert_eq!(user_slot.slots_bag.value(), slots_bag.value());
        assert_eq!(user_slot.slots_bank.value(), slots_bank.value());
        assert_eq!(user_slot.slots_house.value(), slots_house.value());
        assert_eq!(user_slot.slots_auction.value(), slots_auction.value());
    }

    #[test]
    fn test_getters_and_setters() {
        let mut user_slot = UserSlot::default();

        let id = UserSlotId::new(uuid::Uuid::now_v7());
        let user_id = UserId::new(uuid::Uuid::now_v7());
        let slots_bag = UserSlotBag::new(100);
        let slots_bank = UserSlotBank::new(200);
        let slots_house = UserSlotHouse::new(300);
        let slots_auction = UserSlotAuction::new(50);

        user_slot.set_id(id.clone());
        user_slot.set_user_id(user_id.clone());
        user_slot.set_slots_bag(slots_bag.clone());
        user_slot.set_slots_bank(slots_bank.clone());
        user_slot.set_slots_house(slots_house.clone());
        user_slot.set_slots_auction(slots_auction.clone());

        assert_eq!(user_slot.id().get_id(), id.get_id());
        assert_eq!(user_slot.user_id().get_id(), user_id.get_id());
        assert_eq!(user_slot.slots_bag().value(), slots_bag.value());
        assert_eq!(user_slot.slots_bank().value(), slots_bank.value());
        assert_eq!(user_slot.slots_house().value(), slots_house.value());
        assert_eq!(user_slot.slots_auction().value(), slots_auction.value());
    }
}
