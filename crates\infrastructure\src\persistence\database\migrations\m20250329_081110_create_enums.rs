use crate::models::{
    prelude::*,
    sea_orm_active_enums::{Gender, LoginLocation, MarketType, StoreType, UserRole},
};
use extension::postgres::Type;
use sea_orm::{ActiveEnum, Schema};
use sea_orm_migration::prelude::*;

#[derive(DeriveMigrationName)]
pub struct Migration;

#[async_trait::async_trait]
impl MigrationTrait for Migration {
    async fn up(&self, manager: &SchemaManager) -> Result<(), DbErr> {
        let backend = manager.get_database_backend();
        let schema = Schema::new(backend);

        // CREATE ENUM TYPES
        manager
            .create_type(schema.create_enum_from_active_enum::<Gender>().to_owned())
            .await?;
        manager
            .create_type(
                schema
                    .create_enum_from_active_enum::<LoginLocation>()
                    .to_owned(),
            )
            .await?;
        manager
            .create_type(
                schema
                    .create_enum_from_active_enum::<MarketType>()
                    .to_owned(),
            )
            .await?;
        manager
            .create_type(schema.create_enum_from_active_enum::<UserRole>().to_owned())
            .await?;
        manager
            .create_type(
                schema
                    .create_enum_from_active_enum::<StoreType>()
                    .to_owned(),
            )
            .await?;

        // CREATE TABLES AND JUNCTION TABLES
        // MARK: Roles table
        manager
            .create_table(schema.create_table_from_entity(Roles))
            .await?;
        // MARK: titles tables
        manager
            .create_table(schema.create_table_from_entity(Titles))
            .await?;

        Ok(())
    }

    async fn down(&self, manager: &SchemaManager) -> Result<(), DbErr> {
        // drop all enum types
        manager
            .drop_type(Type::drop().if_exists().name(Gender::name()).to_owned())
            .await?;
        manager
            .drop_type(
                Type::drop()
                    .if_exists()
                    .name(LoginLocation::name())
                    .to_owned(),
            )
            .await?;
        manager
            .drop_type(Type::drop().if_exists().name(MarketType::name()).to_owned())
            .await?;
        manager
            .drop_type(Type::drop().if_exists().name(UserRole::name()).to_owned())
            .await?;
        manager
            .drop_type(Type::drop().if_exists().name(StoreType::name()).to_owned())
            .await?;

        // drop all tables from the up migration
        manager
            .drop_table(Table::drop().table(Roles).to_owned())
            .await?;

        manager
            .drop_table(Table::drop().table(Titles).to_owned())
            .await
    }
}
