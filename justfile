set shell := ["nu", "-c"]

default:
    echo 'Hello, world!'

create-entity name:
    mkdir crates/domain/src/models/{{name}}
    touch crates/domain/src/models/{{name}}/mod.rs
    touch crates/domain/src/models/{{name}}/entity.rs
    touch crates/domain/src/models/{{name}}/value_object.rs
    echo "pub mod entity;\n" | save --append crates/domain/src/models/{{name}}/mod.rs
    echo "pub mod value_object;\n" | save --append crates/domain/src/models/{{name}}/mod.rs
    echo "\npub mod {{name}};" | save --append crates/domain/src/models/mod.rs

format-all:
    cargo fmt --all

format-domain:
    cargo fmt --package reforged-domain

format-application:
    cargo fmt --package reforged-application

format-api:
    cargo fmt --package reforged-api

format-shared:
    cargo fmt --package reforged-shared

test:
    cargo test --all

test-domain:
    cargo test --package reforged-domain

test-application:
    cargo test --package reforged-application

test-api:
    cargo test --package reforged-api

test-shared:
    cargo test --package reforged-shared

migrate:
    cargo run --bin migrator

reset-db:
    cargo run --bin migrator fresh

run-server:
    cargo run --bin server

run-all:
    mprocs "cargo run --bin server" "cargo run --bin notification" "cargo run --bin persistence"

add-migration name:
    sea-orm-cli migrate generate -d crates\infrastructure\src\persistence\database\migrations\ --universal-time {{name}}

add-seeder name:
    sea-orm-cli migrate generate -d crates\infrastructure\src\persistence\database\seeders\ --universal-time {{name}}
