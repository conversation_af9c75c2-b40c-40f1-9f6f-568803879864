use async_trait::async_trait;
use mockall::automock;

use crate::error::RepositoryError;
use crate::models::quest_requirement::entity::QuestRequirement;
use crate::models::quest_requirement::value_object::QuestRequirementId;

#[automock]
#[async_trait]
pub trait QuestRequirementRepository: Send + Sync {
    async fn save(&self, entity: &QuestRequirement) -> Result<(), RepositoryError>;
    async fn update(&self, entity: &QuestRequirement) -> Result<(), RepositoryError>;
    async fn delete(&self, id: &QuestRequirementId) -> Result<(), RepositoryError>;
}

#[automock]
#[async_trait]
pub trait QuestRequirementReadRepository: Send + Sync {
    async fn find_by_id(
        &self,
        id: &QuestRequirementId,
    ) -> Result<Option<QuestRequirement>, RepositoryError>;
    async fn find_all(&self) -> Result<Vec<QuestRequirement>, RepositoryError>;
}
