use uuid::NoContext;

pub fn uuid_now() -> uuid::Uuid {
    uuid::Uuid::now_v7()
}

pub fn uuid_from_secs(sec: u64) -> uuid::Uuid {
    uuid::Uuid::new_v7(uuid::Timestamp::from_unix(NoContext, sec, 0))
}

pub fn uuid_from_millis(millis: u64) -> uuid::Uuid {
    uuid::Uuid::new_v7(uuid::Timestamp::from_unix(NoContext, millis / 1000, 0))
}

pub fn uuid_from_micros(micros: u64) -> uuid::Uuid {
    uuid::Uuid::new_v7(uuid::Timestamp::from_unix(NoContext, micros / 1000000, 0))
}

pub fn uuid_from_str(uuid_str: &str) -> Result<uuid::Uuid, uuid::Error> {
    uuid::Uuid::parse_str(uuid_str)
}
