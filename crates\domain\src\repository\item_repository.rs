use async_trait::async_trait;
use mockall::automock;

use crate::error::RepositoryError;
use crate::models::item::entity::Item;
use crate::models::item::value_object::ItemId;

#[automock]
#[async_trait]
pub trait ItemRepository: Send + Sync {
    async fn save(&self, entity: &Item) -> Result<(), RepositoryError>;
    async fn update(&self, entity: &Item) -> Result<(), RepositoryError>;
    async fn delete(&self, id: &ItemId) -> Result<(), RepositoryError>;
}

#[automock]
#[async_trait]
pub trait ItemReadRepository: Send + Sync {
    async fn find_by_id(&self, id: &ItemId) -> Result<Option<Item>, RepositoryError>;
    async fn find_all(&self) -> Result<Vec<Item>, RepositoryError>;
}
