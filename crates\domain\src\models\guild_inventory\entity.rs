use crate::models::{
    guild::value_object::GuildId, item::value_object::ItemId, user::value_object::UserId,
};
use getset::{<PERSON><PERSON>, Set<PERSON>};

use super::value_object::GuildInventoryId;

#[derive(<PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>ult, bon::<PERSON><PERSON><PERSON>, <PERSON><PERSON>, Setters)]
#[getset(get = "pub", set = "pub")]
pub struct GuildInventory {
    id: GuildInventoryId,
    guild_id: GuildId,
    item_id: ItemId,
    user_id: UserId,
}

impl GuildInventory {
    pub fn new(id: GuildInventoryId, guild_id: GuildId, item_id: ItemId, user_id: UserId) -> Self {
        Self {
            id,
            guild_id,
            item_id,
            user_id,
        }
    }
}

#[cfg(test)]
mod tests {
    use reforged_shared::IdTrait;

    use super::*;

    #[test]
    fn test_guild_inventory_new() {
        let id = GuildInventoryId::new(uuid::Uuid::now_v7());
        let guild_id = GuildId::new(uuid::Uuid::now_v7());
        let item_id = ItemId::new(uuid::Uuid::now_v7());
        let user_id = UserId::new(uuid::Uuid::now_v7());

        let inventory = GuildInventory::new(
            id.clone(),
            guild_id.clone(),
            item_id.clone(),
            user_id.clone(),
        );

        assert_eq!(inventory.id().get_id(), id.get_id());
        assert_eq!(inventory.guild_id().get_id(), guild_id.get_id());
        assert_eq!(inventory.item_id().get_id(), item_id.get_id());
        assert_eq!(inventory.user_id().get_id(), user_id.get_id());
    }

    #[test]
    fn test_guild_inventory_builder() {
        let id = GuildInventoryId::new(uuid::Uuid::now_v7());
        let guild_id = GuildId::new(uuid::Uuid::now_v7());
        let item_id = ItemId::new(uuid::Uuid::now_v7());
        let user_id = UserId::new(uuid::Uuid::now_v7());

        let inventory = GuildInventory::builder()
            .id(id.clone())
            .guild_id(guild_id.clone())
            .item_id(item_id.clone())
            .user_id(user_id.clone())
            .build();

        assert_eq!(inventory.id().get_id(), id.get_id());
        assert_eq!(inventory.guild_id().get_id(), guild_id.get_id());
        assert_eq!(inventory.item_id().get_id(), item_id.get_id());
        assert_eq!(inventory.user_id().get_id(), user_id.get_id());
    }

    #[test]
    fn test_getters_and_setters() {
        let mut inventory = GuildInventory::default();

        let id = GuildInventoryId::new(uuid::Uuid::now_v7());
        let guild_id = GuildId::new(uuid::Uuid::now_v7());
        let item_id = ItemId::new(uuid::Uuid::now_v7());
        let user_id = UserId::new(uuid::Uuid::now_v7());

        inventory.set_id(id.clone());
        inventory.set_guild_id(guild_id.clone());
        inventory.set_item_id(item_id.clone());
        inventory.set_user_id(user_id.clone());

        assert_eq!(inventory.id().get_id(), id.get_id());
        assert_eq!(inventory.guild_id().get_id(), guild_id.get_id());
        assert_eq!(inventory.item_id().get_id(), item_id.get_id());
        assert_eq!(inventory.user_id().get_id(), user_id.get_id());
    }
}
