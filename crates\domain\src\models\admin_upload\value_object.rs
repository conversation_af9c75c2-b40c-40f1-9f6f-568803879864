use chrono::{DateTime, Utc};
use reforged_shared::{UuidId, Value};

use super::entity::AdminUpload;

pub type AdminUploadId = UuidId<AdminUpload>;

#[derive(Debug, PartialEq, Eq, PartialOrd, Ord, Clone, Default)]
pub struct AdminUploadType(String);

impl AdminUploadType {
    pub fn new(name: String) -> Self {
        AdminUploadType(name)
    }
}

impl Value<String> for AdminUploadType {
    fn value(&self) -> String {
        self.0.clone()
    }
}

#[derive(Debug, PartialEq, Eq, PartialOrd, Ord, Clone, Default)]
pub struct AdminUploadName(String);

impl AdminUploadName {
    pub fn new(name: String) -> Self {
        AdminUploadName(name)
    }
}

impl Value<String> for AdminUploadName {
    fn value(&self) -> String {
        self.0.clone()
    }
}

#[derive(Debug, PartialEq, Eq, <PERSON><PERSON>Ord, Or<PERSON>, <PERSON><PERSON>, De<PERSON>ult)]
pub struct AdminUploadDate(DateTime<Utc>);

impl AdminUploadDate {
    pub fn new(date: DateTime<Utc>) -> Self {
        AdminUploadDate(date)
    }
}

impl Value<DateTime<Utc>> for AdminUploadDate {
    fn value(&self) -> DateTime<Utc> {
        self.0
    }
}
