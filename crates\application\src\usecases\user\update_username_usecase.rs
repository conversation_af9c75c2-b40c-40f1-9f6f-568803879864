use std::sync::Arc;

use reforged_domain::{
    models::user::{entity::User, value_object::Username},
    repository::user_repository::UserRepository,
};

use crate::{
    error::ApplicationError,
    queries::{user_queries::GetUserByIdQuery, user_query_handlers::UserQueryHandler},
    services::captcha_service::Captcha,
    traits::QueryHandler,
};

pub struct UpdateUsernameUsecase {
    user_query_handler: UserQueryHandler,
    user_repository: Arc<dyn UserRepository>,
    captcha_service: Arc<dyn Captcha>,
}

impl UpdateUsernameUsecase {
    pub fn new(
        user_query_handler: UserQueryHandler,
        user_repository: Arc<dyn UserRepository>,
        captcha_service: Arc<dyn Captcha>,
    ) -> Self {
        Self {
            user_query_handler,
            user_repository,
            captcha_service,
        }
    }
}

impl UpdateUsernameUsecase {
    pub async fn execute(
        &self,
        user_id: uuid::Uuid,
        username: String,
        captcha: String,
    ) -> Result<(), ApplicationError> {
        self.captcha_service.validate(captcha).await?;

        let query = GetUserByIdQuery::new(user_id);
        let user = self
            .user_query_handler
            .handle(query)
            .await?
            .ok_or(ApplicationError::UserNotFound)?;

        let mut user = User::from(user);
        user.set_username(Username::new(username));

        self.user_repository.update(&user).await?;

        Ok(())
    }
}
