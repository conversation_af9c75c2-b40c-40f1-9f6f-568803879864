use crate::models::aura::value_object::AuraId;
use crate::models::aura_effect::value_object::*;

use getset::{<PERSON><PERSON>, Set<PERSON>};

#[derive(<PERSON>bu<PERSON>, <PERSON><PERSON>, <PERSON><PERSON>ult, bon::<PERSON><PERSON><PERSON>, <PERSON><PERSON>, Set<PERSON>)]
#[getset(get = "pub", set = "pub")]
pub struct AuraEffect {
    pub id: AuraEffectId,
    pub aura_id: AuraId,
    pub stat: AuraEffectStat,
    pub value: AuraEffectValue,
    pub effect_type: AuraEffectType,
}

impl AuraEffect {
    pub fn new(
        id: AuraEffectId,
        aura_id: AuraId,
        stat: AuraEffectStat,
        value: AuraEffectValue,
        effect_type: AuraEffectType,
    ) -> Self {
        Self {
            id,
            aura_id,
            stat,
            value,
            effect_type,
        }
    }
}

#[cfg(test)]
mod tests {
    use reforged_shared::{IdTrait, Value};

    use super::*;

    #[test]
    fn test_aura_effect_new() {
        let id = AuraEffectId::new(uuid::Uuid::now_v7());
        let aura_id = AuraId::new(uuid::Uuid::now_v7());
        let stat = AuraEffectStat::new("damage");
        let value = AuraEffectValue::new(15.5);
        let effect_type = AuraEffectType::new("buff");

        let aura_effect = AuraEffect::new(
            id.clone(),
            aura_id.clone(),
            stat.clone(),
            value.clone(),
            effect_type.clone(),
        );

        assert_eq!(aura_effect.id().get_id(), id.get_id());
        assert_eq!(aura_effect.aura_id().get_id(), aura_id.get_id());
        assert_eq!(aura_effect.stat().value(), stat.value());
        assert_eq!(aura_effect.value().value(), value.value());
        assert_eq!(aura_effect.effect_type().value(), effect_type.value());
    }

    #[test]
    fn test_aura_effect_builder() {
        let id = AuraEffectId::new(uuid::Uuid::now_v7());
        let aura_id = AuraId::new(uuid::Uuid::now_v7());
        let stat = AuraEffectStat::new("damage");
        let value = AuraEffectValue::new(15.5);
        let effect_type = AuraEffectType::new("buff");

        let aura_effect = AuraEffect::builder()
            .id(id.clone())
            .aura_id(aura_id.clone())
            .stat(stat.clone())
            .value(value.clone())
            .effect_type(effect_type.clone())
            .build();

        assert_eq!(aura_effect.id().get_id(), id.get_id());
        assert_eq!(aura_effect.aura_id().get_id(), aura_id.get_id());
        assert_eq!(aura_effect.stat().value(), stat.value());
        assert_eq!(aura_effect.value().value(), value.value());
        assert_eq!(aura_effect.effect_type().value(), effect_type.value());
    }

    #[test]
    fn test_getters_and_setters() {
        let mut aura_effect = AuraEffect::default();

        let id = AuraEffectId::new(uuid::Uuid::now_v7());
        let aura_id = AuraId::new(uuid::Uuid::now_v7());
        let stat = AuraEffectStat::new("damage");
        let value = AuraEffectValue::new(15.5);
        let effect_type = AuraEffectType::new("buff");

        aura_effect.set_id(id.clone());
        aura_effect.set_aura_id(aura_id.clone());
        aura_effect.set_stat(stat.clone());
        aura_effect.set_value(value.clone());
        aura_effect.set_effect_type(effect_type.clone());

        assert_eq!(aura_effect.id().get_id(), id.get_id());
        assert_eq!(aura_effect.aura_id().get_id(), aura_id.get_id());
        assert_eq!(aura_effect.stat().value(), stat.value());
        assert_eq!(aura_effect.value().value(), value.value());
        assert_eq!(aura_effect.effect_type().value(), effect_type.value());
    }
}
