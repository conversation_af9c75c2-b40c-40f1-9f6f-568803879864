use chrono::NaiveDate;
use reforged_shared::{UuidId, Value};

use super::entity::UserBoost;

pub type UserBoostId = UuidId<UserBoost>;

#[derive(Debug, PartialEq, Eq, PartialOrd, Ord, Clone, Default)]

pub struct CpBoostExpire(NaiveDate);

impl CpBoostExpire {
    pub fn new(date: NaiveDate) -> Self {
        Self(date)
    }
}

impl Value<NaiveDate> for CpBoostExpire {
    fn value(&self) -> NaiveDate {
        self.0
    }
}

impl From<NaiveDate> for CpBoostExpire {
    fn from(value: NaiveDate) -> Self {
        Self(value)
    }
}

#[derive(Debug, PartialEq, Eq, PartialOrd, Ord, Clone, Default)]

pub struct RepBoostExpire(NaiveDate);

impl RepBoostExpire {
    pub fn new(date: NaiveDate) -> Self {
        Self(date)
    }
}

impl Value<NaiveDate> for RepBoostExpire {
    fn value(&self) -> NaiveDate {
        self.0
    }
}

impl From<NaiveDate> for RepBoostExpire {
    fn from(value: NaiveDate) -> Self {
        Self(value)
    }
}

#[derive(Debug, PartialEq, Eq, PartialOrd, Ord, Clone, Default)]

pub struct GoldBoostExpire(NaiveDate);

impl GoldBoostExpire {
    pub fn new(date: NaiveDate) -> Self {
        Self(date)
    }
}

impl Value<NaiveDate> for GoldBoostExpire {
    fn value(&self) -> NaiveDate {
        self.0
    }
}

impl From<NaiveDate> for GoldBoostExpire {
    fn from(value: NaiveDate) -> Self {
        Self(value)
    }
}

#[derive(Debug, PartialEq, Eq, PartialOrd, Ord, Clone, Default)]

pub struct ExpBoostExpire(NaiveDate);

impl ExpBoostExpire {
    pub fn new(date: NaiveDate) -> Self {
        Self(date)
    }
}

impl Value<NaiveDate> for ExpBoostExpire {
    fn value(&self) -> NaiveDate {
        self.0
    }
}

impl From<NaiveDate> for ExpBoostExpire {
    fn from(value: NaiveDate) -> Self {
        Self(value)
    }
}

#[derive(Debug, PartialEq, Eq, PartialOrd, Ord, Clone, Default)]

pub struct UpgradeExpire(NaiveDate);

impl UpgradeExpire {
    pub fn new(date: NaiveDate) -> Self {
        Self(date)
    }
}

impl Value<NaiveDate> for UpgradeExpire {
    fn value(&self) -> NaiveDate {
        self.0
    }
}

impl From<NaiveDate> for UpgradeExpire {
    fn from(value: NaiveDate) -> Self {
        Self(value)
    }
}

#[derive(Debug, PartialEq, Eq, PartialOrd, Ord, Clone, Default)]

pub struct UpgradeDays(i16);

impl UpgradeDays {
    pub fn new(value: i16) -> Self {
        Self(value)
    }
}

impl Value<i16> for UpgradeDays {
    fn value(&self) -> i16 {
        self.0
    }
}

impl From<i16> for UpgradeDays {
    fn from(value: i16) -> Self {
        Self(value)
    }
}

#[derive(Debug, PartialEq, Eq, PartialOrd, Ord, Clone, Default)]

pub struct Upgraded(bool);

impl Upgraded {
    pub fn new(value: bool) -> Self {
        Self(value)
    }
}

impl Value<bool> for Upgraded {
    fn value(&self) -> bool {
        self.0
    }
}

impl From<bool> for Upgraded {
    fn from(value: bool) -> Self {
        Self(value)
    }
}
