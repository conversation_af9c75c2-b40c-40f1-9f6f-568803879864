use async_trait::async_trait;

use crate::{error::RepositoryError, models::aura::entity::Aura};

#[mockall::automock]
#[async_trait]
pub trait AuraRepository: Send + Sync {
    async fn save(&self, aura: &Aura) -> Result<Aura, RepositoryError>;
    async fn update(&self, aura: &Aura) -> Result<(), RepositoryError>;
    async fn delete(&self, id: i32) -> Result<(), RepositoryError>;
}

#[mockall::automock]
#[async_trait]
pub trait AuraReadRepository: Send + Sync {
    async fn find_by_id(&self, id: i32) -> Result<Option<Aura>, RepositoryError>;
    async fn find_by_name(&self, name: String) -> Result<Option<Aura>, RepositoryError>;
    async fn find_all(&self) -> Result<Vec<Aura>, RepositoryError>;
    async fn find_paginate(&self, page: u32, limit: u32) -> Result<Vec<Aura>, RepositoryError>;
}
