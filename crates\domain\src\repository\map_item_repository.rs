use async_trait::async_trait;
use mockall::automock;

use crate::error::RepositoryError;
use crate::models::map_item::entity::MapItem;
use crate::models::map_item::value_object::MapItemId;

#[automock]
#[async_trait]
pub trait MapItemRepository: Send + Sync {
    async fn save(&self, entity: &MapItem) -> Result<(), RepositoryError>;
    async fn update(&self, entity: &MapItem) -> Result<(), RepositoryError>;
    async fn delete(&self, id: &MapItemId) -> Result<(), RepositoryError>;
}

#[automock]
#[async_trait]
pub trait MapItemReadRepository: Send + Sync {
    async fn find_by_id(&self, id: &MapItemId) -> Result<Option<MapItem>, RepositoryError>;
    async fn find_all(&self) -> Result<Vec<MapItem>, RepositoryError>;
}
