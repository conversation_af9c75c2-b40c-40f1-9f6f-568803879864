use crate::models::item::value_object::ItemId;
use crate::models::quest::value_object::QuestId;
use crate::models::quest_reward::value_object::*;
use getset::{<PERSON><PERSON>, <PERSON><PERSON>};

#[derive(Debu<PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, bon::<PERSON><PERSON><PERSON>, <PERSON><PERSON>, Set<PERSON>)]
#[getset(get = "pub", set = "pub")]
pub struct QuestReward {
    pub id: QuestRewardId,
    pub quest_id: QuestId,
    pub item_id: ItemId,
    pub quantity: QuestRewardQuantity,
    pub rate: QuestRewardRate,
    pub reward_type: QuestRewardType,
}

impl QuestReward {
    pub fn new(
        id: QuestRewardId,
        quest_id: QuestId,
        item_id: ItemId,
        quantity: QuestRewardQuantity,
        rate: QuestRewardRate,
        reward_type: QuestRewardType,
    ) -> Self {
        Self {
            id,
            quest_id,
            item_id,
            quantity,
            rate,
            reward_type,
        }
    }
}

#[cfg(test)]
mod tests {
    use reforged_shared::{IdTrait, Value};

    use super::*;

    #[test]
    fn test_quest_reward_new() {
        let id = QuestRewardId::new(uuid::Uuid::now_v7());
        let quest_id = QuestId::new(uuid::Uuid::now_v7());
        let item_id = ItemId::new(uuid::Uuid::now_v7());
        let quantity = QuestRewardQuantity::new(5);
        let rate = QuestRewardRate::new(0.8);
        let reward_type = QuestRewardType::new("item");

        let quest_reward = QuestReward::new(
            id.clone(),
            quest_id.clone(),
            item_id.clone(),
            quantity.clone(),
            rate.clone(),
            reward_type.clone(),
        );

        assert_eq!(quest_reward.id().get_id(), id.get_id());
        assert_eq!(quest_reward.quest_id().get_id(), quest_id.get_id());
        assert_eq!(quest_reward.item_id().get_id(), item_id.get_id());
        assert_eq!(quest_reward.quantity().value(), quantity.value());
        assert_eq!(quest_reward.rate().value(), rate.value());
        assert_eq!(quest_reward.reward_type().value(), reward_type.value());
    }

    #[test]
    fn test_quest_reward_builder() {
        let id = QuestRewardId::new(uuid::Uuid::now_v7());
        let quest_id = QuestId::new(uuid::Uuid::now_v7());
        let item_id = ItemId::new(uuid::Uuid::now_v7());
        let quantity = QuestRewardQuantity::new(5);
        let rate = QuestRewardRate::new(0.8);
        let reward_type = QuestRewardType::new("item");

        let quest_reward = QuestReward::builder()
            .id(id.clone())
            .quest_id(quest_id.clone())
            .item_id(item_id.clone())
            .quantity(quantity.clone())
            .rate(rate.clone())
            .reward_type(reward_type.clone())
            .build();

        assert_eq!(quest_reward.id().get_id(), id.get_id());
        assert_eq!(quest_reward.quest_id().get_id(), quest_id.get_id());
        assert_eq!(quest_reward.item_id().get_id(), item_id.get_id());
        assert_eq!(quest_reward.quantity().value(), quantity.value());
        assert_eq!(quest_reward.rate().value(), rate.value());
        assert_eq!(quest_reward.reward_type().value(), reward_type.value());
    }

    #[test]
    fn test_getters_and_setters() {
        let mut quest_reward = QuestReward::default();

        let id = QuestRewardId::new(uuid::Uuid::now_v7());
        let quest_id = QuestId::new(uuid::Uuid::now_v7());
        let item_id = ItemId::new(uuid::Uuid::now_v7());
        let quantity = QuestRewardQuantity::new(5);
        let rate = QuestRewardRate::new(0.8);
        let reward_type = QuestRewardType::new("item");

        quest_reward.set_id(id.clone());
        quest_reward.set_quest_id(quest_id.clone());
        quest_reward.set_item_id(item_id.clone());
        quest_reward.set_quantity(quantity.clone());
        quest_reward.set_rate(rate.clone());
        quest_reward.set_reward_type(reward_type.clone());

        assert_eq!(quest_reward.id().get_id(), id.get_id());
        assert_eq!(quest_reward.quest_id().get_id(), quest_id.get_id());
        assert_eq!(quest_reward.item_id().get_id(), item_id.get_id());
        assert_eq!(quest_reward.quantity().value(), quantity.value());
        assert_eq!(quest_reward.rate().value(), rate.value());
        assert_eq!(quest_reward.reward_type().value(), reward_type.value());
    }
}
