use reforged_shared::{UuidId, Value};

use super::entity::ItemBundle;

pub type ItemBundleId = UuidId<ItemBundle>;

#[derive(Debug, PartialEq, Eq, PartialOrd, Ord, <PERSON>lone, Default)]

pub struct ItemBundleQuantity(i32);

impl ItemBundleQuantity {
    pub fn new(quantity: i32) -> Self {
        Self(quantity)
    }
}

impl Value<i32> for ItemBundleQuantity {
    fn value(&self) -> i32 {
        self.0
    }
}

impl From<i32> for ItemBundleQuantity {
    fn from(value: i32) -> Self {
        Self(value)
    }
}
