use dotenvy::dotenv;

use super::{Config, ConfigError};

pub struct MessageBrokerConfig {
    pub url: String,
    pub exchange: String,
}

impl Config for MessageBrokerConfig {
    fn from_env() -> Result<Self, ConfigError> {
        dotenv().ok();

        let url = std::env::var("MESSAGE_BROKER_URL")
            .map_err(|_| ConfigError::EnvVarNotFound("MESSAGE_BROKER_URL".to_string()))?;
        let exchange = std::env::var("MESSAGE_BROKER_EXCHANGE")
            .map_err(|_| ConfigError::EnvVarNotFound("MESSAGE_BROKER_EXCHANGE".to_string()))?;

        Ok(Self { url, exchange })
    }
}
