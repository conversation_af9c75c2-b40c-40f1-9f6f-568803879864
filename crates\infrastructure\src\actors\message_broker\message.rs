use actix::{<PERSON><PERSON>, Message};
use actix_broker::BrokerIssue;
use futures::{FutureExt, future::LocalBoxFuture};
use serde::{Deserialize, Serialize, de::DeserializeOwned};

use super::actor::MessageBrokerActor;

#[derive(Message, Clone)]
#[rtype(result = "()")]
pub struct StartBroker;

/// Message broker actor message.
/// A message the other actors can subscribe to.
#[derive(Message, Clone)]
#[rtype(result = "()")]
pub struct Publish<T: Serialize + DeserializeOwned + Clone> {
    message: T,
}

impl<'a, T> Publish<T>
where
    T: Serialize + DeserializeOwned + Clone,
{
    pub fn new(message: T) -> Self {
        Self { message }
    }

    pub fn message(&self) -> &T {
        &self.message
    }

    pub fn event_type(&self) -> String {
        let event_type = std::any::type_name::<T>();
        event_type
            .to_string()
            .split("::")
            .last()
            .unwrap()
            .to_string()
    }
}

impl<T> Handler<Publish<T>> for MessageBrokerActor
where
    T: Serialize + DeserializeOwned + Clone + Send + 'static,
{
    type Result = LocalBoxFuture<'static, ()>;

    fn handle(&mut self, msg: Publish<T>, _ctx: &mut Self::Context) -> Self::Result {
        self.issue_system_async(msg.clone());

        async move {}.boxed_local()
    }
}

#[derive(Debug, Serialize, Deserialize)]
pub struct Event {
    event_type: String,
    payload: serde_json::Value,
}

impl Event {
    pub fn new(event_type: String, payload: serde_json::Value) -> Self {
        Self {
            event_type,
            payload,
        }
    }

    pub fn event_type(&self) -> &str {
        &self.event_type
    }

    pub fn payload(&self) -> &serde_json::Value {
        &self.payload
    }
}
