use crate::models::{item::value_object::ItemId, map::value_object::MapId};
use getset::{<PERSON><PERSON>, <PERSON><PERSON>};

use super::value_object::MapItemId;

#[derive(<PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>ult, bon::<PERSON><PERSON><PERSON>, <PERSON><PERSON>, Set<PERSON>)]
#[getset(get = "pub", set = "pub")]
pub struct MapItem {
    id: MapItemId,
    map_id: MapId,
    item_id: ItemId,
}

impl MapItem {
    pub fn new(id: MapItemId, map_id: MapId, item_id: ItemId) -> Self {
        Self {
            id,
            map_id,
            item_id,
        }
    }
}

#[cfg(test)]
mod tests {
    use reforged_shared::IdTrait;

    use super::*;

    #[test]
    fn test_map_item_new() {
        let id = MapItemId::new(uuid::Uuid::now_v7());
        let map_id = MapId::new(uuid::Uuid::now_v7());
        let item_id = ItemId::new(uuid::Uuid::now_v7());

        let map_item = MapItem::new(id.clone(), map_id.clone(), item_id.clone());

        assert_eq!(map_item.id().get_id(), id.get_id());
        assert_eq!(map_item.map_id().get_id(), map_id.get_id());
        assert_eq!(map_item.item_id().get_id(), item_id.get_id());
    }

    #[test]
    fn test_map_item_builder() {
        let id = MapItemId::new(uuid::Uuid::now_v7());
        let map_id = MapId::new(uuid::Uuid::now_v7());
        let item_id = ItemId::new(uuid::Uuid::now_v7());

        let map_item = MapItem::builder()
            .id(id.clone())
            .map_id(map_id.clone())
            .item_id(item_id.clone())
            .build();

        assert_eq!(map_item.id().get_id(), id.get_id());
        assert_eq!(map_item.map_id().get_id(), map_id.get_id());
        assert_eq!(map_item.item_id().get_id(), item_id.get_id());
    }

    #[test]
    fn test_getters_and_setters() {
        let mut map_item = MapItem::default();

        let id = MapItemId::new(uuid::Uuid::now_v7());
        let map_id = MapId::new(uuid::Uuid::now_v7());
        let item_id = ItemId::new(uuid::Uuid::now_v7());

        map_item.set_id(id.clone());
        map_item.set_map_id(map_id.clone());
        map_item.set_item_id(item_id.clone());

        assert_eq!(map_item.id().get_id(), id.get_id());
        assert_eq!(map_item.map_id().get_id(), map_id.get_id());
        assert_eq!(map_item.item_id().get_id(), item_id.get_id());
    }
}
