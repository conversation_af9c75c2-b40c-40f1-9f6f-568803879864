use reforged_shared::{UuidId, Value};

use super::entity::ItemRequirement;

pub type ItemRequirementId = UuidId<ItemRequirement>;

#[derive(Debug, PartialEq, Eq, PartialOrd, Ord, <PERSON>lone, <PERSON><PERSON>ult)]

pub struct ItemRequirementQuantity(i16);

impl ItemRequirementQuantity {
    pub fn new(quantity: i16) -> Self {
        Self(quantity)
    }
}

impl Value<i16> for ItemRequirementQuantity {
    fn value(&self) -> i16 {
        self.0
    }
}

impl From<i16> for ItemRequirementQuantity {
    fn from(value: i16) -> Self {
        Self(value)
    }
}
