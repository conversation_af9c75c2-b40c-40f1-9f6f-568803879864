use getset::{<PERSON><PERSON>, <PERSON><PERSON>};

use crate::models::item::value_object::ItemId;

use super::value_object::*;

#[derive(Debug, <PERSON><PERSON>, Default, bon::<PERSON><PERSON><PERSON>, <PERSON><PERSON>, Setters)]
#[getset(get = "pub", set = "pub")]
pub struct RedeemCode {
    id: RedeemCodeId,
    code: RedeemCodeCode,
    coins: RedeemCodeCoins,
    gold: RedeemCodeGold,
    exp: RedeemCodeExp,
    class_points: RedeemCodeClassPoints,
    item_id: ItemId,
    upgrade_days: RedeemCodeUpgradeDays,
    date_expiry: RedeemCodeDateExpiry,
    limit: RedeemCodeLimit,
}

impl RedeemCode {
    pub fn new(
        id: RedeemCodeId,
        code: RedeemCodeCode,
        coins: RedeemCodeCoins,
        gold: RedeemCodeGold,
        exp: RedeemCodeExp,
        class_points: RedeemCodeClassPoints,
        item_id: ItemId,
        upgrade_days: RedeemCodeUpgradeDays,
        date_expiry: RedeemCodeDateExpiry,
        limit: RedeemCodeLimit,
    ) -> Self {
        Self {
            id,
            code,
            coins,
            gold,
            exp,
            class_points,
            item_id,
            upgrade_days,
            date_expiry,
            limit,
        }
    }
}

#[cfg(test)]
mod tests {
    use chrono::Utc;
    use reforged_shared::{IdTrait, Value};

    use super::*;

    #[test]
    fn test_redeem_code_new() {
        let id = RedeemCodeId::new(uuid::Uuid::now_v7());
        let code = RedeemCodeCode::new("TESTCODE123");
        let coins = RedeemCodeCoins::new(500);
        let gold = RedeemCodeGold::new(1000);
        let exp = RedeemCodeExp::new(2000);
        let class_points = RedeemCodeClassPoints::new(100);
        let item_id = ItemId::new(uuid::Uuid::now_v7());
        let upgrade_days = RedeemCodeUpgradeDays::new(30);
        let date_expiry = RedeemCodeDateExpiry::new(Utc::now());
        let limit = RedeemCodeLimit::new(100);

        let redeem_code = RedeemCode::new(
            id.clone(),
            code.clone(),
            coins.clone(),
            gold.clone(),
            exp.clone(),
            class_points.clone(),
            item_id.clone(),
            upgrade_days.clone(),
            date_expiry.clone(),
            limit.clone(),
        );

        assert_eq!(redeem_code.id().get_id(), id.get_id());
        assert_eq!(redeem_code.code().value(), code.value());
        assert_eq!(redeem_code.coins().value(), coins.value());
        assert_eq!(redeem_code.gold().value(), gold.value());
        assert_eq!(redeem_code.exp().value(), exp.value());
        assert_eq!(redeem_code.class_points().value(), class_points.value());
        assert_eq!(redeem_code.item_id().get_id(), item_id.get_id());
        assert_eq!(redeem_code.upgrade_days().value(), upgrade_days.value());
        assert_eq!(redeem_code.date_expiry().value(), date_expiry.value());
        assert_eq!(redeem_code.limit().value(), limit.value());
    }

    #[test]
    fn test_redeem_code_builder() {
        let id = RedeemCodeId::new(uuid::Uuid::now_v7());
        let code = RedeemCodeCode::new("TESTCODE123");
        let coins = RedeemCodeCoins::new(500);
        let gold = RedeemCodeGold::new(1000);
        let exp = RedeemCodeExp::new(2000);
        let class_points = RedeemCodeClassPoints::new(100);
        let item_id = ItemId::new(uuid::Uuid::now_v7());
        let upgrade_days = RedeemCodeUpgradeDays::new(30);
        let date_expiry = RedeemCodeDateExpiry::new(Utc::now());
        let limit = RedeemCodeLimit::new(100);

        let redeem_code = RedeemCode::builder()
            .id(id.clone())
            .code(code.clone())
            .coins(coins.clone())
            .gold(gold.clone())
            .exp(exp.clone())
            .class_points(class_points.clone())
            .item_id(item_id.clone())
            .upgrade_days(upgrade_days.clone())
            .date_expiry(date_expiry.clone())
            .limit(limit.clone())
            .build();

        assert_eq!(redeem_code.id().get_id(), id.get_id());
        assert_eq!(redeem_code.code().value(), code.value());
        assert_eq!(redeem_code.coins().value(), coins.value());
        assert_eq!(redeem_code.gold().value(), gold.value());
        assert_eq!(redeem_code.exp().value(), exp.value());
        assert_eq!(redeem_code.class_points().value(), class_points.value());
        assert_eq!(redeem_code.item_id().get_id(), item_id.get_id());
        assert_eq!(redeem_code.upgrade_days().value(), upgrade_days.value());
        assert_eq!(redeem_code.date_expiry().value(), date_expiry.value());
        assert_eq!(redeem_code.limit().value(), limit.value());
    }

    #[test]
    fn test_getters_and_setters() {
        let mut redeem_code = RedeemCode::default();

        let id = RedeemCodeId::new(uuid::Uuid::now_v7());
        let code = RedeemCodeCode::new("TESTCODE123");
        let coins = RedeemCodeCoins::new(500);
        let gold = RedeemCodeGold::new(1000);
        let exp = RedeemCodeExp::new(2000);
        let class_points = RedeemCodeClassPoints::new(100);
        let item_id = ItemId::new(uuid::Uuid::now_v7());
        let upgrade_days = RedeemCodeUpgradeDays::new(30);
        let date_expiry = RedeemCodeDateExpiry::new(Utc::now());
        let limit = RedeemCodeLimit::new(100);

        redeem_code.set_id(id.clone());
        redeem_code.set_code(code.clone());
        redeem_code.set_coins(coins.clone());
        redeem_code.set_gold(gold.clone());
        redeem_code.set_exp(exp.clone());
        redeem_code.set_class_points(class_points.clone());
        redeem_code.set_item_id(item_id.clone());
        redeem_code.set_upgrade_days(upgrade_days.clone());
        redeem_code.set_date_expiry(date_expiry.clone());
        redeem_code.set_limit(limit.clone());

        assert_eq!(redeem_code.id().get_id(), id.get_id());
        assert_eq!(redeem_code.code().value(), code.value());
        assert_eq!(redeem_code.coins().value(), coins.value());
        assert_eq!(redeem_code.gold().value(), gold.value());
        assert_eq!(redeem_code.exp().value(), exp.value());
        assert_eq!(redeem_code.class_points().value(), class_points.value());
        assert_eq!(redeem_code.item_id().get_id(), item_id.get_id());
        assert_eq!(redeem_code.upgrade_days().value(), upgrade_days.value());
        assert_eq!(redeem_code.date_expiry().value(), date_expiry.value());
        assert_eq!(redeem_code.limit().value(), limit.value());
    }
}
