use reforged_domain::models::user_redeem::entity::UserRedeem;
use reforged_domain::models::user_redeem::value_object::UserRedeemId;
use reforged_domain::models::user::value_object::UserId;
use reforged_domain::models::redeem_code::value_object::RedeemCodeId;
use chrono::{DateTime, Utc};

#[derive(Debug)]
pub struct UserRedeemDbModelMapper(super::super::models::user_redeems::Model);

impl UserRedeemDbModelMapper {
    pub fn new(model: super::super::models::user_redeems::Model) -> Self {
        Self(model)
    }
}

impl From<UserRedeemDbModelMapper> for UserRedeem {
    fn from(value: UserRedeemDbModelMapper) -> Self {
        let model = value.0;

        UserRedeem::builder()
            .id(UserRedeemId::new(model.id))
            .user_id(UserId::new(model.user_id))
            .redeem_id(RedeemCodeId::new(model.redeem_id))
            .date(DateTime::<Utc>::from_naive_utc_and_offset(model.date, Utc).into())
            .build()
    }
}
