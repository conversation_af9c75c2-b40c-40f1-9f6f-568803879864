use super::value_object::{Dps, EnhancementId, EnhancementName, Level, Rarity};
use crate::models::enhancement_pattern::value_object::EnhancementPatternId;

use getset::{<PERSON><PERSON>, Set<PERSON>};

#[derive(<PERSON>bu<PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, bon::<PERSON><PERSON><PERSON>, <PERSON><PERSON>, Set<PERSON>)]
#[getset(get = "pub", set = "pub")]
pub struct Enhancement {
    id: EnhancementId,
    name: EnhancementName,
    pattern_id: EnhancementPatternId,
    rarity: Rarity,
    dps: Dps,
    level: Level,
}

impl Enhancement {
    pub fn new(
        id: EnhancementId,
        name: EnhancementName,
        pattern_id: EnhancementPatternId,
        rarity: Rarity,
        dps: Dps,
        level: Level,
    ) -> Self {
        Self {
            id,
            name,
            pattern_id,
            rarity,
            dps,
            level,
        }
    }
}

#[cfg(test)]
mod tests {
    use reforged_shared::{IdTrait, Value};

    use super::*;

    #[test]
    fn test_enhancement_new() {
        let id = EnhancementId::new(uuid::Uuid::now_v7());
        let name = EnhancementName::new("Magic Enhancement");
        let pattern_id = EnhancementPatternId::new(uuid::Uuid::now_v7());
        let rarity = Rarity::new(uuid::Uuid::now_v7());
        let dps = Dps::new(50);
        let level = Level::new(5);

        let enhancement = Enhancement::new(
            id.clone(),
            name.clone(),
            pattern_id.clone(),
            rarity.clone(),
            dps.clone(),
            level.clone(),
        );

        assert_eq!(enhancement.id().get_id(), id.get_id());
        assert_eq!(enhancement.name().value(), name.value());
        assert_eq!(enhancement.pattern_id().get_id(), pattern_id.get_id());
        assert_eq!(enhancement.rarity().value(), rarity.value());
        assert_eq!(enhancement.dps().value(), dps.value());
        assert_eq!(enhancement.level().value(), level.value());
    }

    #[test]
    fn test_enhancement_builder() {
        let id = EnhancementId::new(uuid::Uuid::now_v7());
        let name = EnhancementName::new("Magic Enhancement");
        let pattern_id = EnhancementPatternId::new(uuid::Uuid::now_v7());
        let rarity = Rarity::new(uuid::Uuid::now_v7());
        let dps = Dps::new(50);
        let level = Level::new(5);

        let enhancement = Enhancement::builder()
            .id(id.clone())
            .name(name.clone())
            .pattern_id(pattern_id.clone())
            .rarity(rarity.clone())
            .dps(dps.clone())
            .level(level.clone())
            .build();

        assert_eq!(enhancement.id().get_id(), id.get_id());
        assert_eq!(enhancement.name().value(), name.value());
        assert_eq!(enhancement.pattern_id().get_id(), pattern_id.get_id());
        assert_eq!(enhancement.rarity().value(), rarity.value());
        assert_eq!(enhancement.dps().value(), dps.value());
        assert_eq!(enhancement.level().value(), level.value());
    }

    #[test]
    fn test_getters_and_setters() {
        let mut enhancement = Enhancement::default();

        let id = EnhancementId::new(uuid::Uuid::now_v7());
        let name = EnhancementName::new("Magic Enhancement");
        let pattern_id = EnhancementPatternId::new(uuid::Uuid::now_v7());
        let rarity = Rarity::new(uuid::Uuid::now_v7());
        let dps = Dps::new(50);
        let level = Level::new(5);

        enhancement.set_id(id.clone());
        enhancement.set_name(name.clone());
        enhancement.set_pattern_id(pattern_id.clone());
        enhancement.set_rarity(rarity.clone());
        enhancement.set_dps(dps.clone());
        enhancement.set_level(level.clone());

        assert_eq!(enhancement.id().get_id(), id.get_id());
        assert_eq!(enhancement.name().value(), name.value());
        assert_eq!(enhancement.pattern_id().get_id(), pattern_id.get_id());
        assert_eq!(enhancement.rarity().value(), rarity.value());
        assert_eq!(enhancement.dps().value(), dps.value());
        assert_eq!(enhancement.level().value(), level.value());
    }
}
