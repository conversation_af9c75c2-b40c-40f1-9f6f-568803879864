use actix_web::{
    <PERSON><PERSON>,
    web::{self, <PERSON>, <PERSON><PERSON>, Path, get, post},
};
use reforged_application::usecases::{
    auth::login_usecase::{LoginResponse, LoginUsecase},
    auth::refresh_token_usecase::{RefreshTokenResponse, RefreshTokenUsecase},
    auth::token_verification_usecase::{TokenVerificationResponse, TokenVerificationUsecase},
    password_reset::forgot_password_usecase::{ForgotPasswordResponse, ForgotPasswordUsecase},
    password_reset::reset_password_usecase::{ResetPasswordResponse, ResetPasswordUsecase},
};

use crate::{
    dtos::{
        auth_dtos::{LogoutDTO, RefreshTokenDTO},
        user_dtos::{ForgotPasswordDTO, LoginDTO, ResetPasswordDTO},
        validate::ValidateJson,
    },
    error::ApiError,
    state::ApiState,
};

pub fn create_auth_service() -> <PERSON>ope {
    web::scope("/auth")
        .service(web::resource("/login").route(post().to(login_user_handler)))
        .service(web::resource("/refresh").route(post().to(refresh_token_handler)))
        .service(web::resource("/validate/{token}").route(get().to(token_verification_handler)))
        .service(web::resource("/logout").route(post().to(logout_handler)))
        .service(web::resource("/forgot-password").route(post().to(forgot_password_handler)))
        .service(web::resource("/reset-password").route(post().to(reset_password_handler)))
}

async fn refresh_token_handler(
    state: Data<ApiState>,
    ValidateJson(dto): ValidateJson<RefreshTokenDTO>,
) -> Result<Json<RefreshTokenResponse>, ApiError> {
    let usecase = RefreshTokenUsecase::new(state.token_service().clone());

    let response = usecase.execute(dto.refresh_token).await?;

    Ok(Json(response))
}

async fn token_verification_handler(
    state: Data<ApiState>,
    token: Path<String>,
) -> Result<Json<TokenVerificationResponse>, ApiError> {
    let token = token.into_inner();
    let usecase = TokenVerificationUsecase::new(state.token_service().clone());

    let response = usecase.execute(token).await?;

    Ok(Json(response))
}

async fn logout_handler(
    state: Data<ApiState>,
    ValidateJson(dto): ValidateJson<LogoutDTO>,
) -> Result<Json<TokenVerificationResponse>, ApiError> {
    let usecase = TokenVerificationUsecase::new(state.token_service().clone());

    let response = usecase.execute(dto.refresh_token).await?;

    Ok(Json(response))
}

async fn login_user_handler(
    state: Data<ApiState>,
    ValidateJson(login): ValidateJson<LoginDTO>,
) -> Result<Json<LoginResponse>, ApiError> {
    let login_usecase = LoginUsecase::new(
        state.captcha_service().clone(),
        state.password_hasher().clone(),
        state.user_query_handler().clone(),
        state.token_service().clone(),
    );

    let login_response = login_usecase
        .execute(login.username, login.password, login.captcha)
        .await?;

    Ok(Json(login_response))
}

async fn forgot_password_handler(
    state: Data<ApiState>,
    ValidateJson(dto): ValidateJson<ForgotPasswordDTO>,
) -> Result<Json<ForgotPasswordResponse>, ApiError> {
    let forgot_password_usecase = ForgotPasswordUsecase::new(
        state.captcha_service().clone(),
        state.user_query_handler().clone(),
        state.token_service().clone(),
        state.user_store_manager().clone(),
    );

    let forgot_password_response = forgot_password_usecase
        .execute(dto.email, dto.captcha)
        .await?;

    Ok(Json(forgot_password_response))
}

async fn reset_password_handler(
    state: Data<ApiState>,
    ValidateJson(dto): ValidateJson<ResetPasswordDTO>,
) -> Result<Json<ResetPasswordResponse>, ApiError> {
    let reset_password_usecase = ResetPasswordUsecase::new(
        state.captcha_service().clone(),
        state.token_service().clone(),
        state.user_query_handler().clone(),
        state.password_hasher().clone(),
        state.password_reset_token_query_handler().clone(),
        state.user_store_manager().clone(),
    );

    let response = reset_password_usecase
        .execute(dto.token, dto.new_password, dto.captcha)
        .await?;

    Ok(Json(response))
}
