use reforged_domain::models::achievement::value_object::AchievementId;
use reforged_domain::models::user::value_object::UserId;
use reforged_domain::models::user_achievement::{
    entity::UserAchievement,
    value_object::{AchievementDate, UserAchievementId},
};

#[derive(Debug)]
pub struct UserAchievementDbModelMapper(super::super::models::user_achievements::Model);

impl UserAchievementDbModelMapper {
    pub fn new(user_achievement: super::super::models::user_achievements::Model) -> Self {
        Self(user_achievement)
    }
}

impl From<UserAchievementDbModelMapper> for UserAchievement {
    fn from(value: UserAchievementDbModelMapper) -> Self {
        let user_achievement_model = value.0;

        let id = UserAchievementId::new(user_achievement_model.id);

        UserAchievement::builder()
            .id(id)
            .user_id(UserId::new(user_achievement_model.user_id))
            .achievement_id(AchievementId::new(user_achievement_model.achievement_id))
            .date(AchievementDate::new(user_achievement_model.date.and_utc()))
            .build()
    }
}
