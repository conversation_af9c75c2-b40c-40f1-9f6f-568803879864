use reforged_shared::{UuidId, Value};

use super::entity::ProfanityFilterSettings;

pub type ProfanityFilterSettingsId = UuidId<ProfanityFilterSettings>;

#[derive(Debug, PartialEq, Eq, PartialOrd, Ord, Clone, De<PERSON>ult)]
pub struct SwearWord(String);

impl SwearWord {
    pub fn new(word: impl Into<String>) -> Self {
        Self(word.into())
    }
}

impl Value<String> for SwearWord {
    fn value(&self) -> String {
        self.0.clone()
    }
}

impl From<String> for SwearWord {
    fn from(word: String) -> Self {
        Self(word)
    }
}

#[derive(Debug, PartialEq, Eq, PartialOrd, Ord, <PERSON><PERSON>, <PERSON><PERSON>ult)]

pub struct TimeToMute(i32);

impl TimeToMute {
    pub fn new(time: i32) -> Self {
        Self(time)
    }
}

impl Value<i32> for TimeToMute {
    fn value(&self) -> i32 {
        self.0
    }
}

impl From<i32> for TimeToMute {
    fn from(time: i32) -> Self {
        Self(time)
    }
}
