use async_trait::async_trait;
use mockall::automock;

use crate::error::RepositoryError;
use crate::models::game_rate_setting::entity::GameRateSetting;
use crate::models::game_rate_setting::value_object::GameRateSettingId;

#[automock]
#[async_trait]
pub trait GameRateSettingRepository: Send + Sync {
    async fn save(&self, entity: &GameRateSetting) -> Result<(), RepositoryError>;
    async fn update(&self, entity: &GameRateSetting) -> Result<(), RepositoryError>;
    async fn delete(&self, id: &GameRateSettingId) -> Result<(), RepositoryError>;
}

#[automock]
#[async_trait]
pub trait GameRateSettingReadRepository: Send + Sync {
    async fn find_by_id(
        &self,
        id: &GameRateSettingId,
    ) -> Result<Option<GameRateSetting>, RepositoryError>;
    async fn find_all(&self) -> Result<Vec<GameRateSetting>, RepositoryError>;
}
