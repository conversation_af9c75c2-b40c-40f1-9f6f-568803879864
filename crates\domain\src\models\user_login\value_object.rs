use chrono::{DateTime, Utc};
use reforged_shared::{UuidId, Value};

use super::entity::UserLogin;

pub type UserLoginId = UuidId<UserLogin>;

#[derive(Debug, PartialEq, Eq, PartialOrd, Ord, Clone, Default)]
pub struct Location(String);

impl Location {
    pub fn new(location: impl Into<String>) -> Self {
        Self(location.into())
    }
}

impl Value<String> for Location {
    fn value(&self) -> String {
        self.0.clone()
    }
}

impl From<String> for Location {
    fn from(value: String) -> Self {
        Self(value)
    }
}

impl From<&str> for Location {
    fn from(value: &str) -> Self {
        Self(value.to_string())
    }
}

#[derive(Debug, PartialEq, Eq, PartialOrd, Ord, Clone, Default)]
pub struct Status(String);

impl Status {
    pub fn new(status: impl Into<String>) -> Self {
        Self(status.into())
    }
}

impl Value<String> for Status {
    fn value(&self) -> String {
        self.0.clone()
    }
}

impl From<String> for Status {
    fn from(value: String) -> Self {
        Self(value)
    }
}

impl From<&str> for Status {
    fn from(value: &str) -> Self {
        Self(value.to_string())
    }
}

#[derive(Debug, PartialEq, Eq, PartialOrd, Ord, Clone, Default)]
pub struct Address(String);

impl Address {
    pub fn new(address: impl Into<String>) -> Self {
        Self(address.into())
    }
}

impl Value<String> for Address {
    fn value(&self) -> String {
        self.0.clone()
    }
}

impl From<String> for Address {
    fn from(value: String) -> Self {
        Self(value)
    }
}

impl From<&str> for Address {
    fn from(value: &str) -> Self {
        Self(value.to_string())
    }
}

#[derive(Debug, PartialEq, Eq, PartialOrd, Ord, Clone, Default)]
pub struct LoginDate(DateTime<Utc>);

impl LoginDate {
    pub fn new(date: DateTime<Utc>) -> Self {
        Self(date)
    }
}

impl Value<DateTime<Utc>> for LoginDate {
    fn value(&self) -> DateTime<Utc> {
        self.0
    }
}

impl From<DateTime<Utc>> for LoginDate {
    fn from(date: DateTime<Utc>) -> Self {
        Self(date)
    }
}