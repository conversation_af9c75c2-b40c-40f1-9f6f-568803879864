use crate::models;
use sea_orm::{ActiveModelTrait, IntoActiveModel};
use sea_orm_migration::prelude::*;

#[derive(DeriveMigrationName)]
pub struct Migration;

#[async_trait::async_trait]
impl MigrationTrait for Migration {
    async fn up(&self, manager: &SchemaManager) -> Result<(), DbErr> {
        let db = manager.get_connection();

        let hairs = include_str!("../json/hairs.json");
        let hairs: Vec<models::hairs::Model> = serde_json::from_str(hairs).unwrap();

        for model in hairs {
            let hair = model.into_active_model();
            hair.insert(db).await?;
        }

        let hair_shops = include_str!("../json/hairs_shops.json");
        let hair_shops: Vec<models::hair_shops::Model> = serde_json::from_str(hair_shops).unwrap();

        for model in hair_shops {
            let hair_shop = model.into_active_model();
            hair_shop.insert(db).await?;
        }

        let hair_shop_items = include_str!("../json/hairs_shops_items.json");
        let hair_shop_items: Vec<models::hair_shop_items::Model> =
            serde_json::from_str(hair_shop_items).unwrap();

        for model in hair_shop_items {
            let hair_shop_item = model.into_active_model();
            hair_shop_item.insert(db).await?;
        }

        Ok(())
    }

    async fn down(&self, _manager: &SchemaManager) -> Result<(), DbErr> {
        Ok(())
    }
}
