use async_trait::async_trait;
use mockall::automock;

use crate::error::RepositoryError;
use crate::models::guild_inventory::entity::GuildInventory;
use crate::models::guild_inventory::value_object::GuildInventoryId;

#[automock]
#[async_trait]
pub trait GuildInventoryRepository: Send + Sync {
    async fn save(&self, entity: &GuildInventory) -> Result<(), RepositoryError>;
    async fn update(&self, entity: &GuildInventory) -> Result<(), RepositoryError>;
    async fn delete(&self, id: &GuildInventoryId) -> Result<(), RepositoryError>;
}

#[automock]
#[async_trait]
pub trait GuildInventoryReadRepository: Send + Sync {
    async fn find_by_id(
        &self,
        id: &GuildInventoryId,
    ) -> Result<Option<GuildInventory>, RepositoryError>;
    async fn find_all(&self) -> Result<Vec<GuildInventory>, RepositoryError>;
}
