use crate::models::{book::value_object::*, shop::value_object::ShopId};

use getset::{<PERSON><PERSON>, <PERSON><PERSON>};

#[derive(<PERSON>bu<PERSON>, <PERSON><PERSON>, Default, bon::<PERSON><PERSON><PERSON>, <PERSON><PERSON>, Setters)]
#[getset(get = "pub", set = "pub")]
pub struct Book {
    pub id: BookId,
    pub file: BookFile,
    pub name: BookName,
    pub linkage: BookLinkage,
    pub lock: BookLock,
    pub desc: BookDesc,
    pub map: BookMap,
    pub book_type: BookType,
    pub hide: BookHide,
    pub label: BookLabel,
    pub shop_id: ShopId,
    pub field: BookField,
    pub index: BookIndex,
    pub value: BookValue,
}

impl Book {
    pub fn new(
        id: BookId,
        file: BookFile,
        name: BookName,
        linkage: BookLinkage,
        lock: BookLock,
        desc: BookDesc,
        map: BookMap,
        book_type: BookType,
        hide: BookHide,
        label: BookLabel,
        shop_id: ShopId,
        field: BookField,
        index: BookIndex,
        value: BookValue,
    ) -> Self {
        Self {
            id,
            file,
            name,
            linkage,
            lock,
            desc,
            map,
            book_type,
            hide,
            label,
            shop_id,
            field,
            index,
            value,
        }
    }
}

#[cfg(test)]
mod tests {
    use reforged_shared::{IdTrait, Value};

    use super::*;

    #[test]
    fn test_book_new() {
        let id = BookId::new(uuid::Uuid::now_v7());
        let file = BookFile::new("book_file.png");
        let name = BookName::new("Test Book");
        let linkage = BookLinkage::new("test_linkage");
        let lock = BookLock::new("locked");
        let desc = BookDesc::new("Book description");
        let map = BookMap::new("map_name");
        let book_type = BookType::new("quest");
        let hide = BookHide::new(0);
        let label = BookLabel::new("book_label");
        let shop_id = ShopId::new(uuid::Uuid::now_v7());
        let field = BookField::new("test_field");
        let index = BookIndex::new(5);
        let value = BookValue::new(100);

        let book = Book::new(
            id.clone(),
            file.clone(),
            name.clone(),
            linkage.clone(),
            lock.clone(),
            desc.clone(),
            map.clone(),
            book_type.clone(),
            hide.clone(),
            label.clone(),
            shop_id.clone(),
            field.clone(),
            index.clone(),
            value.clone(),
        );

        assert_eq!(book.id().get_id(), id.get_id());
        assert_eq!(book.file().value(), file.value());
        assert_eq!(book.name().value(), name.value());
        assert_eq!(book.linkage().value(), linkage.value());
        assert_eq!(book.lock().value(), lock.value());
        assert_eq!(book.desc().value(), desc.value());
        assert_eq!(book.map().value(), map.value());
        assert_eq!(book.book_type().value(), book_type.value());
        assert_eq!(book.hide().value(), hide.value());
        assert_eq!(book.label().value(), label.value());
        assert_eq!(book.shop_id().get_id(), shop_id.get_id());
        assert_eq!(book.field().value(), field.value());
        assert_eq!(book.index().value(), index.value());
        assert_eq!(book.value().value(), value.value());
    }

    #[test]
    fn test_book_builder() {
        let id = BookId::new(uuid::Uuid::now_v7());
        let file = BookFile::new("book_file.png");
        let name = BookName::new("Test Book");
        let linkage = BookLinkage::new("test_linkage");
        let lock = BookLock::new("locked");
        let desc = BookDesc::new("Book description");
        let map = BookMap::new("map_name");
        let book_type = BookType::new("quest");
        let hide = BookHide::new(0);
        let label = BookLabel::new("book_label");
        let shop = ShopId::new(uuid::Uuid::now_v7());
        let field = BookField::new("test_field");
        let index = BookIndex::new(5);
        let value = BookValue::new(100);

        let book = Book::builder()
            .id(id.clone())
            .file(file.clone())
            .name(name.clone())
            .linkage(linkage.clone())
            .lock(lock.clone())
            .desc(desc.clone())
            .map(map.clone())
            .book_type(book_type.clone())
            .hide(hide.clone())
            .label(label.clone())
            .shop_id(shop.clone())
            .field(field.clone())
            .index(index.clone())
            .value(value.clone())
            .build();

        assert_eq!(book.id().get_id(), id.get_id());
        assert_eq!(book.file().value(), file.value());
        assert_eq!(book.name().value(), name.value());
        assert_eq!(book.linkage().value(), linkage.value());
        assert_eq!(book.lock().value(), lock.value());
        assert_eq!(book.desc().value(), desc.value());
        assert_eq!(book.map().value(), map.value());
        assert_eq!(book.book_type().value(), book_type.value());
        assert_eq!(book.hide().value(), hide.value());
        assert_eq!(book.label().value(), label.value());
        assert_eq!(book.shop_id().get_id(), shop.get_id());
        assert_eq!(book.field().value(), field.value());
        assert_eq!(book.index().value(), index.value());
        assert_eq!(book.value().value(), value.value());
    }

    #[test]
    fn test_getters_and_setters() {
        let mut book = Book::default();

        let id = BookId::new(uuid::Uuid::now_v7());
        let file = BookFile::new("book_file.png");
        let name = BookName::new("Test Book");
        let linkage = BookLinkage::new("test_linkage");
        let lock = BookLock::new("locked");
        let desc = BookDesc::new("Book description");
        let map = BookMap::new("map_name");
        let book_type = BookType::new("quest");
        let hide = BookHide::new(0);
        let label = BookLabel::new("book_label");
        let shop = ShopId::new(uuid::Uuid::now_v7());
        let field = BookField::new("test_field");
        let index = BookIndex::new(5);
        let value = BookValue::new(100);

        book.set_id(id.clone());
        book.set_file(file.clone());
        book.set_name(name.clone());
        book.set_linkage(linkage.clone());
        book.set_lock(lock.clone());
        book.set_desc(desc.clone());
        book.set_map(map.clone());
        book.set_book_type(book_type.clone());
        book.set_hide(hide.clone());
        book.set_label(label.clone());
        book.set_shop_id(shop.clone());
        book.set_field(field.clone());
        book.set_index(index.clone());
        book.set_value(value.clone());

        assert_eq!(book.id().get_id(), id.get_id());
        assert_eq!(book.file().value(), file.value());
        assert_eq!(book.name().value(), name.value());
        assert_eq!(book.linkage().value(), linkage.value());
        assert_eq!(book.lock().value(), lock.value());
        assert_eq!(book.desc().value(), desc.value());
        assert_eq!(book.map().value(), map.value());
        assert_eq!(book.book_type().value(), book_type.value());
        assert_eq!(book.hide().value(), hide.value());
        assert_eq!(book.label().value(), label.value());
        assert_eq!(book.shop_id().get_id(), shop.get_id());
        assert_eq!(book.field().value(), field.value());
        assert_eq!(book.index().value(), index.value());
        assert_eq!(book.value().value(), value.value());
    }
}
