use crate::models::{item::value_object::ItemId, monster::value_object::MonsterId};

use super::value_object::*;
use getset::{<PERSON><PERSON>, Set<PERSON>};
#[derive(<PERSON>bu<PERSON>, <PERSON><PERSON>, <PERSON><PERSON>ult, bon::<PERSON><PERSON><PERSON>, <PERSON><PERSON>, Set<PERSON>)]
#[getset(get = "pub", set = "pub")]
pub struct MonsterDrop {
    id: MonsterDropId,
    monster_id: MonsterId,
    item_id: ItemId,
    chance: MonsterDropChance,
    quantity: MonsterDropQuantity,
}

impl MonsterDrop {
    pub fn new(
        id: MonsterDropId,
        monster_id: MonsterId,
        item_id: ItemId,
        chance: MonsterDropChance,
        quantity: MonsterDropQuantity,
    ) -> Self {
        Self {
            id,
            monster_id,
            item_id,
            chance,
            quantity,
        }
    }
}

#[cfg(test)]
mod tests {
    use reforged_shared::{IdTrait, Value};

    use super::*;

    #[test]
    fn test_monster_drop_new() {
        let id = MonsterDropId::new(uuid::Uuid::now_v7());
        let monster_id = MonsterId::new(uuid::Uuid::now_v7());
        let item_id = ItemId::new(uuid::Uuid::now_v7());
        let chance = MonsterDropChance::new(25.5);
        let quantity = MonsterDropQuantity::new(1);

        let monster_drop = MonsterDrop::new(
            id.clone(),
            monster_id.clone(),
            item_id.clone(),
            chance.clone(),
            quantity.clone(),
        );

        assert_eq!(monster_drop.id().get_id(), id.get_id());
        assert_eq!(monster_drop.monster_id().get_id(), monster_id.get_id());
        assert_eq!(monster_drop.item_id().get_id(), item_id.get_id());
        assert_eq!(monster_drop.chance().value(), chance.value());
        assert_eq!(monster_drop.quantity().value(), quantity.value());
    }

    #[test]
    fn test_monster_drop_builder() {
        let id = MonsterDropId::new(uuid::Uuid::now_v7());
        let monster_id = MonsterId::new(uuid::Uuid::now_v7());
        let item_id = ItemId::new(uuid::Uuid::now_v7());
        let chance = MonsterDropChance::new(25.5);
        let quantity = MonsterDropQuantity::new(1);

        let monster_drop = MonsterDrop::builder()
            .id(id.clone())
            .monster_id(monster_id.clone())
            .item_id(item_id.clone())
            .chance(chance.clone())
            .quantity(quantity.clone())
            .build();

        assert_eq!(monster_drop.id().get_id(), id.get_id());
        assert_eq!(monster_drop.monster_id().get_id(), monster_id.get_id());
        assert_eq!(monster_drop.item_id().get_id(), item_id.get_id());
        assert_eq!(monster_drop.chance().value(), chance.value());
        assert_eq!(monster_drop.quantity().value(), quantity.value());
    }

    #[test]
    fn test_getters_and_setters() {
        let mut monster_drop = MonsterDrop::default();

        let id = MonsterDropId::new(uuid::Uuid::now_v7());
        let monster_id = MonsterId::new(uuid::Uuid::now_v7());
        let item_id = ItemId::new(uuid::Uuid::now_v7());
        let chance = MonsterDropChance::new(25.5);
        let quantity = MonsterDropQuantity::new(1);

        monster_drop.set_id(id.clone());
        monster_drop.set_monster_id(monster_id.clone());
        monster_drop.set_item_id(item_id.clone());
        monster_drop.set_chance(chance.clone());
        monster_drop.set_quantity(quantity.clone());

        assert_eq!(monster_drop.id().get_id(), id.get_id());
        assert_eq!(monster_drop.monster_id().get_id(), monster_id.get_id());
        assert_eq!(monster_drop.item_id().get_id(), item_id.get_id());
        assert_eq!(monster_drop.chance().value(), chance.value());
        assert_eq!(monster_drop.quantity().value(), quantity.value());
    }
}
