use reforged_domain::models::wheel_reward::entity::WheelReward;
use reforged_domain::models::wheel_reward::value_object::*;
use reforged_domain::models::item::value_object::ItemId;
use num_traits::ToPrimitive;

#[derive(Debug)]
pub struct WheelRewardDbModelMapper(super::super::models::wheel_rewards::Model);

impl WheelRewardDbModelMapper {
    pub fn new(model: super::super::models::wheel_rewards::Model) -> Self {
        Self(model)
    }
}

impl From<WheelRewardDbModelMapper> for WheelReward {
    fn from(value: WheelRewardDbModelMapper) -> Self {
        let model = value.0;

        WheelReward::builder()
            .id(WheelRewardId::new(model.id))
            .item_id(ItemId::new(model.item_id))
            .chance(WheelRewardChance::new(model.chance.to_f64().unwrap_or(0.0)))
            .build()
    }
}
