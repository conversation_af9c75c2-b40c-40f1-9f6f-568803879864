use reforged_domain::models::wheel_reward::entity::WheelReward;
use reforged_domain::models::wheel_reward::value_object::WheelRewardId;
use reforged_domain::models::item::value_object::ItemId;

#[derive(Debug)]
pub struct WheelRewardDbModelMapper(super::super::models::wheel_rewards::Model);

impl WheelRewardDbModelMapper {
    pub fn new(model: super::super::models::wheel_rewards::Model) -> Self {
        Self(model)
    }
}

impl From<WheelRewardDbModelMapper> for WheelReward {
    fn from(value: WheelRewardDbModelMapper) -> Self {
        let model = value.0;
        
        WheelReward::builder()
            .id(WheelRewardId::new(model.id))
            .maybe_item_id(model.item_id.map(ItemId::new))
            .gold(model.gold.into())
            .coins(model.coins.into())
            .crystal(model.crystal.into())
            .quantity(model.quantity.into())
            .chance(model.chance.into())
            .build()
    }
}
