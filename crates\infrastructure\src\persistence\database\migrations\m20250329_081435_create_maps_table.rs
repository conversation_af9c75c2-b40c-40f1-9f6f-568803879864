use crate::models::prelude::*;
use sea_orm::Schema;
use sea_orm_migration::prelude::*;

#[derive(DeriveMigrationName)]
pub struct Migration;

#[async_trait::async_trait]
impl MigrationTrait for Migration {
    async fn up(&self, manager: &SchemaManager) -> Result<(), DbErr> {
        let backend = manager.get_database_backend();
        let schema = Schema::new(backend);

        // MARK: maps tables and junction tables
        manager
            .create_table(schema.create_table_from_entity(Maps))
            .await?;

        manager
            .create_table(schema.create_table_from_entity(MapCells))
            .await?;
        manager
            .create_table(schema.create_table_from_entity(MapItems))
            .await?;
        manager
            .create_table(schema.create_table_from_entity(MapMonsters))
            .await?;

        manager
            .create_table(schema.create_table_from_entity(MonsterBosses))
            .await?;

        Ok(())
    }

    async fn down(&self, manager: &SchemaManager) -> Result<(), DbErr> {
        manager
            .drop_table(Table::drop().table(MapCells).to_owned())
            .await?;
        manager
            .drop_table(Table::drop().table(MapItems).to_owned())
            .await?;
        manager
            .drop_table(Table::drop().table(MonsterBosses).to_owned())
            .await?;
        manager
            .drop_table(Table::drop().table(MapMonsters).to_owned())
            .await?;
        manager
            .drop_table(Table::drop().table(Maps).to_owned())
            .await
    }
}
