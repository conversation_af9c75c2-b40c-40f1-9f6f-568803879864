use async_trait::async_trait;
use mockall::automock;

use crate::error::RepositoryError;
use crate::models::aura_effect::entity::AuraEffect;
use crate::models::aura_effect::value_object::AuraEffectId;

#[automock]
#[async_trait]
pub trait AuraEffectRepository: Send + Sync {
    async fn save(&self, entity: &AuraEffect) -> Result<(), RepositoryError>;
    async fn update(&self, entity: &AuraEffect) -> Result<(), RepositoryError>;
    async fn delete(&self, id: &AuraEffectId) -> Result<(), RepositoryError>;
}

#[automock]
#[async_trait]
pub trait AuraEffectReadRepository: Send + Sync {
    async fn find_by_id(&self, id: &AuraEffectId) -> Result<Option<AuraEffect>, RepositoryError>;
    async fn find_all(&self) -> Result<Vec<AuraEffect>, RepositoryError>;
}
