use async_trait::async_trait;
use mockall::automock;

use crate::error::RepositoryError;
use crate::models::faction::entity::Faction;
use crate::models::faction::value_object::FactionId;

#[automock]
#[async_trait]
pub trait FactionRepository: Send + Sync {
    async fn save(&self, entity: &Faction) -> Result<(), RepositoryError>;
    async fn update(&self, entity: &Faction) -> Result<(), RepositoryError>;
    async fn delete(&self, id: &FactionId) -> Result<(), RepositoryError>;
}

#[automock]
#[async_trait]
pub trait FactionReadRepository: Send + Sync {
    async fn find_by_id(&self, id: &FactionId) -> Result<Option<Faction>, RepositoryError>;
    async fn find_all(&self) -> Result<Vec<Faction>, RepositoryError>;
}
