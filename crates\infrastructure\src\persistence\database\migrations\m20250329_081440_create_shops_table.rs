use crate::models::prelude::*;
use sea_orm::Schema;
use sea_orm_migration::prelude::*;

#[derive(DeriveMigrationName)]
pub struct Migration;

#[async_trait::async_trait]
impl MigrationTrait for Migration {
    async fn up(&self, manager: &SchemaManager) -> Result<(), DbErr> {
        let backend = manager.get_database_backend();
        let schema = Schema::new(backend);

        // MARK: shops tables and junction tables
        manager
            .create_table(schema.create_table_from_entity(Shops))
            .await?;
        manager
            .create_table(schema.create_table_from_entity(Stores))
            .await?;
        manager
            .create_table(schema.create_table_from_entity(ShopItems))
            .await?;
        manager
            .create_table(schema.create_table_from_entity(ShopLocations))
            .await?;

        Ok(())
    }

    async fn down(&self, manager: &SchemaManager) -> Result<(), DbErr> {
        manager
            .drop_table(Table::drop().table(ShopLocations).to_owned())
            .await?;
        manager
            .drop_table(Table::drop().table(ShopLocations).to_owned())
            .await?;

        manager
            .drop_table(Table::drop().table(Shops).to_owned())
            .await?;

        manager
            .drop_table(Table::drop().table(Stores).to_owned())
            .await
    }
}
