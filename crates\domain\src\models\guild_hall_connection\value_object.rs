use reforged_shared::{UuidId, Value};

use super::entity::GuildHallConnection;

pub type GuildHallConnectionId = UuidId<GuildHallConnection>;

#[derive(Debug, PartialEq, Eq, PartialOrd, Ord, Clone, Default)]
pub struct Pad(String);

impl Pad {
    pub fn new(value: impl Into<String>) -> Self {
        Self(value.into())
    }
}

impl Value<String> for Pad {
    fn value(&self) -> String {
        self.0.clone()
    }
}

impl From<String> for Pad {
    fn from(value: String) -> Self {
        Self(value)
    }
}

impl From<&str> for Pad {
    fn from(value: &str) -> Self {
        Self(value.to_string())
    }
}

#[derive(Debug, PartialEq, Eq, PartialOrd, Ord, Clone, Default)]

pub struct Cell(String);

impl Cell {
    pub fn new(value: impl Into<String>) -> Self {
        Self(value.into())
    }
}

impl Value<String> for Cell {
    fn value(&self) -> String {
        self.0.clone()
    }
}

impl From<String> for Cell {
    fn from(value: String) -> Self {
        Self(value)
    }
}

impl From<&str> for Cell {
    fn from(value: &str) -> Self {
        Self(value.to_string())
    }
}

#[derive(Debug, PartialEq, Eq, PartialOrd, Ord, Clone, Default)]

pub struct PadPosition(String);

impl PadPosition {
    pub fn new(value: impl Into<String>) -> Self {
        Self(value.into())
    }
}

impl Value<String> for PadPosition {
    fn value(&self) -> String {
        self.0.clone()
    }
}

impl From<String> for PadPosition {
    fn from(value: String) -> Self {
        Self(value)
    }
}

impl From<&str> for PadPosition {
    fn from(value: &str) -> Self {
        Self(value.to_string())
    }
}
