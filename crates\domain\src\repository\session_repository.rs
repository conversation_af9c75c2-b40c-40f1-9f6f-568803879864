use async_trait::async_trait;
use mockall::automock;

use crate::error::RepositoryError;
use crate::models::session::entity::Session;
use crate::models::session::value_object::SessionId;

#[automock]
#[async_trait]
pub trait SessionRepository: Send + Sync {
    async fn save(&self, entity: &Session) -> Result<(), RepositoryError>;
    async fn update(&self, entity: &Session) -> Result<(), RepositoryError>;
    async fn delete(&self, id: &SessionId) -> Result<(), RepositoryError>;
}

#[automock]
#[async_trait]
pub trait SessionReadRepository: Send + Sync {
    async fn find_by_id(&self, id: &SessionId) -> Result<Option<Session>, RepositoryError>;
    async fn find_all(&self) -> Result<Vec<Session>, RepositoryError>;
}
