use reforged_shared::{UuidId, Value};

use super::entity::UserStat;

pub type UserStatId = UuidId<UserStat>;

#[derive(Debug, PartialEq, Eq, PartialOrd, Ord, Clone, Default)]

pub struct UserStatLastArea(String);

impl UserStatLastArea {
    pub fn new(last_area: impl Into<String>) -> Self {
        Self(last_area.into())
    }
}

impl Value<String> for UserStatLastArea {
    fn value(&self) -> String {
        self.0.clone()
    }
}

impl From<String> for UserStatLastArea {
    fn from(value: String) -> Self {
        Self(value)
    }
}

impl From<&str> for UserStatLastArea {
    fn from(value: &str) -> Self {
        Self(value.to_string())
    }
}

#[derive(Debug, PartialEq, Eq, PartialOrd, Ord, Clone, Default)]

pub struct UserStatCurrentServer(String);

impl UserStatCurrentServer {
    pub fn new(current_server: impl Into<String>) -> Self {
        Self(current_server.into())
    }
}

impl Value<String> for UserStatCurrentServer {
    fn value(&self) -> String {
        self.0.clone()
    }
}

impl From<String> for UserStatCurrentServer {
    fn from(value: String) -> Self {
        Self(value)
    }
}

impl From<&str> for UserStatCurrentServer {
    fn from(value: &str) -> Self {
        Self(value.to_string())
    }
}

#[derive(Debug, PartialEq, Eq, PartialOrd, Ord, Clone, Default)]

pub struct UserStatHouseInfo(String);

impl UserStatHouseInfo {
    pub fn new(house_info: impl Into<String>) -> Self {
        Self(house_info.into())
    }
}

impl Value<String> for UserStatHouseInfo {
    fn value(&self) -> String {
        self.0.clone()
    }
}

impl From<String> for UserStatHouseInfo {
    fn from(value: String) -> Self {
        Self(value)
    }
}

impl From<&str> for UserStatHouseInfo {
    fn from(value: &str) -> Self {
        Self(value.to_string())
    }
}

#[derive(Debug, PartialEq, Eq, PartialOrd, Ord, Clone, Default)]

pub struct UserStatKillCount(i64);

impl UserStatKillCount {
    pub fn new(kill_count: i64) -> Self {
        Self(kill_count)
    }
}

impl Value<i64> for UserStatKillCount {
    fn value(&self) -> i64 {
        self.0
    }
}

impl From<i64> for UserStatKillCount {
    fn from(value: i64) -> Self {
        Self(value)
    }
}

#[derive(Debug, PartialEq, Eq, PartialOrd, Ord, Clone, Default)]

pub struct UserStatDeathCount(i64);

impl UserStatDeathCount {
    pub fn new(death_count: i64) -> Self {
        Self(death_count)
    }
}

impl Value<i64> for UserStatDeathCount {
    fn value(&self) -> i64 {
        self.0
    }
}

impl From<i64> for UserStatDeathCount {
    fn from(value: i64) -> Self {
        Self(value)
    }
}

#[derive(Debug, PartialEq, Eq, PartialOrd, Ord, Clone, Default)]

pub struct UserStatPvpRatio(Option<i64>);

impl UserStatPvpRatio {
    pub fn new(pvp_ratio: Option<i64>) -> Self {
        Self(pvp_ratio)
    }
}

impl Value<Option<i64>> for UserStatPvpRatio {
    fn value(&self) -> Option<i64> {
        self.0
    }
}

impl From<Option<i64>> for UserStatPvpRatio {
    fn from(value: Option<i64>) -> Self {
        Self(value)
    }
}
