use crate::models::prelude::*;
use sea_orm::Schema;
use sea_orm_migration::prelude::*;

#[derive(DeriveMigrationName)]
pub struct Migration;

#[async_trait::async_trait]
impl MigrationTrait for Migration {
    async fn up(&self, manager: &SchemaManager) -> Result<(), DbErr> {
        let backend = manager.get_database_backend();
        let schema = Schema::new(backend);

        // MARK: classes tables and junction tables
        manager
            .create_table(schema.create_table_from_entity(ClassCategories))
            .await?;

        manager
            .create_table(schema.create_table_from_entity(Classes))
            .await?;

        manager
            .create_table(schema.create_table_from_entity(ClassSkills))
            .await?;

        Ok(())
    }

    async fn down(&self, manager: &SchemaManager) -> Result<(), DbErr> {
        manager
            .drop_table(Table::drop().table(ClassCategories).to_owned())
            .await?;

        manager
            .drop_table(Table::drop().table(ClassSkills).to_owned())
            .await?;

        manager
            .drop_table(Table::drop().table(Classes).to_owned())
            .await
    }
}
