use reforged_domain::models::game_setting::{
    entity::GameSetting,
    value_object::{GameSettingId, Location},
};

use super::location_mapper::LoginLocationMapper;

#[derive(Debug)]
pub struct GameSettingDbModelMapper(super::super::models::game_settings::Model);

impl GameSettingDbModelMapper {
    pub fn new(game_setting: super::super::models::game_settings::Model) -> Self {
        Self(game_setting)
    }
}

impl From<GameSettingDbModelMapper> for GameSetting {
    fn from(value: GameSettingDbModelMapper) -> Self {
        let game_setting_model = value.0;

        let id = GameSettingId::new(game_setting_model.id);
        let location_mapper = LoginLocationMapper::new(game_setting_model.location);
        let location: Location = location_mapper.into();

        GameSetting::builder()
            .id(id)
            .name(game_setting_model.name.into())
            .value(game_setting_model.value.into())
            .location(location)
            .build()
    }
}
