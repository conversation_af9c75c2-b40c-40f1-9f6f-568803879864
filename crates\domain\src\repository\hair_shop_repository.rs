use async_trait::async_trait;
use mockall::automock;

use crate::error::RepositoryError;
use crate::models::hair_shop::entity::HairShop;
use crate::models::hair_shop::value_object::HairShopId;

#[automock]
#[async_trait]
pub trait HairShopRepository: Send + Sync {
    async fn save(&self, entity: &HairShop) -> Result<(), RepositoryError>;
    async fn update(&self, entity: &HairShop) -> Result<(), RepositoryError>;
    async fn delete(&self, id: &HairShopId) -> Result<(), RepositoryError>;
}

#[automock]
#[async_trait]
pub trait HairShopReadRepository: Send + Sync {
    async fn find_by_id(&self, id: &HairShopId) -> Result<Option<HairShop>, RepositoryError>;
    async fn find_all(&self) -> Result<Vec<HairShop>, RepositoryError>;
}
