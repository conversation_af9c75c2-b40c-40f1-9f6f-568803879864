use crate::models::aura::value_object::*;

use getset::{<PERSON><PERSON>, <PERSON><PERSON>};

#[derive(<PERSON>bu<PERSON>, <PERSON><PERSON>, <PERSON><PERSON>ult, bon::<PERSON><PERSON><PERSON>, <PERSON><PERSON>, Set<PERSON>)]
#[getset(get = "pub", set = "pub")]
pub struct Aura {
    pub id: AuraId,
    pub name: AuraName,
    pub duration: AuraDuration,
    pub category: AuraCategory,
    pub damage_increase: AuraDamageIncrease,
    pub damage_taken_decrease: AuraDamageTakenDecrease,
    pub chance: AuraChance,
    pub self_target: AuraSelfTarget,
}

impl Aura {
    pub fn new(
        id: AuraId,
        name: AuraName,
        duration: AuraDuration,
        category: AuraCategory,
        damage_increase: AuraDamageIncrease,
        damage_taken_decrease: AuraDamageTakenDecrease,
        chance: AuraChance,
        self_target: AuraSelfTarget,
    ) -> Self {
        Self {
            id,
            name,
            duration,
            category,
            damage_increase,
            damage_taken_decrease,
            chance,
            self_target,
        }
    }
}

#[cfg(test)]
mod tests {
    use reforged_shared::{Id<PERSON>rai<PERSON>, Value};

    use super::*;

    #[test]
    fn test_aura_new() {
        let id = AuraId::new(uuid::Uuid::now_v7());
        let name = AuraName::new("Test Aura");
        let duration = AuraDuration::new(10);
        let category = AuraCategory::new("Test Category");
        let damage_increase = AuraDamageIncrease::new(0.5);
        let damage_taken_decrease = AuraDamageTakenDecrease::new(0.3);
        let chance = AuraChance::new(0.7);
        let self_target = AuraSelfTarget::new(true);

        let aura = Aura::new(
            id.clone(),
            name.clone(),
            duration.clone(),
            category.clone(),
            damage_increase.clone(),
            damage_taken_decrease.clone(),
            chance.clone(),
            self_target.clone(),
        );

        assert_eq!(aura.id().get_id(), id.get_id());
        assert_eq!(aura.name().value(), name.value());
        assert_eq!(aura.duration().value(), duration.value());
        assert_eq!(aura.category().value(), category.value());
        assert_eq!(aura.damage_increase().value(), damage_increase.value());
        assert_eq!(
            aura.damage_taken_decrease().value(),
            damage_taken_decrease.value()
        );
        assert_eq!(aura.chance().value(), chance.value());
        assert_eq!(aura.self_target().value(), self_target.value());
    }

    #[test]
    fn test_aura_builder() {
        let id = AuraId::new(uuid::Uuid::now_v7());
        let name = AuraName::new("Test Aura");
        let duration = AuraDuration::new(10);
        let category = AuraCategory::new("Test Category");
        let damage_increase = AuraDamageIncrease::new(0.5);
        let damage_taken_decrease = AuraDamageTakenDecrease::new(0.3);
        let chance = AuraChance::new(0.7);
        let self_target = AuraSelfTarget::new(true);

        let aura = Aura::builder()
            .id(id.clone())
            .name(name.clone())
            .duration(duration.clone())
            .category(category.clone())
            .damage_increase(damage_increase.clone())
            .damage_taken_decrease(damage_taken_decrease.clone())
            .chance(chance.clone())
            .self_target(self_target.clone())
            .build();

        assert_eq!(aura.id().get_id(), id.get_id());
        assert_eq!(aura.name().value(), name.value());
        assert_eq!(aura.duration().value(), duration.value());
        assert_eq!(aura.category().value(), category.value());
        assert_eq!(aura.damage_increase().value(), damage_increase.value());
        assert_eq!(
            aura.damage_taken_decrease().value(),
            damage_taken_decrease.value()
        );
        assert_eq!(aura.chance().value(), chance.value());
        assert_eq!(aura.self_target().value(), self_target.value());
    }

    #[test]
    fn test_getters_and_setters() {
        let mut aura = Aura::default();

        let id = AuraId::new(uuid::Uuid::now_v7());
        let name = AuraName::new("Test Aura");
        let duration = AuraDuration::new(10);
        let category = AuraCategory::new("Test Category");
        let damage_increase = AuraDamageIncrease::new(0.5);
        let damage_taken_decrease = AuraDamageTakenDecrease::new(0.3);
        let chance = AuraChance::new(0.7);
        let self_target = AuraSelfTarget::new(true);

        aura.set_id(id.clone());
        aura.set_name(name.clone());
        aura.set_duration(duration.clone());
        aura.set_category(category.clone());
        aura.set_damage_increase(damage_increase.clone());
        aura.set_damage_taken_decrease(damage_taken_decrease.clone());
        aura.set_chance(chance.clone());
        aura.set_self_target(self_target.clone());

        assert_eq!(aura.id().get_id(), id.get_id());
        assert_eq!(aura.name().value(), name.value());
        assert_eq!(aura.duration().value(), duration.value());
        assert_eq!(aura.category().value(), category.value());
        assert_eq!(aura.damage_increase().value(), damage_increase.value());
        assert_eq!(
            aura.damage_taken_decrease().value(),
            damage_taken_decrease.value()
        );
        assert_eq!(aura.chance().value(), chance.value());
        assert_eq!(aura.self_target().value(), self_target.value());
    }
}
