use super::value_object::*;
use crate::models::user::value_object::UserId;
use getset::{<PERSON><PERSON>, <PERSON><PERSON>};

#[derive(<PERSON>bu<PERSON>, <PERSON><PERSON>, Default, bon::<PERSON><PERSON><PERSON>, <PERSON><PERSON>, Set<PERSON>)]
#[getset(get = "pub", set = "pub")]
pub struct Session {
    id: SessionId,
    user_id: UserId,
    token: SessionToken,
    user_agent: SessionUserAgent,
    client_ip: SessionClientIp,
    is_blocked: SessionIsBlocked,
    created_at: SessionCreatedAt,
    expires_at: SessionExpiresAt,
}

impl Session {
    pub fn new(
        id: SessionId,
        user_id: UserId,
        token: SessionToken,
        user_agent: SessionUserAgent,
        client_ip: SessionClientIp,
        is_blocked: SessionIsBlocked,
        created_at: SessionCreatedAt,
        expires_at: SessionExpiresAt,
    ) -> Self {
        Self {
            id,
            user_id,
            token,
            user_agent,
            client_ip,
            is_blocked,
            created_at,
            expires_at,
        }
    }
}

#[cfg(test)]
mod tests {
    use chrono::Utc;
    use reforged_shared::{IdTrait, Value};

    use super::*;

    #[test]
    fn test_session_new() {
        let id = SessionId::new(uuid::Uuid::now_v7());
        let user_id = UserId::new(uuid::Uuid::now_v7());
        let token = SessionToken::new("Test Token");
        let user_agent = SessionUserAgent::new("Test User Agent");
        let client_ip = SessionClientIp::new("127.0.0.1");
        let is_blocked = SessionIsBlocked::new(false);
        let created_at = SessionCreatedAt::new(Utc::now());
        let expires_at = SessionExpiresAt::new(Utc::now());

        let session = Session::new(
            id.clone(),
            user_id.clone(),
            token.clone(),
            user_agent.clone(),
            client_ip.clone(),
            is_blocked.clone(),
            created_at.clone(),
            expires_at.clone(),
        );

        assert_eq!(session.id().get_id(), id.get_id());
        assert_eq!(session.user_id().get_id(), user_id.get_id());
        assert_eq!(session.token().value(), token.value());
        assert_eq!(session.user_agent().value(), user_agent.value());
        assert_eq!(session.client_ip().value(), client_ip.value());
        assert_eq!(session.is_blocked().value(), is_blocked.value());
        assert_eq!(session.created_at().value(), created_at.value());
        assert_eq!(session.expires_at().value(), expires_at.value());
    }

    #[test]
    fn test_session_builder() {
        let id = SessionId::new(uuid::Uuid::now_v7());
        let user_id = UserId::new(uuid::Uuid::now_v7());
        let token = SessionToken::new("Test Token");
        let user_agent = SessionUserAgent::new("Test User Agent");
        let client_ip = SessionClientIp::new("127.0.0.1");
        let is_blocked = SessionIsBlocked::new(false);
        let created_at = SessionCreatedAt::new(Utc::now());
        let expires_at = SessionExpiresAt::new(Utc::now());

        let session = Session::builder()
            .id(id.clone())
            .user_id(user_id.clone())
            .token(token.clone())
            .user_agent(user_agent.clone())
            .client_ip(client_ip.clone())
            .is_blocked(is_blocked.clone())
            .created_at(created_at.clone())
            .expires_at(expires_at.clone())
            .build();

        assert_eq!(session.id().get_id(), id.get_id());
        assert_eq!(session.user_id().get_id(), user_id.get_id());
        assert_eq!(session.token().value(), token.value());
        assert_eq!(session.user_agent().value(), user_agent.value());
        assert_eq!(session.client_ip().value(), client_ip.value());
        assert_eq!(session.is_blocked().value(), is_blocked.value());
        assert_eq!(session.created_at().value(), created_at.value());
        assert_eq!(session.expires_at().value(), expires_at.value());
    }

    #[test]
    fn test_getters_and_setters() {
        let mut session = Session::default();

        let id = SessionId::new(uuid::Uuid::now_v7());
        let user_id = UserId::new(uuid::Uuid::now_v7());
        let token = SessionToken::new("Test Token");
        let user_agent = SessionUserAgent::new("Test User Agent");
        let client_ip = SessionClientIp::new("127.0.0.1");
        let is_blocked = SessionIsBlocked::new(false);
        let created_at = SessionCreatedAt::new(Utc::now());
        let expires_at = SessionExpiresAt::new(Utc::now());

        session.set_id(id.clone());
        session.set_user_id(user_id.clone());
        session.set_token(token.clone());
        session.set_user_agent(user_agent.clone());
        session.set_client_ip(client_ip.clone());
        session.set_is_blocked(is_blocked.clone());
        session.set_created_at(created_at.clone());
        session.set_expires_at(expires_at.clone());

        assert_eq!(session.id().get_id(), id.get_id());
        assert_eq!(session.user_id().get_id(), user_id.get_id());
        assert_eq!(session.token().value(), token.value());
        assert_eq!(session.user_agent().value(), user_agent.value());
        assert_eq!(session.client_ip().value(), client_ip.value());
        assert_eq!(session.is_blocked().value(), is_blocked.value());
        assert_eq!(session.created_at().value(), created_at.value());
        assert_eq!(session.expires_at().value(), expires_at.value());
    }
}
