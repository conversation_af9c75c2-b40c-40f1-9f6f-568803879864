use reforged_domain::models::class::{
    entity::Class,
    value_object::{
        ClassDescription, ClassId, ClassName, ManaRegenerationMethods, StatsDescription,
    },
};
use reforged_domain::models::class_category::value_object::ClassCategoryEnum;

#[derive(Debug)]
pub struct ClassDbModelMapper(super::super::models::classes::Model);

impl ClassDbModelMapper {
    pub fn new(class: super::super::models::classes::Model) -> Self {
        Self(class)
    }
}

impl From<ClassDbModelMapper> for Class {
    fn from(value: ClassDbModelMapper) -> Self {
        let class_model = value.0;
        let category = ClassCategoryEnum::try_from(class_model.category).unwrap_or_default();

        let id = ClassId::new(class_model.id);

        Class::builder()
            .id(id)
            .name(ClassName::new(class_model.name))
            .category(category)
            .description(ClassDescription::new(class_model.description))
            .mana_regeneration_methods(ManaRegenerationMethods::new(
                class_model.mana_regeneration_methods,
            ))
            .stats_description(StatsDescription::new(class_model.stats_description))
            .build()
    }
}
