use reforged_domain::models::user_role::entity::UserRole;

#[derive(Debug)]
pub struct UserRoleDbModelMapper(super::super::models::sea_orm_active_enums::UserRole);

impl UserRoleDbModelMapper {
    pub fn new(role: super::super::models::sea_orm_active_enums::UserRole) -> Self {
        Self(role)
    }
}

impl From<UserRoleDbModelMapper> for UserRole {
    fn from(value: UserRoleDbModelMapper) -> Self {
        match value.0 {
            super::super::models::sea_orm_active_enums::UserRole::Banned => UserRole::Banned,
            super::super::models::sea_orm_active_enums::UserRole::Player => UserRole::Player,
            super::super::models::sea_orm_active_enums::UserRole::Founder => UserRole::Founder,
            super::super::models::sea_orm_active_enums::UserRole::Support => UserRole::Support,
            super::super::models::sea_orm_active_enums::UserRole::Vip => UserRole::Vip,
            super::super::models::sea_orm_active_enums::UserRole::Moderator => UserRole::Moderator,
            super::super::models::sea_orm_active_enums::UserRole::Trainee => UserRole::Trainee,
            super::super::models::sea_orm_active_enums::UserRole::Administrator => UserRole::Administrator,
        }
    }
}
