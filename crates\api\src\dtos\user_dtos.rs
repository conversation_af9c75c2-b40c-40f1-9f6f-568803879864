use serde::{Deserialize, Serialize};
use validator::Validate;

#[derive(Debug, Serialize, Deserialize, Validate)]
pub struct CreateUserDTO {
    #[validate(length(min = 3, max = 20))]
    pub username: String,
    #[validate(length(min = 8, max = 20))]
    pub password: String,
    #[validate(email)]
    pub email: String,
    #[validate(length(min = 1, max = 1), custom(function = "validate_gender"))]
    pub gender: String,
    #[validate(length(min = 1, max = 4096))]
    pub captcha: String,
}

fn validate_gender(gender: &str) -> Result<(), validator::ValidationError> {
    match gender {
        "M" | "F" => Ok(()),
        _ => Err(validator::ValidationError::new("Invalid gender")),
    }
}

#[derive(Debug, Serialize, Deserialize, Validate)]
pub struct LoginDTO {
    #[validate(length(min = 3, max = 20))]
    pub username: String,
    #[validate(length(min = 8, max = 20))]
    pub password: String,
    #[validate(length(min = 1, max = 4096))]
    pub captcha: String,
}

#[derive(Debug, Serialize, Deserialize, Validate)]
pub struct ForgotPasswordDTO {
    #[validate(email)]
    pub email: String,
    #[validate(length(min = 1, max = 4096, message = "Missing captcha response"))]
    pub captcha: String,
}

#[derive(Debug, Serialize, Deserialize, Validate)]
pub struct ResetPasswordDTO {
    #[validate(length(min = 1, max = 4096, message = "New password must not be empty"))]
    pub new_password: String,
    #[validate(length(min = 1, max = 4096, message = "Missing captcha response"))]
    pub captcha: String,
    #[validate(length(min = 1, max = 4096, message = "Missing reset password token"))]
    pub token: String,
}
