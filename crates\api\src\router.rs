use actix_cors::Cors;
use actix_governor::{Governor, GovernorConfigBuilder};
use actix_web::http::StatusCode;
use actix_web::middleware::{ErrorHandlerResponse, ErrorHandlers, <PERSON>gger, NormalizePath};
use actix_web::web::{Data, get};
use actix_web::{
    App,
    body::MessageBody,
    dev::{ServiceFactory, ServiceRequest, ServiceResponse},
    web::{self, <PERSON><PERSON>},
};
use actix_web::{Error, HttpResponse, Result};

use crate::handlers::auth_handlers::create_auth_service;
use crate::handlers::class_category_handler::create_class_category_service;
use crate::handlers::user_handlers::create_user_service;
use crate::{error::ApiError, responses::BaseApiResponse, state::ApiState};

pub fn create_router(
    state: ApiState,
) -> App<
    impl ServiceFactory<
        ServiceRequest,
        Response = ServiceResponse<impl MessageBody>,
        Config = (),
        InitError = (),
        Error = Error,
    >,
> {
    let allowed_origins = state.allowed_origins();

    let governor_config = GovernorConfigBuilder::default()
        .seconds_per_request(10)
        .burst_size(10)
        .finish()
        .unwrap();
    let governor = Governor::new(&governor_config);

    let mut cors = Cors::default();
    cors = cors
        .allow_any_method()
        .allowed_methods(vec!["GET", "POST", "PUT", "PATCH", "DELETE"]);

    for origin in allowed_origins {
        cors = cors.allowed_origin(origin);
    }

    let app = App::new()
        .wrap(Logger::default())
        .wrap(NormalizePath::trim())
        .wrap(cors)
        .wrap(governor)
        .wrap(ErrorHandlers::new().handler(StatusCode::NOT_FOUND, error_404_error_handler))
        .app_data(web::Data::new(state))
        .service(web::resource("/").route(get().to(index))) // Keep main index route at root
        .service(
            web::scope("/api/v1")
                .route("", web::get().to(api_v1_index))
                .service(create_user_service())
                .service(create_auth_service())
                .service(create_class_category_service()),
        );

    app
}

async fn api_v1_index() -> Result<Json<BaseApiResponse>, ApiError> {
    Ok(Json(BaseApiResponse::new(
        "Welcome to the Reforged API v1".to_string(),
    )))
}

async fn index(state: Data<ApiState>) -> Result<Json<BaseApiResponse>, ApiError> {
    let origin = state
        .allowed_origins()
        .first()
        .map(|origin| origin.as_str())
        .unwrap_or("http://localhost:3000");

    let message =
        format!("This is the API for Reforged, please visit <a href='{origin}'>{origin}</a>");

    Ok(Json(BaseApiResponse::new(message)))
}

fn error_404_error_handler<B>(res: ServiceResponse<B>) -> Result<ErrorHandlerResponse<B>> {
    let (req, _) = res.into_parts();
    let state = req.app_data::<Data<ApiState>>().unwrap();

    let origin = state
        .allowed_origins()
        .first()
        .map(|origin| origin.as_str())
        .unwrap_or("http://localhost:3000");

    let message = format!("Seems like you are lost, please visit <a href='{origin}'>{origin}</a>");

    let response = HttpResponse::NotFound()
        .content_type("application/json")
        .json(Json(BaseApiResponse::new(message)));

    Ok(ErrorHandlerResponse::Response(
        ServiceResponse::new(req, response).map_into_right_body(),
    ))
}
