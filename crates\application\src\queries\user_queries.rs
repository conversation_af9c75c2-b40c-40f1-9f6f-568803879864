pub struct GetLatestIdQuery;

pub struct GetUserByIdQuery {
    id: uuid::Uuid,
}

pub struct GetUserByUsernameQuery {
    pub username: String,
}

pub struct GetUserByEmailQuery {
    pub email: String,
}

impl GetUserByIdQuery {
    pub fn new(id: uuid::Uuid) -> Self {
        Self { id }
    }

    pub fn id(&self) -> uuid::Uuid {
        self.id
    }
}

pub struct ListUsersQuery {
    page: u32,
    limit: u32,
}

impl ListUsersQuery {
    pub fn new(page: u32, limit: u32) -> Self {
        Self { page, limit }
    }

    pub fn page(&self) -> u32 {
        self.page
    }

    pub fn limit(&self) -> u32 {
        self.limit
    }
}
