use num_traits::ToPrimitive;
use reforged_domain::models::user::value_object::UserId;
use reforged_domain::models::user_exp::entity::UserExperience;
use reforged_domain::models::user_exp::value_object::UserExperienceId;

#[derive(Debug)]
pub struct UserExperienceDbModelMapper(super::super::models::user_exps::Model);

impl UserExperienceDbModelMapper {
    pub fn new(model: super::super::models::user_exps::Model) -> Self {
        Self(model)
    }
}

impl From<UserExperienceDbModelMapper> for UserExperience {
    fn from(value: UserExperienceDbModelMapper) -> Self {
        let model = value.0;

        UserExperience::builder()
            .id(UserExperienceId::new(model.id))
            .user_id(UserId::new(model.user_id))
            .level(model.level.to_u16().unwrap_or_default().into())
            .experience(model.exp.to_u32().unwrap_or_default().into())
            .build()
    }
}
