use async_trait::async_trait;
use mockall::automock;

use crate::error::RepositoryError;
use crate::models::user_achievement::entity::UserAchievement;
use crate::models::user_achievement::value_object::UserAchievementId;

#[automock]
#[async_trait]
pub trait UserAchievementRepository: Send + Sync {
    async fn save(&self, entity: &UserAchievement) -> Result<(), RepositoryError>;
    async fn update(&self, entity: &UserAchievement) -> Result<(), RepositoryError>;
    async fn delete(&self, id: &UserAchievementId) -> Result<(), RepositoryError>;
}

#[automock]
#[async_trait]
pub trait UserAchievementReadRepository: Send + Sync {
    async fn find_by_id(
        &self,
        id: &UserAchievementId,
    ) -> Result<Option<UserAchievement>, RepositoryError>;
    async fn find_all(&self) -> Result<Vec<UserAchievement>, RepositoryError>;
}
