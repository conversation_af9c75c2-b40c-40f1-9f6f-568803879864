use reforged_shared::{UuidId, Value as SharedValue};

use super::entity::GameSetting;

pub type GameSettingId = UuidId<GameSetting>;

#[derive(Debug, PartialEq, Eq, PartialOrd, Ord, Clone, Default)]
pub struct GameSettingName(String);

impl GameSettingName {
    pub fn new(name: impl Into<String>) -> Self {
        Self(name.into())
    }
}

impl SharedValue<String> for GameSettingName {
    fn value(&self) -> String {
        self.0.clone()
    }
}

impl From<String> for GameSettingName {
    fn from(value: String) -> Self {
        Self(value)
    }
}

impl From<&str> for GameSettingName {
    fn from(value: &str) -> Self {
        Self(value.to_string())
    }
}

#[derive(Debug, PartialEq, Eq, PartialOrd, Ord, Clone, Default)]

pub struct Value(String);

impl Value {
    pub fn new(value: impl Into<String>) -> Self {
        Self(value.into())
    }
}

impl SharedValue<String> for Value {
    fn value(&self) -> String {
        self.0.clone()
    }
}

impl From<String> for Value {
    fn from(value: String) -> Self {
        Self(value)
    }
}

impl From<&str> for Value {
    fn from(value: &str) -> Self {
        Self(value.to_string())
    }
}

#[derive(Debug, PartialEq, Eq, PartialOrd, Ord, Clone, Default)]
pub enum Location {
    Loader,
    #[default]
    Game,
    Wiki,
}

impl Location {
    pub fn as_str(&self) -> &'static str {
        match self {
            Location::Loader => "Loader",
            Location::Game => "Game",
            Location::Wiki => "Wiki",
        }
    }
}

impl SharedValue<String> for Location {
    fn value(&self) -> String {
        self.as_str().to_string()
    }
}

impl From<&str> for Location {
    fn from(value: &str) -> Self {
        match value.to_lowercase().as_str() {
            "loader" => Location::Loader,
            "game" => Location::Game,
            "wiki" => Location::Wiki,
            _ => Location::Game, // Default to Game if unknown
        }
    }
}

impl From<String> for Location {
    fn from(value: String) -> Self {
        Self::from(value.as_str())
    }
}
