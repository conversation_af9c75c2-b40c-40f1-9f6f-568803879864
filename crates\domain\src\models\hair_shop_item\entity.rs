use crate::models::{
    hair::value_object::HairId, hair_shop::value_object::HairShopId, profile::value_object::Gender,
};
use getset::{<PERSON><PERSON>, Set<PERSON>};

use super::value_object::HairShopItemId;

#[derive(<PERSON>bu<PERSON>, <PERSON><PERSON>, <PERSON><PERSON>ult, bon::<PERSON><PERSON><PERSON>, <PERSON><PERSON>, Setters)]
#[getset(get = "pub", set = "pub")]
pub struct HairShopItem {
    id: HairShopItemId,
    gender: Gender,
    hair_shop_id: HairShopId,
    hair_id: HairId,
}

impl HairShopItem {
    pub fn new(
        id: HairShopItemId,
        gender: Gender,
        hair_shop_id: HairShopId,
        hair_id: HairId,
    ) -> Self {
        Self {
            id,
            gender,
            hair_shop_id,
            hair_id,
        }
    }
}

#[cfg(test)]
mod tests {
    use reforged_shared::IdTrait;

    use super::*;

    #[test]
    fn test_hair_shop_item_new() {
        let id = HairShopItemId::new(uuid::Uuid::now_v7());
        let gender = Gender::Female;
        let hair_shop_id = HairShopId::new(uuid::Uuid::now_v7());
        let hair_id = HairId::new(uuid::Uuid::now_v7());

        let item = HairShopItem::new(
            id.clone(),
            gender.clone(),
            hair_shop_id.clone(),
            hair_id.clone(),
        );

        assert_eq!(item.id().get_id(), id.get_id());
        assert_eq!(item.gender(), &gender);
        assert_eq!(item.hair_shop_id().get_id(), hair_shop_id.get_id());
        assert_eq!(item.hair_id().get_id(), hair_id.get_id());
    }

    #[test]
    fn test_hair_shop_item_builder() {
        let id = HairShopItemId::new(uuid::Uuid::now_v7());
        let gender = Gender::Male;
        let hair_shop_id = HairShopId::new(uuid::Uuid::now_v7());
        let hair_id = HairId::new(uuid::Uuid::now_v7());

        let item = HairShopItem::builder()
            .id(id.clone())
            .gender(gender.clone())
            .hair_shop_id(hair_shop_id.clone())
            .hair_id(hair_id.clone())
            .build();

        assert_eq!(item.id().get_id(), id.get_id());
        assert_eq!(item.gender(), &gender);
        assert_eq!(item.hair_shop_id().get_id(), hair_shop_id.get_id());
        assert_eq!(item.hair_id().get_id(), hair_id.get_id());
    }

    #[test]
    fn test_getters_and_setters() {
        let mut item = HairShopItem::default();

        let id = HairShopItemId::new(uuid::Uuid::now_v7());
        let gender = Gender::Female;
        let hair_shop_id = HairShopId::new(uuid::Uuid::now_v7());
        let hair_id = HairId::new(uuid::Uuid::now_v7());

        item.set_id(id.clone());
        item.set_gender(gender.clone());
        item.set_hair_shop_id(hair_shop_id.clone());
        item.set_hair_id(hair_id.clone());

        assert_eq!(item.id().get_id(), id.get_id());
        assert_eq!(item.gender(), &gender);
        assert_eq!(item.hair_shop_id().get_id(), hair_shop_id.get_id());
        assert_eq!(item.hair_id().get_id(), hair_id.get_id());
    }
}
