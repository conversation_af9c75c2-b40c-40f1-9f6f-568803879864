use reforged_domain::models::achievement::value_object::AchievementId;
use reforged_domain::models::item::value_object::ItemId;
use reforged_domain::models::shop::{
    entity::Shop,
    value_object::{ShopField, ShopHouse, ShopId, ShopLimited, ShopName, ShopStaff, ShopUpgrade},
};

#[derive(Debug)]
pub struct ShopDbModelMapper(super::super::models::shops::Model);

impl ShopDbModelMapper {
    pub fn new(shop: super::super::models::shops::Model) -> Self {
        Self(shop)
    }
}

impl From<ShopDbModelMapper> for Shop {
    fn from(value: ShopDbModelMapper) -> Self {
        let shop_model = value.0;

        let id = ShopId::new(shop_model.id);

        Shop::builder()
            .id(id)
            .name(ShopName::new(shop_model.name))
            .house(ShopHouse::new(shop_model.house))
            .upgrade(ShopUpgrade::new(shop_model.upgrade))
            .staff(ShopStaff::new(shop_model.staff))
            .limited(ShopLimited::new(shop_model.limited))
            .field(ShopField::new(shop_model.field))
            .achievement_id(
                shop_model
                    .achievement_id
                    .map(AchievementId::new)
                    .unwrap_or_default(),
            )
            .item_id(shop_model.item_id.map(ItemId::new).unwrap_or_default())
            .build()
    }
}
