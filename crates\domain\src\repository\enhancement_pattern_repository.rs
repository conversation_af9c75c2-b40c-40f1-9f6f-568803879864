use async_trait::async_trait;
use mockall::automock;

use crate::error::RepositoryError;
use crate::models::enhancement_pattern::entity::EnhancementPattern;
use crate::models::enhancement_pattern::value_object::EnhancementPatternId;

#[automock]
#[async_trait]
pub trait EnhancementPatternRepository: Send + Sync {
    async fn save(&self, entity: &EnhancementPattern) -> Result<(), RepositoryError>;
    async fn update(&self, entity: &EnhancementPattern) -> Result<(), RepositoryError>;
    async fn delete(&self, id: &EnhancementPatternId) -> Result<(), RepositoryError>;
}

#[automock]
#[async_trait]
pub trait EnhancementPatternReadRepository: Send + Sync {
    async fn find_by_id(
        &self,
        id: &EnhancementPatternId,
    ) -> Result<Option<EnhancementPattern>, RepositoryError>;
    async fn find_all(&self) -> Result<Vec<EnhancementPattern>, RepositoryError>;
}
