use reforged_domain::models::achievement::{entity::Achievement, value_object::AchievementId};

#[derive(Debug)]
pub struct AchievementDbModelMapper(super::super::models::achievements::Model);

impl AchievementDbModelMapper {
    pub fn new(achievement: super::super::models::achievements::Model) -> Self {
        Self(achievement)
    }
}

impl From<AchievementDbModelMapper> for Achievement {
    fn from(value: AchievementDbModelMapper) -> Self {
        let achievement_model = value.0;

        let id = AchievementId::new(achievement_model.id);

        Achievement::builder()
            .id(id)
            .name(achievement_model.name.into())
            .description(achievement_model.description.into())
            .file(achievement_model.file.into())
            .category(achievement_model.category.into())
            .show(achievement_model.show.into())
            .build()
    }
}
