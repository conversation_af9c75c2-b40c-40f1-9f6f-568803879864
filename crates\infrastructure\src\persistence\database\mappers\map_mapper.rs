use reforged_domain::models::map::{
    entity::Map,
    value_object::{
        MapFile, MapId, MapMaxPlayers, MapName, MapPvp, MapReqLevel, MapStaff, MapUpgrade,
        MapWorldBoss,
    },
};

#[derive(Debug)]
pub struct MapDbModelMapper(super::super::models::maps::Model);

impl MapDbModelMapper {
    pub fn new(map: super::super::models::maps::Model) -> Self {
        Self(map)
    }
}

impl From<MapDbModelMapper> for Map {
    fn from(value: MapDbModelMapper) -> Self {
        let map_model = value.0;

        let id = MapId::new(map_model.id);

        Map::builder()
            .id(id)
            .name(MapName::new(map_model.name))
            .file(MapFile::new(map_model.file))
            .max_players(MapMaxPlayers::new(map_model.max_players))
            .req_level(MapReqLevel::new(map_model.req_level))
            .upgrade(MapUpgrade::new(map_model.upgrade))
            .staff(MapStaff::new(map_model.staff))
            .pvp(MapPvp::new(map_model.pvp))
            .world_boss(MapWorldBoss::new(map_model.world_boss))
            .build()
    }
}
