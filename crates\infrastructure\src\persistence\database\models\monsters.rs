//! `SeaORM` Entity, @generated by sea-orm-codegen 1.1.0

use sea_orm::entity::prelude::*;
use serde::Deserialize;

#[derive(Clone, Debug, PartialEq, DeriveEntityModel, Eq, Deserialize)]
#[sea_orm(table_name = "monsters")]
pub struct Model {
    #[sea_orm(primary_key, auto_increment = false)]
    pub id: Uuid,
    pub name: String,
    pub race: String,
    pub file: String,
    pub health: i32,
    pub mana: i32,
    pub level: i16,
    pub gold: i32,
    pub coins: i32,
    pub experience: i32,
    pub reputation: i32,
    pub dps: i32,
    pub speed: i32,
    pub element: String,
    pub linkage: String,
    pub team_id: i32,
    pub boss: bool,
    pub boss_min_res: i32,
    pub respawn_time: i32,
    pub hide: bool,
    pub world_boss: bool,
}

#[derive(Co<PERSON>, <PERSON><PERSON>, Debug, EnumIter, DeriveRelation)]
pub enum Relation {
    #[sea_orm(has_many = "super::map_monsters::Entity")]
    MapMonsters,
    #[sea_orm(has_many = "super::monster_bosses::Entity")]
    MonsterBosses,
    #[sea_orm(has_many = "super::monster_drops::Entity")]
    MonsterDrops,
    #[sea_orm(has_many = "super::monster_skills::Entity")]
    MonsterSkills,
}

impl Related<super::map_monsters::Entity> for Entity {
    fn to() -> RelationDef {
        Relation::MapMonsters.def()
    }
}

impl Related<super::monster_bosses::Entity> for Entity {
    fn to() -> RelationDef {
        Relation::MonsterBosses.def()
    }
}

impl Related<super::monster_drops::Entity> for Entity {
    fn to() -> RelationDef {
        Relation::MonsterDrops.def()
    }
}

impl Related<super::monster_skills::Entity> for Entity {
    fn to() -> RelationDef {
        Relation::MonsterSkills.def()
    }
}

impl ActiveModelBehavior for ActiveModel {}
