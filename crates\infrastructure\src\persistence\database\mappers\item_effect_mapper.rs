use reforged_domain::models::item_effect::entity::ItemEffect;
use reforged_domain::models::item_effect::value_object::ItemEffectId;
use reforged_domain::models::item::value_object::ItemId;

#[derive(Debug)]
pub struct ItemEffectDbModelMapper(super::super::models::item_effects::Model);

impl ItemEffectDbModelMapper {
    pub fn new(model: super::super::models::item_effects::Model) -> Self {
        Self(model)
    }
}

impl From<ItemEffectDbModelMapper> for ItemEffect {
    fn from(value: ItemEffectDbModelMapper) -> Self {
        let model = value.0;

        ItemEffect::builder()
            .id(ItemEffectId::new(model.id))
            .item_id(ItemId::new(model.item_id))
            .damage_increase(model.damage_increase.to_f64().unwrap_or(0.0).into())
            .damage_taken(model.damage_taken.to_f64().unwrap_or(0.0).into())
            .exp(model.exp.to_f64().unwrap_or(0.0).into())
            .gold(model.gold.to_f64().unwrap_or(0.0).into())
            .coins(model.coins.to_f64().unwrap_or(0.0).into())
            .class_point(model.class_point.to_f64().unwrap_or(0.0).into())
            .reputation(model.reputation.to_f64().unwrap_or(0.0).into())
            .build()
    }
}
