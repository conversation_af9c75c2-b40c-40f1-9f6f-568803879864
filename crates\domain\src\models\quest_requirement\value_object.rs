use reforged_shared::{UuidId, Value};

use super::entity::QuestRequirement;

pub type QuestRequirementId = UuidId<QuestRequirement>;

#[derive(Debug, PartialEq, Eq, PartialOrd, Ord, <PERSON><PERSON>, <PERSON><PERSON>ult)]

pub struct QuestRequirementQuantity(i32);

impl QuestRequirementQuantity {
    pub fn new(quantity: i32) -> Self {
        Self(quantity)
    }
}

impl Value<i32> for QuestRequirementQuantity {
    fn value(&self) -> i32 {
        self.0
    }
}

impl From<i32> for QuestRequirementQuantity {
    fn from(value: i32) -> Self {
        Self(value)
    }
}
