use reforged_shared::{UuidId, Value};

use super::entity::Role;

pub type RoleId = UuidId<Role>;

#[derive(Debug, PartialEq, Eq, PartialOrd, Ord, Clone, Default)]
pub enum UserRoleTag {
    Banned,
    #[default]
    Player,
    Founder,
    Support,
    VIP,
    Moderator,
    Trainee,
    Administrator,
}

#[derive(Debug, PartialEq, Eq, PartialOrd, Ord, Clone, Default)]
pub struct RoleTag(UserRoleTag);

impl RoleTag {
    pub fn new(tag: UserRoleTag) -> Self {
        Self(tag)
    }
}

impl Value<UserRoleTag> for RoleTag {
    fn value(&self) -> UserRoleTag {
        match self.0 {
            UserRoleTag::Banned => UserRoleTag::Banned,
            UserRoleTag::Player => UserRoleTag::Player,
            UserRoleTag::Founder => UserRoleTag::Founder,
            UserRoleTag::Support => UserRoleTag::Support,
            UserRoleTag::VIP => UserRoleTag::VIP,
            UserRoleTag::Moderator => UserRoleTag::Moderator,
            UserRoleTag::Trainee => UserRoleTag::Trainee,
            UserRoleTag::Administrator => UserRoleTag::Administrator,
        }
    }
}

impl From<UserRoleTag> for RoleTag {
    fn from(value: UserRoleTag) -> Self {
        match value {
            UserRoleTag::Banned => Self(UserRoleTag::Banned),
            UserRoleTag::Player => Self(UserRoleTag::Player),
            UserRoleTag::Founder => Self(UserRoleTag::Founder),
            UserRoleTag::Support => Self(UserRoleTag::Support),
            UserRoleTag::VIP => Self(UserRoleTag::VIP),
            UserRoleTag::Moderator => Self(UserRoleTag::Moderator),
            UserRoleTag::Trainee => Self(UserRoleTag::Trainee),
            UserRoleTag::Administrator => Self(UserRoleTag::Administrator),
        }
    }
}

// impl From<&str> for RoleTag {
//     fn from(value: &str) -> Self {
//         match value {
//             "banned" | "Banned" => Self(UserRoleTag::Banned),
//             "player" | "Player" => Self(UserRoleTag::Player),
//             "founder" | "Founder" => Self(UserRoleTag::Founder),
//             "support" | "Support" => Self(UserRoleTag::Support),
//             "vip" | "VIP" => Self(UserRoleTag::VIP),
//             "moderator" | "Moderator" => Self(UserRoleTag::Moderator),
//             "trainee" | "Trainee" => Self(UserRoleTag::Trainee),
//             "administrator" | "Administrator" => Self(UserRoleTag::Administrator),
//             _ => Self(UserRoleTag::Player),
//         }
//     }
// }

#[derive(Debug, PartialEq, Eq, PartialOrd, Ord, Clone, Default)]
pub struct RoleUserColor(String);

impl RoleUserColor {
    pub fn new(color: impl Into<String>) -> Self {
        Self(color.into())
    }
}

impl Value<String> for RoleUserColor {
    fn value(&self) -> String {
        self.0.clone()
    }
}

impl From<String> for RoleUserColor {
    fn from(value: String) -> Self {
        Self(value)
    }
}

impl From<&str> for RoleUserColor {
    fn from(value: &str) -> Self {
        Self(value.to_string())
    }
}
