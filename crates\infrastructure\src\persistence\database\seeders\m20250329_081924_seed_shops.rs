use crate::models;
use sea_orm::{ActiveModelTrait, IntoActiveModel};
use sea_orm_migration::prelude::*;

#[derive(DeriveMigrationName)]
pub struct Migration;

#[async_trait::async_trait]
impl MigrationTrait for Migration {
    async fn up(&self, manager: &SchemaManager) -> Result<(), DbErr> {
        let db = manager.get_connection();

        let shops = include_str!("../json/shops.json");
        let shops: Vec<models::shops::Model> = serde_json::from_str(shops).unwrap();

        for model in shops {
            let shop = model.into_active_model();
            shop.insert(db).await?;
        }

        let shop_items = include_str!("../json/shops_items.json");
        let shop_items: Vec<models::shop_items::Model> = serde_json::from_str(shop_items).unwrap();

        for model in shop_items {
            let shop_item = model.into_active_model();
            shop_item.insert(db).await?;
        }

        let shop_locations = include_str!("../json/shops_locations.json");
        let shop_locations: Vec<models::shop_locations::Model> =
            serde_json::from_str(shop_locations).unwrap();

        for model in shop_locations {
            let shop_location = model.into_active_model();
            shop_location.insert(db).await?;
        }

        Ok(())
    }

    async fn down(&self, _manager: &SchemaManager) -> Result<(), DbErr> {
        Ok(())
    }
}
