use actix_web::{
    <PERSON><PERSON>,
    web::{self, <PERSON>, <PERSON><PERSON>, Path, Query},
};
use reforged_application::{
    queries::class_category_query_handlers::ClassCategoryResponse,
    usecases::{
        class_category::create_class_category_usecase::CreateClassCategoryUsecase,
        class_category::delete_class_category_usecase::DeleteClassCategoryUsecase,
        class_category::get_class_categories_usecase::GetClassCategoriesUsecase,
        class_category::get_class_category_by_id_usecase::GetClassCategoryByIdUsecase,
    },
};

use crate::{
    dtos::{
        class_category_dtos::{CreateClassCategoryDTO, GetClassCategoriesQueryParams},
        validate::ValidateJson,
    },
    error::ApiError,
    middlewares::admin_middleware::AuthorizeAdmin,
    responses::BaseApiResponse,
    state::ApiState,
};

pub fn create_class_category_service() -> Scope {
    web::scope("/class-categories")
        .service(
            web::resource("")
                .route(web::get().to(get_class_categories_handler))
                .route(
                    web::post()
                        .to(create_class_category_handler)
                        .wrap(AuthorizeAdmin),
                ),
        )
        .service(
            web::resource("/{id}")
                .route(web::get().to(get_class_category_by_id_handler))
                .route(
                    web::delete()
                        .to(delete_class_category_handler)
                        .wrap(AuthorizeAdmin),
                ),
        )
}

async fn create_class_category_handler(
    state: Data<ApiState>,
    ValidateJson(dto): ValidateJson<CreateClassCategoryDTO>,
) -> Result<Json<ClassCategoryResponse>, ApiError> {
    let usecase = CreateClassCategoryUsecase::new(
        state.class_category_query_handler().clone(),
        state.class_category_repository().clone(),
    );

    let response = usecase
        .execute(
            dto.name,
            dto.category,
            dto.strength,
            dto.endurance,
            dto.dexterity,
            dto.intellect,
            dto.wisdom,
            dto.luck,
        )
        .await?;

    let response = ClassCategoryResponse::from(response);

    Ok(Json(response))
}

async fn get_class_category_by_id_handler(
    state: Data<ApiState>,
    id: Path<uuid::Uuid>,
) -> Result<Json<ClassCategoryResponse>, ApiError> {
    let id = id.into_inner();

    let usecase = GetClassCategoryByIdUsecase::new(state.class_category_query_handler().clone());

    let response = usecase.execute(id).await?;

    Ok(Json(response))
}

async fn get_class_categories_handler(
    state: Data<ApiState>,
    Query(query): Query<GetClassCategoriesQueryParams>,
) -> Result<Json<Vec<ClassCategoryResponse>>, ApiError> {
    let page = query.page.unwrap_or(1);
    let limit = query.limit.unwrap_or(10);

    let usecase = GetClassCategoriesUsecase::new(state.class_category_query_handler().clone());
    let response = usecase.execute(page, limit).await?;

    Ok(Json(response))
}

async fn delete_class_category_handler(
    state: Data<ApiState>,
    id: Path<uuid::Uuid>,
) -> Result<Json<BaseApiResponse>, ApiError> {
    let id = id.into_inner();
    let usecase = DeleteClassCategoryUsecase::new(state.class_category_repository().clone());
    usecase.execute(id).await?;

    let response = BaseApiResponse::new(format!("Class category with id {} deleted", id));
    Ok(Json(response))
}
