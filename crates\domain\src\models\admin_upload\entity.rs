use crate::models::user::value_object::UserId;
use getset::{<PERSON><PERSON>, <PERSON><PERSON>};

use super::value_object::{AdminUploadDate, AdminUploadId, AdminUploadName, AdminUploadType};

#[derive(<PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, bon::<PERSON><PERSON><PERSON>, <PERSON><PERSON>, Set<PERSON>)]
#[getset(get = "pub", set = "pub")]
pub struct AdminUpload {
    id: AdminUploadId,
    user_id: UserId,
    file_name: AdminUploadName,
    r#type: AdminUploadType,
    date: AdminUploadDate,
}

impl AdminUpload {
    pub fn new(
        id: AdminUploadId,
        user_id: UserId,
        file_name: AdminUploadName,
        r#type: AdminUploadType,
        date: AdminUploadDate,
    ) -> Self {
        Self {
            id,
            user_id,
            file_name,
            r#type,
            date,
        }
    }
}
