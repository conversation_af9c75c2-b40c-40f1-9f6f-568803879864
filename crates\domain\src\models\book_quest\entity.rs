use crate::models::book_quest::value_object::*;
use getset::{<PERSON><PERSON>, <PERSON><PERSON>};

#[derive(<PERSON>bu<PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, bon::<PERSON><PERSON><PERSON>, <PERSON><PERSON>, Set<PERSON>)]
#[getset(get = "pub", set = "pub")]
pub struct BookQuest {
    pub id: BookQuestId,
    pub name: BookQuestName,
    pub field: BookQuestField,
    pub lock: BookQuestLock,
    pub map: BookQuestMap,
    pub quest_type: BookQuestType,
    pub hide: BookQuestHide,
    pub index: BookQuestIndex,
    pub value: BookQuestValue,
}

impl BookQuest {
    pub fn new(
        id: BookQuestId,
        name: BookQuestName,
        field: BookQuestField,
        lock: BookQuestLock,
        map: BookQuestMap,
        quest_type: BookQuestType,
        hide: BookQuestHide,
        index: BookQuestIndex,
        value: BookQuestValue,
    ) -> Self {
        Self {
            id,
            name,
            field,
            lock,
            map,
            quest_type,
            hide,
            index,
            value,
        }
    }
}

#[cfg(test)]
mod tests {
    use reforged_shared::{IdTrait, Value};

    use super::*;

    #[test]
    fn test_book_quest_new() {
        let id = BookQuestId::new(uuid::Uuid::now_v7());
        let name = BookQuestName::new("Test BookQuest");
        let field = BookQuestField::new("test_field");
        let lock = BookQuestLock::new("locked");
        let map = BookQuestMap::new("map_name");
        let quest_type = BookQuestType::new("main");
        let hide = BookQuestHide::new(0);
        let index = BookQuestIndex::new(5);
        let value = BookQuestValue::new(100);

        let book_quest = BookQuest::new(
            id.clone(),
            name.clone(),
            field.clone(),
            lock.clone(),
            map.clone(),
            quest_type.clone(),
            hide.clone(),
            index.clone(),
            value.clone(),
        );

        assert_eq!(book_quest.id().get_id(), id.get_id());
        assert_eq!(book_quest.name().value(), name.value());
        assert_eq!(book_quest.field().value(), field.value());
        assert_eq!(book_quest.lock().value(), lock.value());
        assert_eq!(book_quest.map().value(), map.value());
        assert_eq!(book_quest.quest_type().value(), quest_type.value());
        assert_eq!(book_quest.hide().value(), hide.value());
        assert_eq!(book_quest.index().value(), index.value());
        assert_eq!(book_quest.value().value(), value.value());
    }

    #[test]
    fn test_book_quest_builder() {
        let id = BookQuestId::new(uuid::Uuid::now_v7());
        let name = BookQuestName::new("Test BookQuest");
        let field = BookQuestField::new("test_field");
        let lock = BookQuestLock::new("locked");
        let map = BookQuestMap::new("map_name");
        let quest_type = BookQuestType::new("main");
        let hide = BookQuestHide::new(0);
        let index = BookQuestIndex::new(5);
        let value = BookQuestValue::new(100);

        let book_quest = BookQuest::builder()
            .id(id.clone())
            .name(name.clone())
            .field(field.clone())
            .lock(lock.clone())
            .map(map.clone())
            .quest_type(quest_type.clone())
            .hide(hide.clone())
            .index(index.clone())
            .value(value.clone())
            .build();

        assert_eq!(book_quest.id().get_id(), id.get_id());
        assert_eq!(book_quest.name().value(), name.value());
        assert_eq!(book_quest.field().value(), field.value());
        assert_eq!(book_quest.lock().value(), lock.value());
        assert_eq!(book_quest.map().value(), map.value());
        assert_eq!(book_quest.quest_type().value(), quest_type.value());
        assert_eq!(book_quest.hide().value(), hide.value());
        assert_eq!(book_quest.index().value(), index.value());
        assert_eq!(book_quest.value().value(), value.value());
    }

    #[test]
    fn test_getters_and_setters() {
        let mut book_quest = BookQuest::default();

        let id = BookQuestId::new(uuid::Uuid::now_v7());
        let name = BookQuestName::new("Test BookQuest");
        let field = BookQuestField::new("test_field");
        let lock = BookQuestLock::new("locked");
        let map = BookQuestMap::new("map_name");
        let quest_type = BookQuestType::new("main");
        let hide = BookQuestHide::new(0);
        let index = BookQuestIndex::new(5);
        let value = BookQuestValue::new(100);

        book_quest.set_id(id.clone());
        book_quest.set_name(name.clone());
        book_quest.set_field(field.clone());
        book_quest.set_lock(lock.clone());
        book_quest.set_map(map.clone());
        book_quest.set_quest_type(quest_type.clone());
        book_quest.set_hide(hide.clone());
        book_quest.set_index(index.clone());
        book_quest.set_value(value.clone());

        assert_eq!(book_quest.id().get_id(), id.get_id());
        assert_eq!(book_quest.name().value(), name.value());
        assert_eq!(book_quest.field().value(), field.value());
        assert_eq!(book_quest.lock().value(), lock.value());
        assert_eq!(book_quest.map().value(), map.value());
        assert_eq!(book_quest.quest_type().value(), quest_type.value());
        assert_eq!(book_quest.hide().value(), hide.value());
        assert_eq!(book_quest.index().value(), index.value());
        assert_eq!(book_quest.value().value(), value.value());
    }
}
