use async_trait::async_trait;
use mockall::automock;

use crate::error::RepositoryError;
use crate::models::user_skill::entity::{UserSkill, UserSkillId};

#[automock]
#[async_trait]
pub trait UserSkillRepository: Send + Sync {
    async fn save(&self, entity: &UserSkill) -> Result<(), RepositoryError>;
    async fn update(&self, entity: &UserSkill) -> Result<(), RepositoryError>;
    async fn delete(&self, id: &UserSkillId) -> Result<(), RepositoryError>;
}

#[automock]
#[async_trait]
pub trait UserSkillReadRepository: Send + Sync {
    async fn find_by_id(&self, id: &UserSkillId) -> Result<Option<UserSkill>, RepositoryError>;
    async fn find_all(&self) -> Result<Vec<UserSkill>, RepositoryError>;
}
