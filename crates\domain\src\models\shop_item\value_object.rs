use reforged_shared::{UuidId, Value};

use super::entity::ShopItem;

pub type ShopItemId = UuidId<ShopItem>;

#[derive(<PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>ialEq, Eq, Default)]
pub struct QuantityRemain(i32);

impl Value<i32> for QuantityRemain {
    fn value(&self) -> i32 {
        self.0
    }
}

impl QuantityRemain {
    pub fn new(quantity_remain: i32) -> Self {
        Self(quantity_remain)
    }
}
