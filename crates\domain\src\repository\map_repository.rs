use async_trait::async_trait;
use mockall::automock;

use crate::error::RepositoryError;
use crate::models::map::entity::Map;
use crate::models::map::value_object::MapId;

#[automock]
#[async_trait]
pub trait MapRepository: Send + Sync {
    async fn save(&self, entity: &Map) -> Result<(), RepositoryError>;
    async fn update(&self, entity: &Map) -> Result<(), RepositoryError>;
    async fn delete(&self, id: &MapId) -> Result<(), RepositoryError>;
}

#[automock]
#[async_trait]
pub trait MapReadRepository: Send + Sync {
    async fn find_by_id(&self, id: &MapId) -> Result<Option<Map>, RepositoryError>;
    async fn find_all(&self) -> Result<Vec<Map>, RepositoryError>;
}
