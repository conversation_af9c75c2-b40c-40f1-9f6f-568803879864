use super::value_object::{GameSettingId, GameSettingName, Location, Value};
use getset::{<PERSON><PERSON>, Set<PERSON>};

#[derive(<PERSON>bu<PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, bon::<PERSON><PERSON><PERSON>, <PERSON><PERSON>, Setters)]
#[getset(get = "pub", set = "pub")]
pub struct GameSetting {
    id: GameSettingId,
    name: GameSettingName,
    value: Value,
    location: Location,
}

impl GameSetting {
    pub fn new(id: GameSettingId, name: GameSettingName, value: Value, location: Location) -> Self {
        Self {
            id,
            name,
            value,
            location,
        }
    }
}

#[cfg(test)]
mod tests {
    use reforged_shared::{IdTrait, Value as SharedValue};

    use super::*;

    #[test]
    fn test_game_setting_new() {
        let id = GameSettingId::new(uuid::Uuid::now_v7());
        let name = GameSettingName::new("MAX_LEVEL");
        let value = Value::new("100");
        let location = Location::Game;

        let game_setting =
            GameSetting::new(id.clone(), name.clone(), value.clone(), location.clone());

        assert_eq!(game_setting.id().get_id(), id.get_id());
        assert_eq!(game_setting.name().value(), name.value());
        assert_eq!(game_setting.value().value(), value.value());
        assert_eq!(game_setting.location().value(), location.value());
    }

    #[test]
    fn test_game_setting_builder() {
        let id = GameSettingId::new(uuid::Uuid::now_v7());
        let name = GameSettingName::new("MAX_LEVEL");
        let value = Value::new("100");
        let location = Location::Game;

        let game_setting = GameSetting::builder()
            .id(id.clone())
            .name(name.clone())
            .value(value.clone())
            .location(location.clone())
            .build();

        assert_eq!(game_setting.name().value(), name.value());
        assert_eq!(game_setting.value().value(), value.value());
        assert_eq!(game_setting.location().value(), location.value());
    }

    #[test]
    fn test_getters_and_setters() {
        let mut game_setting = GameSetting::default();

        let name = GameSettingName::new("MAX_LEVEL");
        let value = Value::new("100");
        let location = Location::Game;

        game_setting.set_name(name.clone());
        game_setting.set_value(value.clone());
        game_setting.set_location(location.clone());

        assert_eq!(game_setting.name().value(), name.value());
        assert_eq!(game_setting.value().value(), value.value());
        assert_eq!(game_setting.location().value(), location.value());
    }
}
