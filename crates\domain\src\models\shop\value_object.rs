use reforged_shared::{UuidId, Value};

use super::entity::Shop;

pub type ShopId = UuidId<Shop>;

#[derive(Debug, Clone, PartialEq, Eq, Default)]
pub struct ShopName(String);

impl Value<String> for ShopName {
    fn value(&self) -> String {
        self.0.clone()
    }
}

impl ShopName {
    pub fn new<T: Into<String>>(name: T) -> Self {
        Self(name.into())
    }
}

#[derive(Debug, Clone, PartialEq, Eq, Default)]
pub struct ShopHouse(bool);

impl Value<bool> for ShopHouse {
    fn value(&self) -> bool {
        self.0
    }
}

impl ShopHouse {
    pub fn new(house: bool) -> Self {
        Self(house)
    }
}

#[derive(Debug, Clone, PartialEq, Eq, Default)]
pub struct ShopUpgrade(bool);

impl Value<bool> for ShopUpgrade {
    fn value(&self) -> bool {
        self.0
    }
}

impl ShopUpgrade {
    pub fn new(upgrade: bool) -> Self {
        Self(upgrade)
    }
}

#[derive(Debug, <PERSON><PERSON>, <PERSON>ialEq, Eq, Default)]
pub struct ShopStaff(bool);

impl Value<bool> for ShopStaff {
    fn value(&self) -> bool {
        self.0
    }
}

impl ShopStaff {
    pub fn new(staff: bool) -> Self {
        Self(staff)
    }
}

#[derive(Debug, Clone, PartialEq, Eq, Default)]
pub struct ShopLimited(bool);

impl Value<bool> for ShopLimited {
    fn value(&self) -> bool {
        self.0
    }
}

impl ShopLimited {
    pub fn new(limited: bool) -> Self {
        Self(limited)
    }
}

#[derive(Debug, Clone, PartialEq, Eq, Default)]
pub struct ShopField(String);

impl Value<String> for ShopField {
    fn value(&self) -> String {
        self.0.clone()
    }
}

impl ShopField {
    pub fn new<T: Into<String>>(field: T) -> Self {
        Self(field.into())
    }
}
