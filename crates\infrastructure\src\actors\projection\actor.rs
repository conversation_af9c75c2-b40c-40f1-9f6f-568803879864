use std::sync::Arc;

use crate::actors::message_broker::message::Publish;
use actix::{Actor, Addr};
use actix_broker::BrokerSubscribe;

use reforged_domain::{
    events::user_events::{ResetPasswordRequested, UserCreated, UserPasswordChanged},
    repository::{
        password_reset_token_repository::PasswordResetTokenRepository, // Added
        user_repository::UserRepository,
    },
    traits::password_hasher::PasswordHasher,
};
use tracing_log::log::info;

pub type ProjectionActorAddr = Addr<ProjectionActor>;

pub struct ProjectionActor {
    user_repository: Arc<dyn UserRepository>,
    password_hasher: Arc<dyn PasswordHasher>,
    password_reset_token_repository: Arc<dyn PasswordResetTokenRepository>, // Added
}

impl ProjectionActor {
    pub fn new(
        user_repository: Arc<dyn UserRepository>,
        password_hasher: Arc<dyn PasswordHasher>,
        password_reset_token_repository: Arc<dyn PasswordResetTokenRepository>, // Added
    ) -> Self {
        Self {
            user_repository,
            password_hasher,
            password_reset_token_repository, // Added
        }
    }

    pub fn user_repository(&self) -> &Arc<dyn UserRepository> {
        &self.user_repository
    }

    pub fn password_hasher(&self) -> &Arc<dyn PasswordHasher> {
        &self.password_hasher
    }

    pub fn password_reset_token_repository(&self) -> &Arc<dyn PasswordResetTokenRepository> {
        // Added
        &self.password_reset_token_repository
    }
}

impl Actor for ProjectionActor {
    type Context = actix::Context<Self>;

    fn started(&mut self, ctx: &mut Self::Context) {
        info!("ProjectionActor actor started");
        self.subscribe_system_async::<Publish<UserCreated>>(ctx);
        self.subscribe_system_async::<Publish<ResetPasswordRequested>>(ctx);
        self.subscribe_system_async::<Publish<UserPasswordChanged>>(ctx);
    }

    fn stopped(&mut self, _ctx: &mut Self::Context) {
        info!("ProjectionActor actor stopped");
    }
}
