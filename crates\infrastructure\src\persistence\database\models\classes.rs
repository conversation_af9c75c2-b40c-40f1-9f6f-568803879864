//! `SeaORM` Entity, @generated by sea-orm-codegen 1.1.0

use sea_orm::entity::prelude::*;
use serde::Deserialize;

#[derive(<PERSON><PERSON>, Debug, PartialEq, DeriveEntityModel, Eq, Deserialize)]
#[sea_orm(table_name = "classes")]
pub struct Model {
    #[sea_orm(primary_key, auto_increment = false, unique)]
    pub id: Uuid,
    pub name: String,
    #[sea_orm(primary_key, auto_increment = false)]
    pub category: String,
    #[sea_orm(column_type = "Text")]
    pub description: String,
    #[sea_orm(column_type = "Text")]
    pub mana_regeneration_methods: String,
    #[sea_orm(column_type = "Text")]
    pub stats_description: String,
}

#[derive(<PERSON><PERSON>, <PERSON><PERSON>, Debug, EnumIter, DeriveRelation)]
pub enum Relation {
    #[sea_orm(has_many = "super::class_skills::Entity")]
    ClassSkills,
    #[sea_orm(has_many = "super::items::Entity")]
    Items,
}

impl Related<super::class_skills::Entity> for Entity {
    fn to() -> RelationDef {
        Relation::ClassSkills.def()
    }
}

impl Related<super::items::Entity> for Entity {
    fn to() -> RelationDef {
        Relation::Items.def()
    }
}

impl ActiveModelBehavior for ActiveModel {}
