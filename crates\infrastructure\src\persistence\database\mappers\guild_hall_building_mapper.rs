use reforged_domain::models::guild_hall::value_object::GuildHallId;
use reforged_domain::models::guild_hall_building::value_object::{Size, Slot};
use reforged_domain::models::guild_hall_building::{
    entity::GuildHallBuilding, value_object::GuildHallBuildingId,
};
use reforged_domain::models::item::value_object::ItemId;

#[derive(Debug)]
pub struct GuildHallBuildingDbModelMapper(super::super::models::guild_hall_buildings::Model);

impl GuildHallBuildingDbModelMapper {
    pub fn new(guild_hall_building: super::super::models::guild_hall_buildings::Model) -> Self {
        Self(guild_hall_building)
    }
}

impl From<GuildHallBuildingDbModelMapper> for GuildHallBuilding {
    fn from(value: GuildHallBuildingDbModelMapper) -> Self {
        let guild_hall_building_model = value.0;

        let id = GuildHallBuildingId::new(guild_hall_building_model.id);

        GuildHallBuilding::builder()
            .id(id)
            .hall_id(GuildHallId::new(guild_hall_building_model.hall_id))
            .item_id(ItemId::new(guild_hall_building_model.item_id))
            .slot(Slot::new(guild_hall_building_model.slot))
            .size(Size::new(guild_hall_building_model.size))
            .build()
    }
}
