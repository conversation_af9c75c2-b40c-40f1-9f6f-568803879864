use async_trait::async_trait;
use mockall::automock;

use crate::error::RepositoryError;
use crate::models::quest_location::entity::QuestLocation;
use crate::models::quest_location::value_object::QuestLocationId;

#[automock]
#[async_trait]
pub trait QuestLocationRepository: Send + Sync {
    async fn save(&self, entity: &QuestLocation) -> Result<(), RepositoryError>;
    async fn update(&self, entity: &QuestLocation) -> Result<(), RepositoryError>;
    async fn delete(&self, id: &QuestLocationId) -> Result<(), RepositoryError>;
}

#[automock]
#[async_trait]
pub trait QuestLocationReadRepository: Send + Sync {
    async fn find_by_id(
        &self,
        id: &QuestLocationId,
    ) -> Result<Option<QuestLocation>, RepositoryError>;
    async fn find_all(&self) -> Result<Vec<QuestLocation>, RepositoryError>;
}
