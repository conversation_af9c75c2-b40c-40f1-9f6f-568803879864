use async_trait::async_trait;
use mockall::automock;

use crate::error::RepositoryError;
use crate::models::user_faction::entity::UserFaction;
use crate::models::user_faction::value_object::UserFactionId;

#[automock]
#[async_trait]
pub trait UserFactionRepository: Send + Sync {
    async fn save(&self, entity: &UserFaction) -> Result<(), RepositoryError>;
    async fn update(&self, entity: &UserFaction) -> Result<(), RepositoryError>;
    async fn delete(&self, id: &UserFactionId) -> Result<(), RepositoryError>;
}

#[automock]
#[async_trait]
pub trait UserFactionReadRepository: Send + Sync {
    async fn find_by_id(&self, id: &UserFactionId) -> Result<Option<UserFaction>, RepositoryError>;
    async fn find_all(&self) -> Result<Vec<UserFaction>, RepositoryError>;
}
