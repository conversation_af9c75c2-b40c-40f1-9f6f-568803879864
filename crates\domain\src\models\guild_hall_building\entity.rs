use super::value_object::{GuildHallBuildingId, Size, Slot};
use crate::models::{guild_hall::value_object::GuildHallId, item::value_object::ItemId};
use getset::{<PERSON><PERSON>, Set<PERSON>};

#[derive(<PERSON>bu<PERSON>, <PERSON><PERSON>, De<PERSON>ult, bon::<PERSON><PERSON><PERSON>, <PERSON><PERSON>, Setters)]
#[getset(get = "pub", set = "pub")]
pub struct GuildHallBuilding {
    id: GuildHallBuildingId,
    hall_id: GuildHallId,
    item_id: ItemId,
    slot: Slot,
    size: Size,
}

impl GuildHallBuilding {
    pub fn new(
        id: GuildHallBuildingId,
        hall_id: GuildHallId,
        item_id: ItemId,
        slot: Slot,
        size: Size,
    ) -> Self {
        Self {
            id,
            hall_id,
            item_id,
            slot,
            size,
        }
    }
}

#[cfg(test)]
mod tests {
    use reforged_shared::{IdTrait, Value};

    use super::*;

    #[test]
    fn test_guild_hall_building_new() {
        let id = GuildHallBuildingId::new(uuid::Uuid::now_v7());
        let hall_id = GuildHallId::new(uuid::Uuid::now_v7());
        let item_id = ItemId::new(uuid::Uuid::now_v7());
        let slot = Slot::new(4);
        let size = Size::new(5);

        let building = GuildHallBuilding::new(
            id.clone(),
            hall_id.clone(),
            item_id.clone(),
            slot.clone(),
            size.clone(),
        );

        assert_eq!(building.id().get_id(), id.get_id());
        assert_eq!(building.hall_id().get_id(), hall_id.get_id());
        assert_eq!(building.item_id().get_id(), item_id.get_id());
        assert_eq!(building.slot().value(), slot.value());
        assert_eq!(building.size().value(), size.value());
    }

    #[test]
    fn test_guild_hall_building_builder() {
        let id = GuildHallBuildingId::new(uuid::Uuid::now_v7());
        let hall_id = GuildHallId::new(uuid::Uuid::now_v7());
        let item_id = ItemId::new(uuid::Uuid::now_v7());
        let slot = Slot::new(4);
        let size = Size::new(5);

        let building = GuildHallBuilding::builder()
            .id(id.clone())
            .hall_id(hall_id.clone())
            .item_id(item_id.clone())
            .slot(slot.clone())
            .size(size.clone())
            .build();

        assert_eq!(building.id().get_id(), id.get_id());
        assert_eq!(building.hall_id().get_id(), hall_id.get_id());
        assert_eq!(building.item_id().get_id(), item_id.get_id());
        assert_eq!(building.slot().value(), slot.value());
        assert_eq!(building.size().value(), size.value());
    }

    #[test]
    fn test_getters_and_setters() {
        let mut building = GuildHallBuilding::default();

        let id = GuildHallBuildingId::new(uuid::Uuid::now_v7());
        let hall_id = GuildHallId::new(uuid::Uuid::now_v7());
        let item_id = ItemId::new(uuid::Uuid::now_v7());
        let slot = Slot::new(4);
        let size = Size::new(5);

        building.set_id(id.clone());
        building.set_hall_id(hall_id.clone());
        building.set_item_id(item_id.clone());
        building.set_slot(slot.clone());
        building.set_size(size.clone());

        assert_eq!(building.id().get_id(), id.get_id());
        assert_eq!(building.hall_id().get_id(), hall_id.get_id());
        assert_eq!(building.item_id().get_id(), item_id.get_id());
        assert_eq!(building.slot().value(), slot.value());
        assert_eq!(building.size().value(), size.value());
    }
}
