use async_trait::async_trait;
use mockall::automock;

use crate::error::RepositoryError;
use crate::models::shop::entity::Shop;
use crate::models::shop::value_object::ShopId;

#[automock]
#[async_trait]
pub trait ShopRepository: Send + Sync {
    async fn save(&self, entity: &Shop) -> Result<(), RepositoryError>;
    async fn update(&self, entity: &Shop) -> Result<(), RepositoryError>;
    async fn delete(&self, id: &ShopId) -> Result<(), RepositoryError>;
}

#[automock]
#[async_trait]
pub trait ShopReadRepository: Send + Sync {
    async fn find_by_id(&self, id: &ShopId) -> Result<Option<Shop>, RepositoryError>;
    async fn find_all(&self) -> Result<Vec<Shop>, RepositoryError>;
}
