use reforged_domain::models::book_quest::{entity::BookQuest, value_object::BookQuestId};

#[derive(Debug)]
pub struct BookQuestDbModelMapper(super::super::models::book_quests::Model);

impl BookQuestDbModelMapper {
    pub fn new(book_quest: super::super::models::book_quests::Model) -> Self {
        Self(book_quest)
    }
}

impl From<BookQuestDbModelMapper> for BookQuest {
    fn from(value: BookQuestDbModelMapper) -> Self {
        let book_quest_model = value.0;

        let id = BookQuestId::new(book_quest_model.id);

        BookQuest::builder()
            .id(id)
            .name(book_quest_model.name.into())
            .field(book_quest_model.field.into())
            .lock(book_quest_model.lock.into())
            .map(book_quest_model.map.into())
            .quest_type(book_quest_model.r#type.into())
            .hide(book_quest_model.hide.into())
            .index(book_quest_model.index.into())
            .value(book_quest_model.value.into())
            .build()
    }
}
