use async_trait::async_trait;
use mockall::automock;

use crate::error::RepositoryError;
use crate::models::map_cell::entity::MapCell;
use crate::models::map_cell::value_object::MapCellId;

#[automock]
#[async_trait]
pub trait MapCellRepository: Send + Sync {
    async fn save(&self, entity: &MapCell) -> Result<(), RepositoryError>;
    async fn update(&self, entity: &MapCell) -> Result<(), RepositoryError>;
    async fn delete(&self, id: &MapCellId) -> Result<(), RepositoryError>;
}

#[automock]
#[async_trait]
pub trait MapCellReadRepository: Send + Sync {
    async fn find_by_id(&self, id: &MapCellId) -> Result<Option<MapCell>, RepositoryError>;
    async fn find_all(&self) -> Result<Vec<MapCell>, RepositoryError>;
}
