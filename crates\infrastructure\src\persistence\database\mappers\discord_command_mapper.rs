use reforged_domain::models::discord_command::{
    entity::DiscordCommand, value_object::DiscordCommandId,
};

#[derive(Debug)]
pub struct DiscordCommandDbModelMapper(super::super::models::discord_commands::Model);

impl DiscordCommandDbModelMapper {
    pub fn new(discord_command: super::super::models::discord_commands::Model) -> Self {
        Self(discord_command)
    }
}

impl From<DiscordCommandDbModelMapper> for DiscordCommand {
    fn from(value: DiscordCommandDbModelMapper) -> Self {
        let discord_command_model = value.0;

        let id = DiscordCommandId::new(discord_command_model.id);

        DiscordCommand::builder()
            .id(id)
            .command(discord_command_model.command.into())
            .file(discord_command_model.file.into())
            .build()
    }
}
