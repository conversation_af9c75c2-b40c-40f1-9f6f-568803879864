use actix_web::{
    <PERSON><PERSON>,
    web::{self, <PERSON>, <PERSON><PERSON>, Path, get, post},
};
use reforged_application::{
    error::ApplicationError,
    queries::{user_queries::GetUserByIdQuery, user_query_handlers::UserResponse},
    traits::Que<PERSON><PERSON><PERSON><PERSON>,
    usecases::{
        auth::email_verification_usecase::{EmailVerificationResponse, EmailVerificationUsecase},
        auth::register_usecase::RegisterUsecase,
    },
};

use crate::{
    dtos::{user_dtos::CreateUserDTO, validate::ValidateJson},
    error::ApiError,
    responses::BaseApiResponse,
    state::ApiState,
};

pub fn create_user_service() -> Scope {
    web::scope("/users")
        .service(web::resource("").route(post().to(create_new_user_handler)))
        .service(web::resource("/{user_id}").route(get().to(get_user_by_id_handler)))
        .service(
            web::resource("/email-verification/{token}")
                .route(post().to(email_verification_handler)),
        )
}

async fn get_user_by_id_handler(
    state: Data<ApiState>,
    user_id: Path<uuid::Uuid>,
) -> Result<Json<UserResponse>, ApiError> {
    let user_id = user_id.into_inner();
    let query = GetUserByIdQuery::new(user_id);
    let user = state
        .user_query_handler()
        .handle(query)
        .await?
        .ok_or(ApiError::from(ApplicationError::UserNotFound))?;

    Ok(Json(user))
}

async fn create_new_user_handler(
    state: Data<ApiState>,
    ValidateJson(user): ValidateJson<CreateUserDTO>,
) -> Result<Json<BaseApiResponse>, ApiError> {
    let store_manager = state.user_store_manager();
    let register_usecase = RegisterUsecase::new(
        state.captcha_service().clone(),
        state.user_query_handler().clone(),
        store_manager.clone(),
    );

    register_usecase
        .execute(
            user.username,
            user.email.clone(),
            user.password,
            user.gender,
            user.captcha,
        )
        .await?;

    Ok(Json(BaseApiResponse::new(
        "User has been created successfully".to_string(),
    )))
}

async fn email_verification_handler(
    state: Data<ApiState>,
    token: Path<String>,
) -> Result<Json<EmailVerificationResponse>, ApiError> {
    let token = token.into_inner();
    let usecase = EmailVerificationUsecase::new(state.token_service().clone());

    let response = usecase.execute(token).await?;

    Ok(Json(response))
}
