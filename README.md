# ReForged

A rewrite of DeVoid project.

## PREREQUISITE

- [Rust](https://www.rust-lang.org/tools/install) (latest stable version)
- [PostgreSQL](https://www.postgresql.org/download/) (v14+)
- [RabbitMQ](https://www.rabbitmq.com/download.html) (v3.9+)
- [Just](https://github.com/casey/just) - Command runner
- [Nushell](https://www.nushell.sh/installation/) - Required for the justfile commands

## Installation Guide

1. Clone the repository:
   ```bash
   git clone https://github.com/yourusername/reforged.git
   cd reforged
   ```

2. Set up environment variables:
   ```bash
   cp .env.example .env
   ```
   Edit the `.env` file and update the configuration values for your environment.

3. Set up databases:
   - Create two PostgreSQL databases:
     - `reforged_db` (main database)
     - `reforged-event-store` (event store database)

4. Run the database migrations:
   ```bash
   cargo run --bin migrator
   ```

5. Build the project:
   ```bash
   cargo build
   ```

## Developer Guide

### Project Architecture
```
/crates/
  core/           # Core utilities and shared code
  domain/         # Domain entities, value objects, and business rules
  application/    # Application services and use cases
  infrastructure/ # Infrastructure implementations (DB, messaging, etc.)
  api/            # API endpoints and controllers
  emulator/       # Emulation tools
/src/
  bin/
    main.rs        # Primary executable
    migrate.rs     # Database migrations
/test/
  integration/     # Integration tests
  load/            # Load testing
/scripts/          # Deployment/maintenance scripts
```

### Game Server Diagram
![Diagram](./docs/images/gameserverarchitecture.png)

### Common Tasks

#### Creating a New Entity

```bash
just create-entity entity_name
```

#### Running Tests

```bash
# Run all tests
just test

# Run specific package tests
just test-domain
just test-application
just test-api
```

#### Formatting Code

```bash
# Format all code
just format-all

# Format specific packages
just format-domain
just format-application
just format-api
```

#### Running the Application

```bash
# Start the server
cargo run --bin server

# Start the message broker
cargo run --bin broker
```

### Database Migrations

To create and run database migrations:

```bash
cargo run --bin migrator
```

### Environment Configuration

The application is configured using environment variables. See `.env.example` for all available options.
