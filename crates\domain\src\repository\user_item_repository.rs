use async_trait::async_trait;
use mockall::automock;

use crate::error::RepositoryError;
use crate::models::user_item::entity::UserItem;
use crate::models::user_item::value_object::UserItemId;

#[automock]
#[async_trait]
pub trait UserItemRepository: Send + Sync {
    async fn save(&self, entity: &UserItem) -> Result<(), RepositoryError>;
    async fn update(&self, entity: &UserItem) -> Result<(), RepositoryError>;
    async fn delete(&self, id: &UserItemId) -> Result<(), RepositoryError>;
}

#[automock]
#[async_trait]
pub trait UserItemReadRepository: Send + Sync {
    async fn find_by_id(&self, id: &UserItemId) -> Result<Option<UserItem>, RepositoryError>;
    async fn find_all(&self) -> Result<Vec<UserItem>, RepositoryError>;
}
