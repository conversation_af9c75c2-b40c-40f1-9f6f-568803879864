use async_trait::async_trait;
use num_traits::ToPrimitive;
use reforged_domain::models::class_category::value_object::ClassCategoryId;
use reforged_domain::{
    error::RepositoryError, models::class_category::entity::ClassCategory,
    repository::class_category_repository::ClassCategoryRepository,
};
use reforged_shared::{IdTrait, Value};
use sea_orm::{ActiveModelTrait, ActiveValue::Set, DatabaseConnection};
use sea_orm::{ActiveValue, EntityTrait};

use crate::SeaORMErr;
use crate::mappers::class_category_mapper::ClassCategoryDbModelMapper;

use crate::models::class_categories::ActiveModel as ClassCategoriesActiveModel;
use crate::models::class_categories::Entity as ClassCategories;

#[allow(dead_code)]
pub struct PostgresClassCategoryRepository {
    pool: DatabaseConnection,
}

impl PostgresClassCategoryRepository {
    pub fn new(pool: DatabaseConnection) -> Self {
        Self { pool }
    }
}

#[async_trait]
impl ClassCategoryRepository for PostgresClassCategoryRepository {
    async fn save(&self, category: &ClassCategory) -> Result<ClassCategory, RepositoryError> {
        let category = ClassCategoriesActiveModel {
            id: ActiveValue::NotSet,
            name: Set(category.name().value()),
            category: Set(category.category().to_string()),
            strength: Set(category.strength().value().to_i64().unwrap_or(0).into()),
            endurance: Set(category.endurance().value().to_i64().unwrap_or(0).into()),
            dexterity: Set(category.dexterity().value().to_i64().unwrap_or(0).into()),
            intellect: Set(category.intellect().value().to_i64().unwrap_or(0).into()),
            wisdom: Set(category.wisdom().value().to_i64().unwrap_or(0).into()),
            luck: Set(category.luck().value().to_i64().unwrap_or(0).into()),
        };

        let model = category.insert(&self.pool).await.map_err(SeaORMErr::from)?;

        let mapped_class_category = ClassCategoryDbModelMapper::new(model);
        let class_category = ClassCategory::from(mapped_class_category);

        Ok(class_category)
    }

    async fn delete(&self, category_id: &ClassCategoryId) -> Result<(), RepositoryError> {
        let model = ClassCategoriesActiveModel {
            id: Set(category_id.get_id()),
            ..Default::default()
        };

        let category = ClassCategories::delete(model)
            .exec(&self.pool)
            .await
            .map_err(SeaORMErr::from)?;

        if category.rows_affected == 0 {
            return Err(RepositoryError::NotFound(format!(
                "with id {}",
                category_id
            )));
        }

        Ok(())
    }
}
