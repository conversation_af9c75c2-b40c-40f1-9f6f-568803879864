use reforged_domain::models::class::value_object::ClassId;
use reforged_domain::models::class_skill::{entity::ClassSkill, value_object::ClassSkillId};
use reforged_domain::models::skill::value_object::SkillId;

#[derive(Debug)]
pub struct ClassSkillDbModelMapper(super::super::models::class_skills::Model);

impl ClassSkillDbModelMapper {
    pub fn new(class_skill: super::super::models::class_skills::Model) -> Self {
        Self(class_skill)
    }
}

impl From<ClassSkillDbModelMapper> for ClassSkill {
    fn from(value: ClassSkillDbModelMapper) -> Self {
        let class_skill_model = value.0;

        let id = ClassSkillId::new(class_skill_model.id);

        ClassSkill::builder()
            .id(id)
            .class_id(ClassId::new(class_skill_model.class_id))
            .skill_id(SkillId::new(class_skill_model.skill_id))
            .build()
    }
}
