[package]
name = "reforged"
version = "0.1.0"
edition = "2024"

[workspace]
resolver = "2"
members = ["crates/*", "tests/integration"]

[workspace.dependencies]
actix = "0.13.5"
actix-broker = "0.4.3"
actix-cors = "0.7.1"
actix-governor = "0.8.0"
actix-web = "4"
anyhow = "1.0.97"
argon2 = "0.5.3"
async-trait = "0.1.88"
bon = "3.0.0"
cf-turnstile = "=0.2.0"
chrono = { version = "0.4.40", features = ["serde"] }
dotenvy = "0.15.7"
futures = "0.3.30"
futures-util = "0.3.31"
getset = "0.1.5"
lapin = "2.5.3"
mail-send = "0.5.0"
mockall = "0.13.1"
num-traits = "0.2.19"
rand = "0.9.0"
rustls = "0.23.26"
serde_json = "1.0.140"
thiserror = "2.0.12"
time = "0.3.41"
tokio-rustls = "^0.26.2"
tower = "0.5.2"
tracing = "0.1.41"
tracing-log = "0.2.0"
tracing-subscriber = "0.3.19"

[workspace.dependencies.esrs]
version = "0.18.0"
features = ["postgres"]

[workspace.dependencies.pasetors]
version = "0.7.4"
features = ["v4"]

[workspace.dependencies.sea-orm]
version = "1.1.8"
features = [
    "sqlx-postgres",
    "runtime-tokio-rustls",
    "macros",
    "with-time",
    "with-bigdecimal",
]

[workspace.dependencies.sea-orm-migration]
version = "1.1.8"
features = ["runtime-tokio-rustls", "sqlx-postgres"]

[workspace.dependencies.serde]
version = "1.0.219"
features = ["derive"]

[workspace.dependencies.tokio]
version = "1.44.1"
features = ["full"]

[workspace.dependencies.tower-http]
version = "0.6.2"
features = ["trace"]

[workspace.dependencies.uuid]
version = "1.16.0"
# Lets you generate random UUIDs
features = ["v4", "v7", "serde"]

[workspace.dependencies.validator]
version = "0.20.0"
features = ["derive"]

[[bin]]
name = "reforged"
path = "src/bin/main.rs"

[[bin]]
name = "migrator"
path = "src/bin/migrator.rs"

[[bin]]
name = "server"
path = "src/bin/server.rs"

[[bin]]
name = "emulator"
path = "src/bin/emulator.rs"

[[bin]]
name = "notification"
path = "src/bin/notification.rs"

[[bin]]
name = "persistence"
path = "src/bin/persistence.rs"

[dependencies]
actix.workspace = true
actix-web.workspace = true
anyhow.workspace = true
esrs.workspace = true
lapin.workspace = true
mail-send.workspace = true
reforged-api = { path = "crates/api" }
reforged-application = { path = "crates/application" }
reforged-infrastructure = { path = "crates/infrastructure" }
rustls.workspace = true

tokio.workspace = true
tokio-rustls.workspace = true
tracing.workspace = true
tracing-log.workspace = true
tracing-subscriber.workspace = true

[profile.dev.package.tokio]
unstable-features = true

[profile.release.package.tokio]
unstable-features = true
