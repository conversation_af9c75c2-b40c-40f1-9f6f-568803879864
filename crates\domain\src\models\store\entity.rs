use getset::{<PERSON><PERSON>, Set<PERSON>};

use crate::models::{role::value_object::RoleId, title::value_object::TitleId};

use super::value_object::*;

#[derive(Debug, <PERSON><PERSON>, Default, bon::<PERSON><PERSON><PERSON>, Get<PERSON>, Setters)]
#[getset(get = "pub", set = "pub")]
pub struct Store {
    id: StoreId,
    available: Available,
    name: StoreName,
    price: Price,
    item: Item,
    achievement: Achievement,
    gold: StoreGold,
    coins: StoreCoins,
    crystal: StoreCrystal,
    diamonds: StoreDiamonds,
    upgrade: Upgrade,
    role_id: Option<RoleId>,
    title_id: Option<TitleId>,
    bag_slots: BagSlots,
    bank_slots: BankSlots,
    house_slots: HouseSlots,
    store_type: Option<StoreType>,
    quantity: Quantity,
    img: Img,
}

impl Store {
    #[allow(clippy::too_many_arguments)]
    pub fn new(
        id: StoreId,
        available: Available,
        name: StoreName,
        price: Price,
        item: Item,
        achievement: Achievement,
        gold: StoreGold,
        coins: StoreCoins,
        crystal: StoreCrystal,
        diamonds: StoreDiamonds,
        upgrade: Upgrade,
        role_id: Option<RoleId>,
        title_id: Option<TitleId>,
        bag_slots: BagSlots,
        bank_slots: BankSlots,
        house_slots: HouseSlots,
        store_type: Option<StoreType>,
        quantity: Quantity,
        img: Img,
    ) -> Self {
        Self {
            id,
            available,
            name,
            price,
            item,
            achievement,
            gold,
            coins,
            crystal,
            diamonds,
            upgrade,
            role_id,
            title_id,
            bag_slots,
            bank_slots,
            house_slots,
            store_type,
            quantity,
            img,
        }
    }
}

#[cfg(test)]
mod tests {
    use reforged_shared::{IdTrait, Value};

    use super::*;

    #[test]
    fn test_store_new() {
        let id = StoreId::new(uuid::Uuid::now_v7());
        let available = Available::new(1);
        let name = StoreName::new("Test Store");
        let price = Price::new(9.99);
        let item = Item::new(1);
        let achievement = Achievement::new(None);
        let gold = StoreGold::new(1000);
        let coins = StoreCoins::new(500);
        let crystal = StoreCrystal::new(100);
        let diamonds = StoreDiamonds::new(50);
        let upgrade = Upgrade::new(0);
        let role_id = None;
        let title_id = None;
        let bag_slots = BagSlots::new(10);
        let bank_slots = BankSlots::new(20);
        let house_slots = HouseSlots::new(5);
        let store_type = Some(StoreType::Package);
        let quantity = Quantity::new(1);
        let img = Img::new(Some("test.png".to_string()));

        let store = Store::new(
            id.clone(),
            available.clone(),
            name.clone(),
            price.clone(),
            item.clone(),
            achievement.clone(),
            gold.clone(),
            coins.clone(),
            crystal.clone(),
            diamonds.clone(),
            upgrade.clone(),
            role_id.clone(),
            title_id.clone(),
            bag_slots.clone(),
            bank_slots.clone(),
            house_slots.clone(),
            store_type.clone(),
            quantity.clone(),
            img.clone(),
        );

        assert_eq!(store.id().get_id(), id.get_id());
        assert_eq!(store.name().value(), "Test Store");
        assert_eq!(store.price().value(), 9.99);
        assert_eq!(store.gold().value(), 1000);
    }
}