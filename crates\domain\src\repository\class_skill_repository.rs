use async_trait::async_trait;
use mockall::automock;

use crate::error::RepositoryError;
use crate::models::class_skill::entity::ClassSkill;
use crate::models::class_skill::value_object::ClassSkillId;

#[automock]
#[async_trait]
pub trait ClassSkillRepository: Send + Sync {
    async fn save(&self, entity: &ClassSkill) -> Result<(), RepositoryError>;
    async fn update(&self, entity: &ClassSkill) -> Result<(), RepositoryError>;
    async fn delete(&self, id: &ClassSkillId) -> Result<(), RepositoryError>;
}

#[automock]
#[async_trait]
pub trait ClassSkillReadRepository: Send + Sync {
    async fn find_by_id(&self, id: &ClassSkillId) -> Result<Option<ClassSkill>, RepositoryError>;
    async fn find_all(&self) -> Result<Vec<ClassSkill>, RepositoryError>;
}
