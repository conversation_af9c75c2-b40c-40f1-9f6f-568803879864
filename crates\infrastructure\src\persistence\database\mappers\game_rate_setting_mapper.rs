use num_traits::ToPrimitive;
use reforged_domain::models::game_rate_setting::{
    entity::GameRateSetting,
    value_object::{GameRateSettingId, Value},
};

#[derive(Debug)]
pub struct GameRateSettingDbModelMapper(super::super::models::game_rates_settings::Model);

impl GameRateSettingDbModelMapper {
    pub fn new(game_rate_setting: super::super::models::game_rates_settings::Model) -> Self {
        Self(game_rate_setting)
    }
}

impl From<GameRateSettingDbModelMapper> for GameRateSetting {
    fn from(value: GameRateSettingDbModelMapper) -> Self {
        let game_rate_setting_model = value.0;

        let id = GameRateSettingId::new(game_rate_setting_model.id);

        GameRateSetting::builder()
            .id(id)
            .name(game_rate_setting_model.name.into())
            .value(Value::new(game_rate_setting_model.value.to_f64().unwrap()))
            .build()
    }
}
