use std::sync::Arc;

use reforged_domain::{
    models::class_category::{
        entity::ClassCategory,
        value_object::{
            ClassCategoryEnum, ClassCategoryId, ClassCategoryName, Dexterity, Endurance, Intellect,
            Luck, Strength, Wisdom,
        },
    },
    repository::class_category_repository::ClassCategoryRepository,
};
use uuid::NoContext;

use crate::{
    error::ApplicationError,
    queries::{
        class_category_queries::GetClassCategoryByNameQuery,
        class_category_query_handlers::ClassCategoryQueryHandler,
    },
    traits::Query<PERSON>and<PERSON>,
};

pub struct CreateClassCategoryUsecase {
    class_category_query_handler: ClassCategoryQueryHandler,
    class_category_repository: Arc<dyn ClassCategoryRepository>,
}

impl CreateClassCategoryUsecase {
    pub fn new(
        class_category_query_handler: ClassCategoryQueryHandler,
        class_category_repository: Arc<dyn ClassCategoryRepository>,
    ) -> Self {
        Self {
            class_category_query_handler,
            class_category_repository,
        }
    }
}

impl CreateClassCategoryUsecase {
    pub async fn execute(
        &self,
        name: String,
        category: String,
        strength: f64,
        endurance: f64,
        dexterity: f64,
        intellect: f64,
        wisdom: f64,
        luck: f64,
    ) -> Result<ClassCategory, ApplicationError> {
        let query = GetClassCategoryByNameQuery { name: name.clone() };
        let class_category = self.class_category_query_handler.handle(query).await;
        if class_category.is_ok() {
            return Err(ApplicationError::EntityAlreadyExists(name.clone()));
        }

        let class_category = ClassCategoryEnum::try_from(category.clone())
            .map_err(|e| ApplicationError::InvalidVariant(e.to_string()))?;

        let new_class_category = ClassCategory::new(
            ClassCategoryId::new(uuid::Uuid::new_v7(uuid::Timestamp::from_unix(
                NoContext, 1, 0,
            ))),
            ClassCategoryName::new(name),
            class_category,
            Strength::new(strength),
            Endurance::new(endurance),
            Dexterity::new(dexterity),
            Intellect::new(intellect),
            Wisdom::new(wisdom),
            Luck::new(luck),
        );

        let class_category = self
            .class_category_repository
            .save(&new_class_category)
            .await?;

        Ok(class_category)
    }
}
