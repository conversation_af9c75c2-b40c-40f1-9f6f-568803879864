use std::sync::Arc;

use async_trait::async_trait;
use reforged_domain::{
    models::class_category::{entity::ClassCategory, value_object::ClassCategoryId},
    repository::class_category_repository::ClassCategoryReadRepository,
};
use reforged_shared::{IdTrait, Value};
use serde::Serialize;

use crate::{error::ApplicationError, traits::QueryHandler};

use super::class_category_queries::{
    GetClassCategoryByIdQuery, GetClassCategoryByNameQuery, ListAllClassCategoriesQuery,
    ListClassCategoryPaginatedQuery,
};

#[derive(Clone)]
pub struct ClassCategoryQueryHandler {
    class_category_repo: Arc<dyn ClassCategoryReadRepository>,
}

impl ClassCategoryQueryHandler {
    pub fn new(class_category_repo: Arc<dyn ClassCategoryReadRepository>) -> Self {
        Self {
            class_category_repo,
        }
    }
}

#[async_trait]
impl QueryHandler<GetClassCategoryByIdQuery, Option<ClassCategoryResponse>>
    for ClassCategoryQueryHandler
{
    async fn handle(
        &self,
        query: GetClassCategoryByIdQuery,
    ) -> Result<Option<ClassCategoryResponse>, ApplicationError> {
        let class_category_repo = self.class_category_repo.clone();
        let id = ClassCategoryId::new(query.id);
        let class_category = class_category_repo.find_by_id(&id).await?;

        let class_category = class_category.map(ClassCategoryResponse::from);
        Ok(class_category)
    }
}

#[async_trait]
impl QueryHandler<GetClassCategoryByNameQuery, Option<ClassCategoryResponse>>
    for ClassCategoryQueryHandler
{
    async fn handle(
        &self,
        query: GetClassCategoryByNameQuery,
    ) -> Result<Option<ClassCategoryResponse>, ApplicationError> {
        let class_category_repo = self.class_category_repo.clone();
        let class_category = class_category_repo.find_by_name(&query.name).await?;

        let class_category = class_category.map(ClassCategoryResponse::from);
        Ok(class_category)
    }
}

#[async_trait]
impl QueryHandler<ListAllClassCategoriesQuery, Vec<ClassCategoryResponse>>
    for ClassCategoryQueryHandler
{
    async fn handle(
        &self,
        _query: ListAllClassCategoriesQuery,
    ) -> Result<Vec<ClassCategoryResponse>, ApplicationError> {
        let class_category_repo = self.class_category_repo.clone();
        let class_categories = class_category_repo.find_all().await?;
        let class_categories = class_categories
            .into_iter()
            .map(ClassCategoryResponse::from)
            .collect::<Vec<_>>();

        Ok(class_categories)
    }
}

#[async_trait]
impl QueryHandler<ListClassCategoryPaginatedQuery, Vec<ClassCategoryResponse>>
    for ClassCategoryQueryHandler
{
    async fn handle(
        &self,
        query: ListClassCategoryPaginatedQuery,
    ) -> Result<Vec<ClassCategoryResponse>, ApplicationError> {
        let class_category_repo = self.class_category_repo.clone();
        let class_categories = class_category_repo
            .find_paginate(query.page, query.limit)
            .await?;
        let class_categories = class_categories
            .into_iter()
            .map(ClassCategoryResponse::from)
            .collect::<Vec<_>>();

        Ok(class_categories)
    }
}

#[derive(Serialize)]
pub struct ClassCategoryResponse {
    pub id: uuid::Uuid,
    pub name: String,
    pub category: String,
    pub strength: f64,
    pub endurance: f64,
    pub dexterity: f64,
    pub intellect: f64,
    pub wisdom: f64,
    pub luck: f64,
}

impl From<ClassCategory> for ClassCategoryResponse {
    fn from(value: ClassCategory) -> Self {
        Self {
            id: value.id().get_id(),
            name: value.name().value(),
            category: value.category().to_string(),
            strength: value.strength().value(),
            endurance: value.endurance().value(),
            dexterity: value.dexterity().value(),
            intellect: value.intellect().value(),
            wisdom: value.wisdom().value(),
            luck: value.luck().value(),
        }
    }
}
