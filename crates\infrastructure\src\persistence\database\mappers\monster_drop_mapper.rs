use reforged_domain::models::monster_drop::entity::MonsterDrop;
use reforged_domain::models::monster_drop::value_object::MonsterDropId;
use reforged_domain::models::monster::value_object::MonsterId;
use reforged_domain::models::item::value_object::ItemId;
use num_traits::ToPrimitive;

#[derive(Debug)]
pub struct MonsterDropDbModelMapper(super::super::models::monster_drops::Model);

impl MonsterDropDbModelMapper {
    pub fn new(model: super::super::models::monster_drops::Model) -> Self {
        Self(model)
    }
}

impl From<MonsterDropDbModelMapper> for MonsterDrop {
    fn from(value: MonsterDropDbModelMapper) -> Self {
        let model = value.0;

        MonsterDrop::builder()
            .id(MonsterDropId::new(model.id))
            .monster_id(MonsterId::new(model.monster_id))
            .item_id(ItemId::new(model.item_id))
            .quantity((model.quantity as u32).into())
            .chance(model.chance.to_f32().unwrap_or(0.0).into())
            .build()
    }
}
