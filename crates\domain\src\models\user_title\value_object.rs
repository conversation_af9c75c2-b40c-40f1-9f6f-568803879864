use chrono::{DateTime, Utc};
use reforged_shared::{UuidId, Value};

use super::entity::UserTitle;

pub type UserTitleId = UuidId<UserTitle>;

#[derive(Debug, PartialEq, Eq, PartialOrd, Ord, Clone, Default)]

pub struct UserTitleDate(DateTime<Utc>);

impl UserTitleDate {
    pub fn new(date: DateTime<Utc>) -> Self {
        Self(date)
    }
}

impl Value<DateTime<Utc>> for UserTitleDate {
    fn value(&self) -> DateTime<Utc> {
        self.0
    }
}

impl From<DateTime<Utc>> for UserTitleDate {
    fn from(value: DateTime<Utc>) -> Self {
        Self(value)
    }
}
