use reforged_shared::{UuidId, Value};

use super::entity::Book;

pub type BookId = UuidId<Book>;

#[derive(Debug, PartialEq, Eq, PartialOrd, Ord, Clone, Default)]

pub struct BookFile(String);

impl BookFile {
    pub fn new(file: impl Into<String>) -> Self {
        Self(file.into())
    }
}

impl Value<String> for BookFile {
    fn value(&self) -> String {
        self.0.clone()
    }
}

impl From<String> for BookFile {
    fn from(value: String) -> Self {
        Self(value)
    }
}

impl From<&str> for BookFile {
    fn from(value: &str) -> Self {
        Self(value.to_string())
    }
}

#[derive(Debug, PartialEq, Eq, PartialOrd, Ord, Clone, Default)]

pub struct BookName(String);

impl BookName {
    pub fn new(name: impl Into<String>) -> Self {
        Self(name.into())
    }
}

impl Value<String> for BookName {
    fn value(&self) -> String {
        self.0.clone()
    }
}

impl From<String> for BookName {
    fn from(value: String) -> Self {
        Self(value)
    }
}

impl From<&str> for BookName {
    fn from(value: &str) -> Self {
        Self(value.to_string())
    }
}

#[derive(Debug, PartialEq, Eq, PartialOrd, Ord, Clone, Default)]

pub struct BookLinkage(String);

impl BookLinkage {
    pub fn new(linkage: impl Into<String>) -> Self {
        Self(linkage.into())
    }
}

impl Value<String> for BookLinkage {
    fn value(&self) -> String {
        self.0.clone()
    }
}

impl From<String> for BookLinkage {
    fn from(value: String) -> Self {
        Self(value)
    }
}

impl From<&str> for BookLinkage {
    fn from(value: &str) -> Self {
        Self(value.to_string())
    }
}

#[derive(Debug, PartialEq, Eq, PartialOrd, Ord, Clone, Default)]

pub struct BookLock(String);

impl BookLock {
    pub fn new(lock: impl Into<String>) -> Self {
        Self(lock.into())
    }
}

impl Value<String> for BookLock {
    fn value(&self) -> String {
        self.0.clone()
    }
}

impl From<String> for BookLock {
    fn from(value: String) -> Self {
        Self(value)
    }
}

impl From<&str> for BookLock {
    fn from(value: &str) -> Self {
        Self(value.to_string())
    }
}

#[derive(Debug, PartialEq, Eq, PartialOrd, Ord, Clone, Default)]

pub struct BookDesc(String);

impl BookDesc {
    pub fn new(desc: impl Into<String>) -> Self {
        Self(desc.into())
    }
}

impl Value<String> for BookDesc {
    fn value(&self) -> String {
        self.0.clone()
    }
}

impl From<String> for BookDesc {
    fn from(value: String) -> Self {
        Self(value)
    }
}

impl From<&str> for BookDesc {
    fn from(value: &str) -> Self {
        Self(value.to_string())
    }
}

#[derive(Debug, PartialEq, Eq, PartialOrd, Ord, Clone, Default)]

pub struct BookMap(String);

impl BookMap {
    pub fn new(map: impl Into<String>) -> Self {
        Self(map.into())
    }
}

impl Value<String> for BookMap {
    fn value(&self) -> String {
        self.0.clone()
    }
}

impl From<String> for BookMap {
    fn from(value: String) -> Self {
        Self(value)
    }
}

impl From<&str> for BookMap {
    fn from(value: &str) -> Self {
        Self(value.to_string())
    }
}

#[derive(Debug, PartialEq, Eq, PartialOrd, Ord, Clone, Default)]

pub struct BookType(String);

impl BookType {
    pub fn new(book_type: impl Into<String>) -> Self {
        Self(book_type.into())
    }
}

impl Value<String> for BookType {
    fn value(&self) -> String {
        self.0.clone()
    }
}

impl From<String> for BookType {
    fn from(value: String) -> Self {
        Self(value)
    }
}

impl From<&str> for BookType {
    fn from(value: &str) -> Self {
        Self(value.to_string())
    }
}

#[derive(Debug, PartialEq, Eq, PartialOrd, Ord, Clone, Default)]

pub struct BookHide(i32);

impl BookHide {
    pub fn new(hide: i32) -> Self {
        Self(hide)
    }
}

impl Value<i32> for BookHide {
    fn value(&self) -> i32 {
        self.0
    }
}

impl From<i32> for BookHide {
    fn from(value: i32) -> Self {
        Self(value)
    }
}

#[derive(Debug, PartialEq, Eq, PartialOrd, Ord, Clone, Default)]

pub struct BookLabel(String);

impl BookLabel {
    pub fn new(label: impl Into<String>) -> Self {
        Self(label.into())
    }
}

impl Value<String> for BookLabel {
    fn value(&self) -> String {
        self.0.clone()
    }
}

impl From<String> for BookLabel {
    fn from(value: String) -> Self {
        Self(value)
    }
}

impl From<&str> for BookLabel {
    fn from(value: &str) -> Self {
        Self(value.to_string())
    }
}

#[derive(Debug, PartialEq, Eq, PartialOrd, Ord, Clone, Default)]

pub struct BookField(String);

impl BookField {
    pub fn new(field: impl Into<String>) -> Self {
        Self(field.into())
    }
}

impl Value<String> for BookField {
    fn value(&self) -> String {
        self.0.clone()
    }
}

impl From<String> for BookField {
    fn from(value: String) -> Self {
        Self(value)
    }
}

impl From<&str> for BookField {
    fn from(value: &str) -> Self {
        Self(value.to_string())
    }
}

#[derive(Debug, PartialEq, Eq, PartialOrd, Ord, Clone, Default)]

pub struct BookIndex(i32);

impl BookIndex {
    pub fn new(index: i32) -> Self {
        Self(index)
    }
}

impl Value<i32> for BookIndex {
    fn value(&self) -> i32 {
        self.0
    }
}

impl From<i32> for BookIndex {
    fn from(value: i32) -> Self {
        Self(value)
    }
}

#[derive(Debug, PartialEq, Eq, PartialOrd, Ord, Clone, Default)]

pub struct BookValue(i32);

impl BookValue {
    pub fn new(value: i32) -> Self {
        Self(value)
    }
}

impl Value<i32> for BookValue {
    fn value(&self) -> i32 {
        self.0
    }
}

impl From<i32> for BookValue {
    fn from(value: i32) -> Self {
        Self(value)
    }
}
