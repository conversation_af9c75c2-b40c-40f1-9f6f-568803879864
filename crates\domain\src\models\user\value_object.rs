use std::fmt::Display;

use super::entity::User;
use getset::Getters;
use reforged_shared::{UuidId, Value, uuid_generator::uuid_from_secs};

pub type UserId = UuidId<User>;

#[derive(<PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, bon::<PERSON><PERSON><PERSON>, Get<PERSON>)]
#[getset(get = "pub")]
pub struct HashedPassword {
    hash: String,
    salt: String,
}

#[derive(Debug, PartialEq, De<PERSON><PERSON>, Eq, PartialOrd, Ord, Clone)]
#[repr(u8)]
pub enum UserRole {
    Banned = 0,
    #[default]
    Player = 1,
    Founder = 2,
    Support = 3,
    VIP = 64,
    Moderator = 128,
    Trainee = 192,
    Administrator = 255,
}

#[derive(Debug, PartialEq, Eq, PartialOrd, Ord, Clone, Default)]
pub struct Username(String);

#[derive(Debug, PartialEq, Eq, PartialOrd, Or<PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>)]
pub struct Email(String);

impl Username {
    pub fn new(name: impl Into<String>) -> Self {
        Self(name.into())
    }
}

impl Value<String> for Username {
    fn value(&self) -> String {
        self.0.clone()
    }
}

impl From<String> for Username {
    fn from(value: String) -> Self {
        Self(value)
    }
}

impl<'a> From<&str> for Username {
    fn from(value: &str) -> Self {
        Self(value.to_string())
    }
}

impl Email {
    pub fn new(name: impl Into<String>) -> Self {
        Self(name.into())
    }
}

impl Value<String> for Email {
    fn value(&self) -> String {
        self.0.clone()
    }
}

impl From<String> for Email {
    fn from(value: String) -> Self {
        Self(value)
    }
}

impl<'a> From<&str> for Email {
    fn from(value: &str) -> Self {
        Self(value.to_string())
    }
}

impl HashedPassword {
    pub fn new(hash: String, salt: String) -> Self {
        Self { hash, salt }
    }
}

impl Display for UserRole {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        match self {
            UserRole::Banned => write!(f, "Banned"),
            UserRole::Player => write!(f, "Player"),
            UserRole::Founder => write!(f, "Founder"),
            UserRole::Support => write!(f, "Support"),
            UserRole::VIP => write!(f, "VIP"),
            UserRole::Moderator => write!(f, "Moderator"),
            UserRole::Trainee => write!(f, "Trainee"),
            UserRole::Administrator => write!(f, "Administrator"),
        }
    }
}

impl TryFrom<String> for UserRole {
    type Error = ();

    fn try_from(value: String) -> Result<Self, Self::Error> {
        match value.to_lowercase().as_str() {
            "Banned" | "banned" => Ok(UserRole::Banned),
            "Player" | "player" => Ok(UserRole::Player),
            "Founder" | "founder" => Ok(UserRole::Founder),
            "Support" | "support" => Ok(UserRole::Support),
            "VIP" | "vip" => Ok(UserRole::VIP),
            "Moderator" | "moderator" => Ok(UserRole::Moderator),
            "Trainee" | "trainee" => Ok(UserRole::Trainee),
            "Administrator" | "administrator" => Ok(UserRole::Administrator),
            _ => Err(()),
        }
    }
}

impl From<uuid::Uuid> for UserRole {
    fn from(value: uuid::Uuid) -> Self {
        match value.to_string().as_str() {
            "0196ba87-e68a-7596-9412-8bbfc77406a3" => UserRole::Banned,
            "0196ba87-e68b-743d-9105-4fc1aea15e55" => UserRole::Player,
            "0196ba87-e68b-743d-9105-53e5a14f4519" => UserRole::Founder,
            "0196ba87-e68b-743d-9105-568adbe1a4d3" => UserRole::Support,
            "0196ba87-e68b-743d-9105-59f437b0c11e" => UserRole::VIP,
            "0196ba87-e68b-743d-9105-5fedc09f5776" => UserRole::Moderator,
            "0196ba87-e68b-743d-9105-604dacf2a520" => UserRole::Trainee,
            "0196ba87-e68b-743d-9105-666dda55590f" => UserRole::Administrator,
            _ => UserRole::Banned,
        }
    }
}

impl Into<i32> for UserRole {
    fn into(self) -> i32 {
        match self {
            UserRole::Banned => 0,
            UserRole::Player => 1,
            UserRole::Founder => 2,
            UserRole::Support => 3,
            UserRole::VIP => 64,
            UserRole::Moderator => 128,
            UserRole::Trainee => 192,
            UserRole::Administrator => 255,
        }
    }
}

impl Into<uuid::Uuid> for UserRole {
    fn into(self) -> uuid::Uuid {
        let id: i32 = self.into();
        uuid_from_secs(id as u64)
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_user_role_try_from() {
        let banned = UserRole::try_from("banned".to_string()).unwrap();
        let player = UserRole::try_from("player".to_string()).unwrap();
        let founder = UserRole::try_from("founder".to_string()).unwrap();
        let support = UserRole::try_from("support".to_string()).unwrap();
        let vip = UserRole::try_from("vip".to_string()).unwrap();
        let moderator = UserRole::try_from("moderator".to_string()).unwrap();
        let trainee = UserRole::try_from("trainee".to_string()).unwrap();
        let administrator = UserRole::try_from("administrator".to_string()).unwrap();

        assert_eq!(banned, UserRole::Banned);
        assert_eq!(player, UserRole::Player);
        assert_eq!(founder, UserRole::Founder);
        assert_eq!(support, UserRole::Support);
        assert_eq!(vip, UserRole::VIP);
        assert_eq!(moderator, UserRole::Moderator);
        assert_eq!(trainee, UserRole::Trainee);
        assert_eq!(administrator, UserRole::Administrator);
    }

    #[test]
    fn test_user_role_display() {
        let banned = UserRole::Banned;
        let player = UserRole::Player;
        let founder = UserRole::Founder;
        let support = UserRole::Support;
        let vip = UserRole::VIP;
        let moderator = UserRole::Moderator;
        let trainee = UserRole::Trainee;
        let administrator = UserRole::Administrator;
        let invalid = UserRole::try_from("invalid".to_string());

        assert_eq!(banned.to_string(), "Banned");
        assert_eq!(player.to_string(), "Player");
        assert_eq!(founder.to_string(), "Founder");
        assert_eq!(support.to_string(), "Support");
        assert_eq!(vip.to_string(), "VIP");
        assert_eq!(moderator.to_string(), "Moderator");
        assert_eq!(trainee.to_string(), "Trainee");
        assert_eq!(administrator.to_string(), "Administrator");
        assert!(invalid.is_err());
    }
}
