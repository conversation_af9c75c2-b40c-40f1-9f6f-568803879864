use reforged_domain::models::user_faction::entity::UserFaction;
use reforged_domain::models::user_faction::value_object::UserFactionId;
use reforged_domain::models::user::value_object::UserId;
use reforged_domain::models::faction::value_object::FactionId;

#[derive(Debug)]
pub struct UserFactionDbModelMapper(super::super::models::user_factions::Model);

impl UserFactionDbModelMapper {
    pub fn new(model: super::super::models::user_factions::Model) -> Self {
        Self(model)
    }
}

impl From<UserFactionDbModelMapper> for UserFaction {
    fn from(value: UserFactionDbModelMapper) -> Self {
        let model = value.0;
        
        UserFaction::builder()
            .id(UserFactionId::new(model.id))
            .user_id(UserId::new(model.user_id))
            .faction_id(FactionId::new(model.faction_id))
            .reputation(model.reputation.into())
            .build()
    }
}
