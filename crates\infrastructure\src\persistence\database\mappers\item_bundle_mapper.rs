use reforged_domain::models::item_bundle::entity::ItemBundle;
use reforged_domain::models::item_bundle::value_object::ItemBundleId;
use reforged_domain::models::item::value_object::ItemId;

#[derive(Debug)]
pub struct ItemBundleDbModelMapper(super::super::models::item_bundles::Model);

impl ItemBundleDbModelMapper {
    pub fn new(model: super::super::models::item_bundles::Model) -> Self {
        Self(model)
    }
}

impl From<ItemBundleDbModelMapper> for ItemBundle {
    fn from(value: ItemBundleDbModelMapper) -> Self {
        let model = value.0;
        
        ItemBundle::builder()
            .id(ItemBundleId::new(model.id))
            .item_id(ItemId::new(model.item_id))
            .reward_id(ItemId::new(model.reward_id))
            .quantity(model.quantity.into())
            .build()
    }
}
