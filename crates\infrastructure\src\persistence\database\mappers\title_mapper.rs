use reforged_domain::models::role::value_object::RoleId;
use reforged_domain::models::title::{
    entity::Title,
    value_object::{
        TitleColor, TitleDescription, TitleDexterity, TitleEndurance, TitleId, TitleIntellect,
        TitleLuck, TitleName, TitleStrength, TitleWisdom,
    },
};

#[derive(Debug)]
pub struct TitleDbModelMapper(super::super::models::titles::Model);

impl TitleDbModelMapper {
    pub fn new(title: super::super::models::titles::Model) -> Self {
        Self(title)
    }
}

impl From<TitleDbModelMapper> for Title {
    fn from(value: TitleDbModelMapper) -> Self {
        let title_model = value.0;

        let id = TitleId::new(title_model.id);

        Title::builder()
            .id(id)
            .name(TitleName::new(title_model.name))
            .description(TitleDescription::new(title_model.description))
            .color(TitleColor::new(title_model.color))
            .strength(TitleStrength::new(title_model.strength))
            .intellect(TitleIntellect::new(title_model.intellect))
            .endurance(TitleEndurance::new(title_model.endurance))
            .dexterity(TitleDexterity::new(title_model.dexterity))
            .wisdom(TitleWisdom::new(title_model.wisdom))
            .luck(TitleLuck::new(title_model.luck))
            .role_id(RoleId::new(title_model.role_id))
            .build()
    }
}
