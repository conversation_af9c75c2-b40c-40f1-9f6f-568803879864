use crate::models::{
    class::value_object::ClassId, enhancement::value_object::EnhancementId,
    faction::value_object::FactionId, item_rarity::value_object::ItemRarityId,
};

use super::value_object::{
    Coins, Color, Cost, Crystal, Diamonds, Dps, Element, Equipment, File, ItemDescription,
    ItemIcon, ItemId, ItemName, ItemRange, ItemType, Level, Link, Market, Meta, Quantity,
    QuestStringIndex, QuestStringValue, ReqClassPoints, ReqQuests, ReqReputation, Sell, Stack,
    Staff, Temporary, Upgrade,
};

use getset::{Get<PERSON>, Setters};

#[derive(Debug, <PERSON>lone, Default, bon::<PERSON><PERSON><PERSON>, Get<PERSON>, Setters)]
#[getset(get = "pub", set = "pub")]
pub struct Item {
    id: ItemId,
    class_id: Option<ClassId>,
    name: ItemName,
    description: ItemDescription,
    item_type: ItemType,
    element: Element,
    file: File,
    link: Link,
    icon: ItemIcon,
    equipment: Equipment,
    level: Level,
    dps: Dps,
    item_range: ItemRange,
    rarity_id: ItemRarityId,
    quantity: Quantity,
    stack: Stack,
    cost: Cost,
    coins: Coins,
    diamonds: Diamonds,
    crystal: Crystal,
    sell: Sell,
    market: Market,
    temporary: Temporary,
    upgrade: Upgrade,
    staff: Staff,
    enh_id: Option<EnhancementId>,
    faction_id: Option<FactionId>,
    req_reputation: ReqReputation,
    req_class_id: Option<ClassId>,
    req_class_points: ReqClassPoints,
    req_quests: ReqQuests,
    quest_string_index: QuestStringIndex,
    quest_string_value: QuestStringValue,
    meta: Meta,
    color: Color,
}

impl Item {
    #[allow(clippy::too_many_arguments)]
    pub fn new(
        id: ItemId,
        class_id: Option<ClassId>,
        name: ItemName,
        description: ItemDescription,
        item_type: ItemType,
        element: Element,
        file: File,
        link: Link,
        icon: ItemIcon,
        equipment: Equipment,
        level: Level,
        dps: Dps,
        item_range: ItemRange,
        rarity_id: ItemRarityId,
        quantity: Quantity,
        stack: Stack,
        cost: Cost,
        coins: Coins,
        diamonds: Diamonds,
        crystal: Crystal,
        sell: Sell,
        market: Market,
        temporary: Temporary,
        upgrade: Upgrade,
        staff: Staff,
        enh_id: Option<EnhancementId>,
        faction_id: Option<FactionId>,
        req_reputation: ReqReputation,
        req_class_id: Option<ClassId>,
        req_class_points: ReqClassPoints,
        req_quests: ReqQuests,
        quest_string_index: QuestStringIndex,
        quest_string_value: QuestStringValue,
        meta: Meta,
        color: Color,
    ) -> Self {
        Self {
            id,
            class_id,
            name,
            description,
            item_type,
            element,
            file,
            link,
            icon,
            equipment,
            level,
            dps,
            item_range,
            rarity_id,
            quantity,
            stack,
            cost,
            coins,
            diamonds,
            crystal,
            sell,
            market,
            temporary,
            upgrade,
            staff,
            enh_id,
            faction_id,
            req_reputation,
            req_class_id,
            req_class_points,
            req_quests,
            quest_string_index,
            quest_string_value,
            meta,
            color,
        }
    }
}

#[cfg(test)]
mod tests {
    use reforged_shared::{IdTrait, Value};

    use super::*;

    fn create_test_values() -> (
        ItemId,
        Option<ClassId>,
        ItemName,
        ItemDescription,
        ItemType,
        Element,
        File,
        Link,
        ItemIcon,
        Equipment,
        Level,
        Dps,
        ItemRange,
        ItemRarityId,
        Quantity,
        Stack,
        Cost,
        Coins,
        Diamonds,
        Crystal,
        Sell,
        Market,
        Temporary,
        Upgrade,
        Staff,
        Option<EnhancementId>,
        Option<FactionId>,
        ReqReputation,
        Option<ClassId>,
        ReqClassPoints,
        ReqQuests,
        QuestStringIndex,
        QuestStringValue,
        Meta,
        Color,
    ) {
        (
            ItemId::new(uuid::Uuid::now_v7()),
            Some(ClassId::new(uuid::Uuid::now_v7())),
            ItemName::new("Test Item".to_string()),
            ItemDescription::new("This is a test item".to_string()),
            ItemType::new("Weapon".to_string()),
            Element::new("Fire".to_string()),
            File::new("items/test_item.png".to_string()),
            Link::new("http://example.com/item".to_string()),
            ItemIcon::new("icon.png".to_string()),
            Equipment::new("Equippable".to_string()),
            Level::new(10),
            Dps::new(100),
            ItemRange::new(5),
            ItemRarityId::new(uuid::Uuid::now_v7()),
            Quantity::new(1),
            Stack::new(10),
            Cost::new(100),
            Coins::new(50),
            Diamonds::new(5),
            Crystal::new(2),
            Sell::new(true),
            Market::new(true),
            Temporary::new(false),
            Upgrade::new(true),
            Staff::new(false),
            Some(EnhancementId::new(uuid::Uuid::now_v7())),
            Some(FactionId::new(uuid::Uuid::now_v7())),
            ReqReputation::new(500),
            Some(ClassId::new(uuid::Uuid::now_v7())),
            ReqClassPoints::new(200),
            ReqQuests::new("1,2,3".to_string()),
            QuestStringIndex::new(7),
            QuestStringValue::new(1),
            Meta::new(Some("Test meta".to_string())),
            Color::new("red".to_string()),
        )
    }

    #[test]
    fn test_item_new() {
        let (
            id,
            class_id,
            name,
            description,
            item_type,
            element,
            file,
            link,
            icon,
            equipment,
            level,
            dps,
            item_range,
            rarity_id,
            quantity,
            stack,
            cost,
            coins,
            diamonds,
            crystal,
            sell,
            market,
            temporary,
            upgrade,
            staff,
            enh_id,
            faction_id,
            req_reputation,
            req_class_id,
            req_class_points,
            req_quests,
            quest_string_index,
            quest_string_value,
            meta,
            color,
        ) = create_test_values();

        let item = Item::new(
            id.clone(),
            class_id.clone(),
            name.clone(),
            description.clone(),
            item_type.clone(),
            element.clone(),
            file.clone(),
            link.clone(),
            icon.clone(),
            equipment.clone(),
            level.clone(),
            dps.clone(),
            item_range.clone(),
            rarity_id.clone(),
            quantity.clone(),
            stack.clone(),
            cost.clone(),
            coins.clone(),
            diamonds.clone(),
            crystal.clone(),
            sell.clone(),
            market.clone(),
            temporary.clone(),
            upgrade.clone(),
            staff.clone(),
            enh_id.clone(),
            faction_id.clone(),
            req_reputation.clone(),
            req_class_id.clone(),
            req_class_points.clone(),
            req_quests.clone(),
            quest_string_index.clone(),
            quest_string_value.clone(),
            meta.clone(),
            color.clone(),
        );

        assert_eq!(item.id().get_id(), id.get_id());
        assert_eq!(
            item.class_id().clone().unwrap().get_id(),
            class_id.unwrap().get_id()
        );
        assert_eq!(item.name().value(), name.value());
        assert_eq!(item.description().value(), description.value());
        assert_eq!(item.item_type().value(), item_type.value());
        assert_eq!(item.element().value(), element.value());
        assert_eq!(item.file().value(), file.value());
        assert_eq!(item.link().value(), link.value());
        assert_eq!(item.icon().value(), icon.value());
        assert_eq!(item.equipment().value(), equipment.value());
        assert_eq!(item.level().value(), level.value());
        assert_eq!(item.dps().value(), dps.value());
        assert_eq!(item.item_range().value(), item_range.value());
        assert_eq!(item.rarity_id().get_id(), rarity_id.get_id());
        assert_eq!(item.quantity().value(), quantity.value());
        assert_eq!(item.stack().value(), stack.value());
        assert_eq!(item.cost().value(), cost.value());
        assert_eq!(item.coins().value(), coins.value());
        assert_eq!(item.diamonds().value(), diamonds.value());
        assert_eq!(item.crystal().value(), crystal.value());
        assert_eq!(item.sell().value(), sell.value());
        assert_eq!(item.market().value(), market.value());
        assert_eq!(item.temporary().value(), temporary.value());
        assert_eq!(item.upgrade().value(), upgrade.value());
        assert_eq!(item.staff().value(), staff.value());
        assert_eq!(
            item.enh_id().clone().unwrap().get_id(),
            enh_id.unwrap().get_id()
        );
        assert_eq!(
            item.faction_id().clone().unwrap().get_id(),
            faction_id.unwrap().get_id()
        );
        assert_eq!(item.req_reputation().value(), req_reputation.value());
        assert_eq!(
            item.req_class_id().clone().unwrap().get_id(),
            req_class_id.unwrap().get_id()
        );
        assert_eq!(item.req_class_points().value(), req_class_points.value());
        assert_eq!(item.req_quests().value(), req_quests.value());
        assert_eq!(
            item.quest_string_index().value(),
            quest_string_index.value()
        );
        assert_eq!(
            item.quest_string_value().value(),
            quest_string_value.value()
        );
        assert_eq!(item.meta().value(), meta.value());
        assert_eq!(item.color().value(), color.value());
    }

    #[test]
    fn test_item_builder() {
        let (
            id,
            class_id,
            name,
            description,
            item_type,
            element,
            file,
            link,
            icon,
            equipment,
            level,
            dps,
            item_range,
            rarity_id,
            quantity,
            stack,
            cost,
            coins,
            diamonds,
            crystal,
            sell,
            market,
            temporary,
            upgrade,
            staff,
            enh_id,
            faction_id,
            req_reputation,
            req_class_id,
            req_class_points,
            req_quests,
            quest_string_index,
            quest_string_value,
            meta,
            color,
        ) = create_test_values();

        let item = Item::builder()
            .id(id.clone())
            .class_id(class_id.clone().unwrap())
            .name(name.clone())
            .description(description.clone())
            .item_type(item_type.clone())
            .element(element.clone())
            .file(file.clone())
            .link(link.clone())
            .icon(icon.clone())
            .equipment(equipment.clone())
            .level(level.clone())
            .dps(dps.clone())
            .item_range(item_range.clone())
            .rarity_id(rarity_id.clone())
            .quantity(quantity.clone())
            .stack(stack.clone())
            .cost(cost.clone())
            .coins(coins.clone())
            .diamonds(diamonds.clone())
            .crystal(crystal.clone())
            .sell(sell.clone())
            .market(market.clone())
            .temporary(temporary.clone())
            .upgrade(upgrade.clone())
            .staff(staff.clone())
            .enh_id(enh_id.clone().unwrap())
            .faction_id(faction_id.clone().unwrap())
            .req_reputation(req_reputation.clone())
            .req_class_id(req_class_id.clone().unwrap())
            .req_class_points(req_class_points.clone())
            .req_quests(req_quests.clone())
            .quest_string_index(quest_string_index.clone())
            .quest_string_value(quest_string_value.clone())
            .meta(meta.clone())
            .color(color.clone())
            .build();

        assert_eq!(item.id().get_id(), id.get_id());
        assert_eq!(
            item.class_id().clone().unwrap().get_id(),
            class_id.unwrap().get_id()
        );
        assert_eq!(item.name().value(), name.value());
        assert_eq!(item.description().value(), description.value());
        assert_eq!(item.item_type().value(), item_type.value());
        assert_eq!(item.element().value(), element.value());
        assert_eq!(item.file().value(), file.value());
        assert_eq!(item.link().value(), link.value());
        assert_eq!(item.icon().value(), icon.value());
        assert_eq!(item.equipment().value(), equipment.value());
        assert_eq!(item.level().value(), level.value());
        assert_eq!(item.dps().value(), dps.value());
        assert_eq!(item.item_range().value(), item_range.value());
        assert_eq!(item.rarity_id().get_id(), rarity_id.get_id());
        assert_eq!(item.quantity().value(), quantity.value());
        assert_eq!(item.stack().value(), stack.value());
        assert_eq!(item.cost().value(), cost.value());
        assert_eq!(item.coins().value(), coins.value());
        assert_eq!(item.diamonds().value(), diamonds.value());
        assert_eq!(item.crystal().value(), crystal.value());
        assert_eq!(item.sell().value(), sell.value());
        assert_eq!(item.market().value(), market.value());
        assert_eq!(item.temporary().value(), temporary.value());
        assert_eq!(item.upgrade().value(), upgrade.value());
        assert_eq!(item.staff().value(), staff.value());
        assert_eq!(
            item.enh_id().clone().unwrap().get_id(),
            enh_id.unwrap().get_id()
        );
        assert_eq!(
            item.faction_id().clone().unwrap().get_id(),
            faction_id.unwrap().get_id()
        );
        assert_eq!(item.req_reputation().value(), req_reputation.value());
        assert_eq!(
            item.req_class_id().clone().unwrap().get_id(),
            req_class_id.unwrap().get_id()
        );
        assert_eq!(item.req_class_points().value(), req_class_points.value());
        assert_eq!(item.req_quests().value(), req_quests.value());
        assert_eq!(
            item.quest_string_index().value(),
            quest_string_index.value()
        );
        assert_eq!(
            item.quest_string_value().value(),
            quest_string_value.value()
        );
        assert_eq!(item.meta().value(), meta.value());
        assert_eq!(item.color().value(), color.value());
    }

    #[test]
    fn test_getters_and_setters() {
        let mut item = Item::default();

        let (
            id,
            class_id,
            name,
            description,
            item_type,
            element,
            file,
            link,
            icon,
            equipment,
            level,
            dps,
            item_range,
            rarity_id,
            quantity,
            stack,
            cost,
            coins,
            diamonds,
            crystal,
            sell,
            market,
            temporary,
            upgrade,
            staff,
            enh_id,
            faction_id,
            req_reputation,
            req_class_id,
            req_class_points,
            req_quests,
            quest_string_index,
            quest_string_value,
            meta,
            color,
        ) = create_test_values();

        item.set_id(id.clone());
        item.set_class_id(class_id.clone());
        item.set_name(name.clone());
        item.set_description(description.clone());
        item.set_item_type(item_type.clone());
        item.set_element(element.clone());
        item.set_file(file.clone());
        item.set_link(link.clone());
        item.set_icon(icon.clone());
        item.set_equipment(equipment.clone());
        item.set_level(level.clone());
        item.set_dps(dps.clone());
        item.set_item_range(item_range.clone());
        item.set_rarity_id(rarity_id.clone());
        item.set_quantity(quantity.clone());
        item.set_stack(stack.clone());
        item.set_cost(cost.clone());
        item.set_coins(coins.clone());
        item.set_diamonds(diamonds.clone());
        item.set_crystal(crystal.clone());
        item.set_sell(sell.clone());
        item.set_market(market.clone());
        item.set_temporary(temporary.clone());
        item.set_upgrade(upgrade.clone());
        item.set_staff(staff.clone());
        item.set_enh_id(enh_id.clone());
        item.set_faction_id(faction_id.clone());
        item.set_req_reputation(req_reputation.clone());
        item.set_req_class_id(req_class_id.clone());
        item.set_req_class_points(req_class_points.clone());
        item.set_req_quests(req_quests.clone());
        item.set_quest_string_index(quest_string_index.clone());
        item.set_quest_string_value(quest_string_value.clone());
        item.set_meta(meta.clone());
        item.set_color(color.clone());

        assert_eq!(item.id().get_id(), id.get_id());
        assert_eq!(
            item.class_id().clone().unwrap().get_id(),
            class_id.unwrap().get_id()
        );
        assert_eq!(item.name().value(), name.value());
        assert_eq!(item.description().value(), description.value());
        assert_eq!(item.item_type().value(), item_type.value());
        assert_eq!(item.element().value(), element.value());
        assert_eq!(item.file().value(), file.value());
        assert_eq!(item.link().value(), link.value());
        assert_eq!(item.icon().value(), icon.value());
        assert_eq!(item.equipment().value(), equipment.value());
        assert_eq!(item.level().value(), level.value());
        assert_eq!(item.dps().value(), dps.value());
        assert_eq!(item.item_range().value(), item_range.value());
        assert_eq!(item.rarity_id().get_id(), rarity_id.get_id());
        assert_eq!(item.quantity().value(), quantity.value());
        assert_eq!(item.stack().value(), stack.value());
        assert_eq!(item.cost().value(), cost.value());
        assert_eq!(item.coins().value(), coins.value());
        assert_eq!(item.diamonds().value(), diamonds.value());
        assert_eq!(item.crystal().value(), crystal.value());
        assert_eq!(item.sell().value(), sell.value());
        assert_eq!(item.market().value(), market.value());
        assert_eq!(item.temporary().value(), temporary.value());
        assert_eq!(item.upgrade().value(), upgrade.value());
        assert_eq!(item.staff().value(), staff.value());
        assert_eq!(
            item.enh_id().clone().unwrap().get_id(),
            enh_id.unwrap().get_id()
        );
        assert_eq!(
            item.faction_id().clone().unwrap().get_id(),
            faction_id.unwrap().get_id()
        );
        assert_eq!(item.req_reputation().value(), req_reputation.value());
        assert_eq!(
            item.req_class_id().clone().unwrap().get_id(),
            req_class_id.unwrap().get_id()
        );
        assert_eq!(item.req_class_points().value(), req_class_points.value());
        assert_eq!(item.req_quests().value(), req_quests.value());
        assert_eq!(
            item.quest_string_index().value(),
            quest_string_index.value()
        );
        assert_eq!(
            item.quest_string_value().value(),
            quest_string_value.value()
        );
        assert_eq!(item.meta().value(), meta.value());
        assert_eq!(item.color().value(), color.value());
    }
}
