use reforged_shared::{UuidId, Value};

use super::entity::ItemEffect;

pub type ItemEffectId = UuidId<ItemEffect>;

#[derive(Debug, PartialEq, PartialOrd, Clone, Default)]

pub struct ItemEffectDamageIncrease(f64);

impl ItemEffectDamageIncrease {
    pub fn new(damage_increase: f64) -> Self {
        Self(damage_increase)
    }
}

impl Value<f64> for ItemEffectDamageIncrease {
    fn value(&self) -> f64 {
        self.0
    }
}

impl From<f64> for ItemEffectDamageIncrease {
    fn from(value: f64) -> Self {
        Self(value)
    }
}

#[derive(Debug, PartialEq, PartialOrd, Clone, Default)]

pub struct ItemEffectDamageTaken(f64);

impl ItemEffectDamageTaken {
    pub fn new(damage_taken: f64) -> Self {
        Self(damage_taken)
    }
}

impl Value<f64> for ItemEffectDamageTaken {
    fn value(&self) -> f64 {
        self.0
    }
}

impl From<f64> for ItemEffectDamageTaken {
    fn from(value: f64) -> Self {
        Self(value)
    }
}

#[derive(Debug, PartialEq, PartialOrd, Clone, Default)]

pub struct ItemEffectExp(f64);

impl ItemEffectExp {
    pub fn new(exp: f64) -> Self {
        Self(exp)
    }
}

impl Value<f64> for ItemEffectExp {
    fn value(&self) -> f64 {
        self.0
    }
}

impl From<f64> for ItemEffectExp {
    fn from(value: f64) -> Self {
        Self(value)
    }
}

#[derive(Debug, PartialEq, PartialOrd, Clone, Default)]

pub struct ItemEffectGold(f64);

impl ItemEffectGold {
    pub fn new(gold: f64) -> Self {
        Self(gold)
    }
}

impl Value<f64> for ItemEffectGold {
    fn value(&self) -> f64 {
        self.0
    }
}

impl From<f64> for ItemEffectGold {
    fn from(value: f64) -> Self {
        Self(value)
    }
}

#[derive(Debug, PartialEq, PartialOrd, Clone, Default)]

pub struct ItemEffectCoins(f64);

impl ItemEffectCoins {
    pub fn new(coins: f64) -> Self {
        Self(coins)
    }
}

impl Value<f64> for ItemEffectCoins {
    fn value(&self) -> f64 {
        self.0
    }
}

impl From<f64> for ItemEffectCoins {
    fn from(value: f64) -> Self {
        Self(value)
    }
}

#[derive(Debug, PartialEq, PartialOrd, Clone, Default)]

pub struct ItemEffectClassPoint(f64);

impl ItemEffectClassPoint {
    pub fn new(class_point: f64) -> Self {
        Self(class_point)
    }
}

impl Value<f64> for ItemEffectClassPoint {
    fn value(&self) -> f64 {
        self.0
    }
}

impl From<f64> for ItemEffectClassPoint {
    fn from(value: f64) -> Self {
        Self(value)
    }
}

#[derive(Debug, PartialEq, PartialOrd, Clone, Default)]

pub struct ItemEffectReputation(f64);

impl ItemEffectReputation {
    pub fn new(reputation: f64) -> Self {
        Self(reputation)
    }
}

impl Value<f64> for ItemEffectReputation {
    fn value(&self) -> f64 {
        self.0
    }
}

impl From<f64> for ItemEffectReputation {
    fn from(value: f64) -> Self {
        Self(value)
    }
}
