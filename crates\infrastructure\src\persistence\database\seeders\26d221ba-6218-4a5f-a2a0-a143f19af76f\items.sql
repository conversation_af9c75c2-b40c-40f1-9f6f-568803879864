INSERT INTO "items" ("id", "class_id", "name", "description", "type", "element", "file", "link", "icon", "equipment", "level", "dps", "range", "rarity", "quantity", "stack", "cost", "coins", "diamonds", "crystal", "sell", "market", "temporary", "upgrade", "staff", "enh_id", "faction_id", "req_reputation", "req_class_id", "req_class_points", "req_quests", "quest_string_index", "quest_string_value", "meta", "color") VALUES (1, NULL, 'Nytherian Staff', 'Nytherian weapon for Magical uses.', 'Staff', 'None', 'items/staves/NytherianStaff.swf', 'StaffWeaponStaff', 'iwstaff', 'Weapon', 1, 25, 10, 10, 1, 1, 0, 0, 0, '0', true, false, false, false, false, 1, NULL, '0', NULL, '0', '', -1, 0, '', 'ffffff');
INSERT INTO "items" ("id", "class_id", "name", "description", "type", "element", "file", "link", "icon", "equipment", "level", "dps", "range", "rarity", "quantity", "stack", "cost", "coins", "diamonds", "crystal", "sell", "market", "temporary", "upgrade", "staff", "enh_id", "faction_id", "req_reputation", "req_class_id", "req_class_points", "req_quests", "quest_string_index", "quest_string_value", "meta", "color") VALUES (2, 1, 'Nytherian', 'A Nytherian that doesn''t have much that of experience;we must keep moving and learn more about our Adventure!', 'Class', 'None', 'Nytherian.swf', 'DWMage', 'iiclass', 'ar', 1, 25, 50, 11, 1, 1, 1000, 0, 0, '0', true, true, false, false, false, 1, NULL, '0', 0, '0', '', -1, 0, '', 'ffffff');
INSERT INTO "items" ("id", "class_id", "name", "description", "type", "element", "file", "link", "icon", "equipment", "level", "dps", "range", "rarity", "quantity", "stack", "cost", "coins", "diamonds", "crystal", "sell", "market", "temporary", "upgrade", "staff", "enh_id", "faction_id", "req_reputation", "req_class_id", "req_class_points", "req_quests", "quest_string_index", "quest_string_value", "meta", "color") VALUES (3, NULL, 'Skeletal Bone', 'Skeletal Bone - Use for stuffs and medicinal herbs.', 'Item', 'None', 'none.swf', 'iibag', 'iibag', 'None', 1, 25, 50, 1, 1, 500, 0, 1, 0, '0', true, false, false, false, false, 1, 1, '0', NULL, '0', '', -1, 0, '', 'c5c2b4');
INSERT INTO "items" ("id", "class_id", "name", "description", "type", "element", "file", "link", "icon", "equipment", "level", "dps", "range", "rarity", "quantity", "stack", "cost", "coins", "diamonds", "crystal", "sell", "market", "temporary", "upgrade", "staff", "enh_id", "faction_id", "req_reputation", "req_class_id", "req_class_points", "req_quests", "quest_string_index", "quest_string_value", "meta", "color") VALUES (4, NULL, 'Darkness Absolution', 'Darkness Absolution;magical fire provided by the monster in Nasari;you could reserve the darkness absolution they will serve you!', 'Item', 'None', 'ief1', 'ief1', 'ief1', 'None', 1, 25, 50, 10, 1, 500, 0, 1, 0, '0', true, false, false, false, false, 1, NULL, '0', NULL, '0', '', -1, 0, '', '353626');
INSERT INTO "items" ("id", "class_id", "name", "description", "type", "element", "file", "link", "icon", "equipment", "level", "dps", "range", "rarity", "quantity", "stack", "cost", "coins", "diamonds", "crystal", "sell", "market", "temporary", "upgrade", "staff", "enh_id", "faction_id", "req_reputation", "req_class_id", "req_class_points", "req_quests", "quest_string_index", "quest_string_value", "meta", "color") VALUES (5, NULL, 'Balrog Blade (Beta)', 'Weapons of such ferocity are a rare find indeed. Its blade burns with eternal fire.', 'Sword', 'None', 'items/swords/sword20.swf', 'sword20', 'iwsword', 'Weapon', 1, 25, 50, 11, 1, 1, 80000, 0, 0, '0', true, true, false, false, false, 1, NULL, '0', NULL, '0', '', -1, 0, '', 'ffffff');
INSERT INTO "items" ("id", "class_id", "name", "description", "type", "element", "file", "link", "icon", "equipment", "level", "dps", "range", "rarity", "quantity", "stack", "cost", "coins", "diamonds", "crystal", "sell", "market", "temporary", "upgrade", "staff", "enh_id", "faction_id", "req_reputation", "req_class_id", "req_class_points", "req_quests", "quest_string_index", "quest_string_value", "meta", "color") VALUES (6, NULL, 'Beta Tester Star Sword', 'This blade seems to harness the power of light itself. It''s a force to be reckoned with.', 'Sword', 'None', 'items/swords/sword23.swf', 'sword23', 'iwsword', 'Weapon', 1, 25, 50, 11, 1, 1, 100000, 0, 0, '0', true, true, false, false, false, 1, NULL, '0', NULL, '0', '', -1, 0, '', 'ffffff');
INSERT INTO "items" ("id", "class_id", "name", "description", "type", "element", "file", "link", "icon", "equipment", "level", "dps", "range", "rarity", "quantity", "stack", "cost", "coins", "diamonds", "crystal", "sell", "market", "temporary", "upgrade", "staff", "enh_id", "faction_id", "req_reputation", "req_class_id", "req_class_points", "req_quests", "quest_string_index", "quest_string_value", "meta", "color") VALUES (7, NULL, 'Grumpy Hammer', 'This hammer is visibly upset. You would be too if people used your head as a weapon!', 'Mace', 'None', 'items/maces/mace03.swf', 'mace03', 'iwmace', 'Weapon', 1, 25, 50, 11, 1, 1, 40000, 0, 0, '0', true, true, false, false, false, 1, NULL, '0', NULL, '0', '', -1, 0, '', 'ffffff');
INSERT INTO "items" ("id", "class_id", "name", "description", "type", "element", "file", "link", "icon", "equipment", "level", "dps", "range", "rarity", "quantity", "stack", "cost", "coins", "diamonds", "crystal", "sell", "market", "temporary", "upgrade", "staff", "enh_id", "faction_id", "req_reputation", "req_class_id", "req_class_points", "req_quests", "quest_string_index", "quest_string_value", "meta", "color") VALUES (8, NULL, 'Shinobi Katana', 'A sword commonly used by ninja. The blade is much shorter than the traditional daito katana used by the samurai of feudal Japan.', 'Sword', 'None', 'items/swords/sword27.swf', 'sword27', 'iwsword', 'Weapon', 1, 25, 50, 11, 1, 1, 80000, 0, 0, '0', true, true, false, false, false, 1, NULL, '0', NULL, '0', '', -1, 0, '', 'ffffff');
INSERT INTO "items" ("id", "class_id", "name", "description", "type", "element", "file", "link", "icon", "equipment", "level", "dps", "range", "rarity", "quantity", "stack", "cost", "coins", "diamonds", "crystal", "sell", "market", "temporary", "upgrade", "staff", "enh_id", "faction_id", "req_reputation", "req_class_id", "req_class_points", "req_quests", "quest_string_index", "quest_string_value", "meta", "color") VALUES (9, NULL, 'Undead Plague Spear', 'A mere scrape from this vile weapon is deadly.', 'Polearm', 'None', 'items/polearms/polearm03.swf', 'polearm03', 'iwpolearm', 'Weapon', 1, 25, 50, 11, 1, 1, 5000, 0, 0, '0', true, true, false, false, false, 1, NULL, '0', NULL, '0', '', -1, 0, '', 'ffffff');
INSERT INTO "items" ("id", "class_id", "name", "description", "type", "element", "file", "link", "icon", "equipment", "level", "dps", "range", "rarity", "quantity", "stack", "cost", "coins", "diamonds", "crystal", "sell", "market", "temporary", "upgrade", "staff", "enh_id", "faction_id", "req_reputation", "req_class_id", "req_class_points", "req_quests", "quest_string_index", "quest_string_value", "meta", "color") VALUES (10, NULL, 'Dual Shinobi Katana', 'A sword commonly used by ninja. The blade is much shorter than the traditional daito katana used by the samurai of feudal Japan.', 'Dagger', 'None', 'items/daggers/sword27.swf', 'sword27', 'iwdagger', 'Weapon', 1, 25, 50, 11, 1, 1, 100000, 0, 0, '0', true, true, false, false, false, 1, NULL, '0', NULL, '0', '', -1, 0, '', 'ffffff');
INSERT INTO "items" ("id", "class_id", "name", "description", "type", "element", "file", "link", "icon", "equipment", "level", "dps", "range", "rarity", "quantity", "stack", "cost", "coins", "diamonds", "crystal", "sell", "market", "temporary", "upgrade", "staff", "enh_id", "faction_id", "req_reputation", "req_class_id", "req_class_points", "req_quests", "quest_string_index", "quest_string_value", "meta", "color") VALUES (11, NULL, 'Ranger Hat (Beta)', 'Ranger Hat. (This items is color customizable)', 'Helm', 'None', 'items/helms/rangerhat.swf', 'RangerHat', 'iihelm', 'he', 1, 25, 50, 11, 1, 1, 5000, 0, 0, '0', true, true, false, false, false, 1, NULL, '0', NULL, '0', '', -1, 0, '', 'ffffff');
INSERT INTO "items" ("id", "class_id", "name", "description", "type", "element", "file", "link", "icon", "equipment", "level", "dps", "range", "rarity", "quantity", "stack", "cost", "coins", "diamonds", "crystal", "sell", "market", "temporary", "upgrade", "staff", "enh_id", "faction_id", "req_reputation", "req_class_id", "req_class_points", "req_quests", "quest_string_index", "quest_string_value", "meta", "color") VALUES (12, NULL, 'Dragonslayer Helm (Beta)', 'The Helm worn by the elite order of Dragonslayers.', 'Helm', 'None', 'items/helms/Dragonslayer.swf', 'DragonslayerFHair', 'iihelm', 'he', 1, 25, 50, 11, 1, 1, 50000, 0, 0, '0', true, true, false, false, false, 1, NULL, '0', NULL, '0', '', -1, 0, '', 'ffffff');
INSERT INTO "items" ("id", "class_id", "name", "description", "type", "element", "file", "link", "icon", "equipment", "level", "dps", "range", "rarity", "quantity", "stack", "cost", "coins", "diamonds", "crystal", "sell", "market", "temporary", "upgrade", "staff", "enh_id", "faction_id", "req_reputation", "req_class_id", "req_class_points", "req_quests", "quest_string_index", "quest_string_value", "meta", "color") VALUES (13, NULL, 'Blue Cape', 'Blue Cape', 'Cape', 'None', 'items/capes/bluecape.swf', 'BlueCape', 'iicape', 'ba', 1, 25, 50, 11, 1, 1, 500, 0, 0, '0', true, true, false, false, false, 1, NULL, '0', NULL, '0', '', -1, 0, '', 'ffffff');
INSERT INTO "items" ("id", "class_id", "name", "description", "type", "element", "file", "link", "icon", "equipment", "level", "dps", "range", "rarity", "quantity", "stack", "cost", "coins", "diamonds", "crystal", "sell", "market", "temporary", "upgrade", "staff", "enh_id", "faction_id", "req_reputation", "req_class_id", "req_class_points", "req_quests", "quest_string_index", "quest_string_value", "meta", "color") VALUES (14, NULL, 'Brown Cape', 'Brown Cape', 'Cape', 'None', 'items/capes/browncape.swf', 'BrownCape', 'iicape', 'ba', 1, 25, 50, 11, 1, 1, 500, 0, 0, '0', true, true, false, false, false, 1, NULL, '0', NULL, '0', '', -1, 0, '', 'ffffff');
INSERT INTO "items" ("id", "class_id", "name", "description", "type", "element", "file", "link", "icon", "equipment", "level", "dps", "range", "rarity", "quantity", "stack", "cost", "coins", "diamonds", "crystal", "sell", "market", "temporary", "upgrade", "staff", "enh_id", "faction_id", "req_reputation", "req_class_id", "req_class_points", "req_quests", "quest_string_index", "quest_string_value", "meta", "color") VALUES (15, NULL, 'Dark Blue Cape', 'Dark Blue Cape', 'Cape', 'None', 'items/capes/darkbluecape.swf', 'DarkblueCape', 'iicape', 'ba', 1, 25, 50, 11, 1, 1, 500, 0, 0, '0', true, true, false, false, false, 1, NULL, '0', NULL, '0', '', -1, 0, '', 'ffffff');
INSERT INTO "items" ("id", "class_id", "name", "description", "type", "element", "file", "link", "icon", "equipment", "level", "dps", "range", "rarity", "quantity", "stack", "cost", "coins", "diamonds", "crystal", "sell", "market", "temporary", "upgrade", "staff", "enh_id", "faction_id", "req_reputation", "req_class_id", "req_class_points", "req_quests", "quest_string_index", "quest_string_value", "meta", "color") VALUES (16, NULL, 'Green Cape', 'Green Cape', 'Cape', 'None', 'items/capes/greencape.swf', 'GreenCape', 'iicape', 'ba', 1, 25, 50, 11, 1, 1, 500, 0, 0, '0', true, true, false, false, false, 1, NULL, '0', NULL, '0', '', -1, 0, '', 'ffffff');
INSERT INTO "items" ("id", "class_id", "name", "description", "type", "element", "file", "link", "icon", "equipment", "level", "dps", "range", "rarity", "quantity", "stack", "cost", "coins", "diamonds", "crystal", "sell", "market", "temporary", "upgrade", "staff", "enh_id", "faction_id", "req_reputation", "req_class_id", "req_class_points", "req_quests", "quest_string_index", "quest_string_value", "meta", "color") VALUES (17, NULL, 'Red Cape', 'Red Cape', 'Cape', 'None', 'items/capes/redcape.swf', 'RedCape', 'iicape', 'ba', 1, 25, 50, 11, 1, 1, 500, 0, 0, '0', true, true, false, false, false, 1, NULL, '0', NULL, '0', '', -1, 0, '', 'ffffff');
INSERT INTO "items" ("id", "class_id", "name", "description", "type", "element", "file", "link", "icon", "equipment", "level", "dps", "range", "rarity", "quantity", "stack", "cost", "coins", "diamonds", "crystal", "sell", "market", "temporary", "upgrade", "staff", "enh_id", "faction_id", "req_reputation", "req_class_id", "req_class_points", "req_quests", "quest_string_index", "quest_string_value", "meta", "color") VALUES (18, NULL, 'Yellow Cape', 'Yellow Cape', 'Cape', 'None', 'items/capes/yellowcape.swf', 'yellowcape', 'iicape', 'ba', 1, 25, 50, 11, 1, 1, 500, 0, 0, '0', true, true, false, false, false, 1, NULL, '0', NULL, '0', '', -1, 0, '', 'ffffff');
INSERT INTO "items" ("id", "class_id", "name", "description", "type", "element", "file", "link", "icon", "equipment", "level", "dps", "range", "rarity", "quantity", "stack", "cost", "coins", "diamonds", "crystal", "sell", "market", "temporary", "upgrade", "staff", "enh_id", "faction_id", "req_reputation", "req_class_id", "req_class_points", "req_quests", "quest_string_index", "quest_string_value", "meta", "color") VALUES (19, NULL, 'Blade of the Fallen', 'A terrifying powerful artifact of the Void;this one has been enchanted with a small amount of the Purple Mage''s immense power. This makes it all the more terrifying and all the more powerful.', 'Sword', 'None', 'items/swords/FallensJusticeFix.swf', 'FallensJusticeFix', 'iwsword', 'Weapon', 1, 27, 50, 35, 1, 1, 0, 1, 0, '0', true, false, false, false, false, 1, NULL, '0', NULL, '0', '', -1, 0, '', 'b577df');
INSERT INTO "items" ("id", "class_id", "name", "description", "type", "element", "file", "link", "icon", "equipment", "level", "dps", "range", "rarity", "quantity", "stack", "cost", "coins", "diamonds", "crystal", "sell", "market", "temporary", "upgrade", "staff", "enh_id", "faction_id", "req_reputation", "req_class_id", "req_class_points", "req_quests", "quest_string_index", "quest_string_value", "meta", "color") VALUES (20, NULL, 'Soon', 'Soon', 'Resource', 'None', 'Soon', 'Soon', 'iibag', 'Weapon', 1, 27, 50, 35, 1, 1, 0, 1, 0, '0', true, false, false, false, false, 1, NULL, '0', NULL, '0', '', -1, 0, '', 'b577df');
INSERT INTO "items" ("id", "class_id", "name", "description", "type", "element", "file", "link", "icon", "equipment", "level", "dps", "range", "rarity", "quantity", "stack", "cost", "coins", "diamonds", "crystal", "sell", "market", "temporary", "upgrade", "staff", "enh_id", "faction_id", "req_reputation", "req_class_id", "req_class_points", "req_quests", "quest_string_index", "quest_string_value", "meta", "color") VALUES (21, NULL, 'Mariachi Hero Guitar', 'All I wanted was to be a nightmare;like my ancestors. But the dream I thought would bring me luck;brpught only a curse. I lost my moglin;my mustache;and my maracas.', 'Axe', 'None', 'items/axes/MariachiHeroGuitar.swf', 'MariachiHeroGuitar', 'iwaxe', 'Weapon', 1, 25, 50, 50, 1, 1, 25555, 0, 0, '0', true, true, false, false, false, 1, NULL, '0', NULL, '0', '', -1, 0, '', 'ffffff');
INSERT INTO "items" ("id", "class_id", "name", "description", "type", "element", "file", "link", "icon", "equipment", "level", "dps", "range", "rarity", "quantity", "stack", "cost", "coins", "diamonds", "crystal", "sell", "market", "temporary", "upgrade", "staff", "enh_id", "faction_id", "req_reputation", "req_class_id", "req_class_points", "req_quests", "quest_string_index", "quest_string_value", "meta", "color") VALUES (22, NULL, 'Victoria Magica', 'Celebrate Cinco de Mayo with this epic more colorful dancewear!', 'Armor', 'None', 'LaDancerCC.swf', 'LaDancerCC', 'iwarmor', 'co', 1, 25, 50, 50, 1, 1, 750, 1, 0, '0', true, true, false, false, false, 1, NULL, '0', NULL, '0', '', -1, 0, '', 'ffffff');
INSERT INTO "items" ("id", "class_id", "name", "description", "type", "element", "file", "link", "icon", "equipment", "level", "dps", "range", "rarity", "quantity", "stack", "cost", "coins", "diamonds", "crystal", "sell", "market", "temporary", "upgrade", "staff", "enh_id", "faction_id", "req_reputation", "req_class_id", "req_class_points", "req_quests", "quest_string_index", "quest_string_value", "meta", "color") VALUES (23, NULL, 'The Ol Fishin'' Garb', 'How many fisherman do you see wearing a full suit of chain mail? I rest my case. Grab your Fishin'' Rod and favorite hat and let''s catch some grub!', 'Armor', 'None', 'Fishing1.swf', 'Fishing1', 'iwarmor', 'co', 1, 25, 50, 50, 1, 1, 400, 1, 0, '0', true, true, false, false, false, 1, NULL, '0', NULL, '0', '', -1, 0, '', 'ffffff');
INSERT INTO "items" ("id", "class_id", "name", "description", "type", "element", "file", "link", "icon", "equipment", "level", "dps", "range", "rarity", "quantity", "stack", "cost", "coins", "diamonds", "crystal", "sell", "market", "temporary", "upgrade", "staff", "enh_id", "faction_id", "req_reputation", "req_class_id", "req_class_points", "req_quests", "quest_string_index", "quest_string_value", "meta", "color") VALUES (24, NULL, 'Skeleton Bones', '-', 'Item', 'None', 'iibag', 'iibag', 'iibag', 'Item', 1, 25, 50, 10, 1, 50, 0, 0, 0, '0', true, true, true, false, false, 1, NULL, '0', NULL, '0', '', -1, 0, '', 'ffffff');
INSERT INTO "items" ("id", "class_id", "name", "description", "type", "element", "file", "link", "icon", "equipment", "level", "dps", "range", "rarity", "quantity", "stack", "cost", "coins", "diamonds", "crystal", "sell", "market", "temporary", "upgrade", "staff", "enh_id", "faction_id", "req_reputation", "req_class_id", "req_class_points", "req_quests", "quest_string_index", "quest_string_value", "meta", "color") VALUES (25, NULL, 'Skeletons Part''s', '-', 'Item', 'None', 'iibag', 'iibag', 'iibag', 'Item', 1, 25, 50, 10, 1, 50, 0, 0, 0, '0', true, true, true, false, false, 1, NULL, '0', NULL, '0', '', -1, 0, '', 'ffffff');
INSERT INTO "items" ("id", "class_id", "name", "description", "type", "element", "file", "link", "icon", "equipment", "level", "dps", "range", "rarity", "quantity", "stack", "cost", "coins", "diamonds", "crystal", "sell", "market", "temporary", "upgrade", "staff", "enh_id", "faction_id", "req_reputation", "req_class_id", "req_class_points", "req_quests", "quest_string_index", "quest_string_value", "meta", "color") VALUES (26, NULL, 'Undead Giant Defeated', '', 'Item', 'None', 'iibag', 'iibag', 'iibag', 'Item', 1, 25, 50, 10, 1, 10, 0, 0, 0, '0', true, true, true, false, false, 1, NULL, '0', NULL, '0', '', -1, 0, '', 'ffffff');
INSERT INTO "items" ("id", "class_id", "name", "description", "type", "element", "file", "link", "icon", "equipment", "level", "dps", "range", "rarity", "quantity", "stack", "cost", "coins", "diamonds", "crystal", "sell", "market", "temporary", "upgrade", "staff", "enh_id", "faction_id", "req_reputation", "req_class_id", "req_class_points", "req_quests", "quest_string_index", "quest_string_value", "meta", "color") VALUES (27, NULL, 'Jack Sprat Bone part', '-', 'Item', 'None', 'iibag', 'iibag', 'iibag', 'Item', 1, 25, 50, 10, 1, 5, 0, 0, 0, '0', true, false, true, false, false, 1, NULL, '0', NULL, '0', '', -1, 0, '', 'ffffff');
INSERT INTO "items" ("id", "class_id", "name", "description", "type", "element", "file", "link", "icon", "equipment", "level", "dps", "range", "rarity", "quantity", "stack", "cost", "coins", "diamonds", "crystal", "sell", "market", "temporary", "upgrade", "staff", "enh_id", "faction_id", "req_reputation", "req_class_id", "req_class_points", "req_quests", "quest_string_index", "quest_string_value", "meta", "color") VALUES (28, NULL, 'Mariachi Hero Hair', 'All I wanted was to be a nightmare;like my ancestors. But the dream I thought would bring me luck;brought only a curse. I lost my moglin;my mustache;and my maracas.', 'Helm', 'None', 'items/helms/MariachiHeroHair.swf', 'MariachiHeroHair', 'iihelm', 'he', 1, 25, 50, 10, 1, 1, 5555, 0, 0, '0', true, true, false, false, false, 1, NULL, '0', NULL, '0', '', -1, 0, '', 'ffffff');
INSERT INTO "items" ("id", "class_id", "name", "description", "type", "element", "file", "link", "icon", "equipment", "level", "dps", "range", "rarity", "quantity", "stack", "cost", "coins", "diamonds", "crystal", "sell", "market", "temporary", "upgrade", "staff", "enh_id", "faction_id", "req_reputation", "req_class_id", "req_class_points", "req_quests", "quest_string_index", "quest_string_value", "meta", "color") VALUES (29, NULL, 'Mariachi Hero Locks', 'All I wanted was to be a nightmare;like my ancestors. But the dream I thought would bring me luck;brought only a curse. I lost my moglin;my mustache;and my maracas.', 'Helm', 'None', 'items/helms/MariachiHeroLocks.swf', 'MariachiHeroLocks', 'iihelm', 'he', 1, 25, 50, 50, 1, 1, 5555, 0, 0, '0', true, true, false, false, false, 1, NULL, '0', NULL, '0', '', -1, 0, '', 'ffffff');
INSERT INTO "items" ("id", "class_id", "name", "description", "type", "element", "file", "link", "icon", "equipment", "level", "dps", "range", "rarity", "quantity", "stack", "cost", "coins", "diamonds", "crystal", "sell", "market", "temporary", "upgrade", "staff", "enh_id", "faction_id", "req_reputation", "req_class_id", "req_class_points", "req_quests", "quest_string_index", "quest_string_value", "meta", "color") VALUES (30, NULL, 'Sombrero de la Magica', 'Celebrate Cinco de Mayo with this epic more colorful dancewear!', 'Helm', 'None', 'items/helms/DancerHatCC.swf', 'DancerHatCC', 'iihelm', 'he', 1, 25, 50, 50, 1, 1, 150, 1, 0, '0', true, true, false, false, false, 1, NULL, '0', NULL, '0', '', -1, 0, '', 'ffffff');
INSERT INTO "items" ("id", "class_id", "name", "description", "type", "element", "file", "link", "icon", "equipment", "level", "dps", "range", "rarity", "quantity", "stack", "cost", "coins", "diamonds", "crystal", "sell", "market", "temporary", "upgrade", "staff", "enh_id", "faction_id", "req_reputation", "req_class_id", "req_class_points", "req_quests", "quest_string_index", "quest_string_value", "meta", "color") VALUES (372, NULL, 'Jungle Delivery', 'Jungle Delivery', 'Item', 'None', 'iibag', 'iibag', 'iibag', 'None', 1, 25, 50, 18, 1, 1, 2500, 0, 0, '0', true, false, false, false, false, 1, NULL, '0', NULL, '0', '', -1, 0, '', 'ffffff');
INSERT INTO "items" ("id", "class_id", "name", "description", "type", "element", "file", "link", "icon", "equipment", "level", "dps", "range", "rarity", "quantity", "stack", "cost", "coins", "diamonds", "crystal", "sell", "market", "temporary", "upgrade", "staff", "enh_id", "faction_id", "req_reputation", "req_class_id", "req_class_points", "req_quests", "quest_string_index", "quest_string_value", "meta", "color") VALUES (31, NULL, 'BBQ Spatula', 'Extra-wide stainless steel spatula blade will hold the largest cuts of meat and has the capability to flip a fully grown cow.', 'Mace', 'None', 'items/maces/SpatulaBBQSword.swf', 'SpatulaBBQSword', 'iwmace', 'Weapon', 1, 25, 50, 50, 1, 1, 5000, 0, 0, '0', true, true, false, false, false, 1, NULL, '0', NULL, '0', '', -1, 0, '', 'ffffff');
INSERT INTO "items" ("id", "class_id", "name", "description", "type", "element", "file", "link", "icon", "equipment", "level", "dps", "range", "rarity", "quantity", "stack", "cost", "coins", "diamonds", "crystal", "sell", "market", "temporary", "upgrade", "staff", "enh_id", "faction_id", "req_reputation", "req_class_id", "req_class_points", "req_quests", "quest_string_index", "quest_string_value", "meta", "color") VALUES (32, NULL, 'Gravity Defying Coffee Mug', 'A steaming cup of coffee that will never spill - the perfect gift for the AQWorld''s best dad this Father''s Day!', 'Mace', 'None', 'items/maces/mugCoffee.swf', 'mugCoffee', 'iwmace', 'Weapon', 1, 25, 50, 50, 1, 1, 150, 1, 0, '0', true, true, false, false, false, 1, NULL, '0', NULL, '0', '', -1, 0, '', 'ffffff');
INSERT INTO "items" ("id", "class_id", "name", "description", "type", "element", "file", "link", "icon", "equipment", "level", "dps", "range", "rarity", "quantity", "stack", "cost", "coins", "diamonds", "crystal", "sell", "market", "temporary", "upgrade", "staff", "enh_id", "faction_id", "req_reputation", "req_class_id", "req_class_points", "req_quests", "quest_string_index", "quest_string_value", "meta", "color") VALUES (33, NULL, 'The Ol Fishin'' Hat', 'Anglers swear by the khaki bucket hat. Never leave on your fishing trip without one!', 'Helm', 'None', 'items/helms/FishingHat1.swf', 'FishingHat1', 'iihelm', 'he', 1, 25, 50, 50, 1, 1, 120, 1, 0, '0', true, true, false, false, false, 1, NULL, '0', NULL, '0', '', -1, 0, '', 'ffffff');
INSERT INTO "items" ("id", "class_id", "name", "description", "type", "element", "file", "link", "icon", "equipment", "level", "dps", "range", "rarity", "quantity", "stack", "cost", "coins", "diamonds", "crystal", "sell", "market", "temporary", "upgrade", "staff", "enh_id", "faction_id", "req_reputation", "req_class_id", "req_class_points", "req_quests", "quest_string_index", "quest_string_value", "meta", "color") VALUES (34, NULL, 'Fairy Godmother', 'She''ll bippity boppity boop all ''round you. Happy Mother''s Day!', 'Pet', 'None', 'items/pets/godmother.swf', 'PetGodmother', 'iipet', 'pe', 1, 25, 50, 50, 1, 1, 25000, 0, 0, '0', true, true, false, true, false, 1, NULL, '0', NULL, '0', '', -1, 0, '', 'ffffff');
INSERT INTO "items" ("id", "class_id", "name", "description", "type", "element", "file", "link", "icon", "equipment", "level", "dps", "range", "rarity", "quantity", "stack", "cost", "coins", "diamonds", "crystal", "sell", "market", "temporary", "upgrade", "staff", "enh_id", "faction_id", "req_reputation", "req_class_id", "req_class_points", "req_quests", "quest_string_index", "quest_string_value", "meta", "color") VALUES (35, NULL, 'Acalagon Demise', 'Exclusive Item', 'Sword', 'None', 'items/swords/DracoLichKingSword.swf', 'DracoLichKingSword', 'iwsword', 'Weapon', 1, 25, 50, 35, 1, 1, 500, 1, 0, '0', true, false, false, true, false, 1, NULL, '0', NULL, '0', '', -1, 0, '', '00ffff');
INSERT INTO "items" ("id", "class_id", "name", "description", "type", "element", "file", "link", "icon", "equipment", "level", "dps", "range", "rarity", "quantity", "stack", "cost", "coins", "diamonds", "crystal", "sell", "market", "temporary", "upgrade", "staff", "enh_id", "faction_id", "req_reputation", "req_class_id", "req_class_points", "req_quests", "quest_string_index", "quest_string_value", "meta", "color") VALUES (36, NULL, 'XP Boost! (1 hr)', 'Using this item will DOUBLE all experience gained from killing monsters or completing quests for 1 hour of in-game play time.', 'ServerUse', 'None', 'icbxp', 'xpboost::60::true', 'icbxp', 'None', 1, 25, 50, 11, 1, 30, 150, 1, 0, '0', true, true, false, false, false, 1, NULL, '0', NULL, '0', '', -1, 0, '', 'ffffff');
INSERT INTO "items" ("id", "class_id", "name", "description", "type", "element", "file", "link", "icon", "equipment", "level", "dps", "range", "rarity", "quantity", "stack", "cost", "coins", "diamonds", "crystal", "sell", "market", "temporary", "upgrade", "staff", "enh_id", "faction_id", "req_reputation", "req_class_id", "req_class_points", "req_quests", "quest_string_index", "quest_string_value", "meta", "color") VALUES (37, NULL, 'Legion Acalagon Demise', 'Exclusive Item', 'Sword', 'None', 'items/swords/LegDracoLichKingSword.swf', 'LegDracoLichKingSword', 'iwsword', 'Weapon', 1, 25, 50, 35, 1, 1, 500, 1, 0, '0', true, false, false, true, false, 1, NULL, '0', NULL, '0', '', -1, 0, '', '04def0');
INSERT INTO "items" ("id", "class_id", "name", "description", "type", "element", "file", "link", "icon", "equipment", "level", "dps", "range", "rarity", "quantity", "stack", "cost", "coins", "diamonds", "crystal", "sell", "market", "temporary", "upgrade", "staff", "enh_id", "faction_id", "req_reputation", "req_class_id", "req_class_points", "req_quests", "quest_string_index", "quest_string_value", "meta", "color") VALUES (38, NULL, 'CP Boost! (1 hr)', 'Using this item will DOUBLE all cp gained from killing monsters or completing quests for 1 hour of in-game play time. Does not expire while logged out. Only available every four years for Leap Day.', 'ServerUse', 'None', 'icbcp', 'cpboost::60::false', 'icbcp', 'None', 1, 25, 50, 10, 1, 30, 150, 1, 0, '0', true, true, false, false, false, 1, NULL, '0', NULL, '0', '', -1, 0, '', 'ffffff');
INSERT INTO "items" ("id", "class_id", "name", "description", "type", "element", "file", "link", "icon", "equipment", "level", "dps", "range", "rarity", "quantity", "stack", "cost", "coins", "diamonds", "crystal", "sell", "market", "temporary", "upgrade", "staff", "enh_id", "faction_id", "req_reputation", "req_class_id", "req_class_points", "req_quests", "quest_string_index", "quest_string_value", "meta", "color") VALUES (39, NULL, 'DracoLich King', 'Exclusive Item', 'Armor', 'None', 'DracoLichKing.swf', 'DracoLichKing', 'iwarmor', 'co', 1, 25, 50, 35, 1, 1, 1000, 1, 0, '0', true, false, false, true, false, 1, NULL, '0', NULL, '0', '', -1, 0, '', '00ffff');
INSERT INTO "items" ("id", "class_id", "name", "description", "type", "element", "file", "link", "icon", "equipment", "level", "dps", "range", "rarity", "quantity", "stack", "cost", "coins", "diamonds", "crystal", "sell", "market", "temporary", "upgrade", "staff", "enh_id", "faction_id", "req_reputation", "req_class_id", "req_class_points", "req_quests", "quest_string_index", "quest_string_value", "meta", "color") VALUES (40, NULL, 'Legion DracoLich King', 'Exclusive Item', 'Armor', 'None', 'LegDracoLichKing.swf', 'LegDracoLichKing', 'iwarmor', 'co', 1, 25, 50, 35, 1, 1, 1000, 1, 0, '0', true, false, false, true, false, 1, NULL, '0', NULL, '0', '', -1, 0, '', '00ffff');
INSERT INTO "items" ("id", "class_id", "name", "description", "type", "element", "file", "link", "icon", "equipment", "level", "dps", "range", "rarity", "quantity", "stack", "cost", "coins", "diamonds", "crystal", "sell", "market", "temporary", "upgrade", "staff", "enh_id", "faction_id", "req_reputation", "req_class_id", "req_class_points", "req_quests", "quest_string_index", "quest_string_value", "meta", "color") VALUES (41, NULL, 'DracoLich Crown', 'Exclusive Item', 'Helm', 'None', 'items/helms/DracoLichKingHelm.swf', 'DracoLichKingHelm', 'iihelm', 'he', 1, 25, 50, 35, 1, 1, 100, 1, 0, '0', true, false, false, true, false, 1, NULL, '0', NULL, '0', '', -1, 0, '', '00ffff');
INSERT INTO "items" ("id", "class_id", "name", "description", "type", "element", "file", "link", "icon", "equipment", "level", "dps", "range", "rarity", "quantity", "stack", "cost", "coins", "diamonds", "crystal", "sell", "market", "temporary", "upgrade", "staff", "enh_id", "faction_id", "req_reputation", "req_class_id", "req_class_points", "req_quests", "quest_string_index", "quest_string_value", "meta", "color") VALUES (42, NULL, 'REP Boost! (1 hr)', 'Equip this boost to gain double rep for 60 minutes!', 'ServerUse', 'None', 'icbrep', 'repboost::60::false', 'icbrep', 'None', 1, 25, 50, 11, 1, 30, 150, 1, 0, '0', true, true, false, false, false, 1, NULL, '0', NULL, '0', '', -1, 0, '', 'ffffff');
INSERT INTO "items" ("id", "class_id", "name", "description", "type", "element", "file", "link", "icon", "equipment", "level", "dps", "range", "rarity", "quantity", "stack", "cost", "coins", "diamonds", "crystal", "sell", "market", "temporary", "upgrade", "staff", "enh_id", "faction_id", "req_reputation", "req_class_id", "req_class_points", "req_quests", "quest_string_index", "quest_string_value", "meta", "color") VALUES (43, NULL, 'Legion DracoLich Crown', 'Exclusive Item', 'Helm', 'None', 'items/helms/LegDracoLichKingHelm.swf', 'LegDracoLichKingHelm', 'iihelm', 'he', 1, 25, 50, 35, 1, 1, 100, 1, 0, '0', true, false, false, true, false, 1, NULL, '0', NULL, '0', '', -1, 0, '', '00ffff');
INSERT INTO "items" ("id", "class_id", "name", "description", "type", "element", "file", "link", "icon", "equipment", "level", "dps", "range", "rarity", "quantity", "stack", "cost", "coins", "diamonds", "crystal", "sell", "market", "temporary", "upgrade", "staff", "enh_id", "faction_id", "req_reputation", "req_class_id", "req_class_points", "req_quests", "quest_string_index", "quest_string_value", "meta", "color") VALUES (44, NULL, 'Acalagon Shadow', 'Exlcusive Item', 'Cape', 'None', 'items/capes/AncalagonCape.swf', 'AncalagonCape', 'iicape', 'ba', 1, 25, 50, 35, 1, 1, 500, 1, 0, '0', true, false, false, true, false, 1, NULL, '0', NULL, '0', '', -1, 0, '', '00ffff');
INSERT INTO "items" ("id", "class_id", "name", "description", "type", "element", "file", "link", "icon", "equipment", "level", "dps", "range", "rarity", "quantity", "stack", "cost", "coins", "diamonds", "crystal", "sell", "market", "temporary", "upgrade", "staff", "enh_id", "faction_id", "req_reputation", "req_class_id", "req_class_points", "req_quests", "quest_string_index", "quest_string_value", "meta", "color") VALUES (45, NULL, 'DracoLich King BackBlade and Cape', 'Exclusive Item', 'Cape', 'None', 'items/capes/DLKBladeNCape.swf', 'DLKBladeNCape', 'iicape', 'ba', 1, 25, 50, 35, 1, 1, 500, 1, 0, '0', true, false, false, true, false, 1, NULL, '0', NULL, '0', '', -1, 0, '', '00ffff');
INSERT INTO "items" ("id", "class_id", "name", "description", "type", "element", "file", "link", "icon", "equipment", "level", "dps", "range", "rarity", "quantity", "stack", "cost", "coins", "diamonds", "crystal", "sell", "market", "temporary", "upgrade", "staff", "enh_id", "faction_id", "req_reputation", "req_class_id", "req_class_points", "req_quests", "quest_string_index", "quest_string_value", "meta", "color") VALUES (46, NULL, 'DracoLich King BackBlades', 'Exclusive Item', 'Cape', 'None', '/items/capes/DLKBladesCape.swf', 'DLKBladesCape', 'iicape', 'ba', 1, 25, 50, 35, 1, 1, 500, 1, 0, '0', true, false, false, true, false, 1, NULL, '0', NULL, '0', '', -1, 0, '', '00ffff');
INSERT INTO "items" ("id", "class_id", "name", "description", "type", "element", "file", "link", "icon", "equipment", "level", "dps", "range", "rarity", "quantity", "stack", "cost", "coins", "diamonds", "crystal", "sell", "market", "temporary", "upgrade", "staff", "enh_id", "faction_id", "req_reputation", "req_class_id", "req_class_points", "req_quests", "quest_string_index", "quest_string_value", "meta", "color") VALUES (47, NULL, 'Legion DracoLichKing BackBlades and Cape', 'Exclusive Item', 'Cape', 'None', 'items/capes/LegDLKBladeNCape.swf', 'LegDLKBladeNCape', 'iicape', 'ba', 1, 25, 50, 35, 1, 1, 500, 1, 0, '0', true, false, false, true, false, 1, NULL, '0', NULL, '0', '', -1, 0, '', '00ffff');
INSERT INTO "items" ("id", "class_id", "name", "description", "type", "element", "file", "link", "icon", "equipment", "level", "dps", "range", "rarity", "quantity", "stack", "cost", "coins", "diamonds", "crystal", "sell", "market", "temporary", "upgrade", "staff", "enh_id", "faction_id", "req_reputation", "req_class_id", "req_class_points", "req_quests", "quest_string_index", "quest_string_value", "meta", "color") VALUES (48, NULL, 'Legion DracoLichKing BackBlades', 'Exclusive Item', 'Cape', 'None', 'items/capes/LegDLKBladesCape.swf', 'LegDLKBladesCape', 'iicape', 'ba', 1, 25, 50, 35, 1, 1, 500, 1, 0, '0', true, false, false, true, false, 1, NULL, '0', NULL, '0', '', -1, 0, '', '00ffff');
INSERT INTO "items" ("id", "class_id", "name", "description", "type", "element", "file", "link", "icon", "equipment", "level", "dps", "range", "rarity", "quantity", "stack", "cost", "coins", "diamonds", "crystal", "sell", "market", "temporary", "upgrade", "staff", "enh_id", "faction_id", "req_reputation", "req_class_id", "req_class_points", "req_quests", "quest_string_index", "quest_string_value", "meta", "color") VALUES (49, NULL, 'Acalagon Guardian', 'Exclusive Item', 'Pet', 'None', 'items/pets/DLKSwordPet.swf', 'DLKSwordPet', 'iipet', 'pe', 1, 25, 50, 35, 1, 1, 250, 1, 0, '0', true, false, false, true, false, 1, NULL, '0', NULL, '0', '', -1, 0, '', '00ffff');
INSERT INTO "items" ("id", "class_id", "name", "description", "type", "element", "file", "link", "icon", "equipment", "level", "dps", "range", "rarity", "quantity", "stack", "cost", "coins", "diamonds", "crystal", "sell", "market", "temporary", "upgrade", "staff", "enh_id", "faction_id", "req_reputation", "req_class_id", "req_class_points", "req_quests", "quest_string_index", "quest_string_value", "meta", "color") VALUES (50, NULL, 'Legion Acalagon Guardian', 'Exclusive Item', 'Pet', 'None', 'items/pets/LegDLKSwordPet.swf', 'LegDLKSwordPet', 'iipet', 'pe', 1, 25, 50, 35, 1, 1, 250, 1, 0, '0', true, false, false, true, false, 1, NULL, '0', NULL, '0', '', -1, 0, '', '00ffff');
INSERT INTO "items" ("id", "class_id", "name", "description", "type", "element", "file", "link", "icon", "equipment", "level", "dps", "range", "rarity", "quantity", "stack", "cost", "coins", "diamonds", "crystal", "sell", "market", "temporary", "upgrade", "staff", "enh_id", "faction_id", "req_reputation", "req_class_id", "req_class_points", "req_quests", "quest_string_index", "quest_string_value", "meta", "color") VALUES (51, 2, 'Beta Berserker (RARE)', 'Recommended enhancement: Fighter. Special Berserker class available only to characters who played in the Beta version!', 'Class', 'None', 'warrior2a_skin.swf', 'Warrior2a', 'iiclass', 'ar', 1, 25, 50, 100, 1, 1, 100000, 0, 0, '0', true, false, false, false, false, 1, NULL, '0', NULL, '0', '', -1, 0, '', 'ffffff');
INSERT INTO "items" ("id", "class_id", "name", "description", "type", "element", "file", "link", "icon", "equipment", "level", "dps", "range", "rarity", "quantity", "stack", "cost", "coins", "diamonds", "crystal", "sell", "market", "temporary", "upgrade", "staff", "enh_id", "faction_id", "req_reputation", "req_class_id", "req_class_points", "req_quests", "quest_string_index", "quest_string_value", "meta", "color") VALUES (52, NULL, 'Dual Beta Tester Star Sword', '-', 'Dagger', 'None', 'items/daggers/Dualsword23.swf', 'sword23', 'iwdagger', 'Weapon', 1, 25, 50, 11, 1, 1, 50000, 0, 0, '0', true, false, false, true, false, 1, NULL, '0', NULL, '0', '', -1, 0, '', 'ffffff');
INSERT INTO "items" ("id", "class_id", "name", "description", "type", "element", "file", "link", "icon", "equipment", "level", "dps", "range", "rarity", "quantity", "stack", "cost", "coins", "diamonds", "crystal", "sell", "market", "temporary", "upgrade", "staff", "enh_id", "faction_id", "req_reputation", "req_class_id", "req_class_points", "req_quests", "quest_string_index", "quest_string_value", "meta", "color") VALUES (53, NULL, 'Beta Berserker Armor', 'Special Berserker Armor available only to characters who played in the Beta version!', 'Armor', 'None', 'warrior2a_skin.swf', 'Warrior2a', 'iwarmor', 'co', 1, 25, 50, 35, 1, 1, 100000, 0, 0, '0', true, false, false, false, false, 1, NULL, '0', NULL, '0', '', -1, 0, '', 'ffffff');
INSERT INTO "items" ("id", "class_id", "name", "description", "type", "element", "file", "link", "icon", "equipment", "level", "dps", "range", "rarity", "quantity", "stack", "cost", "coins", "diamonds", "crystal", "sell", "market", "temporary", "upgrade", "staff", "enh_id", "faction_id", "req_reputation", "req_class_id", "req_class_points", "req_quests", "quest_string_index", "quest_string_value", "meta", "color") VALUES (54, NULL, 'Icy Blade Of Doom', 'Even Evil heroes (maybe ESPECIALLY Evil heroes) need to chill out after battle. With your Icy Blade of Doom;you can cool off without sacrificing that battle-ready edge.', 'Sword', 'None', 'items/swords/IcyBladeofDoom.swf', 'IcyBladeofDoom', 'iwsword', 'Weapon', 1, 25, 50, 30, 1, 1, 450, 1, 0, '0', true, true, false, false, false, 1, NULL, '0', NULL, '0', '', -1, 0, '', 'ffffff');
INSERT INTO "items" ("id", "class_id", "name", "description", "type", "element", "file", "link", "icon", "equipment", "level", "dps", "range", "rarity", "quantity", "stack", "cost", "coins", "diamonds", "crystal", "sell", "market", "temporary", "upgrade", "staff", "enh_id", "faction_id", "req_reputation", "req_class_id", "req_class_points", "req_quests", "quest_string_index", "quest_string_value", "meta", "color") VALUES (55, NULL, 'Molten GreatSword', 'Pyroclastic necromancers walk where the faint-hearted fear to tread;seeking undead minions among those who''ve perished in the hearts of volcanic lands.', 'Sword', 'None', 'items/swords/MoltenCalamityGreatSword.swf', 'MoltenCalamityGreatSword', 'iwsword', 'Weapon', 1, 25, 50, 30, 1, 1, 450, 1, 0, '0', true, true, false, false, false, 1, NULL, '0', NULL, '0', '', -1, 0, '', 'ffffff');
INSERT INTO "items" ("id", "class_id", "name", "description", "type", "element", "file", "link", "icon", "equipment", "level", "dps", "range", "rarity", "quantity", "stack", "cost", "coins", "diamonds", "crystal", "sell", "market", "temporary", "upgrade", "staff", "enh_id", "faction_id", "req_reputation", "req_class_id", "req_class_points", "req_quests", "quest_string_index", "quest_string_value", "meta", "color") VALUES (56, NULL, 'Riptide Blade', 'Everybody needs to get away after a long session of waging war;boss battles;and hauling home loot… and a hidden island retreat is the BEST place to /rest. recoup;and re-energize.', 'Sword', 'None', 'items/swords/Riptide.swf', 'Riptide', 'iwsword', 'Weapon', 1, 25, 50, 30, 1, 1, 75000, 0, 0, '0', true, true, false, false, false, 1, NULL, '0', NULL, '0', '', -1, 0, '', 'ffffff');
INSERT INTO "items" ("id", "class_id", "name", "description", "type", "element", "file", "link", "icon", "equipment", "level", "dps", "range", "rarity", "quantity", "stack", "cost", "coins", "diamonds", "crystal", "sell", "market", "temporary", "upgrade", "staff", "enh_id", "faction_id", "req_reputation", "req_class_id", "req_class_points", "req_quests", "quest_string_index", "quest_string_value", "meta", "color") VALUES (57, NULL, 'Summer Rescuer Oath Blade', 'Citizens Lifeguards are encouraged by the Crystallis Administration to follow all stated rules and procedures before attempting rescue. Failure to comply will result in censure or sanctioning.', 'Sword', 'None', 'items/swords/CrystallisSummerRescuerOath.swf', 'CrystallisSummerRescuerOath', 'iwsword', 'Weapon', 1, 25, 50, 30, 1, 1, 350, 1, 0, '0', true, true, false, false, false, 1, NULL, '0', NULL, '0', '', -1, 0, '', 'ffffff');
INSERT INTO "items" ("id", "class_id", "name", "description", "type", "element", "file", "link", "icon", "equipment", "level", "dps", "range", "rarity", "quantity", "stack", "cost", "coins", "diamonds", "crystal", "sell", "market", "temporary", "upgrade", "staff", "enh_id", "faction_id", "req_reputation", "req_class_id", "req_class_points", "req_quests", "quest_string_index", "quest_string_value", "meta", "color") VALUES (58, NULL, 'Icy Blade Of Doom', 'Even Evil heroes (maybe ESPECIALLY Evil heroes) need to chill out after battle. With your Icy Blade of Doom;you can cool off without sacrificing that battle-ready edge.', 'Dagger', 'None', 'items/daggers/IcyBladeofDoom.swf', 'IcyBladeofDoom', 'iwdagger', 'Weapon', 1, 25, 50, 30, 1, 1, 100000, 0, 0, '0', true, true, false, true, false, 1, NULL, '0', NULL, '0', '', -1, 0, '', 'ffffff');
INSERT INTO "items" ("id", "class_id", "name", "description", "type", "element", "file", "link", "icon", "equipment", "level", "dps", "range", "rarity", "quantity", "stack", "cost", "coins", "diamonds", "crystal", "sell", "market", "temporary", "upgrade", "staff", "enh_id", "faction_id", "req_reputation", "req_class_id", "req_class_points", "req_quests", "quest_string_index", "quest_string_value", "meta", "color") VALUES (461, NULL, 'Map Fragment', 'Quest Item!', 'QuestItem', 'None', 'iidesign', 'iidesign', 'iidesign', 'None', 1, 25, 50, 11, 1, 5, 0, 0, 0, '0', true, false, false, false, false, 1, NULL, '0', NULL, '0', '', -1, 0, '', 'ffffff');
INSERT INTO "items" ("id", "class_id", "name", "description", "type", "element", "file", "link", "icon", "equipment", "level", "dps", "range", "rarity", "quantity", "stack", "cost", "coins", "diamonds", "crystal", "sell", "market", "temporary", "upgrade", "staff", "enh_id", "faction_id", "req_reputation", "req_class_id", "req_class_points", "req_quests", "quest_string_index", "quest_string_value", "meta", "color") VALUES (59, NULL, 'Riptide Blade', 'Everybody needs to get away after a long session of waging war;boss battles;and hauling home loot… and a hidden island retreat is the BEST place to /rest. recoup;and re-energize.', 'Dagger', 'None', 'items/daggers/Riptide.swf', 'Riptide', 'iwdagger', 'Weapon', 1, 25, 50, 30, 1, 1, 75000, 0, 0, '0', true, true, false, true, false, 1, NULL, '0', NULL, '0', '', -1, 0, '', 'ffffff');
INSERT INTO "items" ("id", "class_id", "name", "description", "type", "element", "file", "link", "icon", "equipment", "level", "dps", "range", "rarity", "quantity", "stack", "cost", "coins", "diamonds", "crystal", "sell", "market", "temporary", "upgrade", "staff", "enh_id", "faction_id", "req_reputation", "req_class_id", "req_class_points", "req_quests", "quest_string_index", "quest_string_value", "meta", "color") VALUES (60, NULL, 'Molten GreatSwords', 'Pyroclastic necromancers walk where the faint-hearted fear to tread;seeking undead minions among those who''ve perished in the hearts of volcanic lands.', 'Dagger', 'None', 'items/daggers/MoltenCalamityGreatSword.swf', 'MoltenCalamityGreatSword', 'iwdagger', 'Weapon', 1, 25, 50, 30, 1, 1, 450, 1, 0, '0', true, true, false, false, false, 1, NULL, '0', NULL, '0', '', -1, 0, '', 'ffffff');
INSERT INTO "items" ("id", "class_id", "name", "description", "type", "element", "file", "link", "icon", "equipment", "level", "dps", "range", "rarity", "quantity", "stack", "cost", "coins", "diamonds", "crystal", "sell", "market", "temporary", "upgrade", "staff", "enh_id", "faction_id", "req_reputation", "req_class_id", "req_class_points", "req_quests", "quest_string_index", "quest_string_value", "meta", "color") VALUES (61, NULL, 'Claws of the Pyroclastic Necromancer', 'Pyroclastic necromancers walk where the faint-hearted fear to tread;seeking undead minions among those who''ve perished in the hearts of volcanic lands.', 'Gauntlet', 'None', 'items/gauntlets/HandofCalamity.swf', 'HandofCalamity', 'iwclaws', 'Weapon', 1, 25, 50, 30, 1, 1, 400, 1, 0, '0', true, true, false, false, false, 1, NULL, '0', NULL, '0', '', -1, 0, '', 'ffffff');
INSERT INTO "items" ("id", "class_id", "name", "description", "type", "element", "file", "link", "icon", "equipment", "level", "dps", "range", "rarity", "quantity", "stack", "cost", "coins", "diamonds", "crystal", "sell", "market", "temporary", "upgrade", "staff", "enh_id", "faction_id", "req_reputation", "req_class_id", "req_class_points", "req_quests", "quest_string_index", "quest_string_value", "meta", "color") VALUES (62, NULL, 'Claws of the Pyroclastic Necromancer', 'Pyroclastic necromancers walk where the faint-hearted fear to tread;seeking undead minions among those who''ve perished in the hearts of volcanic lands.', 'Gauntlet', 'None', 'items/gauntlets/ClawsofCalamity.swf', 'ClawsofCalamity', 'iwclaws', 'Weapon', 1, 25, 50, 30, 1, 1, 400, 1, 0, '0', true, true, false, false, false, 1, NULL, '0', NULL, '0', '', -1, 0, '', 'ffffff');
INSERT INTO "items" ("id", "class_id", "name", "description", "type", "element", "file", "link", "icon", "equipment", "level", "dps", "range", "rarity", "quantity", "stack", "cost", "coins", "diamonds", "crystal", "sell", "market", "temporary", "upgrade", "staff", "enh_id", "faction_id", "req_reputation", "req_class_id", "req_class_points", "req_quests", "quest_string_index", "quest_string_value", "meta", "color") VALUES (63, NULL, 'Crystallis Summer Salvation', 'Citizens Lifeguards are encouraged by the Crystallis Administration to follow all stated rules and procedures before attempting rescue. Failure to comply will result in censure or sanctioning.', 'Gauntlet', 'None', 'items/gauntlets/CrystallisSummerSaver.swf', 'CrystallisSummerSaver', 'iwdagger', 'Weapon', 1, 25, 50, 30, 1, 1, 350, 1, 0, '0', true, true, false, false, false, 1, NULL, '0', NULL, '0', '', -1, 0, '', 'ffffff');
INSERT INTO "items" ("id", "class_id", "name", "description", "type", "element", "file", "link", "icon", "equipment", "level", "dps", "range", "rarity", "quantity", "stack", "cost", "coins", "diamonds", "crystal", "sell", "market", "temporary", "upgrade", "staff", "enh_id", "faction_id", "req_reputation", "req_class_id", "req_class_points", "req_quests", "quest_string_index", "quest_string_value", "meta", "color") VALUES (64, NULL, 'Crystallis Summer Salvation', 'Citizens Lifeguards are encouraged by the Crystallis Administration to follow all stated rules and procedures before attempting rescue. Failure to comply will result in censure or sanctioning.', 'Gauntlet', 'None', 'items/gauntlets/CrystallisSummerSaver.swf', 'CrystallisSummerSaver', 'iwdagger', 'Weapon', 1, 25, 50, 30, 1, 1, 75000, 0, 0, '0', true, true, false, true, false, 1, NULL, '0', NULL, '0', '', -1, 0, '', 'ffffff');
INSERT INTO "items" ("id", "class_id", "name", "description", "type", "element", "file", "link", "icon", "equipment", "level", "dps", "range", "rarity", "quantity", "stack", "cost", "coins", "diamonds", "crystal", "sell", "market", "temporary", "upgrade", "staff", "enh_id", "faction_id", "req_reputation", "req_class_id", "req_class_points", "req_quests", "quest_string_index", "quest_string_value", "meta", "color") VALUES (65, NULL, 'Pyroclastic Necromancer''s Whip', 'Pyroclastic necromancers walk where the faint-hearted fear to tread;seeking undead minions among those who''ve perished in the hearts of volcanic lands.', 'Mace', 'None', 'items/maces/CalamityWhip.swf', 'CalamityWhip', 'iwmace', 'Weapon', 1, 25, 50, 30, 1, 1, 350, 1, 0, '0', true, true, false, false, false, 1, NULL, '0', NULL, '0', '', -1, 0, '', 'ffffff');
INSERT INTO "items" ("id", "class_id", "name", "description", "type", "element", "file", "link", "icon", "equipment", "level", "dps", "range", "rarity", "quantity", "stack", "cost", "coins", "diamonds", "crystal", "sell", "market", "temporary", "upgrade", "staff", "enh_id", "faction_id", "req_reputation", "req_class_id", "req_class_points", "req_quests", "quest_string_index", "quest_string_value", "meta", "color") VALUES (66, NULL, 'Bow of Embers', 'This bow as discovered by a Pyroclastic Necromancer as they journeyed deep into the heart of a volcano;hunting for new minions.', 'Gun', 'None', 'items/bows/BowofEmbers.swf', 'BowofEmbers', 'iwbow', 'Weapon', 1, 25, 50, 30, 1, 1, 350, 1, 0, '0', true, true, false, false, false, 1, NULL, '0', NULL, '0', '', -1, 0, '', 'ffffff');
INSERT INTO "items" ("id", "class_id", "name", "description", "type", "element", "file", "link", "icon", "equipment", "level", "dps", "range", "rarity", "quantity", "stack", "cost", "coins", "diamonds", "crystal", "sell", "market", "temporary", "upgrade", "staff", "enh_id", "faction_id", "req_reputation", "req_class_id", "req_class_points", "req_quests", "quest_string_index", "quest_string_value", "meta", "color") VALUES (67, NULL, 'Oceanic Symphony Cane', 'Still waters run deep;but those who chance to adventure below will find a world of magic;mystery;and beauty.', 'Staff', 'None', 'items/staves/ClassyOceanCane.swf', 'ClassyOceanCane', 'iwstaff', 'Weapon', 1, 25, 50, 30, 1, 1, 50000, 0, 0, '0', true, true, false, false, false, 1, NULL, '0', NULL, '0', '', -1, 0, '', 'ffffff');
INSERT INTO "items" ("id", "class_id", "name", "description", "type", "element", "file", "link", "icon", "equipment", "level", "dps", "range", "rarity", "quantity", "stack", "cost", "coins", "diamonds", "crystal", "sell", "market", "temporary", "upgrade", "staff", "enh_id", "faction_id", "req_reputation", "req_class_id", "req_class_points", "req_quests", "quest_string_index", "quest_string_value", "meta", "color") VALUES (68, NULL, 'Pyroclastic Necromancer', 'Pyroclastic necromancers walk where the faint-hearted fear to tread;seeking undead minions among those who''ve perished in the hearts of volcanic lands.', 'Armor', 'None', 'CalamityNecromancer.swf', 'CalamityNecromancer', 'iwarmor', 'co', 1, 25, 50, 30, 1, 1, 1000, 1, 0, '0', true, true, false, false, false, 1, NULL, '0', NULL, '0', '', -1, 0, '', 'ffffff');
INSERT INTO "items" ("id", "class_id", "name", "description", "type", "element", "file", "link", "icon", "equipment", "level", "dps", "range", "rarity", "quantity", "stack", "cost", "coins", "diamonds", "crystal", "sell", "market", "temporary", "upgrade", "staff", "enh_id", "faction_id", "req_reputation", "req_class_id", "req_class_points", "req_quests", "quest_string_index", "quest_string_value", "meta", "color") VALUES (69, NULL, 'Oceanic Symphony Armor', 'Still waters run deep;but those who chance to adventure below will find a world of magic;mystery;and beauty.', 'Armor', 'None', 'ClassyOcean.swf', 'ClassyOcean', 'iwarmor', 'co', 1, 25, 50, 55, 1, 1, 750, 1, 0, '0', true, true, false, false, false, 1, NULL, '0', NULL, '0', '', -1, 0, '', 'ffffff');
INSERT INTO "items" ("id", "class_id", "name", "description", "type", "element", "file", "link", "icon", "equipment", "level", "dps", "range", "rarity", "quantity", "stack", "cost", "coins", "diamonds", "crystal", "sell", "market", "temporary", "upgrade", "staff", "enh_id", "faction_id", "req_reputation", "req_class_id", "req_class_points", "req_quests", "quest_string_index", "quest_string_value", "meta", "color") VALUES (70, NULL, 'Crystallis Casual Rescuer', 'The design of this official off-duty Citizen Lifeguard gear has been approved by the Department of Outfit Inspection and Couture Correction. (This item is color-customizable to trim and accessory)', 'Armor', 'None', 'CrystallisRescuerCasual.swf', 'CrystallisRescuerCasual', 'iwarmor', 'co', 1, 25, 50, 30, 1, 1, 1000, 1, 0, '0', true, true, false, false, false, 1, NULL, '0', NULL, '0', '', -1, 0, '', 'ffffff');
INSERT INTO "items" ("id", "class_id", "name", "description", "type", "element", "file", "link", "icon", "equipment", "level", "dps", "range", "rarity", "quantity", "stack", "cost", "coins", "diamonds", "crystal", "sell", "market", "temporary", "upgrade", "staff", "enh_id", "faction_id", "req_reputation", "req_class_id", "req_class_points", "req_quests", "quest_string_index", "quest_string_value", "meta", "color") VALUES (71, NULL, 'Crystallis Summer Rescuer', 'Citizens Lifeguards are encouraged by the Crystallis Administration to follow all stated rules and procedures before attempting rescue. Failure to comply will result in censure or sanctioning.', 'Armor', 'None', 'CrystallisSummerRescuer1.swf', 'CrystallisSummerRescuer', 'iwarmor', 'co', 1, 25, 50, 30, 1, 1, 150000, 0, 0, '0', true, true, false, true, false, 1, NULL, '0', NULL, '0', '', -1, 0, '', 'ffffff');
INSERT INTO "items" ("id", "class_id", "name", "description", "type", "element", "file", "link", "icon", "equipment", "level", "dps", "range", "rarity", "quantity", "stack", "cost", "coins", "diamonds", "crystal", "sell", "market", "temporary", "upgrade", "staff", "enh_id", "faction_id", "req_reputation", "req_class_id", "req_class_points", "req_quests", "quest_string_index", "quest_string_value", "meta", "color") VALUES (72, NULL, 'Crystallis Summer Rescuer', 'Citizens Lifeguards are encouraged by the Crystallis Administration to follow all stated rules and procedures before attempting rescue. Failure to comply will result in censure or sanctioning.', 'Armor', 'None', 'CrystallisSummerRescuer1.swf', 'CrystallisSummerRescuer', 'iwarmor', 'co', 1, 25, 50, 30, 1, 1, 1000, 1, 0, '0', true, true, false, false, false, 1, NULL, '0', NULL, '0', '', -1, 0, '', 'ffffff');
INSERT INTO "items" ("id", "class_id", "name", "description", "type", "element", "file", "link", "icon", "equipment", "level", "dps", "range", "rarity", "quantity", "stack", "cost", "coins", "diamonds", "crystal", "sell", "market", "temporary", "upgrade", "staff", "enh_id", "faction_id", "req_reputation", "req_class_id", "req_class_points", "req_quests", "quest_string_index", "quest_string_value", "meta", "color") VALUES (73, NULL, 'Island Retreat Outfit', 'Everybody needs to get away after a long season of waging war;boss battles;and hauling home loot… and a hidden island retreat is the BEST place to /rent;recoup;and re-energize. (Item is color-customizable to base and trim)', 'Armor', 'None', 'LoneIslander1.swf', 'LoneIslander', 'iwarmor', 'co', 1, 25, 50, 30, 1, 1, 900, 1, 0, '0', true, true, false, false, false, 1, NULL, '0', NULL, '0', '', -1, 0, '', 'ffffff');
INSERT INTO "items" ("id", "class_id", "name", "description", "type", "element", "file", "link", "icon", "equipment", "level", "dps", "range", "rarity", "quantity", "stack", "cost", "coins", "diamonds", "crystal", "sell", "market", "temporary", "upgrade", "staff", "enh_id", "faction_id", "req_reputation", "req_class_id", "req_class_points", "req_quests", "quest_string_index", "quest_string_value", "meta", "color") VALUES (74, NULL, 'Prismatic Starry Swimsuit', 'As you stare across the water;stars reflect on its surface;an ocean of diamond-like points of light. This suit lets you capture the magic of that moment. (This armor is color-customizable to base)', 'Armor', 'None', 'StarrySwimsuit1CC.swf', 'StarrySwimsuit1CC', 'iwarmor', 'co', 1, 25, 50, 30, 1, 1, 850, 1, 0, '0', true, true, false, false, false, 1, NULL, '0', NULL, '0', '', -1, 0, '', 'ffffff');
INSERT INTO "items" ("id", "class_id", "name", "description", "type", "element", "file", "link", "icon", "equipment", "level", "dps", "range", "rarity", "quantity", "stack", "cost", "coins", "diamonds", "crystal", "sell", "market", "temporary", "upgrade", "staff", "enh_id", "faction_id", "req_reputation", "req_class_id", "req_class_points", "req_quests", "quest_string_index", "quest_string_value", "meta", "color") VALUES (75, NULL, 'Prismatic Starry Swimwear', 'As you stare across the water;stars reflect on its surface;an ocean of diamond-like points of light. This suit lets you capture the magic of that moment. (This armor is color-customizable to base).', 'Armor', 'None', 'StarrySwimsuit2CC.swf', 'StarrySwimsuit2CC', 'iwarmor', 'co', 1, 25, 50, 30, 1, 1, 850, 1, 0, '0', true, true, false, false, false, 1, NULL, '0', NULL, '0', '', -1, 0, '', 'ffffff');
INSERT INTO "items" ("id", "class_id", "name", "description", "type", "element", "file", "link", "icon", "equipment", "level", "dps", "range", "rarity", "quantity", "stack", "cost", "coins", "diamonds", "crystal", "sell", "market", "temporary", "upgrade", "staff", "enh_id", "faction_id", "req_reputation", "req_class_id", "req_class_points", "req_quests", "quest_string_index", "quest_string_value", "meta", "color") VALUES (76, NULL, 'Starry Swimsuit', 'As you stare across the water;stars reflect on its surface;an ocean of diamond-like points of light. This suit lets you capture the magic of that moment.', 'Armor', 'None', 'StarrySwimsuit1r2.swf', 'StarrySwimsuit1', 'iwarmor', 'co', 1, 25, 50, 30, 1, 1, 750, 1, 0, '0', true, true, false, false, false, 1, NULL, '0', NULL, '0', '', -1, 0, '', 'ffffff');
INSERT INTO "items" ("id", "class_id", "name", "description", "type", "element", "file", "link", "icon", "equipment", "level", "dps", "range", "rarity", "quantity", "stack", "cost", "coins", "diamonds", "crystal", "sell", "market", "temporary", "upgrade", "staff", "enh_id", "faction_id", "req_reputation", "req_class_id", "req_class_points", "req_quests", "quest_string_index", "quest_string_value", "meta", "color") VALUES (77, NULL, 'Starry Swimwear', 'As you stare across the water;stars reflect on its surface;an ocean of diamond-like points of light. This suit lets you capture the magic of that moment.', 'Armor', 'None', 'StarrySwimsuit2r4.swf', 'StarrySwimsuit2', 'iwarmor', 'co', 1, 25, 50, 30, 1, 1, 750, 1, 0, '0', true, true, false, false, false, 1, NULL, '0', NULL, '0', '', -1, 0, '', 'ffffff');
INSERT INTO "items" ("id", "class_id", "name", "description", "type", "element", "file", "link", "icon", "equipment", "level", "dps", "range", "rarity", "quantity", "stack", "cost", "coins", "diamonds", "crystal", "sell", "market", "temporary", "upgrade", "staff", "enh_id", "faction_id", "req_reputation", "req_class_id", "req_class_points", "req_quests", "quest_string_index", "quest_string_value", "meta", "color") VALUES (78, NULL, 'Pyroclastic Necromancer''s Bangs', 'Pyroclastic necromancers walk where the faint-hearted fear to tread;seeking undead minions among those who''ve perished in the hearts of volcanic lands.', 'Helm', 'None', 'items/helms/CalamityBangs.swf', 'CalamityBangs', 'iihelm', 'he', 1, 25, 50, 30, 1, 1, 25000, 0, 0, '0', true, true, false, false, false, 1, NULL, '0', NULL, '0', '', -1, 0, '', 'ffffff');
INSERT INTO "items" ("id", "class_id", "name", "description", "type", "element", "file", "link", "icon", "equipment", "level", "dps", "range", "rarity", "quantity", "stack", "cost", "coins", "diamonds", "crystal", "sell", "market", "temporary", "upgrade", "staff", "enh_id", "faction_id", "req_reputation", "req_class_id", "req_class_points", "req_quests", "quest_string_index", "quest_string_value", "meta", "color") VALUES (79, NULL, 'Pyroclastic Necromancer''s Hair', 'Pyroclastic necromancers walk where the faint-hearted fear to tread;seeking undead minions among those who''ve perished in the hearts of volcanic lands.', 'Helm', 'None', 'items/helms/CalamityHair.swf', 'CalamityHair', 'iihelm', 'he', 1, 25, 50, 30, 1, 1, 25000, 0, 0, '0', true, true, false, false, false, 1, NULL, '0', NULL, '0', '', -1, 0, '', 'ffffff');
INSERT INTO "items" ("id", "class_id", "name", "description", "type", "element", "file", "link", "icon", "equipment", "level", "dps", "range", "rarity", "quantity", "stack", "cost", "coins", "diamonds", "crystal", "sell", "market", "temporary", "upgrade", "staff", "enh_id", "faction_id", "req_reputation", "req_class_id", "req_class_points", "req_quests", "quest_string_index", "quest_string_value", "meta", "color") VALUES (80, NULL, 'Pyroclastic Necromancer''s Hood', 'Pyroclastic necromancers walk where the faint-hearted fear to tread;seeking undead minions among those who''ve perished in the hearts of volcanic lands.', 'Helm', 'None', 'items/helms/CalamityHood.swf', 'CalamityHood', 'iihelm', 'he', 1, 25, 50, 30, 1, 1, 45000, 0, 0, '0', true, true, false, false, false, 1, NULL, '0', NULL, '0', '', -1, 0, '', 'ffffff');
INSERT INTO "items" ("id", "class_id", "name", "description", "type", "element", "file", "link", "icon", "equipment", "level", "dps", "range", "rarity", "quantity", "stack", "cost", "coins", "diamonds", "crystal", "sell", "market", "temporary", "upgrade", "staff", "enh_id", "faction_id", "req_reputation", "req_class_id", "req_class_points", "req_quests", "quest_string_index", "quest_string_value", "meta", "color") VALUES (81, NULL, 'Pyroclastic Female Visage', 'Pyroclastic necromancers walk where the faint-hearted fear to tread;seeking undead minions among those who''ve perished in the hearts of volcanic lands.', 'Helm', 'None', 'items/helms/CalamityVisageF.swf', 'CalamityVisageF', 'iihelm', 'he', 1, 25, 50, 30, 1, 1, 25000, 0, 0, '0', true, true, false, true, false, 1, NULL, '0', NULL, '0', '', -1, 0, '', 'ffffff');
INSERT INTO "items" ("id", "class_id", "name", "description", "type", "element", "file", "link", "icon", "equipment", "level", "dps", "range", "rarity", "quantity", "stack", "cost", "coins", "diamonds", "crystal", "sell", "market", "temporary", "upgrade", "staff", "enh_id", "faction_id", "req_reputation", "req_class_id", "req_class_points", "req_quests", "quest_string_index", "quest_string_value", "meta", "color") VALUES (82, NULL, 'Pyroclastic Male Visage', 'Pyroclastic necromancers walk where the faint-hearted fear to tread;seeking undead minions among those who''ve perished in the hearts of volcanic lands.', 'Helm', 'None', 'items/helms/CalamityVisageM.swf', 'CalamityVisageM', 'iihelm', 'he', 1, 25, 50, 30, 1, 1, 25000, 0, 0, '0', true, true, false, true, false, 1, NULL, '0', NULL, '0', '', -1, 0, '', 'ffffff');
INSERT INTO "items" ("id", "class_id", "name", "description", "type", "element", "file", "link", "icon", "equipment", "level", "dps", "range", "rarity", "quantity", "stack", "cost", "coins", "diamonds", "crystal", "sell", "market", "temporary", "upgrade", "staff", "enh_id", "faction_id", "req_reputation", "req_class_id", "req_class_points", "req_quests", "quest_string_index", "quest_string_value", "meta", "color") VALUES (83, NULL, 'Pyroclastic Necromancer''s Rage', 'Pyroclastic necromancers walk where the faint-hearted fear to tread;seeking undead minions among those who''ve perished in the hearts of volcanic lands.', 'Helm', 'None', 'items/helms/ScreamofCalamity.swf', 'ScreamofCalamity', 'iihelm', 'he', 1, 25, 50, 30, 1, 1, 300, 1, 0, '0', true, true, false, false, false, 1, NULL, '0', NULL, '0', '', -1, 0, '', 'ffffff');
INSERT INTO "items" ("id", "class_id", "name", "description", "type", "element", "file", "link", "icon", "equipment", "level", "dps", "range", "rarity", "quantity", "stack", "cost", "coins", "diamonds", "crystal", "sell", "market", "temporary", "upgrade", "staff", "enh_id", "faction_id", "req_reputation", "req_class_id", "req_class_points", "req_quests", "quest_string_index", "quest_string_value", "meta", "color") VALUES (84, NULL, 'Chill Island Lass'' Hat', 'Everybody need to get away after a long season of waging war;boss battles;and hauling home loot… and a hidden island retreat is the BEST place to /rest;recoup;and re-energize.', 'Helm', 'None', 'items/helms/ChillLoneIslanderStrawHatF.swf', 'ChillLoneIslanderStrawHatF', 'iihelm', 'he', 1, 25, 50, 30, 1, 1, 250, 1, 0, '0', true, true, false, false, false, 1, NULL, '0', NULL, '0', '', -1, 0, '', 'ffffff');
INSERT INTO "items" ("id", "class_id", "name", "description", "type", "element", "file", "link", "icon", "equipment", "level", "dps", "range", "rarity", "quantity", "stack", "cost", "coins", "diamonds", "crystal", "sell", "market", "temporary", "upgrade", "staff", "enh_id", "faction_id", "req_reputation", "req_class_id", "req_class_points", "req_quests", "quest_string_index", "quest_string_value", "meta", "color") VALUES (85, NULL, 'Chill Island Lad''s Hat', 'Everybody need to get away after a long season of waging war;boss battles;and hauling home loot… and a hidden island retreat is the BEST place to /rest;recoup;and re-energize.', 'Helm', 'None', 'items/helms/ChillLoneIslanderStrawHatM.swf', 'ChillLoneIslanderStrawHatM', 'iihelm', 'he', 1, 25, 50, 30, 1, 1, 250, 1, 0, '0', true, true, false, false, false, 1, NULL, '0', NULL, '0', '', -1, 0, '', 'ffffff');
INSERT INTO "items" ("id", "class_id", "name", "description", "type", "element", "file", "link", "icon", "equipment", "level", "dps", "range", "rarity", "quantity", "stack", "cost", "coins", "diamonds", "crystal", "sell", "market", "temporary", "upgrade", "staff", "enh_id", "faction_id", "req_reputation", "req_class_id", "req_class_points", "req_quests", "quest_string_index", "quest_string_value", "meta", "color") VALUES (86, NULL, 'Oceanic Accessories Top Hat + Locks', 'Still waters run deep;but those who chance to adventure below will find a world of magic;mystery;and beauty.', 'Helm', 'None', 'items/helms/ClassyOceanHatF.swf', 'ClassyOceanHatF', 'iihelm', 'he', 1, 25, 50, 55, 1, 1, 200, 1, 0, '0', true, true, false, false, false, 1, NULL, '0', NULL, '0', '', -1, 0, '', 'ffffff');
INSERT INTO "items" ("id", "class_id", "name", "description", "type", "element", "file", "link", "icon", "equipment", "level", "dps", "range", "rarity", "quantity", "stack", "cost", "coins", "diamonds", "crystal", "sell", "market", "temporary", "upgrade", "staff", "enh_id", "faction_id", "req_reputation", "req_class_id", "req_class_points", "req_quests", "quest_string_index", "quest_string_value", "meta", "color") VALUES (87, NULL, 'Oceanic Accessories Top Hat', 'Still waters run deep;but those who chance to adventure below will find a world of magic;mystery;and beauty.', 'Helm', 'None', 'items/helms/ClassyOceanHatM.swf', 'ClassyOceanHatM', 'iihelm', 'he', 1, 25, 50, 55, 1, 1, 200, 1, 0, '0', true, true, false, false, false, 1, NULL, '0', NULL, '0', '', -1, 0, '', 'ffffff');
INSERT INTO "items" ("id", "class_id", "name", "description", "type", "element", "file", "link", "icon", "equipment", "level", "dps", "range", "rarity", "quantity", "stack", "cost", "coins", "diamonds", "crystal", "sell", "market", "temporary", "upgrade", "staff", "enh_id", "faction_id", "req_reputation", "req_class_id", "req_class_points", "req_quests", "quest_string_index", "quest_string_value", "meta", "color") VALUES (88, NULL, 'Oceanic Accessories Locks', 'Still waters run deep;but those who chance to adventure below will find a world of magic;mystery;and beauty.', 'Helm', 'None', 'items/helms/ClassyOceanHairF.swf', 'ClassyOceanHairF', 'iihelm', 'he', 1, 25, 50, 55, 1, 1, 200, 1, 0, '0', true, true, false, false, false, 1, NULL, '0', NULL, '0', '', -1, 0, '', 'ffffff');
INSERT INTO "items" ("id", "class_id", "name", "description", "type", "element", "file", "link", "icon", "equipment", "level", "dps", "range", "rarity", "quantity", "stack", "cost", "coins", "diamonds", "crystal", "sell", "market", "temporary", "upgrade", "staff", "enh_id", "faction_id", "req_reputation", "req_class_id", "req_class_points", "req_quests", "quest_string_index", "quest_string_value", "meta", "color") VALUES (89, NULL, 'Oceanic Accessories Hair', 'Still waters run deep;but those who chance to adventure below will find a world of magic;mystery;and beauty.', 'Helm', 'None', 'items/helms/ClassyOceanHairM.swf', 'ClassyOceanHairM', 'iihelm', 'he', 1, 25, 50, 55, 1, 1, 200, 1, 0, '0', true, true, false, false, false, 1, NULL, '0', NULL, '0', '', -1, 0, '', 'ffffff');
INSERT INTO "items" ("id", "class_id", "name", "description", "type", "element", "file", "link", "icon", "equipment", "level", "dps", "range", "rarity", "quantity", "stack", "cost", "coins", "diamonds", "crystal", "sell", "market", "temporary", "upgrade", "staff", "enh_id", "faction_id", "req_reputation", "req_class_id", "req_class_points", "req_quests", "quest_string_index", "quest_string_value", "meta", "color") VALUES (90, NULL, 'Island Retreat Hair', 'Everybody needs to get away after a long season of waging war;boss battles;and hauling home loot… and a hidden island retreat is the BEST place to /rest;recoup;and re-energize.', 'Helm', 'None', 'items/helms/LoneIslanderHair.swf', 'LoneIslanderHair', 'iihelm', 'he', 1, 25, 50, 30, 1, 1, 25000, 0, 0, '0', true, true, false, false, false, 1, NULL, '0', NULL, '0', '', -1, 0, '', 'ffffff');
INSERT INTO "items" ("id", "class_id", "name", "description", "type", "element", "file", "link", "icon", "equipment", "level", "dps", "range", "rarity", "quantity", "stack", "cost", "coins", "diamonds", "crystal", "sell", "market", "temporary", "upgrade", "staff", "enh_id", "faction_id", "req_reputation", "req_class_id", "req_class_points", "req_quests", "quest_string_index", "quest_string_value", "meta", "color") VALUES (91, NULL, 'Island Retreat Lad''s Hat', 'Everybody needs to get away after a long season of waging war;boss battles;and hauling home loot… and a hidden island retreat is the BEST place to /rest;recoup;and re-energize.', 'Helm', 'None', 'items/helms/LoneIslanderStrawHatM.swf', 'LoneIslanderStrawHatM', 'iihelm', 'he', 1, 25, 50, 30, 1, 1, 350000, 0, 0, '0', true, true, false, true, false, 1, NULL, '0', NULL, '0', '', -1, 0, '', 'ffffff');
INSERT INTO "items" ("id", "class_id", "name", "description", "type", "element", "file", "link", "icon", "equipment", "level", "dps", "range", "rarity", "quantity", "stack", "cost", "coins", "diamonds", "crystal", "sell", "market", "temporary", "upgrade", "staff", "enh_id", "faction_id", "req_reputation", "req_class_id", "req_class_points", "req_quests", "quest_string_index", "quest_string_value", "meta", "color") VALUES (92, NULL, 'Island Retreat Lass'' Hat', 'Everybody needs to get away after a long season of waging war;boss battles;and hauling home loot… and a hidden island retreat is the BEST place to /rest;recoup;and re-energize.', 'Helm', 'None', 'items/helms/LoneIslanderStrawHatF.swf', 'LoneIslanderStrawHatF', 'iihelm', 'he', 1, 25, 50, 30, 1, 1, 35000, 0, 0, '0', true, true, false, true, false, 1, NULL, '0', NULL, '0', '', -1, 0, '', 'ffffff');
INSERT INTO "items" ("id", "class_id", "name", "description", "type", "element", "file", "link", "icon", "equipment", "level", "dps", "range", "rarity", "quantity", "stack", "cost", "coins", "diamonds", "crystal", "sell", "market", "temporary", "upgrade", "staff", "enh_id", "faction_id", "req_reputation", "req_class_id", "req_class_points", "req_quests", "quest_string_index", "quest_string_value", "meta", "color") VALUES (93, NULL, 'Island Retreat Locks', 'Everybody needs to get away after a long season of waging war;boss battles;and hauling home loot… and a hidden island retreat is the BEST place to /rest;recoup;and re-energize.', 'Helm', 'None', 'items/helms/LoneIslanderLocks.swf', 'LoneIslanderLocks', 'iihelm', 'he', 1, 25, 50, 30, 1, 1, 25000, 0, 0, '0', true, true, false, false, false, 1, NULL, '0', NULL, '0', '', -1, 0, '', 'ffffff');
INSERT INTO "items" ("id", "class_id", "name", "description", "type", "element", "file", "link", "icon", "equipment", "level", "dps", "range", "rarity", "quantity", "stack", "cost", "coins", "diamonds", "crystal", "sell", "market", "temporary", "upgrade", "staff", "enh_id", "faction_id", "req_reputation", "req_class_id", "req_class_points", "req_quests", "quest_string_index", "quest_string_value", "meta", "color") VALUES (94, NULL, 'Island Retreat Morph Locks', 'Everybody needs to get away after a long season of waging war;boss battles;and hauling home loot… and a hidden island retreat is the BEST place to /rest;recoup;and re-energize.', 'Helm', 'None', 'items/helms/LoneIslanderMorphF.swf', 'LoneIslanderMorphF', 'iihelm', 'he', 1, 25, 50, 30, 1, 1, 200, 1, 0, '0', true, true, false, false, false, 1, NULL, '0', NULL, '0', '', -1, 0, '', 'ffffff');
INSERT INTO "items" ("id", "class_id", "name", "description", "type", "element", "file", "link", "icon", "equipment", "level", "dps", "range", "rarity", "quantity", "stack", "cost", "coins", "diamonds", "crystal", "sell", "market", "temporary", "upgrade", "staff", "enh_id", "faction_id", "req_reputation", "req_class_id", "req_class_points", "req_quests", "quest_string_index", "quest_string_value", "meta", "color") VALUES (95, NULL, 'Island Retreat Morph Hair', 'Everybody needs to get away after a long season of waging war;boss battles;and hauling home loot… and a hidden island retreat is the BEST place to /rest;recoup;and re-energize.', 'Helm', 'None', 'items/helms/LoneIslanderMorphM.swf', 'LoneIslanderMorphM', 'iihelm', 'he', 1, 25, 50, 30, 1, 1, 200, 1, 0, '0', true, true, false, false, false, 1, NULL, '0', NULL, '0', '', -1, 0, '', 'ffffff');
INSERT INTO "items" ("id", "class_id", "name", "description", "type", "element", "file", "link", "icon", "equipment", "level", "dps", "range", "rarity", "quantity", "stack", "cost", "coins", "diamonds", "crystal", "sell", "market", "temporary", "upgrade", "staff", "enh_id", "faction_id", "req_reputation", "req_class_id", "req_class_points", "req_quests", "quest_string_index", "quest_string_value", "meta", "color") VALUES (96, NULL, 'Sand Buddy Morph', 'Everybody need to get away after a long season of waging war;boss battles;and hauling home loot… and a hidden island retreat is the BEST place to /rest;recoup;and re-energize. (Flag is color-customizable to trim)', 'Helm', 'None', 'items/helms/SandBuddyMorph.swf', 'SandBuddyMorph', 'iihelm', 'he', 1, 25, 50, 30, 1, 1, 25000, 0, 0, '0', true, true, false, false, false, 1, NULL, '0', NULL, '0', '', -1, 0, '', 'ffffff');
INSERT INTO "items" ("id", "class_id", "name", "description", "type", "element", "file", "link", "icon", "equipment", "level", "dps", "range", "rarity", "quantity", "stack", "cost", "coins", "diamonds", "crystal", "sell", "market", "temporary", "upgrade", "staff", "enh_id", "faction_id", "req_reputation", "req_class_id", "req_class_points", "req_quests", "quest_string_index", "quest_string_value", "meta", "color") VALUES (97, NULL, 'Starry Summer Hair', 'As you stare across the water;stars reflect on its surface;an ocean of diamond-like points of light. This suit lets you capture the magic of that moment.', 'Helm', 'None', 'items/helms/StarrySwimsuitHair1.swf', 'StarrySwimsuitHair1', 'iihelm', 'he', 1, 25, 50, 30, 1, 1, 150, 1, 0, '0', true, true, false, false, false, 1, NULL, '0', NULL, '0', '', -1, 0, '', 'ffffff');
INSERT INTO "items" ("id", "class_id", "name", "description", "type", "element", "file", "link", "icon", "equipment", "level", "dps", "range", "rarity", "quantity", "stack", "cost", "coins", "diamonds", "crystal", "sell", "market", "temporary", "upgrade", "staff", "enh_id", "faction_id", "req_reputation", "req_class_id", "req_class_points", "req_quests", "quest_string_index", "quest_string_value", "meta", "color") VALUES (98, NULL, 'Starry Summer Locks', 'As you stare across the water;stars reflect on its surface;an ocean of diamond-like points of light. This suit lets you capture the magic of that moment.', 'Helm', 'None', 'items/helms/StarrySwimsuitHair2.swf', 'StarrySwimsuitHair2', 'iihelm', 'he', 1, 25, 50, 30, 1, 1, 150, 1, 0, '0', true, true, false, false, false, 1, NULL, '0', NULL, '0', '', -1, 0, '', 'ffffff');
INSERT INTO "items" ("id", "class_id", "name", "description", "type", "element", "file", "link", "icon", "equipment", "level", "dps", "range", "rarity", "quantity", "stack", "cost", "coins", "diamonds", "crystal", "sell", "market", "temporary", "upgrade", "staff", "enh_id", "faction_id", "req_reputation", "req_class_id", "req_class_points", "req_quests", "quest_string_index", "quest_string_value", "meta", "color") VALUES (99, NULL, 'Crystallis Rescuer Lad''s Snorkel', 'Citizens Lifeguards are encouraged by the Crystallis Administration to follow all stated rules and procedures before attempting rescue. Failure to comply will result in censure or sanctioning. (Highlights are CC to Trim)', 'Helm', 'None', 'items/helms/CrystallisSummerRescuerSnorkelM.swf', 'CrystallisSummerRescuerSnorkelM', 'iihelm', 'he', 1, 25, 50, 30, 1, 1, 200, 1, 0, '0', true, true, false, false, false, 1, NULL, '0', NULL, '0', '', -1, 0, '', 'ffffff');
INSERT INTO "items" ("id", "class_id", "name", "description", "type", "element", "file", "link", "icon", "equipment", "level", "dps", "range", "rarity", "quantity", "stack", "cost", "coins", "diamonds", "crystal", "sell", "market", "temporary", "upgrade", "staff", "enh_id", "faction_id", "req_reputation", "req_class_id", "req_class_points", "req_quests", "quest_string_index", "quest_string_value", "meta", "color") VALUES (120, NULL, 'Sand Buddy Pet', 'Everybody needs to get away after a long season of waging war;boss battles;and hauling loot… and a hidden island retreat is the BEST place to /rest;recoup;and re-energize.', 'Pet', 'None', 'items/pets/SandBuddyPet.swf', 'SandBuddyPet', 'iipet', 'pe', 1, 25, 50, 30, 1, 1, 75000, 0, 0, '0', true, true, false, true, false, 1, NULL, '0', NULL, '0', '', -1, 0, '', 'ffffff');
INSERT INTO "items" ("id", "class_id", "name", "description", "type", "element", "file", "link", "icon", "equipment", "level", "dps", "range", "rarity", "quantity", "stack", "cost", "coins", "diamonds", "crystal", "sell", "market", "temporary", "upgrade", "staff", "enh_id", "faction_id", "req_reputation", "req_class_id", "req_class_points", "req_quests", "quest_string_index", "quest_string_value", "meta", "color") VALUES (100, NULL, 'Crystallis Rescuer Lass'' Snorkel', 'Citizens Lifeguards are encouraged by the Crystallis Administration to follow all stated rules and procedures before attempting rescue. Failure to comply will result in censure or sanctioning. (Highlights are CC to Trim)', 'Helm', 'None', 'items/helms/CrystallisSummerRescuerSnorkelF.swf', 'CrystallisSummerRescuerSnorkelF', 'iihelm', 'he', 1, 25, 50, 30, 1, 1, 200, 1, 0, '0', true, true, false, false, false, 1, NULL, '0', NULL, '0', '', -1, 0, '', 'ffffff');
INSERT INTO "items" ("id", "class_id", "name", "description", "type", "element", "file", "link", "icon", "equipment", "level", "dps", "range", "rarity", "quantity", "stack", "cost", "coins", "diamonds", "crystal", "sell", "market", "temporary", "upgrade", "staff", "enh_id", "faction_id", "req_reputation", "req_class_id", "req_class_points", "req_quests", "quest_string_index", "quest_string_value", "meta", "color") VALUES (101, NULL, 'Crystallis Rescuer Morph', 'Citizens Lifeguards are encouraged by the Crystallis Administration to follow all stated rules and procedures before attempting rescue. Failure to comply will result in censure or sanctioning. (Highlights are CC to Trim)', 'Helm', 'None', 'items/helms/CrystallisSummerRescuerMorphM.swf', 'CrystallisSummerRescuerMorphM', 'iihelm', 'he', 1, 25, 50, 30, 1, 1, 200, 1, 0, '0', true, true, false, false, false, 1, NULL, '0', NULL, '0', '', -1, 0, '', 'ffffff');
INSERT INTO "items" ("id", "class_id", "name", "description", "type", "element", "file", "link", "icon", "equipment", "level", "dps", "range", "rarity", "quantity", "stack", "cost", "coins", "diamonds", "crystal", "sell", "market", "temporary", "upgrade", "staff", "enh_id", "faction_id", "req_reputation", "req_class_id", "req_class_points", "req_quests", "quest_string_index", "quest_string_value", "meta", "color") VALUES (102, NULL, 'Crystallis Rescuer Morph + Locks', 'Citizens Lifeguards are encouraged by the Crystallis Administration to follow all stated rules and procedures before attempting rescue. Failure to comply will result in censure or sanctioning. (Highlights are CC to Trim)', 'Helm', 'None', 'items/helms/CrystallisSummerRescuerMorphF.swf', 'CrystallisSummerRescuerMorphF', 'iihelm', 'he', 1, 25, 50, 30, 1, 1, 200, 1, 0, '0', true, true, false, false, false, 1, NULL, '0', NULL, '0', '', -1, 0, '', 'ffffff');
INSERT INTO "items" ("id", "class_id", "name", "description", "type", "element", "file", "link", "icon", "equipment", "level", "dps", "range", "rarity", "quantity", "stack", "cost", "coins", "diamonds", "crystal", "sell", "market", "temporary", "upgrade", "staff", "enh_id", "faction_id", "req_reputation", "req_class_id", "req_class_points", "req_quests", "quest_string_index", "quest_string_value", "meta", "color") VALUES (103, NULL, 'Crystallis Rescuer Visor', 'Citizens Lifeguards are encouraged by the Crystallis Administration to follow all stated rules and procedures before attempting rescue. Failure to comply will result in censure or sanctioning. (Highlights are CC to Trim)', 'Helm', 'None', 'items/helms/CrystallisSummerRescuerVisor.swf', 'CrystallisSummerRescuerVisor', 'iihelm', 'he', 1, 25, 50, 30, 1, 1, 35000, 0, 0, '0', true, true, false, true, false, 1, NULL, '0', NULL, '0', '', -1, 0, '', 'ffffff');
INSERT INTO "items" ("id", "class_id", "name", "description", "type", "element", "file", "link", "icon", "equipment", "level", "dps", "range", "rarity", "quantity", "stack", "cost", "coins", "diamonds", "crystal", "sell", "market", "temporary", "upgrade", "staff", "enh_id", "faction_id", "req_reputation", "req_class_id", "req_class_points", "req_quests", "quest_string_index", "quest_string_value", "meta", "color") VALUES (104, NULL, 'Crystallis Rescuer Visor', 'Citizens Lifeguards are encouraged by the Crystallis Administration to follow all stated rules and procedures before attempting rescue. Failure to comply will result in censure or sanctioning.', 'Helm', 'None', 'items/helms/CrystallisSummerRescuerVisor.swf', 'CrystallisSummerRescuerVisor', 'iihelm', 'he', 1, 25, 50, 30, 1, 1, 200, 1, 0, '0', true, true, false, false, false, 1, NULL, '0', NULL, '0', '', -1, 0, '', 'ffffff');
INSERT INTO "items" ("id", "class_id", "name", "description", "type", "element", "file", "link", "icon", "equipment", "level", "dps", "range", "rarity", "quantity", "stack", "cost", "coins", "diamonds", "crystal", "sell", "market", "temporary", "upgrade", "staff", "enh_id", "faction_id", "req_reputation", "req_class_id", "req_class_points", "req_quests", "quest_string_index", "quest_string_value", "meta", "color") VALUES (105, NULL, 'Crystallis Summer Ponytail', 'Citizens Lifeguards are encouraged by the Crystallis Administration to follow all stated rules and procedures before attempting rescue. Failure to comply will result in censure or sanctioning. (Highlights are CC to Trim)', 'Helm', 'None', 'items/helms/CrystallisSummerRescuerLocksTied.swf', 'CrystallisSummerRescuerLocksTied', 'iihelm', 'he', 1, 25, 50, 30, 1, 1, 50000, 0, 0, '0', true, true, false, true, false, 1, NULL, '0', NULL, '0', '', -1, 0, '', 'ffffff');
INSERT INTO "items" ("id", "class_id", "name", "description", "type", "element", "file", "link", "icon", "equipment", "level", "dps", "range", "rarity", "quantity", "stack", "cost", "coins", "diamonds", "crystal", "sell", "market", "temporary", "upgrade", "staff", "enh_id", "faction_id", "req_reputation", "req_class_id", "req_class_points", "req_quests", "quest_string_index", "quest_string_value", "meta", "color") VALUES (106, NULL, 'Crystallis Summer Rescuer Hair', 'Citizens Lifeguards are encouraged by the Crystallis Administration to follow all stated rules and procedures before attempting rescue. Failure to comply will result in censure or sanctioning. (Highlights are CC to Trim)', 'Helm', 'None', 'items/helms/CrystallisSummerRescuerHair.swf', 'CrystallisSummerRescuerHair', 'iihelm', 'he', 1, 25, 50, 30, 1, 1, 50000, 0, 0, '0', true, true, false, true, false, 1, NULL, '0', NULL, '0', '', -1, 0, '', 'ffffff');
INSERT INTO "items" ("id", "class_id", "name", "description", "type", "element", "file", "link", "icon", "equipment", "level", "dps", "range", "rarity", "quantity", "stack", "cost", "coins", "diamonds", "crystal", "sell", "market", "temporary", "upgrade", "staff", "enh_id", "faction_id", "req_reputation", "req_class_id", "req_class_points", "req_quests", "quest_string_index", "quest_string_value", "meta", "color") VALUES (107, NULL, 'Crystallis Summer Tumbled Locks', 'Citizens Lifeguards are encouraged by the Crystallis Administration to follow all stated rules and procedures before attempting rescue. Failure to comply will result in censure or sanctioning. (Highlights are CC to Trim)', 'Helm', 'None', 'items/helms/CrystallisSummerRescuerLocksDown.swf', 'CrystallisSummerRescuerLocksDown', 'iihelm', 'he', 1, 25, 50, 30, 1, 1, 50000, 0, 0, '0', true, true, false, true, false, 1, NULL, '0', NULL, '0', '', -1, 0, '', 'ffffff');
INSERT INTO "items" ("id", "class_id", "name", "description", "type", "element", "file", "link", "icon", "equipment", "level", "dps", "range", "rarity", "quantity", "stack", "cost", "coins", "diamonds", "crystal", "sell", "market", "temporary", "upgrade", "staff", "enh_id", "faction_id", "req_reputation", "req_class_id", "req_class_points", "req_quests", "quest_string_index", "quest_string_value", "meta", "color") VALUES (108, NULL, 'Celestial Water Spirit', 'From deep below Lore''s surface;the neriads dance to honor the celestial water spirits who first gave them life.', 'Cape', 'None', 'items/capes/HeavenlyWaterSpirit1.swf', 'HeavenlyWaterSpirit', 'iicape', 'ba', 1, 25, 50, 30, 1, 1, 500, 1, 0, '0', true, true, false, false, false, 1, NULL, '0', NULL, '0', '', -1, 0, '', 'ffffff');
INSERT INTO "items" ("id", "class_id", "name", "description", "type", "element", "file", "link", "icon", "equipment", "level", "dps", "range", "rarity", "quantity", "stack", "cost", "coins", "diamonds", "crystal", "sell", "market", "temporary", "upgrade", "staff", "enh_id", "faction_id", "req_reputation", "req_class_id", "req_class_points", "req_quests", "quest_string_index", "quest_string_value", "meta", "color") VALUES (109, NULL, 'Crystallis First Aid Kit Cape', 'Citizens Lifeguards are encouraged by the Crystallis Administration to follow all stated rules and procedures before attempting rescue. Failure to comply will result in censure of sanctioning.', 'Cape', 'None', 'items/capes/CrystallisAidKitCape.swf', 'CrystallisAidKitCape', 'iicape', 'ba', 1, 25, 50, 30, 1, 1, 15000, 0, 0, '0', true, true, false, false, false, 1, NULL, '0', NULL, '0', '', -1, 0, '', 'ffffff');
INSERT INTO "items" ("id", "class_id", "name", "description", "type", "element", "file", "link", "icon", "equipment", "level", "dps", "range", "rarity", "quantity", "stack", "cost", "coins", "diamonds", "crystal", "sell", "market", "temporary", "upgrade", "staff", "enh_id", "faction_id", "req_reputation", "req_class_id", "req_class_points", "req_quests", "quest_string_index", "quest_string_value", "meta", "color") VALUES (110, NULL, 'Floating Sand Buddy Cape', 'Everybody needs to get away after a long season of waging war;boss battles;and hauling some loot… and a hidden island retreat is the BEST place to /rest;recoup;and re-energize. (Item is color-customizable to trim and accessory)', 'Cape', 'None', 'items/capes/SandBuddy.swf', 'SandBuddy', 'iicape', 'ba', 1, 25, 50, 30, 1, 1, 350, 1, 0, '0', true, true, false, false, false, 1, NULL, '0', NULL, '0', '', -1, 0, '', 'ffffff');
INSERT INTO "items" ("id", "class_id", "name", "description", "type", "element", "file", "link", "icon", "equipment", "level", "dps", "range", "rarity", "quantity", "stack", "cost", "coins", "diamonds", "crystal", "sell", "market", "temporary", "upgrade", "staff", "enh_id", "faction_id", "req_reputation", "req_class_id", "req_class_points", "req_quests", "quest_string_index", "quest_string_value", "meta", "color") VALUES (111, NULL, 'Mystical Oceanic Butterflies', 'Still waters run deep;but those who chance to adventure below will find a world of magic;mystery;and beauty.', 'Cape', 'None', 'items/capes/ClassyOceanButterflyCape.swf', 'ClassyOceanButterflyCape', 'iicape', 'ba', 1, 25, 50, 30, 1, 1, 350, 1, 0, '0', true, true, false, false, false, 1, NULL, '0', NULL, '0', '', -1, 0, '', 'ffffff');
INSERT INTO "items" ("id", "class_id", "name", "description", "type", "element", "file", "link", "icon", "equipment", "level", "dps", "range", "rarity", "quantity", "stack", "cost", "coins", "diamonds", "crystal", "sell", "market", "temporary", "upgrade", "staff", "enh_id", "faction_id", "req_reputation", "req_class_id", "req_class_points", "req_quests", "quest_string_index", "quest_string_value", "meta", "color") VALUES (112, NULL, 'Mystical Oceanic Butterflies Wrap', 'Still waters run deep;but those who chance to adventure below will find a world of magic;mystery;and beauty.', 'Cape', 'None', 'items/capes/ClassyOceanCapeAndButterfly.swf', 'ClassyOceanCapeAndButterfly', 'iicape', 'ba', 1, 25, 50, 55, 1, 1, 45000, 0, 0, '0', true, true, false, true, false, 1, NULL, '0', NULL, '0', '', -1, 0, '', 'ffffff');
INSERT INTO "items" ("id", "class_id", "name", "description", "type", "element", "file", "link", "icon", "equipment", "level", "dps", "range", "rarity", "quantity", "stack", "cost", "coins", "diamonds", "crystal", "sell", "market", "temporary", "upgrade", "staff", "enh_id", "faction_id", "req_reputation", "req_class_id", "req_class_points", "req_quests", "quest_string_index", "quest_string_value", "meta", "color") VALUES (113, NULL, 'Mystical Oceanic Butterflies Wrap', 'Still waters run deep;but those who chance to adventure below will find a world of magic;mystery;and beauty.', 'Cape', 'None', 'items/capes/ClassyOceanCapeAndButterfly.swf', 'ClassyOceanCapeAndButterfly', 'iicape', 'ba', 1, 25, 50, 55, 1, 1, 350, 1, 0, '0', true, true, false, false, false, 1, NULL, '0', NULL, '0', '', -1, 0, '', 'ffffff');
INSERT INTO "items" ("id", "class_id", "name", "description", "type", "element", "file", "link", "icon", "equipment", "level", "dps", "range", "rarity", "quantity", "stack", "cost", "coins", "diamonds", "crystal", "sell", "market", "temporary", "upgrade", "staff", "enh_id", "faction_id", "req_reputation", "req_class_id", "req_class_points", "req_quests", "quest_string_index", "quest_string_value", "meta", "color") VALUES (114, NULL, 'Oceanic Symphony Wrap', 'Still waters run deep;but those who chance to adventure below will find a world of magic;mystery;and beauty.', 'Cape', 'None', 'items/capes/ClassyOceanCape.swf', 'ClassyOceanCape', 'iicape', 'ba', 1, 25, 50, 30, 1, 1, 15000, 0, 0, '0', true, true, false, false, false, 1, NULL, '0', NULL, '0', '', -1, 0, '', 'ffffff');
INSERT INTO "items" ("id", "class_id", "name", "description", "type", "element", "file", "link", "icon", "equipment", "level", "dps", "range", "rarity", "quantity", "stack", "cost", "coins", "diamonds", "crystal", "sell", "market", "temporary", "upgrade", "staff", "enh_id", "faction_id", "req_reputation", "req_class_id", "req_class_points", "req_quests", "quest_string_index", "quest_string_value", "meta", "color") VALUES (115, NULL, 'Pyroclastic Necromancer''s Minion', 'Pyroclastic necromancers walk where the faint-hearted fear to tread;seeking undead minions among those who''ve perished in the hearts of volcanic lands.', 'Cape', 'None', 'items/capes/MinionofCalamity.swf', 'MinionofCalamity', 'iicape', 'ba', 1, 25, 50, 30, 1, 1, 500, 1, 0, '0', true, true, false, false, false, 1, NULL, '0', NULL, '0', '', -1, 0, '', 'ffffff');
INSERT INTO "items" ("id", "class_id", "name", "description", "type", "element", "file", "link", "icon", "equipment", "level", "dps", "range", "rarity", "quantity", "stack", "cost", "coins", "diamonds", "crystal", "sell", "market", "temporary", "upgrade", "staff", "enh_id", "faction_id", "req_reputation", "req_class_id", "req_class_points", "req_quests", "quest_string_index", "quest_string_value", "meta", "color") VALUES (116, NULL, 'Summer Rescuer Parachute', 'Citizens Lifeguards are encouraged by the Crystallis Administration to follow all stated rules and procedures before attempting rescue. Failure to comply will result in censure of sanctioning.', 'Cape', 'None', 'items/capes/CrystallisSummerRescuerParachute.swf', 'CrystallisSummerRescuerParachute', 'iicape', 'ba', 1, 25, 50, 30, 1, 1, 500, 0, 0, '0', true, true, false, false, false, 1, NULL, '0', NULL, '0', '', -1, 0, '', 'ffffff');
INSERT INTO "items" ("id", "class_id", "name", "description", "type", "element", "file", "link", "icon", "equipment", "level", "dps", "range", "rarity", "quantity", "stack", "cost", "coins", "diamonds", "crystal", "sell", "market", "temporary", "upgrade", "staff", "enh_id", "faction_id", "req_reputation", "req_class_id", "req_class_points", "req_quests", "quest_string_index", "quest_string_value", "meta", "color") VALUES (117, NULL, 'Islander Quibble Bank Pet', 'Even time-traveling sales moglins need to take a break and get away from it all once in a while.', 'Pet', 'None', 'items/pets/QuibbleIslander2022Bank.swf', 'QuibbleIslander2022Bank', 'iipet', 'pe', 1, 25, 50, 30, 1, 1, 2000, 1, 0, '0', true, true, false, false, false, 1, NULL, '0', NULL, '0', '', -1, 0, '', 'ffffff');
INSERT INTO "items" ("id", "class_id", "name", "description", "type", "element", "file", "link", "icon", "equipment", "level", "dps", "range", "rarity", "quantity", "stack", "cost", "coins", "diamonds", "crystal", "sell", "market", "temporary", "upgrade", "staff", "enh_id", "faction_id", "req_reputation", "req_class_id", "req_class_points", "req_quests", "quest_string_index", "quest_string_value", "meta", "color") VALUES (118, NULL, 'Islander Quibble Pet', 'Even time-traveling sales moglins need to take a break and get away from it all once in a while.', 'Pet', 'None', 'items/pets/QuibbleIslander2022Pet.swf', 'QuibbleIslander2022Pet', 'iipet', 'pe', 1, 25, 50, 30, 1, 1, 400, 1, 0, '0', true, true, false, false, false, 1, NULL, '0', NULL, '0', '', -1, 0, '', 'ffffff');
INSERT INTO "items" ("id", "class_id", "name", "description", "type", "element", "file", "link", "icon", "equipment", "level", "dps", "range", "rarity", "quantity", "stack", "cost", "coins", "diamonds", "crystal", "sell", "market", "temporary", "upgrade", "staff", "enh_id", "faction_id", "req_reputation", "req_class_id", "req_class_points", "req_quests", "quest_string_index", "quest_string_value", "meta", "color") VALUES (119, NULL, 'Sand Buddy Pet', 'Everybody needs to get away after a long season of waging war;boss battles;and hauling loot… and a hidden island retreat is the BEST place to /rest;recoup;and re-energize.', 'Pet', 'None', 'items/pets/SandBuddyPet.swf', 'SandBuddyPet', 'iipet', 'pe', 1, 25, 50, 30, 1, 1, 300, 1, 0, '0', true, true, false, false, false, 1, NULL, '0', NULL, '0', '', -1, 0, '', 'ffffff');
INSERT INTO "items" ("id", "class_id", "name", "description", "type", "element", "file", "link", "icon", "equipment", "level", "dps", "range", "rarity", "quantity", "stack", "cost", "coins", "diamonds", "crystal", "sell", "market", "temporary", "upgrade", "staff", "enh_id", "faction_id", "req_reputation", "req_class_id", "req_class_points", "req_quests", "quest_string_index", "quest_string_value", "meta", "color") VALUES (121, NULL, 'Hand of the Pyroclastic Necromancer', 'Pyroclastic necromancers walk where the faint-hearted fear to tread;seeking undead minions among those who''ve perished in the hearts of volcanic lands.', 'Gauntlet', 'None', 'items/gauntlets/HandofCalamity.swf', 'HandofCalamity', 'iwclaws', 'Weapon', 1, 25, 50, 30, 1, 1, 400, 1, 0, '0', true, true, false, false, false, 1, NULL, '0', NULL, '0', '', -1, 0, '', 'ffffff');
INSERT INTO "items" ("id", "class_id", "name", "description", "type", "element", "file", "link", "icon", "equipment", "level", "dps", "range", "rarity", "quantity", "stack", "cost", "coins", "diamonds", "crystal", "sell", "market", "temporary", "upgrade", "staff", "enh_id", "faction_id", "req_reputation", "req_class_id", "req_class_points", "req_quests", "quest_string_index", "quest_string_value", "meta", "color") VALUES (122, NULL, 'Oceanic Pool', 'Still waters run deep;but those who chance to adventure below will find a world of magic;mystery;and beauty.', 'Amulet', 'None', 'items/grounds/ClassyOceanGroundRune.swf', 'ClassyOceanGroundRune', 'imr2', 'mi', 1, 25, 50, 55, 1, 1, 500, 1, 0, '0', true, true, false, false, false, 1, NULL, '0', NULL, '0', '', -1, 0, '', 'ffffff');
INSERT INTO "items" ("id", "class_id", "name", "description", "type", "element", "file", "link", "icon", "equipment", "level", "dps", "range", "rarity", "quantity", "stack", "cost", "coins", "diamonds", "crystal", "sell", "market", "temporary", "upgrade", "staff", "enh_id", "faction_id", "req_reputation", "req_class_id", "req_class_points", "req_quests", "quest_string_index", "quest_string_value", "meta", "color") VALUES (123, NULL, 'Little Island Retreat', 'Everybody needs to get away after a long season of waging war;boss battles;and hauling some loot… and a hidden island retreat is the BEST place to /rest;recoup;and re-energize.', 'Amulet', 'None', 'items/grounds/LittleIsland.swf', 'LittleIsland', 'imr2', 'mi', 1, 25, 50, 30, 1, 1, 1000, 1, 0, '0', true, true, false, false, false, 1, NULL, '0', NULL, '0', '', -1, 0, '', 'ffffff');
INSERT INTO "items" ("id", "class_id", "name", "description", "type", "element", "file", "link", "icon", "equipment", "level", "dps", "range", "rarity", "quantity", "stack", "cost", "coins", "diamonds", "crystal", "sell", "market", "temporary", "upgrade", "staff", "enh_id", "faction_id", "req_reputation", "req_class_id", "req_class_points", "req_quests", "quest_string_index", "quest_string_value", "meta", "color") VALUES (124, NULL, 'Beta Player', 'Thank you for playing our Beta Phase! we reward you this great Title that will be yours forever! Keep it on Nytherians!!', 'ServerUse', 'None', 'title::24', 'title::24', 'iCheck', 'None', 1, 25, 50, 10, 1, 1, 0, 0, 0, '0', true, false, false, false, false, 1, NULL, '0', NULL, '0', '', -1, 0, '', 'ffffff');
INSERT INTO "items" ("id", "class_id", "name", "description", "type", "element", "file", "link", "icon", "equipment", "level", "dps", "range", "rarity", "quantity", "stack", "cost", "coins", "diamonds", "crystal", "sell", "market", "temporary", "upgrade", "staff", "enh_id", "faction_id", "req_reputation", "req_class_id", "req_class_points", "req_quests", "quest_string_index", "quest_string_value", "meta", "color") VALUES (125, NULL, 'Sun God Warrior', 'Sun God Warrior the Sun God demands vengeance. Pay homage to his power with the blood of your enemies;and you will prosper in his service. (Tattoo is CC to trim)', 'Armor', 'None', 'AzuraArmor2CC2.swf', 'AzuraArmor2CC', 'iwarmor', 'co', 1, 25, 50, 30, 1, 1, 350, 1, 0, '0', true, true, false, false, false, 1, NULL, '0', NULL, '0', '', -1, 0, '', 'ffffff');
INSERT INTO "items" ("id", "class_id", "name", "description", "type", "element", "file", "link", "icon", "equipment", "level", "dps", "range", "rarity", "quantity", "stack", "cost", "coins", "diamonds", "crystal", "sell", "market", "temporary", "upgrade", "staff", "enh_id", "faction_id", "req_reputation", "req_class_id", "req_class_points", "req_quests", "quest_string_index", "quest_string_value", "meta", "color") VALUES (126, NULL, 'Lightning Bolt', 'Damage 20% to all Monster\r\nDefense 10%', 'Sword', 'None', 'items/swords/LightningBotlInHandsDatapaw.swf', 'HSElectinaLightingRodCC', 'iwsword', 'Weapon', 1, 50, 50, 11, 1, 1, 0, 0, 0, '0', true, true, false, false, false, 1, NULL, '0', NULL, '0', '', -1, 0, '', 'e9bb03');
INSERT INTO "items" ("id", "class_id", "name", "description", "type", "element", "file", "link", "icon", "equipment", "level", "dps", "range", "rarity", "quantity", "stack", "cost", "coins", "diamonds", "crystal", "sell", "market", "temporary", "upgrade", "staff", "enh_id", "faction_id", "req_reputation", "req_class_id", "req_class_points", "req_quests", "quest_string_index", "quest_string_value", "meta", "color") VALUES (127, NULL, 'Sun God Hair', 'Son God the Sun God demands vengeance. Pay homage to his power with the blood of your enemies;and you will prosper in his service.', 'Helm', 'None', 'items/helms/AzuraHelmMCC.swf', 'AzuraHelmMCC', 'iihelm', 'he', 1, 25, 50, 30, 1, 1, 0, 1, 0, '0', true, true, false, false, false, 1, NULL, '0', NULL, '0', '', -1, 0, '', 'ffffff');
INSERT INTO "items" ("id", "class_id", "name", "description", "type", "element", "file", "link", "icon", "equipment", "level", "dps", "range", "rarity", "quantity", "stack", "cost", "coins", "diamonds", "crystal", "sell", "market", "temporary", "upgrade", "staff", "enh_id", "faction_id", "req_reputation", "req_class_id", "req_class_points", "req_quests", "quest_string_index", "quest_string_value", "meta", "color") VALUES (128, NULL, 'Sun God Cape', 'Sun God the Sun God demands vengeance. Pay homage to his power with the blood of your enemies;and you will prosper in his service.', 'Cape', 'None', 'items/capes/Azura3MCapeCC.swf', 'Azura3MCapeCC', 'iicape', 'ba', 1, 25, 50, 30, 1, 1, 0, 1, 0, '0', true, true, false, false, false, 1, NULL, '0', NULL, '0', '', -1, 0, '', 'ffffff');
INSERT INTO "items" ("id", "class_id", "name", "description", "type", "element", "file", "link", "icon", "equipment", "level", "dps", "range", "rarity", "quantity", "stack", "cost", "coins", "diamonds", "crystal", "sell", "market", "temporary", "upgrade", "staff", "enh_id", "faction_id", "req_reputation", "req_class_id", "req_class_points", "req_quests", "quest_string_index", "quest_string_value", "meta", "color") VALUES (129, NULL, 'CC Elder big Squid', 'Amulet', 'None', 'items/grounds/BigSquid.swf', 'BEldWarriorbigSquid', 'imr2', 'mi', '1', 25, 50, 35, 1, 1, 5000000, 1, 0, 0, '1', true, false, false, false, true, NULL, 0, '0', 0, '0', '', 0, 0, 'ffffff', '');
INSERT INTO "items" ("id", "class_id", "name", "description", "type", "element", "file", "link", "icon", "equipment", "level", "dps", "range", "rarity", "quantity", "stack", "cost", "coins", "diamonds", "crystal", "sell", "market", "temporary", "upgrade", "staff", "enh_id", "faction_id", "req_reputation", "req_class_id", "req_class_points", "req_quests", "quest_string_index", "quest_string_value", "meta", "color") VALUES (130, NULL, 'Elder Warrior of Squid', 'Armor', 'None', 'BEldWarrior2.swf', 'BEldWarrior2', 'iwarmor', 'co', '1', 25, 50, 35, 1, 1, 50000, 1, 0, 0, '1', true, false, false, false, true, NULL, 0, '0', 0, '0', '', 0, 0, 'ffffff', '');
INSERT INTO "items" ("id", "class_id", "name", "description", "type", "element", "file", "link", "icon", "equipment", "level", "dps", "range", "rarity", "quantity", "stack", "cost", "coins", "diamonds", "crystal", "sell", "market", "temporary", "upgrade", "staff", "enh_id", "faction_id", "req_reputation", "req_class_id", "req_class_points", "req_quests", "quest_string_index", "quest_string_value", "meta", "color") VALUES (131, NULL, 'Elder Squid Sword', '', 'Sword', 'None', 'items/swords/BEldWarriorBlade.swf', 'BEldWarriorBlade', 'iwsword', 'Weapon', 1, 25, 50, 35, 1, 1, 50000, 1, 0, '0', true, true, false, false, false, 1, NULL, '0', NULL, '0', '', -1, 0, '', 'ffffff');
INSERT INTO "items" ("id", "class_id", "name", "description", "type", "element", "file", "link", "icon", "equipment", "level", "dps", "range", "rarity", "quantity", "stack", "cost", "coins", "diamonds", "crystal", "sell", "market", "temporary", "upgrade", "staff", "enh_id", "faction_id", "req_reputation", "req_class_id", "req_class_points", "req_quests", "quest_string_index", "quest_string_value", "meta", "color") VALUES (132, NULL, 'Elder Squid Hood', 'Helm', 'None', 'items/helms/BEldWarriorH2.swf', 'BEldWarriorH2', 'iihelm', 'he', '1', 25, 50, 10, 1, 1, 0, 0, 0, 0, '1', true, false, false, false, true, NULL, 0, '0', 0, '0', '', 0, 0, 'ffffff', '');
INSERT INTO "items" ("id", "class_id", "name", "description", "type", "element", "file", "link", "icon", "equipment", "level", "dps", "range", "rarity", "quantity", "stack", "cost", "coins", "diamonds", "crystal", "sell", "market", "temporary", "upgrade", "staff", "enh_id", "faction_id", "req_reputation", "req_class_id", "req_class_points", "req_quests", "quest_string_index", "quest_string_value", "meta", "color") VALUES (133, NULL, 'Elder Warrior of Squid', 'Armor', 'None', 'BEldWarrior2.swf', 'BEldWarrior2', 'iwarmor', 'co', '1', 25, 50, 10, 1, 1, 0, 0, 0, 0, '1', true, false, false, false, true, NULL, 0, '0', 0, '0', '', 0, 0, 'ffffff', '');
INSERT INTO "items" ("id", "class_id", "name", "description", "type", "element", "file", "link", "icon", "equipment", "level", "dps", "range", "rarity", "quantity", "stack", "cost", "coins", "diamonds", "crystal", "sell", "market", "temporary", "upgrade", "staff", "enh_id", "faction_id", "req_reputation", "req_class_id", "req_class_points", "req_quests", "quest_string_index", "quest_string_value", "meta", "color") VALUES (134, NULL, 'Elder Warrior Squid', 'Armor', 'None', 'BEldWarrior2.swf', 'BEldWarrior2', 'iwarmor', 'co', '1', 25, 50, 10, 1, 1, 0, 0, 0, 0, '1', true, false, false, false, true, NULL, 0, '0', 0, '0', '', 0, 0, 'ffffff', '');
INSERT INTO "items" ("id", "class_id", "name", "description", "type", "element", "file", "link", "icon", "equipment", "level", "dps", "range", "rarity", "quantity", "stack", "cost", "coins", "diamonds", "crystal", "sell", "market", "temporary", "upgrade", "staff", "enh_id", "faction_id", "req_reputation", "req_class_id", "req_class_points", "req_quests", "quest_string_index", "quest_string_value", "meta", "color") VALUES (135, NULL, 'Squid Warrior', 'Armor', 'None', 'BEldWarrior21.swf', 'BEldWarrior2', 'iwarmor', 'co', '1', 25, 50, 10, 1, 1, 50000, 1, 0, 0, '1', true, false, false, false, true, NULL, 0, '0', 0, '0', '', 0, 0, 'ffffff', '');
INSERT INTO "items" ("id", "class_id", "name", "description", "type", "element", "file", "link", "icon", "equipment", "level", "dps", "range", "rarity", "quantity", "stack", "cost", "coins", "diamonds", "crystal", "sell", "market", "temporary", "upgrade", "staff", "enh_id", "faction_id", "req_reputation", "req_class_id", "req_class_points", "req_quests", "quest_string_index", "quest_string_value", "meta", "color") VALUES (136, NULL, 'Squid Tactecle', '', 'Cape', 'None', 'items/capes/BEldWarriorTentaclesC.swf', 'BEldWarriorTentaclesC', 'iicape', 'ba', 1, 25, 50, 35, 1, 1, 60000, 1, 0, '0', true, true, false, false, false, 1, NULL, '0', NULL, '0', '', -1, 0, '', 'ffffff');
INSERT INTO "items" ("id", "class_id", "name", "description", "type", "element", "file", "link", "icon", "equipment", "level", "dps", "range", "rarity", "quantity", "stack", "cost", "coins", "diamonds", "crystal", "sell", "market", "temporary", "upgrade", "staff", "enh_id", "faction_id", "req_reputation", "req_class_id", "req_class_points", "req_quests", "quest_string_index", "quest_string_value", "meta", "color") VALUES (137, NULL, 'Jungle Fragment', 'Kill Them all Zard is so scared of them and Sun god Called You to Defeat them and save the jungle', 'Item', 'None', 'none.swf', 'iibag', 'iibag', 'None', 1, 25, 50, 11, 1, 1000, 0, 0, 0, '0', true, true, false, false, false, 1, NULL, '0', NULL, '0', '', -1, 0, '', 'c0c0c0');
INSERT INTO "items" ("id", "class_id", "name", "description", "type", "element", "file", "link", "icon", "equipment", "level", "dps", "range", "rarity", "quantity", "stack", "cost", "coins", "diamonds", "crystal", "sell", "market", "temporary", "upgrade", "staff", "enh_id", "faction_id", "req_reputation", "req_class_id", "req_class_points", "req_quests", "quest_string_index", "quest_string_value", "meta", "color") VALUES (138, NULL, 'Soul of a Goddess', 'this can be found in roftitan', 'Item', 'None', 'iibag', 'iibag', 'iibag', 'None', 1, 25, 50, 11, 1, 1300, 500, 1, 0, '0', true, true, false, false, false, 1, NULL, '0', NULL, '0', '', -1, 0, '', 'ff0000');
INSERT INTO "items" ("id", "class_id", "name", "description", "type", "element", "file", "link", "icon", "equipment", "level", "dps", "range", "rarity", "quantity", "stack", "cost", "coins", "diamonds", "crystal", "sell", "market", "temporary", "upgrade", "staff", "enh_id", "faction_id", "req_reputation", "req_class_id", "req_class_points", "req_quests", "quest_string_index", "quest_string_value", "meta", "color") VALUES (139, NULL, 'Skeleton Halt', 'Use skeleton resource to complete mision in newbie.', 'Item', 'None', 'iibag', 'iibag', 'ied1', 'None', 1, 25, 50, 10, 1, 1, 5000, 0, 0, '0', true, false, false, false, false, 1, NULL, '0', NULL, '0', '', -1, 0, '', 'ffffff');
INSERT INTO "items" ("id", "class_id", "name", "description", "type", "element", "file", "link", "icon", "equipment", "level", "dps", "range", "rarity", "quantity", "stack", "cost", "coins", "diamonds", "crystal", "sell", "market", "temporary", "upgrade", "staff", "enh_id", "faction_id", "req_reputation", "req_class_id", "req_class_points", "req_quests", "quest_string_index", "quest_string_value", "meta", "color") VALUES (140, NULL, 'C Ultima Astral', 'Ultima Astral the last you;thing that you can get in LQS and it will never you can get it', 'Sword', 'None', 'items/swords/UltimaDatapaw.swf', 'CustomUltimaAstral', 'iwsword', 'Weapon', 1, 25, 50, 70, 1, 1, 1000, 1, 0, '0', true, true, false, false, false, 1, NULL, '0', NULL, '0', '', -1, 0, '', 'ffffff');
INSERT INTO "items" ("id", "class_id", "name", "description", "type", "element", "file", "link", "icon", "equipment", "level", "dps", "range", "rarity", "quantity", "stack", "cost", "coins", "diamonds", "crystal", "sell", "market", "temporary", "upgrade", "staff", "enh_id", "faction_id", "req_reputation", "req_class_id", "req_class_points", "req_quests", "quest_string_index", "quest_string_value", "meta", "color") VALUES (141, NULL, 'Bepic Token', 'Use in Estarta Shop.', 'Item', 'None', 'imc3', 'imc3', 'imc3', 'None', 1, 25, 50, 1, 1, 500, 0, 0, 0, '0', true, true, false, false, false, 1, NULL, '0', NULL, '0', '', -1, 0, '', 'ffffff');
INSERT INTO "items" ("id", "class_id", "name", "description", "type", "element", "file", "link", "icon", "equipment", "level", "dps", "range", "rarity", "quantity", "stack", "cost", "coins", "diamonds", "crystal", "sell", "market", "temporary", "upgrade", "staff", "enh_id", "faction_id", "req_reputation", "req_class_id", "req_class_points", "req_quests", "quest_string_index", "quest_string_value", "meta", "color") VALUES (142, NULL, 'Ethan', '.', 'Armor', 'None', 'Ethan1.swf', 'Ethan', 'iwarmor', 'co', 1, 25, 50, 10, 1, 1, 0, 0, 0, '0', true, true, false, false, false, 1, NULL, '0', NULL, '0', '', -1, 0, '', 'ffffff');
INSERT INTO "items" ("id", "class_id", "name", "description", "type", "element", "file", "link", "icon", "equipment", "level", "dps", "range", "rarity", "quantity", "stack", "cost", "coins", "diamonds", "crystal", "sell", "market", "temporary", "upgrade", "staff", "enh_id", "faction_id", "req_reputation", "req_class_id", "req_class_points", "req_quests", "quest_string_index", "quest_string_value", "meta", "color") VALUES (143, NULL, 'Ethan', '.', 'Armor', 'None', 'Ethan1.swf', 'Ethan', 'iwarmor', 'co', 1, 25, 50, 10, 1, 1, 0, 0, 0, '0', true, true, false, false, false, 1, NULL, '0', NULL, '0', '', -1, 0, '', 'ffffff');
INSERT INTO "items" ("id", "class_id", "name", "description", "type", "element", "file", "link", "icon", "equipment", "level", "dps", "range", "rarity", "quantity", "stack", "cost", "coins", "diamonds", "crystal", "sell", "market", "temporary", "upgrade", "staff", "enh_id", "faction_id", "req_reputation", "req_class_id", "req_class_points", "req_quests", "quest_string_index", "quest_string_value", "meta", "color") VALUES (144, NULL, 'Ethan', '.', 'Armor', 'None', 'Ethan1.swf', 'Ethan', 'iwarmor', 'co', 1, 25, 50, 10, 1, 1, 0, 0, 0, '0', true, true, false, false, false, 1, NULL, '0', NULL, '0', '', -1, 0, '', 'ffffff');
INSERT INTO "items" ("id", "class_id", "name", "description", "type", "element", "file", "link", "icon", "equipment", "level", "dps", "range", "rarity", "quantity", "stack", "cost", "coins", "diamonds", "crystal", "sell", "market", "temporary", "upgrade", "staff", "enh_id", "faction_id", "req_reputation", "req_class_id", "req_class_points", "req_quests", "quest_string_index", "quest_string_value", "meta", "color") VALUES (145, NULL, 'Beta Berserker Access', 'Beta Berserker Access.', 'Resource', 'None', 'iibag', 'iibag', 'iibag', 'None', 1, 25, 50, 10, 1, 1, 0, 0, 0, '0', true, true, false, false, false, 1, NULL, '0', NULL, '0', '', -1, 0, '', 'ffffff');
INSERT INTO "items" ("id", "class_id", "name", "description", "type", "element", "file", "link", "icon", "equipment", "level", "dps", "range", "rarity", "quantity", "stack", "cost", "coins", "diamonds", "crystal", "sell", "market", "temporary", "upgrade", "staff", "enh_id", "faction_id", "req_reputation", "req_class_id", "req_class_points", "req_quests", "quest_string_index", "quest_string_value", "meta", "color") VALUES (146, NULL, 'Pink Gift of The Fire', '', 'Dagger', 'None', 'items/daggers/pinkgiftDatapaw.swf', 'PinkGiftOfTheFireAvatar', 'iwdagger', 'Weapon', 1, 25, 50, 10, 1, 1, 0, 0, 0, '0', true, true, false, false, false, 1, NULL, '0', NULL, '0', '', -1, 0, '', 'ffffff');
INSERT INTO "items" ("id", "class_id", "name", "description", "type", "element", "file", "link", "icon", "equipment", "level", "dps", "range", "rarity", "quantity", "stack", "cost", "coins", "diamonds", "crystal", "sell", "market", "temporary", "upgrade", "staff", "enh_id", "faction_id", "req_reputation", "req_class_id", "req_class_points", "req_quests", "quest_string_index", "quest_string_value", "meta", "color") VALUES (147, NULL, 'Matrix', '', 'Armor', 'None', 'Matrixr1.swf', 'Matrix', 'iwarmor', 'co', 1, 25, 50, 10, 1, 1, 0, 0, 0, '0', true, true, false, false, false, 1, NULL, '0', NULL, '0', '', -1, 0, '', 'ffffff');
INSERT INTO "items" ("id", "class_id", "name", "description", "type", "element", "file", "link", "icon", "equipment", "level", "dps", "range", "rarity", "quantity", "stack", "cost", "coins", "diamonds", "crystal", "sell", "market", "temporary", "upgrade", "staff", "enh_id", "faction_id", "req_reputation", "req_class_id", "req_class_points", "req_quests", "quest_string_index", "quest_string_value", "meta", "color") VALUES (148, NULL, 'The Voider Claws', '', 'Gauntlet', 'None', 'items/gauntlets/VoidClaws.swf', 'VoidClaws', 'iwclaws', 'Weapon', 1, 25, 50, 10, 1, 1, 0, 0, 0, '0', true, true, false, false, false, 1, NULL, '0', NULL, '0', '', -1, 0, '', 'ffffff');
INSERT INTO "items" ("id", "class_id", "name", "description", "type", "element", "file", "link", "icon", "equipment", "level", "dps", "range", "rarity", "quantity", "stack", "cost", "coins", "diamonds", "crystal", "sell", "market", "temporary", "upgrade", "staff", "enh_id", "faction_id", "req_reputation", "req_class_id", "req_class_points", "req_quests", "quest_string_index", "quest_string_value", "meta", "color") VALUES (149, NULL, 'Scray Hand''s', '', 'Amulet', 'None', 'items/grounds/LNHandsFiendGround1r1.swf', 'LNHandsFiendGround1', 'imr2', 'mi', 1, 25, 50, 10, 1, 1, 0, 0, 0, '0', true, true, false, false, false, 1, NULL, '0', NULL, '0', '', -1, 0, '', 'ffffff');
INSERT INTO "items" ("id", "class_id", "name", "description", "type", "element", "file", "link", "icon", "equipment", "level", "dps", "range", "rarity", "quantity", "stack", "cost", "coins", "diamonds", "crystal", "sell", "market", "temporary", "upgrade", "staff", "enh_id", "faction_id", "req_reputation", "req_class_id", "req_class_points", "req_quests", "quest_string_index", "quest_string_value", "meta", "color") VALUES (150, NULL, 'Creature Hood''s', '', 'Helm', 'None', 'items/helms/Creature16sHood.swf', 'Creature16sHood', 'iihelm', 'he', 1, 25, 50, 10, 1, 1, 0, 0, 0, '0', true, true, false, false, false, 1, NULL, '0', NULL, '0', '', -1, 0, '', 'ffffff');
INSERT INTO "items" ("id", "class_id", "name", "description", "type", "element", "file", "link", "icon", "equipment", "level", "dps", "range", "rarity", "quantity", "stack", "cost", "coins", "diamonds", "crystal", "sell", "market", "temporary", "upgrade", "staff", "enh_id", "faction_id", "req_reputation", "req_class_id", "req_class_points", "req_quests", "quest_string_index", "quest_string_value", "meta", "color") VALUES (151, NULL, 'Ultra Hair', '', 'Helm', 'None', 'items/helms/DataUltra.swf', 'UltraOverpoweredHair', 'iihelm', 'he', 1, 25, 50, 10, 1, 1, 0, 0, 0, '0', true, true, false, false, false, 1, NULL, '0', NULL, '0', '', -1, 0, '', 'ff0000');
INSERT INTO "items" ("id", "class_id", "name", "description", "type", "element", "file", "link", "icon", "equipment", "level", "dps", "range", "rarity", "quantity", "stack", "cost", "coins", "diamonds", "crystal", "sell", "market", "temporary", "upgrade", "staff", "enh_id", "faction_id", "req_reputation", "req_class_id", "req_class_points", "req_quests", "quest_string_index", "quest_string_value", "meta", "color") VALUES (152, NULL, 'Warrior Weapon Enhancement Level 5', '', 'Enhancement', 'None', 'Enhancement.swf', 'Enhancement', 'iwsword', 'Weapon', 5, 25, 50, 10, 1, 1, 2500, 0, 0, '0', true, true, false, false, false, 2, NULL, '0', NULL, '0', '', -1, 0, '', 'ffffff');
INSERT INTO "items" ("id", "class_id", "name", "description", "type", "element", "file", "link", "icon", "equipment", "level", "dps", "range", "rarity", "quantity", "stack", "cost", "coins", "diamonds", "crystal", "sell", "market", "temporary", "upgrade", "staff", "enh_id", "faction_id", "req_reputation", "req_class_id", "req_class_points", "req_quests", "quest_string_index", "quest_string_value", "meta", "color") VALUES (153, NULL, 'Walker Cloak', '', 'Cape', 'None', 'items/capes/Onesssaannn.swf', 'TimewalkerCloak', 'iicape', 'ba', 1, 25, 50, 10, 1, 1, 0, 0, 0, '0', true, true, false, false, false, 1, NULL, '0', NULL, '0', '', -1, 0, '', 'ffffff');
INSERT INTO "items" ("id", "class_id", "name", "description", "type", "element", "file", "link", "icon", "equipment", "level", "dps", "range", "rarity", "quantity", "stack", "cost", "coins", "diamonds", "crystal", "sell", "market", "temporary", "upgrade", "staff", "enh_id", "faction_id", "req_reputation", "req_class_id", "req_class_points", "req_quests", "quest_string_index", "quest_string_value", "meta", "color") VALUES (371, NULL, 'Katniss Access', 'Use to open Katniss NPC.', 'Item', 'None', 'iCheck', 'iCheck', 'iCheck', 'None', 1, 25, 50, 10, 1, 1, 0, 1, 0, '0', true, false, false, false, false, 1, NULL, '0', NULL, '0', '', -1, 0, '', 'ffffff');
INSERT INTO "items" ("id", "class_id", "name", "description", "type", "element", "file", "link", "icon", "equipment", "level", "dps", "range", "rarity", "quantity", "stack", "cost", "coins", "diamonds", "crystal", "sell", "market", "temporary", "upgrade", "staff", "enh_id", "faction_id", "req_reputation", "req_class_id", "req_class_points", "req_quests", "quest_string_index", "quest_string_value", "meta", "color") VALUES (373, 3, 'Bloondermon', 'Congratulations to all the players who completed these quests and helped take back the jungle from the villains.', 'Class', 'None', 'Nytherian.swf', 'DWMage', 'imc3', 'ar', 1, 25, 50, 23, 1, 1, 0, 1, 0, '0', true, false, false, false, false, 1, NULL, '0', NULL, '0', '', -1, 0, '', 'ffffff');
INSERT INTO "items" ("id", "class_id", "name", "description", "type", "element", "file", "link", "icon", "equipment", "level", "dps", "range", "rarity", "quantity", "stack", "cost", "coins", "diamonds", "crystal", "sell", "market", "temporary", "upgrade", "staff", "enh_id", "faction_id", "req_reputation", "req_class_id", "req_class_points", "req_quests", "quest_string_index", "quest_string_value", "meta", "color") VALUES (374, NULL, 'Warlord of Void', 'Attack Power (+10%) Defense (+40%)', 'Armor', 'None', 'Luciaa.swf', 'Luciaa', 'iwarmor', 'co', 1, 35, 50, 30, 1, 1, 0, 0, 0, '0', true, true, false, false, false, 1, NULL, '0', NULL, '0', '', -1, 0, '', 'e1e401');
INSERT INTO "items" ("id", "class_id", "name", "description", "type", "element", "file", "link", "icon", "equipment", "level", "dps", "range", "rarity", "quantity", "stack", "cost", "coins", "diamonds", "crystal", "sell", "market", "temporary", "upgrade", "staff", "enh_id", "faction_id", "req_reputation", "req_class_id", "req_class_points", "req_quests", "quest_string_index", "quest_string_value", "meta", "color") VALUES (375, NULL, 'Warlord of Void Guard', 'Im Your Guid in the beginning until you in top of your dream just never give up of your dream loser always be quiter', 'Helm', 'None', 'items/helms/WOVhelmonly1.swf', 'WOVHelm', 'iihelm', 'he', 1, 25, 50, 30, 1, 1, 0, 0, 0, '0', true, true, false, false, false, 1, NULL, '0', NULL, '0', '', -1, 0, '', 'e4d501');
INSERT INTO "items" ("id", "class_id", "name", "description", "type", "element", "file", "link", "icon", "equipment", "level", "dps", "range", "rarity", "quantity", "stack", "cost", "coins", "diamonds", "crystal", "sell", "market", "temporary", "upgrade", "staff", "enh_id", "faction_id", "req_reputation", "req_class_id", "req_class_points", "req_quests", "quest_string_index", "quest_string_value", "meta", "color") VALUES (376, NULL, 'Pile of gold''s', '-', 'Item', 'None', 'iibag', 'iibag', 'iibag', 'Item', 1, 25, 50, 10, 1, 30, 0, 0, 0, '0', true, true, true, false, false, 1, NULL, '0', NULL, '0', '', -1, 0, '', 'ffffff');
INSERT INTO "items" ("id", "class_id", "name", "description", "type", "element", "file", "link", "icon", "equipment", "level", "dps", "range", "rarity", "quantity", "stack", "cost", "coins", "diamonds", "crystal", "sell", "market", "temporary", "upgrade", "staff", "enh_id", "faction_id", "req_reputation", "req_class_id", "req_class_points", "req_quests", "quest_string_index", "quest_string_value", "meta", "color") VALUES (377, NULL, 'Sketch Naval Commander', 'The highest ranking captain of all. Take control of the helm.', 'Armor', 'None', 'PencilHook1.swf', 'PencilHook1', 'iwarmor', 'co', 1, 25, 50, 30, 1, 1, 0, 1, 0, '0', true, true, false, false, false, 1, NULL, '0', NULL, '0', '', -1, 0, '', 'ffffff');
INSERT INTO "items" ("id", "class_id", "name", "description", "type", "element", "file", "link", "icon", "equipment", "level", "dps", "range", "rarity", "quantity", "stack", "cost", "coins", "diamonds", "crystal", "sell", "market", "temporary", "upgrade", "staff", "enh_id", "faction_id", "req_reputation", "req_class_id", "req_class_points", "req_quests", "quest_string_index", "quest_string_value", "meta", "color") VALUES (378, NULL, 'Paper Warrior', 'Literal.', 'Armor', 'None', 'PaperRareWarrior.swf', 'PaperRareWarrior', 'iwarmor', 'co', 1, 25, 50, 30, 1, 1, 0, 1, 0, '0', true, true, false, false, false, 1, NULL, '0', NULL, '0', '', -1, 0, '', 'ffffff');
INSERT INTO "items" ("id", "class_id", "name", "description", "type", "element", "file", "link", "icon", "equipment", "level", "dps", "range", "rarity", "quantity", "stack", "cost", "coins", "diamonds", "crystal", "sell", "market", "temporary", "upgrade", "staff", "enh_id", "faction_id", "req_reputation", "req_class_id", "req_class_points", "req_quests", "quest_string_index", "quest_string_value", "meta", "color") VALUES (379, NULL, 'Paper Helm Warrior', 'Literal..', 'Helm', 'None', 'items/helms/RareWingedHelmKertas.swf', 'RareWingedHelmKertas', 'iihelm', 'he', 1, 25, 50, 30, 1, 1, 0, 1, 0, '0', true, true, false, false, false, 1, NULL, '0', NULL, '0', '', -1, 0, '', 'ffffff');
INSERT INTO "items" ("id", "class_id", "name", "description", "type", "element", "file", "link", "icon", "equipment", "level", "dps", "range", "rarity", "quantity", "stack", "cost", "coins", "diamonds", "crystal", "sell", "market", "temporary", "upgrade", "staff", "enh_id", "faction_id", "req_reputation", "req_class_id", "req_class_points", "req_quests", "quest_string_index", "quest_string_value", "meta", "color") VALUES (380, NULL, 'Cellulose Fiber Star Sword', 'This blade seems to harness the power of light itself. It''s a force to be reckoned with.', 'Sword', 'None', 'items/swords/starswordpencil.swf', 'starswordpencil', 'iwsword', 'Weapon', 1, 25, 50, 30, 1, 1, 0, 1, 0, '0', true, true, false, false, false, 1, NULL, '0', NULL, '0', '', -1, 0, '', 'ffffff');
INSERT INTO "items" ("id", "class_id", "name", "description", "type", "element", "file", "link", "icon", "equipment", "level", "dps", "range", "rarity", "quantity", "stack", "cost", "coins", "diamonds", "crystal", "sell", "market", "temporary", "upgrade", "staff", "enh_id", "faction_id", "req_reputation", "req_class_id", "req_class_points", "req_quests", "quest_string_index", "quest_string_value", "meta", "color") VALUES (381, NULL, 'Cellulose Fiber Star Sword B', 'This blade seems to harness the power of light itself. It''s a force to be reckoned with.', 'Sword', 'None', 'items/swords/starswordpencilb.swf', 'starswordpencilb', 'iwsword', 'Weapon', 1, 25, 50, 30, 1, 1, 0, 1, 0, '0', true, true, false, false, false, 1, NULL, '0', NULL, '0', '', -1, 0, '', 'ffffff');
INSERT INTO "items" ("id", "class_id", "name", "description", "type", "element", "file", "link", "icon", "equipment", "level", "dps", "range", "rarity", "quantity", "stack", "cost", "coins", "diamonds", "crystal", "sell", "market", "temporary", "upgrade", "staff", "enh_id", "faction_id", "req_reputation", "req_class_id", "req_class_points", "req_quests", "quest_string_index", "quest_string_value", "meta", "color") VALUES (382, NULL, 'Doodle Awe Sword', 'If you have a Legend only weapon equipped when your membership upgrade expires or when merging a weapon that''s equipped into something else;this weapon is temporarily equipped by default.', 'Sword', 'None', 'items/swords/sword12kertas.swf', 'sword12kertas', 'iwsword', 'Weapon', 1, 25, 50, 30, 1, 1, 0, 1, 0, '0', true, true, false, false, false, 1, NULL, '0', NULL, '0', '', -1, 0, '', 'ffffff');
INSERT INTO "items" ("id", "class_id", "name", "description", "type", "element", "file", "link", "icon", "equipment", "level", "dps", "range", "rarity", "quantity", "stack", "cost", "coins", "diamonds", "crystal", "sell", "market", "temporary", "upgrade", "staff", "enh_id", "faction_id", "req_reputation", "req_class_id", "req_class_points", "req_quests", "quest_string_index", "quest_string_value", "meta", "color") VALUES (383, NULL, 'Paper Burning Sword', 'Huh. This looks pretty cool. *gives it a click* Whoa. WHOA.''', 'Sword', 'None', 'items/swords/BurningPencil.swf', 'BurningPencil', 'iwsword', 'Weapon', 1, 25, 50, 30, 1, 1, 0, 1, 0, '0', true, true, false, false, false, 1, NULL, '0', NULL, '0', '', -1, 0, '', 'ffffff');
INSERT INTO "items" ("id", "class_id", "name", "description", "type", "element", "file", "link", "icon", "equipment", "level", "dps", "range", "rarity", "quantity", "stack", "cost", "coins", "diamonds", "crystal", "sell", "market", "temporary", "upgrade", "staff", "enh_id", "faction_id", "req_reputation", "req_class_id", "req_class_points", "req_quests", "quest_string_index", "quest_string_value", "meta", "color") VALUES (384, NULL, 'Nebula Star Sword', 'Beta Item', 'Sword', 'None', 'items/swords/AnaxianNebulaBreaker.swf', 'AnaxianNebulaBreaker', 'iwsword', 'Weapon', 1, 25, 50, 11, 1, 1, 0, 1, 0, '0', true, true, false, false, false, 1, NULL, '0', NULL, '0', '', -1, 0, '', 'ffffff');
INSERT INTO "items" ("id", "class_id", "name", "description", "type", "element", "file", "link", "icon", "equipment", "level", "dps", "range", "rarity", "quantity", "stack", "cost", "coins", "diamonds", "crystal", "sell", "market", "temporary", "upgrade", "staff", "enh_id", "faction_id", "req_reputation", "req_class_id", "req_class_points", "req_quests", "quest_string_index", "quest_string_value", "meta", "color") VALUES (385, NULL, 'Anaxian Blade', 'Exclusive Item', 'Dagger', 'None', 'items/daggers/AnaxianBalrogBlade3.swf', 'AnaxianBalrogBlade', 'iwdagger', 'Weapon', 1, 25, 50, 35, 1, 1, 250, 1, 0, '0', true, false, false, true, false, 1, NULL, '0', NULL, '0', '', -1, 0, '', '00ffff');
INSERT INTO "items" ("id", "class_id", "name", "description", "type", "element", "file", "link", "icon", "equipment", "level", "dps", "range", "rarity", "quantity", "stack", "cost", "coins", "diamonds", "crystal", "sell", "market", "temporary", "upgrade", "staff", "enh_id", "faction_id", "req_reputation", "req_class_id", "req_class_points", "req_quests", "quest_string_index", "quest_string_value", "meta", "color") VALUES (386, NULL, 'Azure Anaxian Blade', 'Exclusive Item', 'Dagger', 'None', 'items/daggers/AzureBalrogBlade.swf', 'AzureBalrogBlade', 'iwdagger', 'Weapon', 1, 25, 50, 35, 1, 1, 250, 1, 0, '0', true, false, false, true, false, 1, NULL, '0', NULL, '0', '', -1, 0, '', '00ffff');
INSERT INTO "items" ("id", "class_id", "name", "description", "type", "element", "file", "link", "icon", "equipment", "level", "dps", "range", "rarity", "quantity", "stack", "cost", "coins", "diamonds", "crystal", "sell", "market", "temporary", "upgrade", "staff", "enh_id", "faction_id", "req_reputation", "req_class_id", "req_class_points", "req_quests", "quest_string_index", "quest_string_value", "meta", "color") VALUES (387, NULL, 'Teeth Sketch', '', 'Item', 'None', '', 'iibag', 'iibag', 'Item', 1, 25, 50, 10, 1, 6, 0, 0, 0, '0', true, false, true, false, false, 1, NULL, '0', NULL, '0', '685', -1, 0, '', 'ffffff');
INSERT INTO "items" ("id", "class_id", "name", "description", "type", "element", "file", "link", "icon", "equipment", "level", "dps", "range", "rarity", "quantity", "stack", "cost", "coins", "diamonds", "crystal", "sell", "market", "temporary", "upgrade", "staff", "enh_id", "faction_id", "req_reputation", "req_class_id", "req_class_points", "req_quests", "quest_string_index", "quest_string_value", "meta", "color") VALUES (388, NULL, 'Sketch Grizzly Defeated', '', 'Item', 'None', '', 'iibag', 'iibag', 'Item', 1, 25, 50, 10, 1, 6, 0, 0, 0, '0', true, false, true, false, false, 1, NULL, '0', NULL, '0', '686', -1, 0, '', 'ffffff');
INSERT INTO "items" ("id", "class_id", "name", "description", "type", "element", "file", "link", "icon", "equipment", "level", "dps", "range", "rarity", "quantity", "stack", "cost", "coins", "diamonds", "crystal", "sell", "market", "temporary", "upgrade", "staff", "enh_id", "faction_id", "req_reputation", "req_class_id", "req_class_points", "req_quests", "quest_string_index", "quest_string_value", "meta", "color") VALUES (389, NULL, 'Crown Sketch', '', 'Item', 'None', '', 'iibag', 'iibag', 'Item', 1, 25, 50, 10, 1, 1, 0, 0, 0, '0', true, false, true, false, false, 1, NULL, '0', NULL, '0', '687', -1, 0, '', 'ffffff');
INSERT INTO "items" ("id", "class_id", "name", "description", "type", "element", "file", "link", "icon", "equipment", "level", "dps", "range", "rarity", "quantity", "stack", "cost", "coins", "diamonds", "crystal", "sell", "market", "temporary", "upgrade", "staff", "enh_id", "faction_id", "req_reputation", "req_class_id", "req_class_points", "req_quests", "quest_string_index", "quest_string_value", "meta", "color") VALUES (390, NULL, '???', '', 'Item', 'None', '', 'iibag', 'iibag', 'Item', 1, 25, 50, 10, 1, 1, 0, 0, 0, '0', true, false, true, false, false, 1, NULL, '0', NULL, '0', '688', -1, 0, '', 'ffffff');
INSERT INTO "items" ("id", "class_id", "name", "description", "type", "element", "file", "link", "icon", "equipment", "level", "dps", "range", "rarity", "quantity", "stack", "cost", "coins", "diamonds", "crystal", "sell", "market", "temporary", "upgrade", "staff", "enh_id", "faction_id", "req_reputation", "req_class_id", "req_class_points", "req_quests", "quest_string_index", "quest_string_value", "meta", "color") VALUES (391, NULL, 'Sketch Seal''s', 'Crafting gear in /Sketch', 'Item', 'None', '', 'iibag', 'iibag', 'Item', 1, 25, 50, 10, 1, 100, 0, 1, 0, '0', true, true, false, false, false, 1, NULL, '0', NULL, '0', '', -1, 0, '', 'ffffff');
INSERT INTO "items" ("id", "class_id", "name", "description", "type", "element", "file", "link", "icon", "equipment", "level", "dps", "range", "rarity", "quantity", "stack", "cost", "coins", "diamonds", "crystal", "sell", "market", "temporary", "upgrade", "staff", "enh_id", "faction_id", "req_reputation", "req_class_id", "req_class_points", "req_quests", "quest_string_index", "quest_string_value", "meta", "color") VALUES (392, NULL, 'Sketch Letter of Stroke', 'The dual blades of the Sketch Stroke.', 'Dagger', 'None', 'items/daggers/BloodletterOirgCCkertas.swf', 'BloodletterOirgCCkertas', 'iwdagger', 'Weapon', 1, 25, 50, 30, 1, 1, 0, 1, 0, '0', true, true, false, false, false, 1, NULL, '0', NULL, '0', '', -1, 0, '', 'ffffff');
INSERT INTO "items" ("id", "class_id", "name", "description", "type", "element", "file", "link", "icon", "equipment", "level", "dps", "range", "rarity", "quantity", "stack", "cost", "coins", "diamonds", "crystal", "sell", "market", "temporary", "upgrade", "staff", "enh_id", "faction_id", "req_reputation", "req_class_id", "req_class_points", "req_quests", "quest_string_index", "quest_string_value", "meta", "color") VALUES (393, NULL, 'Nulgath Sword of Sketch', 'Nulgath;Abyss General''s monstrous form is the result of his original form shifting to another reality. His sword has also shifted. but sketch', 'Sword', 'None', 'items/swords/MiltoniusNulgathswordKertas.swf', 'MiltoniusNulgathswordKertas', 'iwsword', 'Weapon', 1, 25, 50, 30, 1, 1, 0, 1, 0, '0', true, true, false, false, false, 1, NULL, '0', NULL, '0', '', -1, 0, '', 'ffffff');
INSERT INTO "items" ("id", "class_id", "name", "description", "type", "element", "file", "link", "icon", "equipment", "level", "dps", "range", "rarity", "quantity", "stack", "cost", "coins", "diamonds", "crystal", "sell", "market", "temporary", "upgrade", "staff", "enh_id", "faction_id", "req_reputation", "req_class_id", "req_class_points", "req_quests", "quest_string_index", "quest_string_value", "meta", "color") VALUES (394, NULL, 'Crystal Phoenix Sketch of Nulgath', 'As he experiments with alchemy;Nulgath uses his power to trans-mutate the Phoenix Blade into an indestructible crystal sword. What other iconic items will Nulgath manipulate next?', 'Sword', 'None', 'items/swords/MiltonPoolPhoenixSwordKertas.swf', 'MiltonPoolPhoenixSwordKertas', 'iwsword', 'Weapon', 1, 25, 50, 30, 1, 1, 0, 1, 0, '0', true, true, false, false, false, 1, NULL, '0', NULL, '0', '', -1, 0, '', 'ffffff');
INSERT INTO "items" ("id", "class_id", "name", "description", "type", "element", "file", "link", "icon", "equipment", "level", "dps", "range", "rarity", "quantity", "stack", "cost", "coins", "diamonds", "crystal", "sell", "market", "temporary", "upgrade", "staff", "enh_id", "faction_id", "req_reputation", "req_class_id", "req_class_points", "req_quests", "quest_string_index", "quest_string_value", "meta", "color") VALUES (395, NULL, 'Oblivion Sword Sketch of Nulgath', 'This mysterious;living blade has destroyed innumerable lives. You know of only one who has had the power to wield it;the old one. And now you may wield it''s power and live to tell about it… for Sketch.', 'Sword', 'None', 'items/swords/MiltoniusPencilsword01.swf', 'MiltoniusPencilsword01', 'iwsword', 'Weapon', 1, 25, 50, 30, 1, 1, 0, 1, 0, '0', true, true, false, false, false, 1, NULL, '0', NULL, '0', '', -1, 0, '', 'ffffff');
INSERT INTO "items" ("id", "class_id", "name", "description", "type", "element", "file", "link", "icon", "equipment", "level", "dps", "range", "rarity", "quantity", "stack", "cost", "coins", "diamonds", "crystal", "sell", "market", "temporary", "upgrade", "staff", "enh_id", "faction_id", "req_reputation", "req_class_id", "req_class_points", "req_quests", "quest_string_index", "quest_string_value", "meta", "color") VALUES (396, NULL, 'Sketch Pirate Cutlass', 'none...', 'Sword', 'None', 'items/swords/SketchRhubarbCutlass.swf', 'SketchRhubarbCutlass', 'iwsword', 'Weapon', 1, 25, 50, 30, 1, 1, 0, 1, 0, '0', true, true, false, false, false, 1, NULL, '0', NULL, '0', '', -1, 0, '', 'ffffff');
INSERT INTO "items" ("id", "class_id", "name", "description", "type", "element", "file", "link", "icon", "equipment", "level", "dps", "range", "rarity", "quantity", "stack", "cost", "coins", "diamonds", "crystal", "sell", "market", "temporary", "upgrade", "staff", "enh_id", "faction_id", "req_reputation", "req_class_id", "req_class_points", "req_quests", "quest_string_index", "quest_string_value", "meta", "color") VALUES (397, NULL, 'Sketch Pirate Captain Cutie', 'Everyone knows you''re in charge now. Make those scallywags walk the plank!', 'Helm', 'None', 'items/helms/SketchPirateCaptainhat1.swf', 'SketchPirateCaptainhat1', 'iihelm', 'he', 1, 25, 50, 30, 1, 1, 0, 1, 0, '0', true, true, false, false, false, 1, NULL, '0', NULL, '0', '', -1, 0, '', 'ffffff');
INSERT INTO "items" ("id", "class_id", "name", "description", "type", "element", "file", "link", "icon", "equipment", "level", "dps", "range", "rarity", "quantity", "stack", "cost", "coins", "diamonds", "crystal", "sell", "market", "temporary", "upgrade", "staff", "enh_id", "faction_id", "req_reputation", "req_class_id", "req_class_points", "req_quests", "quest_string_index", "quest_string_value", "meta", "color") VALUES (398, NULL, 'Sketch Pirate Captain Hat', 'Everyone knows you''re in charge now. Make those scallywags walk the plank!', 'Helm', 'None', 'items/helms/SketchPirateCaptainhat2.swf', 'SketchPirateCaptainhat2', 'iihelm', 'he', 1, 25, 50, 30, 1, 1, 0, 1, 0, '0', true, true, false, false, false, 1, NULL, '0', NULL, '0', '', -1, 0, '', 'ffffff');
INSERT INTO "items" ("id", "class_id", "name", "description", "type", "element", "file", "link", "icon", "equipment", "level", "dps", "range", "rarity", "quantity", "stack", "cost", "coins", "diamonds", "crystal", "sell", "market", "temporary", "upgrade", "staff", "enh_id", "faction_id", "req_reputation", "req_class_id", "req_class_points", "req_quests", "quest_string_index", "quest_string_value", "meta", "color") VALUES (399, NULL, 'Sketch Royal Blade Alteon', 'It may seem abnormally heavy at first;but if the wielder is a worthy owner;the soothing power of light helps support the weight you must carry. It''s the same way your burdened responsibility as a faithful commander must carry to save the world.', 'Sword', 'None', 'items/swords/SketchAltDragSword.swf', 'SketchAltDragSword', 'iwsword', 'Weapon', 1, 25, 50, 22, 1, 1, 5000, 0, 0, '0', true, true, false, false, false, 1, NULL, '0', NULL, '0', '', -1, 0, '', 'ffffff');
INSERT INTO "items" ("id", "class_id", "name", "description", "type", "element", "file", "link", "icon", "equipment", "level", "dps", "range", "rarity", "quantity", "stack", "cost", "coins", "diamonds", "crystal", "sell", "market", "temporary", "upgrade", "staff", "enh_id", "faction_id", "req_reputation", "req_class_id", "req_class_points", "req_quests", "quest_string_index", "quest_string_value", "meta", "color") VALUES (400, NULL, 'Sketch Baby Dragon', 'You must be very lucky to own such a dragon…', 'Pet', 'None', 'items/pets/SketchBabyDragon1.swf', 'SketchBabyDragon1', 'iipet', 'pe', 1, 25, 50, 30, 1, 1, 0, 1, 0, '0', true, true, false, false, false, 1, NULL, '0', NULL, '0', '', -1, 0, '', 'ffffff');
INSERT INTO "items" ("id", "class_id", "name", "description", "type", "element", "file", "link", "icon", "equipment", "level", "dps", "range", "rarity", "quantity", "stack", "cost", "coins", "diamonds", "crystal", "sell", "market", "temporary", "upgrade", "staff", "enh_id", "faction_id", "req_reputation", "req_class_id", "req_class_points", "req_quests", "quest_string_index", "quest_string_value", "meta", "color") VALUES (401, 8, 'Rogue (Class)', 'Rogues follow their own set of rules. With a combination of speed;cunning and poisons;you take your enemies by surprise.', 'Class', 'None', 'BaseRogue2xx.swf', 'Rogue2', 'iiclass', 'ar', 1, 25, 50, 11, 1, 1, 1000, 0, 0, '0', true, true, false, false, false, 1, NULL, '0', NULL, '0', '', -1, 0, '', 'ffffff');
INSERT INTO "items" ("id", "class_id", "name", "description", "type", "element", "file", "link", "icon", "equipment", "level", "dps", "range", "rarity", "quantity", "stack", "cost", "coins", "diamonds", "crystal", "sell", "market", "temporary", "upgrade", "staff", "enh_id", "faction_id", "req_reputation", "req_class_id", "req_class_points", "req_quests", "quest_string_index", "quest_string_value", "meta", "color") VALUES (462, NULL, '??? P', '', 'QuestItem', 'None', 'iibag', 'iibag', 'iibag', 'None', 1, 25, 50, 10, 1, 15, 0, 0, 0, '0', true, true, true, false, false, 1, NULL, '0', NULL, '0', '', -1, 0, '', 'ffffff');
INSERT INTO "items" ("id", "class_id", "name", "description", "type", "element", "file", "link", "icon", "equipment", "level", "dps", "range", "rarity", "quantity", "stack", "cost", "coins", "diamonds", "crystal", "sell", "market", "temporary", "upgrade", "staff", "enh_id", "faction_id", "req_reputation", "req_class_id", "req_class_points", "req_quests", "quest_string_index", "quest_string_value", "meta", "color") VALUES (402, 5, 'Warrior (Class)', 'The heart of a warrior is filled with courage and strength. Your skills with weapons in close combat make you a powerful force on the battlefield.', 'Class', 'None', 'NewWarriorB2.swf', 'NewWarriorB2', 'iiclass', 'ar', 1, 25, 50, 11, 1, 1, 1000, 0, 0, '0', true, true, false, false, false, 1, NULL, '0', NULL, '0', '', -1, 0, '', 'ffffff');
INSERT INTO "items" ("id", "class_id", "name", "description", "type", "element", "file", "link", "icon", "equipment", "level", "dps", "range", "rarity", "quantity", "stack", "cost", "coins", "diamonds", "crystal", "sell", "market", "temporary", "upgrade", "staff", "enh_id", "faction_id", "req_reputation", "req_class_id", "req_class_points", "req_quests", "quest_string_index", "quest_string_value", "meta", "color") VALUES (403, 6, 'Mage (Class)', 'Magic courses through your veins. Your arcane powers enable you to cast powerful spells.', 'Class', 'None', 'BaseMage2xx.swf', 'Mage2', 'iiclass', 'ar', 1, 25, 50, 11, 1, 1, 1000, 0, 0, '0', true, true, false, false, false, 1, NULL, '0', NULL, '0', '', -1, 0, '', 'ffffff');
INSERT INTO "items" ("id", "class_id", "name", "description", "type", "element", "file", "link", "icon", "equipment", "level", "dps", "range", "rarity", "quantity", "stack", "cost", "coins", "diamonds", "crystal", "sell", "market", "temporary", "upgrade", "staff", "enh_id", "faction_id", "req_reputation", "req_class_id", "req_class_points", "req_quests", "quest_string_index", "quest_string_value", "meta", "color") VALUES (404, 7, 'Healer (Class)', 'Healers are servants of good who use their powers to aid the sick;weak;and injured. Their powerful healing magic is often the difference between a groups victory or doom.', 'Class', 'None', 'NewHealerR2.swf', 'NewHealerB2', 'iiclass', 'ar', 1, 25, 50, 11, 1, 1, 1000, 0, 0, '0', true, true, false, false, false, 1, NULL, '0', NULL, '0', '', -1, 0, '', 'ffffff');
INSERT INTO "items" ("id", "class_id", "name", "description", "type", "element", "file", "link", "icon", "equipment", "level", "dps", "range", "rarity", "quantity", "stack", "cost", "coins", "diamonds", "crystal", "sell", "market", "temporary", "upgrade", "staff", "enh_id", "faction_id", "req_reputation", "req_class_id", "req_class_points", "req_quests", "quest_string_index", "quest_string_value", "meta", "color") VALUES (405, 4, 'Alpha Pirate (Class)', 'Pirates are opportunistic rogues and treasure hunters of the sea. Combat might not always go their way but they will do the best they can to make the most of any combat situation they find themselves in. You never really know what move a pirate will make next;and just as often;they don''t know either.', 'Class', 'None', 'pirate2_skin.swf', 'Pirate2', 'iiclass', 'ar', 1, 25, 50, 65, 1, 1, 0, 1, 0, '0', true, false, false, false, false, 1, 2, '302500', NULL, '0', '', -1, 0, '', 'ffffff');
INSERT INTO "items" ("id", "class_id", "name", "description", "type", "element", "file", "link", "icon", "equipment", "level", "dps", "range", "rarity", "quantity", "stack", "cost", "coins", "diamonds", "crystal", "sell", "market", "temporary", "upgrade", "staff", "enh_id", "faction_id", "req_reputation", "req_class_id", "req_class_points", "req_quests", "quest_string_index", "quest_string_value", "meta", "color") VALUES (406, NULL, 'Soul King Guard', 'Never more........ see what i can do', 'Armor', 'None', 'datapaw11.swf', 'Datapaw', 'iwarmor', 'co', 1, 25, 50, 10, 1, 1, 0, 0, 0, '0', true, true, false, false, false, 1, NULL, '0', NULL, '0', '', -1, 0, '', 'ffffff');
INSERT INTO "items" ("id", "class_id", "name", "description", "type", "element", "file", "link", "icon", "equipment", "level", "dps", "range", "rarity", "quantity", "stack", "cost", "coins", "diamonds", "crystal", "sell", "market", "temporary", "upgrade", "staff", "enh_id", "faction_id", "req_reputation", "req_class_id", "req_class_points", "req_quests", "quest_string_index", "quest_string_value", "meta", "color") VALUES (407, NULL, 'Replacement Item #1459 - Erebus', 'Never more........ see what i can do', 'Helm', 'None', 'items/helms/Datapawhelm.swf', 'DatapawHelm', 'iihelm', 'he', 1, 25, 50, 1, 1, 1, 0, 0, 0, '0', true, true, false, false, false, 1, NULL, '0', NULL, '0', '', -1, 0, '', 'ffffff');
INSERT INTO "items" ("id", "class_id", "name", "description", "type", "element", "file", "link", "icon", "equipment", "level", "dps", "range", "rarity", "quantity", "stack", "cost", "coins", "diamonds", "crystal", "sell", "market", "temporary", "upgrade", "staff", "enh_id", "faction_id", "req_reputation", "req_class_id", "req_class_points", "req_quests", "quest_string_index", "quest_string_value", "meta", "color") VALUES (408, NULL, 'Soul King Guard Aura', '', 'Cape', 'None', 'items/capes/SoulKingGuardAura.swf', 'SoulKingGuardAura', 'iicape', 'ba', 1, 25, 50, 10, 1, 1, 0, 0, 0, '0', true, true, false, false, false, 1, NULL, '0', NULL, '0', '', -1, 0, '', 'ffffff');
INSERT INTO "items" ("id", "class_id", "name", "description", "type", "element", "file", "link", "icon", "equipment", "level", "dps", "range", "rarity", "quantity", "stack", "cost", "coins", "diamonds", "crystal", "sell", "market", "temporary", "upgrade", "staff", "enh_id", "faction_id", "req_reputation", "req_class_id", "req_class_points", "req_quests", "quest_string_index", "quest_string_value", "meta", "color") VALUES (409, NULL, 'Guardian Soul King', '', 'Armor', 'None', 'DatapawFixed1.swf', 'datapaw11', 'iwarmor', 'co', 1, 25, 50, 10, 1, 1, 0, 0, 0, '0', true, true, false, false, false, 1, NULL, '0', NULL, '0', '', -1, 0, '', 'ffffff');
INSERT INTO "items" ("id", "class_id", "name", "description", "type", "element", "file", "link", "icon", "equipment", "level", "dps", "range", "rarity", "quantity", "stack", "cost", "coins", "diamonds", "crystal", "sell", "market", "temporary", "upgrade", "staff", "enh_id", "faction_id", "req_reputation", "req_class_id", "req_class_points", "req_quests", "quest_string_index", "quest_string_value", "meta", "color") VALUES (410, NULL, 'Ridero Skeleton', '', 'Armor', 'None', 'okayokay.swf', 'HBDynoRider', 'iwarmor', 'co', 1, 25, 50, 10, 1, 1, 0, 0, 0, '0', true, true, false, false, false, 1, NULL, '0', NULL, '0', '', -1, 0, '', 'ffffff');
INSERT INTO "items" ("id", "class_id", "name", "description", "type", "element", "file", "link", "icon", "equipment", "level", "dps", "range", "rarity", "quantity", "stack", "cost", "coins", "diamonds", "crystal", "sell", "market", "temporary", "upgrade", "staff", "enh_id", "faction_id", "req_reputation", "req_class_id", "req_class_points", "req_quests", "quest_string_index", "quest_string_value", "meta", "color") VALUES (411, NULL, 'gwe yong', '', 'Armor', 'None', 'Hakdog.swf', 'HBDynoRider', 'iwarmor', 'co', 1, 25, 50, 10, 1, 1, 0, 0, 0, '0', true, true, false, false, false, 1, NULL, '0', NULL, '0', '', -1, 0, '', 'ffffff');
INSERT INTO "items" ("id", "class_id", "name", "description", "type", "element", "file", "link", "icon", "equipment", "level", "dps", "range", "rarity", "quantity", "stack", "cost", "coins", "diamonds", "crystal", "sell", "market", "temporary", "upgrade", "staff", "enh_id", "faction_id", "req_reputation", "req_class_id", "req_class_points", "req_quests", "quest_string_index", "quest_string_value", "meta", "color") VALUES (412, NULL, 'Supreme katana Cape', 'This cape are limted get this as soon possible because you will never get this again', 'Cape', 'None', 'items/capes/DatapawCapeKatana1.swf', 'DatapawCapeKatana', 'iicape', 'ba', 1, 25, 50, 70, 1, 1, 1500, 1, 0, '0', true, true, false, false, false, 1, NULL, '0', NULL, '0', '', -1, 0, '', 'ffffff');
INSERT INTO "items" ("id", "class_id", "name", "description", "type", "element", "file", "link", "icon", "equipment", "level", "dps", "range", "rarity", "quantity", "stack", "cost", "coins", "diamonds", "crystal", "sell", "market", "temporary", "upgrade", "staff", "enh_id", "faction_id", "req_reputation", "req_class_id", "req_class_points", "req_quests", "quest_string_index", "quest_string_value", "meta", "color") VALUES (413, NULL, 'Ultima Devine Cape', 'This Cape has combined 7 strongest sword;this only 5x quantity', 'Cape', 'None', 'items/capes/DatapawDevineKatana1.swf', 'DatapawDevineKatana', 'iicape', 'ba', 1, 25, 50, 70, 1, 1, 2000, 1, 0, '0', true, true, false, false, false, 1, NULL, '0', NULL, '0', '', -1, 0, '', 'ffffff');
INSERT INTO "items" ("id", "class_id", "name", "description", "type", "element", "file", "link", "icon", "equipment", "level", "dps", "range", "rarity", "quantity", "stack", "cost", "coins", "diamonds", "crystal", "sell", "market", "temporary", "upgrade", "staff", "enh_id", "faction_id", "req_reputation", "req_class_id", "req_class_points", "req_quests", "quest_string_index", "quest_string_value", "meta", "color") VALUES (414, NULL, 'Red Hunting Hood', '-', 'Helm', 'None', 'items/helms/Hoodredriding1.swf', 'Hoodredriding1', 'iihelm', 'he', 1, 25, 50, 11, 1, 1, 0, 1, 0, '0', true, true, false, false, false, 1, NULL, '0', NULL, '0', '', -1, 0, '', 'ffffff');
INSERT INTO "items" ("id", "class_id", "name", "description", "type", "element", "file", "link", "icon", "equipment", "level", "dps", "range", "rarity", "quantity", "stack", "cost", "coins", "diamonds", "crystal", "sell", "market", "temporary", "upgrade", "staff", "enh_id", "faction_id", "req_reputation", "req_class_id", "req_class_points", "req_quests", "quest_string_index", "quest_string_value", "meta", "color") VALUES (415, NULL, 'Crystallis Rescuer', 'I <3 Pink', 'Armor', 'None', 'CrystallisRescuerCasual.swf', 'CrystallisRescuerCasual', 'armor', 'co', 1, 25, 50, 11, 1, 1, 100, 0, 0, '0', true, true, false, false, true, 1, 1, '0', NULL, '0', '', -1, 0, '', 'fffaff');
INSERT INTO "items" ("id", "class_id", "name", "description", "type", "element", "file", "link", "icon", "equipment", "level", "dps", "range", "rarity", "quantity", "stack", "cost", "coins", "diamonds", "crystal", "sell", "market", "temporary", "upgrade", "staff", "enh_id", "faction_id", "req_reputation", "req_class_id", "req_class_points", "req_quests", "quest_string_index", "quest_string_value", "meta", "color") VALUES (416, NULL, 'Plate of the Fallen', 'Foes will want to make sure you stay down because if there is just a ounce of breath left in a member of the fallen;they will strike their enemies down with all their might. EVIL WILL FALL AS THE FALLEN', 'Armor', 'None', 'TheFallen-10Jul13.swf', 'TheFallen', 'iwarmor', 'co', 1, 25, 50, 35, 1, 1, 0, 1, 0, '0', true, true, false, false, false, 1, NULL, '0', NULL, '0', '', -1, 0, '', '8080c0');
INSERT INTO "items" ("id", "class_id", "name", "description", "type", "element", "file", "link", "icon", "equipment", "level", "dps", "range", "rarity", "quantity", "stack", "cost", "coins", "diamonds", "crystal", "sell", "market", "temporary", "upgrade", "staff", "enh_id", "faction_id", "req_reputation", "req_class_id", "req_class_points", "req_quests", "quest_string_index", "quest_string_value", "meta", "color") VALUES (417, NULL, 'Bright Hood of The Fallen', 'Evil will never know the face of The Fallen;but they will always remember The Fallen''s smirk as their lives draw to an end!', 'Helm', 'None', 'items/helms/TheFallensHood2-20150219_r1.swf', 'TheFallensHood3b', 'iihelm', 'he', 1, 25, 50, 35, 1, 1, 0, 1, 0, '0', true, true, false, false, false, 1, NULL, '0', NULL, '0', '', -1, 0, '', '8080c0');
INSERT INTO "items" ("id", "class_id", "name", "description", "type", "element", "file", "link", "icon", "equipment", "level", "dps", "range", "rarity", "quantity", "stack", "cost", "coins", "diamonds", "crystal", "sell", "market", "temporary", "upgrade", "staff", "enh_id", "faction_id", "req_reputation", "req_class_id", "req_class_points", "req_quests", "quest_string_index", "quest_string_value", "meta", "color") VALUES (418, NULL, 'Cape of the Fallen', 'Wrap your fallen hopes in this tattered cape. They wont save you now.', 'Cape', 'None', 'items/capes/SFCCape.swf', 'SFCCape', 'iicape', 'ba', 1, 25, 50, 35, 1, 1, 0, 1, 0, '0', true, true, false, false, false, 1, NULL, '0', NULL, '0', '', -1, 0, '', '8080c0');
INSERT INTO "items" ("id", "class_id", "name", "description", "type", "element", "file", "link", "icon", "equipment", "level", "dps", "range", "rarity", "quantity", "stack", "cost", "coins", "diamonds", "crystal", "sell", "market", "temporary", "upgrade", "staff", "enh_id", "faction_id", "req_reputation", "req_class_id", "req_class_points", "req_quests", "quest_string_index", "quest_string_value", "meta", "color") VALUES (419, NULL, 'Greenguard Ranger', 'Skulking through Greenguard Forest;the Greenguard Rangers are stealthy;wealthy;and wise to the ways of their opponents.', 'Armor', 'None', 'rangerbido.swf', 'RangerBido', 'iwarmor', 'co', 1, 25, 50, 11, 1, 1, 0, 1, 0, '0', true, true, false, false, false, 1, NULL, '0', NULL, '0', '', -1, 0, '', 'ffffff');
INSERT INTO "items" ("id", "class_id", "name", "description", "type", "element", "file", "link", "icon", "equipment", "level", "dps", "range", "rarity", "quantity", "stack", "cost", "coins", "diamonds", "crystal", "sell", "market", "temporary", "upgrade", "staff", "enh_id", "faction_id", "req_reputation", "req_class_id", "req_class_points", "req_quests", "quest_string_index", "quest_string_value", "meta", "color") VALUES (420, NULL, 'GreenGuard Ranger Hood', 'Skulking through Greenguard Forest;the Greenguard Rangers are stealthy;wealthy and wise to the ways of their opponents.', 'Helm', 'None', 'items/helms/rangerbidohoodm.swf', 'RangerBidoHoodM', 'iihelm', 'he', 1, 25, 50, 11, 1, 1, 0, 1, 0, '0', true, true, false, false, false, 1, NULL, '0', NULL, '0', '', -1, 0, '', 'ffffff');
INSERT INTO "items" ("id", "class_id", "name", "description", "type", "element", "file", "link", "icon", "equipment", "level", "dps", "range", "rarity", "quantity", "stack", "cost", "coins", "diamonds", "crystal", "sell", "market", "temporary", "upgrade", "staff", "enh_id", "faction_id", "req_reputation", "req_class_id", "req_class_points", "req_quests", "quest_string_index", "quest_string_value", "meta", "color") VALUES (421, NULL, 'Greenguard Ranger''s Quiver', 'Skulking through Greenguard Forest;the Greenguard Rangers are stealthy;wealthy and wise to the ways of their opponents.', 'Cape', 'None', 'items/capes/rangerbidocape.swf', 'RangerBidoCape', 'iicape', 'ba', 1, 25, 50, 11, 1, 1, 0, 1, 0, '0', true, true, false, false, false, 1, NULL, '0', NULL, '0', '', -1, 0, '', 'ffffff');
INSERT INTO "items" ("id", "class_id", "name", "description", "type", "element", "file", "link", "icon", "equipment", "level", "dps", "range", "rarity", "quantity", "stack", "cost", "coins", "diamonds", "crystal", "sell", "market", "temporary", "upgrade", "staff", "enh_id", "faction_id", "req_reputation", "req_class_id", "req_class_points", "req_quests", "quest_string_index", "quest_string_value", "meta", "color") VALUES (422, NULL, 'Rogue Fiend Katana', 'Skulking through Greenguard Forest;the Greenguard Rangers are stealthy;wealthy and wise to the ways of their opponents.', 'Sword', 'None', 'items/bows/roguefiendkat.swf', 'RogueFiendKat', 'iwsword', 'Weapon', 1, 25, 50, 10, 1, 1, 0, 1, 0, '0', true, true, false, false, false, 1, NULL, '0', NULL, '0', '', -1, 0, '', 'ffffff');
INSERT INTO "items" ("id", "class_id", "name", "description", "type", "element", "file", "link", "icon", "equipment", "level", "dps", "range", "rarity", "quantity", "stack", "cost", "coins", "diamonds", "crystal", "sell", "market", "temporary", "upgrade", "staff", "enh_id", "faction_id", "req_reputation", "req_class_id", "req_class_points", "req_quests", "quest_string_index", "quest_string_value", "meta", "color") VALUES (423, NULL, 'Greenguard Ranger''s Bow', 'Skulking through Greenguard Forest;the Greenguard Rangers are stealthy;wealthy and wise to the ways of their opponents.', 'Sword', 'None', 'items/bows/rangerbidobowr1.swf', 'RangerBidoBow', 'iwsword', 'Weapon', 1, 25, 50, 10, 1, 1, 0, 1, 0, '0', true, true, false, false, false, 1, NULL, '0', NULL, '0', '', -1, 0, '', 'ffffff');
INSERT INTO "items" ("id", "class_id", "name", "description", "type", "element", "file", "link", "icon", "equipment", "level", "dps", "range", "rarity", "quantity", "stack", "cost", "coins", "diamonds", "crystal", "sell", "market", "temporary", "upgrade", "staff", "enh_id", "faction_id", "req_reputation", "req_class_id", "req_class_points", "req_quests", "quest_string_index", "quest_string_value", "meta", "color") VALUES (424, NULL, 'Classic Healer Armor', 'Healers are servants of good who use their powers to aid the sick;weak;and injured. Their powerful healing magic is often the difference between a groups victory or doom.', 'Armor', 'None', 'priest_skin.swf', 'Priest', 'iwarmor', 'co', 1, 25, 50, 11, 1, 1, 3000, 0, 0, '0', true, true, false, false, false, 1, NULL, '0', NULL, '0', '', -1, 0, '', 'ffffff');
INSERT INTO "items" ("id", "class_id", "name", "description", "type", "element", "file", "link", "icon", "equipment", "level", "dps", "range", "rarity", "quantity", "stack", "cost", "coins", "diamonds", "crystal", "sell", "market", "temporary", "upgrade", "staff", "enh_id", "faction_id", "req_reputation", "req_class_id", "req_class_points", "req_quests", "quest_string_index", "quest_string_value", "meta", "color") VALUES (425, NULL, 'Classic Rogue Armor', 'Rogues follow their own set of rules. With a combination of speed;cunning and poisons you take your enemies by surprise.', 'Armor', 'None', 'rogue_skin.swf', 'Rogue', 'iwarmor', 'co', 1, 25, 50, 11, 1, 1, 3000, 0, 0, '0', true, true, false, false, false, 1, NULL, '0', NULL, '0', '', -1, 0, '', 'ffffff');
INSERT INTO "items" ("id", "class_id", "name", "description", "type", "element", "file", "link", "icon", "equipment", "level", "dps", "range", "rarity", "quantity", "stack", "cost", "coins", "diamonds", "crystal", "sell", "market", "temporary", "upgrade", "staff", "enh_id", "faction_id", "req_reputation", "req_class_id", "req_class_points", "req_quests", "quest_string_index", "quest_string_value", "meta", "color") VALUES (426, NULL, 'Classic Mage Armor', 'Magic courses through your veins. Your arcane powers enable you to cast powerful spells.', 'Armor', 'None', 'mage_skin.swf', 'Mage', 'iwarmor', 'co', 1, 25, 50, 11, 1, 1, 3000, 0, 0, '0', true, true, false, false, false, 1, NULL, '0', NULL, '0', '', -1, 0, '', 'ffffff');
INSERT INTO "items" ("id", "class_id", "name", "description", "type", "element", "file", "link", "icon", "equipment", "level", "dps", "range", "rarity", "quantity", "stack", "cost", "coins", "diamonds", "crystal", "sell", "market", "temporary", "upgrade", "staff", "enh_id", "faction_id", "req_reputation", "req_class_id", "req_class_points", "req_quests", "quest_string_index", "quest_string_value", "meta", "color") VALUES (427, NULL, 'Undead Pirate Armor', 'An undead pirate''s life for me!', 'Armor', 'None', 'undead4_skin.swf', 'Undead4', 'iwarmor', 'co', 1, 25, 50, 12, 1, 1, 10000, 0, 0, '0', true, true, false, false, false, 1, NULL, '0', NULL, '0', '', -1, 0, '', 'ffffff');
INSERT INTO "items" ("id", "class_id", "name", "description", "type", "element", "file", "link", "icon", "equipment", "level", "dps", "range", "rarity", "quantity", "stack", "cost", "coins", "diamonds", "crystal", "sell", "market", "temporary", "upgrade", "staff", "enh_id", "faction_id", "req_reputation", "req_class_id", "req_class_points", "req_quests", "quest_string_index", "quest_string_value", "meta", "color") VALUES (428, NULL, 'Undead Fighter Armor', 'Surprisingly strong for something that is just cartilage and calcium. Be careful;though; sticks and stones may break these bones!', 'Armor', 'None', 'TempUndeadMelee_skin.swf', 'TempUndeadMelee', 'iwarmor', 'co', 1, 25, 50, 12, 1, 1, 10000, 0, 0, '0', true, true, false, false, false, 1, NULL, '0', NULL, '0', '', -1, 0, '', 'ffffff');
INSERT INTO "items" ("id", "class_id", "name", "description", "type", "element", "file", "link", "icon", "equipment", "level", "dps", "range", "rarity", "quantity", "stack", "cost", "coins", "diamonds", "crystal", "sell", "market", "temporary", "upgrade", "staff", "enh_id", "faction_id", "req_reputation", "req_class_id", "req_class_points", "req_quests", "quest_string_index", "quest_string_value", "meta", "color") VALUES (429, NULL, 'Undead Mage Armor', 'You''ve been turned into a skeletal undead!', 'Armor', 'None', 'TempUndeadMage_skin.swf', 'TempUndeadMage', 'iwarmor', 'co', 1, 25, 50, 12, 1, 1, 10000, 0, 0, '0', true, true, false, false, false, 1, NULL, '0', NULL, '0', '', -1, 0, '', 'ffffff');
INSERT INTO "items" ("id", "class_id", "name", "description", "type", "element", "file", "link", "icon", "equipment", "level", "dps", "range", "rarity", "quantity", "stack", "cost", "coins", "diamonds", "crystal", "sell", "market", "temporary", "upgrade", "staff", "enh_id", "faction_id", "req_reputation", "req_class_id", "req_class_points", "req_quests", "quest_string_index", "quest_string_value", "meta", "color") VALUES (430, NULL, 'Default Sword', 'If you have a Legend only weapon equipped when your membership upgrade expires or when merging a weapon that''s equipped into something else;this weapon is temporarily equipped by default.', 'Sword', 'None', 'items/swords/sword01.swf', 'sword01', 'iwsword', 'Weapon', 1, 25, 50, 10, 1, 1, 800, 0, 0, '0', true, true, false, false, false, 1, NULL, '0', NULL, '0', '', -1, 0, '', 'ffffff');
INSERT INTO "items" ("id", "class_id", "name", "description", "type", "element", "file", "link", "icon", "equipment", "level", "dps", "range", "rarity", "quantity", "stack", "cost", "coins", "diamonds", "crystal", "sell", "market", "temporary", "upgrade", "staff", "enh_id", "faction_id", "req_reputation", "req_class_id", "req_class_points", "req_quests", "quest_string_index", "quest_string_value", "meta", "color") VALUES (431, NULL, 'Genesis Sword', 'No one''s quite sure where this sword came from but it was one of the first to be found in this land.', 'Sword', 'None', 'items/swords/sword01a.swf', 'WarriorFWeapon', 'iwsword', 'Weapon', 1, 25, 50, 10, 1, 1, 5000, 0, 0, '0', true, true, false, false, false, 1, NULL, '0', NULL, '0', '', -1, 0, '', 'ffffff');
INSERT INTO "items" ("id", "class_id", "name", "description", "type", "element", "file", "link", "icon", "equipment", "level", "dps", "range", "rarity", "quantity", "stack", "cost", "coins", "diamonds", "crystal", "sell", "market", "temporary", "upgrade", "staff", "enh_id", "faction_id", "req_reputation", "req_class_id", "req_class_points", "req_quests", "quest_string_index", "quest_string_value", "meta", "color") VALUES (432, NULL, 'Dragon Saw', 'Carved from a massive scale;the damage this sword deals is outweighed only by its cool look.', 'Sword', 'None', 'items/swords/sword04.swf', 'sword04', 'iwsword', 'Weapon', 1, 25, 50, 10, 1, 1, 2500, 0, 0, '0', true, true, false, false, false, 1, NULL, '0', NULL, '0', '', -1, 0, '', 'ffffff');
INSERT INTO "items" ("id", "class_id", "name", "description", "type", "element", "file", "link", "icon", "equipment", "level", "dps", "range", "rarity", "quantity", "stack", "cost", "coins", "diamonds", "crystal", "sell", "market", "temporary", "upgrade", "staff", "enh_id", "faction_id", "req_reputation", "req_class_id", "req_class_points", "req_quests", "quest_string_index", "quest_string_value", "meta", "color") VALUES (433, NULL, 'Sun Sabre', 'Sword of the burning sun!', 'Sword', 'None', 'items/swords/sword46.swf', 'sword46', 'iwsword', 'Weapon', 1, 25, 50, 10, 1, 1, 20000, 0, 0, '0', true, true, false, false, false, 1, NULL, '0', NULL, '0', '', -1, 0, '', 'ffffff');
INSERT INTO "items" ("id", "class_id", "name", "description", "type", "element", "file", "link", "icon", "equipment", "level", "dps", "range", "rarity", "quantity", "stack", "cost", "coins", "diamonds", "crystal", "sell", "market", "temporary", "upgrade", "staff", "enh_id", "faction_id", "req_reputation", "req_class_id", "req_class_points", "req_quests", "quest_string_index", "quest_string_value", "meta", "color") VALUES (434, NULL, 'Default Staff', 'Staff', 'Staff', 'None', 'items/staves/staff01.swf', 'staff01', 'iwstaff', 'Weapon', 1, 25, 50, 10, 1, 1, 700, 0, 0, '0', true, true, false, false, false, 1, NULL, '0', NULL, '0', '', -1, 0, '', 'ffffff');
INSERT INTO "items" ("id", "class_id", "name", "description", "type", "element", "file", "link", "icon", "equipment", "level", "dps", "range", "rarity", "quantity", "stack", "cost", "coins", "diamonds", "crystal", "sell", "market", "temporary", "upgrade", "staff", "enh_id", "faction_id", "req_reputation", "req_class_id", "req_class_points", "req_quests", "quest_string_index", "quest_string_value", "meta", "color") VALUES (435, NULL, 'Default Dagger', 'Dagger', 'Dagger', 'None', 'items/daggers/dagger01.swf', 'dagger01', 'iwdagger', 'Weapon', 1, 25, 50, 10, 1, 1, 700, 0, 0, '0', true, true, false, false, false, 1, NULL, '0', NULL, '0', '', -1, 0, '', 'ffffff');
INSERT INTO "items" ("id", "class_id", "name", "description", "type", "element", "file", "link", "icon", "equipment", "level", "dps", "range", "rarity", "quantity", "stack", "cost", "coins", "diamonds", "crystal", "sell", "market", "temporary", "upgrade", "staff", "enh_id", "faction_id", "req_reputation", "req_class_id", "req_class_points", "req_quests", "quest_string_index", "quest_string_value", "meta", "color") VALUES (436, NULL, 'Sword Breaker', 'This weapon was crafted by a strange race of beings. It was designed to be the only weapon capable of fighting a Starsword;but it was lost before it could be put into production.', 'Sword', 'None', 'items/swords/starswordbreaker.swf', 'starswordbreaker', 'iwsword', 'Weapon', 1, 25, 50, 10, 1, 1, 35000, 0, 0, '0', true, true, false, false, false, 1, NULL, '0', NULL, '0', '', -1, 0, '', 'ffffff');
INSERT INTO "items" ("id", "class_id", "name", "description", "type", "element", "file", "link", "icon", "equipment", "level", "dps", "range", "rarity", "quantity", "stack", "cost", "coins", "diamonds", "crystal", "sell", "market", "temporary", "upgrade", "staff", "enh_id", "faction_id", "req_reputation", "req_class_id", "req_class_points", "req_quests", "quest_string_index", "quest_string_value", "meta", "color") VALUES (437, NULL, 'Camo Paintball Uniform', 'Paint the town red! (And blue;green;yellow;purple;pink;black…) this April Fool''s Day! Show those bland corporate suits that they can''t take away AQW''s color!', 'Armor', 'None', 'JunglePaintball.swf', 'JunglePaintball', 'iwarmor', 'co', 1, 25, 50, 13, 1, 1, 2000, 0, 0, '0', true, true, false, false, false, 1, NULL, '0', NULL, '0', '', -1, 0, '', 'ffffff');
INSERT INTO "items" ("id", "class_id", "name", "description", "type", "element", "file", "link", "icon", "equipment", "level", "dps", "range", "rarity", "quantity", "stack", "cost", "coins", "diamonds", "crystal", "sell", "market", "temporary", "upgrade", "staff", "enh_id", "faction_id", "req_reputation", "req_class_id", "req_class_points", "req_quests", "quest_string_index", "quest_string_value", "meta", "color") VALUES (438, NULL, 'Desaturated Paintball Uniform', 'Paint the town red! (And blue;green;yellow;purple;pink;black…)', 'Armor', 'None', 'SnowPaintball.swf', 'SnowPaintball', 'iwarmor', 'co', 1, 25, 50, 13, 1, 1, 30000, 0, 0, '0', true, true, false, false, false, 1, NULL, '0', NULL, '0', '', -1, 0, '', 'ffffff');
INSERT INTO "items" ("id", "class_id", "name", "description", "type", "element", "file", "link", "icon", "equipment", "level", "dps", "range", "rarity", "quantity", "stack", "cost", "coins", "diamonds", "crystal", "sell", "market", "temporary", "upgrade", "staff", "enh_id", "faction_id", "req_reputation", "req_class_id", "req_class_points", "req_quests", "quest_string_index", "quest_string_value", "meta", "color") VALUES (439, NULL, 'Monochromancer', 'Paint the town red! (And blue;green;yellow;purple;pink;black…)', 'Armor', 'None', 'MageSat5.swf', 'MageSat', 'iwarmor', 'co', 1, 25, 50, 13, 1, 1, 10000, 0, 0, '0', true, true, false, false, false, 1, NULL, '0', NULL, '0', '', -1, 0, '', 'ffffff');
INSERT INTO "items" ("id", "class_id", "name", "description", "type", "element", "file", "link", "icon", "equipment", "level", "dps", "range", "rarity", "quantity", "stack", "cost", "coins", "diamonds", "crystal", "sell", "market", "temporary", "upgrade", "staff", "enh_id", "faction_id", "req_reputation", "req_class_id", "req_class_points", "req_quests", "quest_string_index", "quest_string_value", "meta", "color") VALUES (440, NULL, 'Paintball Faceshield', 'Paint the town red! (And blue;green;yellow;purple;pink;black…)', 'Helm', 'None', 'items/helms/PaintballHelm.swf', 'PaintballHelm', 'iihelm', 'he', 1, 25, 50, 13, 1, 1, 5000, 0, 0, '0', true, true, false, false, false, 1, NULL, '0', NULL, '0', '', -1, 0, '', 'ffffff');
INSERT INTO "items" ("id", "class_id", "name", "description", "type", "element", "file", "link", "icon", "equipment", "level", "dps", "range", "rarity", "quantity", "stack", "cost", "coins", "diamonds", "crystal", "sell", "market", "temporary", "upgrade", "staff", "enh_id", "faction_id", "req_reputation", "req_class_id", "req_class_points", "req_quests", "quest_string_index", "quest_string_value", "meta", "color") VALUES (441, NULL, 'Light Paintball Faceshield', 'Paint the town red! (And blue;green;yellow;purple;pink;black…)', 'Helm', 'None', 'items/helms/PaintballHelm2.swf', 'PaintballHelm2', 'iihelm', 'he', 1, 25, 50, 13, 1, 1, 10000, 0, 0, '0', true, true, false, false, false, 1, NULL, '0', NULL, '0', '', -1, 0, '', 'ffffff');
INSERT INTO "items" ("id", "class_id", "name", "description", "type", "element", "file", "link", "icon", "equipment", "level", "dps", "range", "rarity", "quantity", "stack", "cost", "coins", "diamonds", "crystal", "sell", "market", "temporary", "upgrade", "staff", "enh_id", "faction_id", "req_reputation", "req_class_id", "req_class_points", "req_quests", "quest_string_index", "quest_string_value", "meta", "color") VALUES (442, NULL, 'Paintball FaceGuard', 'Paint the town red! (And blue;green;yellow;purple;pink;black…)', 'Helm', 'None', 'items/helms/PaintballHelmF.swf', 'PaintballHelmF', 'iihelm', 'he', 1, 25, 50, 13, 1, 1, 5000, 0, 0, '0', true, true, false, false, false, 1, NULL, '0', NULL, '0', '', -1, 0, '', 'ffffff');
INSERT INTO "items" ("id", "class_id", "name", "description", "type", "element", "file", "link", "icon", "equipment", "level", "dps", "range", "rarity", "quantity", "stack", "cost", "coins", "diamonds", "crystal", "sell", "market", "temporary", "upgrade", "staff", "enh_id", "faction_id", "req_reputation", "req_class_id", "req_class_points", "req_quests", "quest_string_index", "quest_string_value", "meta", "color") VALUES (443, NULL, 'Light Paintball FaceGuard', 'Paint the town red! (And blue;green;yellow;purple;pink;black…)', 'Helm', 'None', 'items/helms/PaintballHelmF2.swf', 'PaintballHelmF2', 'iihelm', 'he', 1, 25, 50, 13, 1, 1, 10000, 0, 0, '0', true, true, false, false, false, 1, NULL, '0', NULL, '0', '', -1, 0, '', 'ffffff');
INSERT INTO "items" ("id", "class_id", "name", "description", "type", "element", "file", "link", "icon", "equipment", "level", "dps", "range", "rarity", "quantity", "stack", "cost", "coins", "diamonds", "crystal", "sell", "market", "temporary", "upgrade", "staff", "enh_id", "faction_id", "req_reputation", "req_class_id", "req_class_points", "req_quests", "quest_string_index", "quest_string_value", "meta", "color") VALUES (444, NULL, 'Pablly Mogcasso', 'Paint the town red! (And blue;green;yellow;purple;pink;black…)', 'Pet', 'None', 'items/pets/ArtMog.swf', 'ArtMog', 'iipet', 'pe', 1, 25, 50, 13, 1, 1, 30000, 0, 0, '0', true, true, false, false, false, 1, NULL, '0', NULL, '0', '', -1, 0, '', 'ffffff');
INSERT INTO "items" ("id", "class_id", "name", "description", "type", "element", "file", "link", "icon", "equipment", "level", "dps", "range", "rarity", "quantity", "stack", "cost", "coins", "diamonds", "crystal", "sell", "market", "temporary", "upgrade", "staff", "enh_id", "faction_id", "req_reputation", "req_class_id", "req_class_points", "req_quests", "quest_string_index", "quest_string_value", "meta", "color") VALUES (445, NULL, 'Desaturated Brush and Shield', 'Paint the town red! (And blue;green;yellow;purple;pink;black…)', 'Dagger', 'None', 'items/daggers/BWBrushAndpshield.swf', 'BWBrushAndpshield', 'iwdagger', 'Weapon', 1, 25, 50, 13, 1, 1, 15000, 0, 0, '0', true, true, false, false, false, 1, NULL, '0', NULL, '0', '', -1, 0, '', 'ffffff');
INSERT INTO "items" ("id", "class_id", "name", "description", "type", "element", "file", "link", "icon", "equipment", "level", "dps", "range", "rarity", "quantity", "stack", "cost", "coins", "diamonds", "crystal", "sell", "market", "temporary", "upgrade", "staff", "enh_id", "faction_id", "req_reputation", "req_class_id", "req_class_points", "req_quests", "quest_string_index", "quest_string_value", "meta", "color") VALUES (446, NULL, 'Paintball Gun', 'Paint the town red! (And blue;green;yellow;purple;pink;black…)', 'Gun', 'None', 'items/bows/PaintballGun.swf', 'PaintballGun', 'iwgun', 'Weapon', 1, 25, 50, 13, 1, 1, 10000, 0, 0, '0', true, true, false, false, false, 1, NULL, '0', NULL, '0', '', -1, 0, '', 'ffffff');
INSERT INTO "items" ("id", "class_id", "name", "description", "type", "element", "file", "link", "icon", "equipment", "level", "dps", "range", "rarity", "quantity", "stack", "cost", "coins", "diamonds", "crystal", "sell", "market", "temporary", "upgrade", "staff", "enh_id", "faction_id", "req_reputation", "req_class_id", "req_class_points", "req_quests", "quest_string_index", "quest_string_value", "meta", "color") VALUES (447, NULL, 'Dr. Dryden Darkwood''s Suit', 'The perfect look for a Purveyor of Fine Antiques;and fancy parties too!', 'Armor', 'None', 'DDarkwood.swf', 'DDarkwood', 'iwarmor', 'co', 1, 25, 50, 35, 1, 1, 700, 1, 0, '0', true, true, false, false, false, 1, NULL, '0', NULL, '0', '', -1, 0, '', 'ffffff');
INSERT INTO "items" ("id", "class_id", "name", "description", "type", "element", "file", "link", "icon", "equipment", "level", "dps", "range", "rarity", "quantity", "stack", "cost", "coins", "diamonds", "crystal", "sell", "market", "temporary", "upgrade", "staff", "enh_id", "faction_id", "req_reputation", "req_class_id", "req_class_points", "req_quests", "quest_string_index", "quest_string_value", "meta", "color") VALUES (448, NULL, 'Melodia''s Uniform', 'Be careful! Try not to get captured by an evil villain and imprisoned in their awful lair… again…', 'Armor', 'None', 'MelodiaOutfit.swf', 'MelodiaOutfit', 'iwarmor', 'co', 1, 25, 50, 35, 1, 1, 500, 1, 0, '0', true, true, false, false, false, 1, NULL, '0', NULL, '0', '', -1, 0, '', 'ffffff');
INSERT INTO "items" ("id", "class_id", "name", "description", "type", "element", "file", "link", "icon", "equipment", "level", "dps", "range", "rarity", "quantity", "stack", "cost", "coins", "diamonds", "crystal", "sell", "market", "temporary", "upgrade", "staff", "enh_id", "faction_id", "req_reputation", "req_class_id", "req_class_points", "req_quests", "quest_string_index", "quest_string_value", "meta", "color") VALUES (449, NULL, 'Dr. Dryden Darkwood''s Eyepatch', 'The perfect look for a Purveyor of Fine Antiques;and fancy parties too.', 'Helm', 'None', 'items/helms/DDarkwoodMorph.swf', 'DDarkwoodMorph', 'iihelm', 'he', 1, 25, 50, 35, 1, 1, 100, 1, 0, '0', true, true, false, false, false, 1, NULL, '0', NULL, '0', '', -1, 0, '', 'ffffff');
INSERT INTO "items" ("id", "class_id", "name", "description", "type", "element", "file", "link", "icon", "equipment", "level", "dps", "range", "rarity", "quantity", "stack", "cost", "coins", "diamonds", "crystal", "sell", "market", "temporary", "upgrade", "staff", "enh_id", "faction_id", "req_reputation", "req_class_id", "req_class_points", "req_quests", "quest_string_index", "quest_string_value", "meta", "color") VALUES (450, NULL, 'Melodia''s Ponytails', 'I wonder how Melodia can clean and sing with these.', 'Helm', 'None', 'items/helms/MelodiaOutfitPonytails2.swf', 'MelodiaOutfitPonytails2', 'iihelm', 'he', 1, 25, 50, 10, 1, 1, 150, 1, 0, '0', true, true, false, false, false, 1, NULL, '0', NULL, '0', '', -1, 0, '', 'ffffff');
INSERT INTO "items" ("id", "class_id", "name", "description", "type", "element", "file", "link", "icon", "equipment", "level", "dps", "range", "rarity", "quantity", "stack", "cost", "coins", "diamonds", "crystal", "sell", "market", "temporary", "upgrade", "staff", "enh_id", "faction_id", "req_reputation", "req_class_id", "req_class_points", "req_quests", "quest_string_index", "quest_string_value", "meta", "color") VALUES (451, NULL, 'Dr. Dryden Darkwood''s Gun', 'Dr. Dryden Darkwood''s fancy personal gun', 'Gun', 'None', 'items/bows/DDarkwoodPistol.swf', 'DDarkwoodPistol', 'iwgun', 'Weapon', 1, 25, 50, 35, 1, 1, 250, 1, 0, '0', true, true, false, false, false, 1, NULL, '0', NULL, '0', '', -1, 0, '', 'ffffff');
INSERT INTO "items" ("id", "class_id", "name", "description", "type", "element", "file", "link", "icon", "equipment", "level", "dps", "range", "rarity", "quantity", "stack", "cost", "coins", "diamonds", "crystal", "sell", "market", "temporary", "upgrade", "staff", "enh_id", "faction_id", "req_reputation", "req_class_id", "req_class_points", "req_quests", "quest_string_index", "quest_string_value", "meta", "color") VALUES (452, NULL, 'Eternal Warrior', 'Limited Rare', 'Armor', 'None', 'EW.swf', 'EW', 'iwarmor', 'co', 1, 25, 50, 70, 1, 1, 1000, 1, 0, '0', true, true, false, false, false, 1, NULL, '0', NULL, '0', '', -1, 0, '', 'ffffff');
INSERT INTO "items" ("id", "class_id", "name", "description", "type", "element", "file", "link", "icon", "equipment", "level", "dps", "range", "rarity", "quantity", "stack", "cost", "coins", "diamonds", "crystal", "sell", "market", "temporary", "upgrade", "staff", "enh_id", "faction_id", "req_reputation", "req_class_id", "req_class_points", "req_quests", "quest_string_index", "quest_string_value", "meta", "color") VALUES (453, NULL, 'Dual Eternal Chakrams', 'Limited Rare', 'Dagger', 'None', 'items/daggers/EternalChakrams.swf', 'EternalChakrams', 'iwdagger', 'Weapon', 1, 25, 50, 70, 1, 1, 450, 1, 0, '0', true, true, false, false, false, 1, NULL, '0', NULL, '0', '', -1, 0, '', 'ffffff');
INSERT INTO "items" ("id", "class_id", "name", "description", "type", "element", "file", "link", "icon", "equipment", "level", "dps", "range", "rarity", "quantity", "stack", "cost", "coins", "diamonds", "crystal", "sell", "market", "temporary", "upgrade", "staff", "enh_id", "faction_id", "req_reputation", "req_class_id", "req_class_points", "req_quests", "quest_string_index", "quest_string_value", "meta", "color") VALUES (454, NULL, 'Eternal Chakrams', 'Limited Rare', 'Mace', 'None', 'items/maces/EternalChakramsMace.swf', 'EternalChakrams', 'iwmace', 'Weapon', 1, 25, 50, 70, 1, 1, 350, 1, 0, '0', true, true, false, false, false, 1, NULL, '0', NULL, '0', '', -1, 0, '', 'ffffff');
INSERT INTO "items" ("id", "class_id", "name", "description", "type", "element", "file", "link", "icon", "equipment", "level", "dps", "range", "rarity", "quantity", "stack", "cost", "coins", "diamonds", "crystal", "sell", "market", "temporary", "upgrade", "staff", "enh_id", "faction_id", "req_reputation", "req_class_id", "req_class_points", "req_quests", "quest_string_index", "quest_string_value", "meta", "color") VALUES (455, NULL, 'Infinity Hood', 'Limited Rare', 'Helm', 'None', 'items/helms/EWHood.swf', 'EWHood', 'iihelm', 'he', 1, 25, 50, 70, 1, 1, 100, 1, 0, '0', true, true, false, false, false, 1, NULL, '0', NULL, '0', '', -1, 0, '', 'ffffff');
INSERT INTO "items" ("id", "class_id", "name", "description", "type", "element", "file", "link", "icon", "equipment", "level", "dps", "range", "rarity", "quantity", "stack", "cost", "coins", "diamonds", "crystal", "sell", "market", "temporary", "upgrade", "staff", "enh_id", "faction_id", "req_reputation", "req_class_id", "req_class_points", "req_quests", "quest_string_index", "quest_string_value", "meta", "color") VALUES (456, NULL, 'Infinite Visor', 'Limited Rare', 'Helm', 'None', 'items/helms/EWVisor.swf', 'EWVisor', 'iihelm', 'he', 1, 25, 50, 70, 1, 1, 250, 1, 0, '0', true, true, false, false, false, 1, NULL, '0', NULL, '0', '', -1, 0, '', 'ffffff');
INSERT INTO "items" ("id", "class_id", "name", "description", "type", "element", "file", "link", "icon", "equipment", "level", "dps", "range", "rarity", "quantity", "stack", "cost", "coins", "diamonds", "crystal", "sell", "market", "temporary", "upgrade", "staff", "enh_id", "faction_id", "req_reputation", "req_class_id", "req_class_points", "req_quests", "quest_string_index", "quest_string_value", "meta", "color") VALUES (457, NULL, 'Chakrams Cape', 'Limited Rare', 'Cape', 'None', 'items/capes/ChakramsBackitem.swf', 'ChakramsBackitem', 'iicape', 'ba', 1, 25, 50, 70, 1, 1, 300, 1, 0, '0', true, true, false, false, false, 1, NULL, '0', NULL, '0', '', -1, 0, '', 'ffffff');
INSERT INTO "items" ("id", "class_id", "name", "description", "type", "element", "file", "link", "icon", "equipment", "level", "dps", "range", "rarity", "quantity", "stack", "cost", "coins", "diamonds", "crystal", "sell", "market", "temporary", "upgrade", "staff", "enh_id", "faction_id", "req_reputation", "req_class_id", "req_class_points", "req_quests", "quest_string_index", "quest_string_value", "meta", "color") VALUES (458, NULL, 'Legendary Manslayer of Nythera', 'Damage 20% to all monster\r\nDefense 10% to all monster/Player', 'Sword', 'None', 'items/swords/DatapawTaroRevampe.swf', 'DatapawTaroRevampe', 'iwsword', 'Weapon', 1, 40, 50, 35, 1, 1, 0, 0, 0, '0', true, true, false, false, false, 1, NULL, '0', NULL, '0', '', -1, 0, '', 'b300ad');
INSERT INTO "items" ("id", "class_id", "name", "description", "type", "element", "file", "link", "icon", "equipment", "level", "dps", "range", "rarity", "quantity", "stack", "cost", "coins", "diamonds", "crystal", "sell", "market", "temporary", "upgrade", "staff", "enh_id", "faction_id", "req_reputation", "req_class_id", "req_class_points", "req_quests", "quest_string_index", "quest_string_value", "meta", "color") VALUES (459, NULL, 'Treasures Key''s', 'Treasure''s Key', 'QuestItem', 'None', 'iibag', 'iibag', 'iibag', 'None', 1, 25, 50, 40, 1, 10, 0, 1, 0, '0', true, false, false, false, false, 1, NULL, '0', NULL, '0', '', -1, 0, '', 'ffffff');
INSERT INTO "items" ("id", "class_id", "name", "description", "type", "element", "file", "link", "icon", "equipment", "level", "dps", "range", "rarity", "quantity", "stack", "cost", "coins", "diamonds", "crystal", "sell", "market", "temporary", "upgrade", "staff", "enh_id", "faction_id", "req_reputation", "req_class_id", "req_class_points", "req_quests", "quest_string_index", "quest_string_value", "meta", "color") VALUES (460, NULL, 'Fish Scale', 'Quest Item!', 'QuestItem', 'None', 'iibag', 'iibag', 'iibag', 'None', 1, 25, 50, 11, 1, 12, 0, 0, 0, '0', true, true, true, false, false, 1, NULL, '0', NULL, '0', '', -1, 0, '', 'ffffff');
INSERT INTO "items" ("id", "class_id", "name", "description", "type", "element", "file", "link", "icon", "equipment", "level", "dps", "range", "rarity", "quantity", "stack", "cost", "coins", "diamonds", "crystal", "sell", "market", "temporary", "upgrade", "staff", "enh_id", "faction_id", "req_reputation", "req_class_id", "req_class_points", "req_quests", "quest_string_index", "quest_string_value", "meta", "color") VALUES (463, NULL, '??? F', '', 'Item', 'None', 'iibag', 'iibag', 'iibag', 'None', 1, 25, 50, 10, 1, 15, 0, 0, 0, '0', true, true, true, false, false, 1, NULL, '0', NULL, '0', '', -1, 0, '', 'ffffff');
INSERT INTO "items" ("id", "class_id", "name", "description", "type", "element", "file", "link", "icon", "equipment", "level", "dps", "range", "rarity", "quantity", "stack", "cost", "coins", "diamonds", "crystal", "sell", "market", "temporary", "upgrade", "staff", "enh_id", "faction_id", "req_reputation", "req_class_id", "req_class_points", "req_quests", "quest_string_index", "quest_string_value", "meta", "color") VALUES (464, NULL, 'Pirate Bait', 'Pirate  Bait', 'QuestItem', 'None', 'iibag', 'iibag', 'iibag', 'None', 1, 25, 50, 11, 1, 10, 0, 0, 0, '0', true, true, true, false, false, 1, NULL, '0', NULL, '0', '', -1, 0, '', 'ffffff');
INSERT INTO "items" ("id", "class_id", "name", "description", "type", "element", "file", "link", "icon", "equipment", "level", "dps", "range", "rarity", "quantity", "stack", "cost", "coins", "diamonds", "crystal", "sell", "market", "temporary", "upgrade", "staff", "enh_id", "faction_id", "req_reputation", "req_class_id", "req_class_points", "req_quests", "quest_string_index", "quest_string_value", "meta", "color") VALUES (465, NULL, 'Pirate Key', 'Pirate Key', 'Item', 'None', 'iibag', 'iibag', 'iibag', 'None', 1, 25, 50, 10, 1, 1, 0, 0, 0, '0', true, true, false, false, false, 1, NULL, '0', NULL, '0', '', -1, 0, '', 'ffffff');
INSERT INTO "items" ("id", "class_id", "name", "description", "type", "element", "file", "link", "icon", "equipment", "level", "dps", "range", "rarity", "quantity", "stack", "cost", "coins", "diamonds", "crystal", "sell", "market", "temporary", "upgrade", "staff", "enh_id", "faction_id", "req_reputation", "req_class_id", "req_class_points", "req_quests", "quest_string_index", "quest_string_value", "meta", "color") VALUES (466, NULL, 'Pirate I', 'Pirate I', 'Item', 'None', 'iibag', 'iibag', 'iibag', 'None', 1, 25, 50, 10, 1, 1, 0, 0, 0, '0', true, true, false, false, false, 1, NULL, '0', NULL, '0', '', -1, 0, '', 'ffffff');
INSERT INTO "items" ("id", "class_id", "name", "description", "type", "element", "file", "link", "icon", "equipment", "level", "dps", "range", "rarity", "quantity", "stack", "cost", "coins", "diamonds", "crystal", "sell", "market", "temporary", "upgrade", "staff", "enh_id", "faction_id", "req_reputation", "req_class_id", "req_class_points", "req_quests", "quest_string_index", "quest_string_value", "meta", "color") VALUES (467, NULL, 'Pirate II', 'Pirate II', 'Item', 'None', 'iibag', 'iibag', 'iibag', 'None', 1, 25, 50, 10, 1, 1, 0, 0, 0, '0', true, true, false, false, false, 1, NULL, '0', NULL, '0', '', -1, 0, '', 'ffffff');
INSERT INTO "items" ("id", "class_id", "name", "description", "type", "element", "file", "link", "icon", "equipment", "level", "dps", "range", "rarity", "quantity", "stack", "cost", "coins", "diamonds", "crystal", "sell", "market", "temporary", "upgrade", "staff", "enh_id", "faction_id", "req_reputation", "req_class_id", "req_class_points", "req_quests", "quest_string_index", "quest_string_value", "meta", "color") VALUES (468, NULL, 'Pirate III', 'Pirate III', 'Item', 'None', 'iibag', 'iibag', 'iibag', 'None', 1, 25, 50, 10, 1, 1, 0, 0, 0, '0', true, true, false, false, false, 1, NULL, '0', NULL, '0', '', -1, 0, '', 'ffffff');
INSERT INTO "items" ("id", "class_id", "name", "description", "type", "element", "file", "link", "icon", "equipment", "level", "dps", "range", "rarity", "quantity", "stack", "cost", "coins", "diamonds", "crystal", "sell", "market", "temporary", "upgrade", "staff", "enh_id", "faction_id", "req_reputation", "req_class_id", "req_class_points", "req_quests", "quest_string_index", "quest_string_value", "meta", "color") VALUES (469, NULL, 'Pie-rate Cutlass', 'Avast! Captain Rhubarb has been Artix Entertainment''s favorite programming pirate for 10 years! /cheer! Here''s to many more years keeping AE sailing in the right course!', 'Sword', 'None', 'items/swords/RhubarbCutlass2.swf', 'RhubarbCutlass', 'iwsword', 'Weapon', 1, 25, 50, 50, 1, 1, 1000, 0, 0, '0', true, true, false, false, false, 1, NULL, '0', NULL, '0', '', -1, 0, '', 'ffffff');
INSERT INTO "items" ("id", "class_id", "name", "description", "type", "element", "file", "link", "icon", "equipment", "level", "dps", "range", "rarity", "quantity", "stack", "cost", "coins", "diamonds", "crystal", "sell", "market", "temporary", "upgrade", "staff", "enh_id", "faction_id", "req_reputation", "req_class_id", "req_class_points", "req_quests", "quest_string_index", "quest_string_value", "meta", "color") VALUES (470, NULL, 'Rhubar''s Cutlass Cannon', 'Wish Captain Rhubarb a VERY Happy birthday;and celebrate with this GIANT Cannon Cutlass. YARRRRRR!', 'Sword', 'None', 'items/swords/RevolvingRifleCutlass.swf', 'RevolvingRifleCutlass', 'iwsword', 'Weapon', 1, 25, 50, 50, 1, 1, 100000, 0, 0, '0', true, true, false, false, false, 1, NULL, '0', NULL, '0', '', -1, 0, '', 'ffffff');
INSERT INTO "items" ("id", "class_id", "name", "description", "type", "element", "file", "link", "icon", "equipment", "level", "dps", "range", "rarity", "quantity", "stack", "cost", "coins", "diamonds", "crystal", "sell", "market", "temporary", "upgrade", "staff", "enh_id", "faction_id", "req_reputation", "req_class_id", "req_class_points", "req_quests", "quest_string_index", "quest_string_value", "meta", "color") VALUES (471, NULL, 'Pie-rate Captain', 'Avast! Captain Rhubarb has been Artix Entertainment''s favorite programming pirate for 10 years! /cheer! Here''s to many more years keeping AE sailing in the right course!', 'Armor', 'None', 'CaptainRhubarbArmor8113.swf', 'CaptainRhubarbArmor8113', 'iwarmor', 'co', 1, 25, 50, 12, 1, 1, 10000, 0, 0, '0', true, true, false, false, false, 1, NULL, '0', NULL, '0', '', -1, 0, '', 'ffffff');
INSERT INTO "items" ("id", "class_id", "name", "description", "type", "element", "file", "link", "icon", "equipment", "level", "dps", "range", "rarity", "quantity", "stack", "cost", "coins", "diamonds", "crystal", "sell", "market", "temporary", "upgrade", "staff", "enh_id", "faction_id", "req_reputation", "req_class_id", "req_class_points", "req_quests", "quest_string_index", "quest_string_value", "meta", "color") VALUES (472, NULL, 'Furious Captain''s Locks', 'Avast! Captain Rhubarb has been Artix Entertainment''s favorite programming pirate for 10 years! /cheer! Here''s to many more years keeping AE sailing in the right course!', 'Helm', 'None', 'items/helms/RhubarbHatGirl.swf', 'RhubarbHatGirl', 'iihelm', 'he', 1, 25, 50, 11, 1, 1, 5000, 0, 0, '0', true, true, false, false, false, 1, NULL, '0', NULL, '0', '', -1, 0, '', 'ffffff');
INSERT INTO "items" ("id", "class_id", "name", "description", "type", "element", "file", "link", "icon", "equipment", "level", "dps", "range", "rarity", "quantity", "stack", "cost", "coins", "diamonds", "crystal", "sell", "market", "temporary", "upgrade", "staff", "enh_id", "faction_id", "req_reputation", "req_class_id", "req_class_points", "req_quests", "quest_string_index", "quest_string_value", "meta", "color") VALUES (473, NULL, 'Rugged Pie-rate Helm', 'Avast! Captain Rhubarb has been Artix Entertainment''s favorite programming pirate for 10 years! /cheer! Here''s to many more years keeping AE sailing in the right course!', 'Helm', 'None', 'items/helms/RhubarbHatGuy.swf', 'RhubarbHatGuy', 'iihelm', 'he', 1, 25, 50, 11, 1, 1, 5000, 0, 0, '0', true, true, false, false, false, 1, NULL, '0', NULL, '0', '', -1, 0, '', 'ffffff');
INSERT INTO "items" ("id", "class_id", "name", "description", "type", "element", "file", "link", "icon", "equipment", "level", "dps", "range", "rarity", "quantity", "stack", "cost", "coins", "diamonds", "crystal", "sell", "market", "temporary", "upgrade", "staff", "enh_id", "faction_id", "req_reputation", "req_class_id", "req_class_points", "req_quests", "quest_string_index", "quest_string_value", "meta", "color") VALUES (474, NULL, 'Young Pie-rate Helm', 'Avast! Captain Rhubarb has been Artix Entertainment''s favorite programming pirate for 10 years! /cheer! Here''s to many more years keeping AE sailing in the right course!', 'Helm', 'None', 'items/helms/RhubarbHatGuy2.swf', 'RhubarbHatGuy2', 'iihelm', 'he', 1, 25, 50, 30, 1, 1, 5033, 0, 0, '0', true, true, false, false, false, 1, NULL, '0', NULL, '0', '', -1, 0, '', 'ffffff');
INSERT INTO "items" ("id", "class_id", "name", "description", "type", "element", "file", "link", "icon", "equipment", "level", "dps", "range", "rarity", "quantity", "stack", "cost", "coins", "diamonds", "crystal", "sell", "market", "temporary", "upgrade", "staff", "enh_id", "faction_id", "req_reputation", "req_class_id", "req_class_points", "req_quests", "quest_string_index", "quest_string_value", "meta", "color") VALUES (475, NULL, 'Swabbie Cape', 'Avast! Captain Rhubarb has been Artix Entertainment''s favorite programming pirate for 10 years! /cheer! Here''s to many more years keeping AE sailing in the right course!', 'Cape', 'None', 'items/capes/SwabbieCape.swf', 'SwabbieCape', 'iicape', 'ba', 1, 25, 50, 30, 1, 1, 500, 0, 0, '0', true, true, false, false, false, 1, NULL, '0', NULL, '0', '', -1, 0, '', 'ffffff');
INSERT INTO "items" ("id", "class_id", "name", "description", "type", "element", "file", "link", "icon", "equipment", "level", "dps", "range", "rarity", "quantity", "stack", "cost", "coins", "diamonds", "crystal", "sell", "market", "temporary", "upgrade", "staff", "enh_id", "faction_id", "req_reputation", "req_class_id", "req_class_points", "req_quests", "quest_string_index", "quest_string_value", "meta", "color") VALUES (476, NULL, 'Pie-rate Cape', 'Avast! Captain Rhubarb has been Artix Entertainment''s favorite programming pirate for 10 years! /cheer! Here''s to many more years keeping AE sailing in the right course!', 'Cape', 'None', 'items/capes/RhubarbCape1.swf', 'RhubarbCape1', 'iicape', 'ba', 1, 25, 50, 50, 1, 1, 778, 0, 0, '0', true, true, false, false, false, 1, NULL, '0', NULL, '0', '', -1, 0, '', 'ffffff');
INSERT INTO "items" ("id", "class_id", "name", "description", "type", "element", "file", "link", "icon", "equipment", "level", "dps", "range", "rarity", "quantity", "stack", "cost", "coins", "diamonds", "crystal", "sell", "market", "temporary", "upgrade", "staff", "enh_id", "faction_id", "req_reputation", "req_class_id", "req_class_points", "req_quests", "quest_string_index", "quest_string_value", "meta", "color") VALUES (477, NULL, 'Pie-rate Puppet', 'Avast! Captain Rhubarb has been Artix Entertainment''s favorite programming pirate for 10 years! /cheer! Here''s to many more years keeping AE sailing in the right course!', 'Pet', 'None', 'items/pets/CaptainPuppet.swf', 'CaptainPuppet', 'iipet', 'pe', 1, 25, 50, 50, 1, 1, 5000, 0, 0, '0', true, true, false, false, false, 1, NULL, '0', NULL, '0', '', -1, 0, '', 'ffffff');
INSERT INTO "items" ("id", "class_id", "name", "description", "type", "element", "file", "link", "icon", "equipment", "level", "dps", "range", "rarity", "quantity", "stack", "cost", "coins", "diamonds", "crystal", "sell", "market", "temporary", "upgrade", "staff", "enh_id", "faction_id", "req_reputation", "req_class_id", "req_class_points", "req_quests", "quest_string_index", "quest_string_value", "meta", "color") VALUES (478, NULL, 'Amenonuhoko', 'Amenonuhoko is the most remembrance of soul king he used that to kill the all monster in the Newbie and save everyone to that dangerous place of war and defeated the all bosses (this weapon is boosted)', 'Polearm', 'None', 'items/polearms/1lastDatapawAmeno1.swf', '22DatapawAmeno', 'iwpolearm', 'Weapon', 1, 60, 50, 24, 1, 1, 0, 0, 0, '0', true, true, false, false, false, 8, NULL, '0', NULL, '0', '', -1, 0, '', 'e2e600');
INSERT INTO "items" ("id", "class_id", "name", "description", "type", "element", "file", "link", "icon", "equipment", "level", "dps", "range", "rarity", "quantity", "stack", "cost", "coins", "diamonds", "crystal", "sell", "market", "temporary", "upgrade", "staff", "enh_id", "faction_id", "req_reputation", "req_class_id", "req_class_points", "req_quests", "quest_string_index", "quest_string_value", "meta", "color") VALUES (479, NULL, 'Amenonuhoko Devine Cape', 'Amenonuhoko is the most remembrance of soul king he used that to kill the all monster in the Newbie and save everyone to that dangerous place of war and defeated the all bosses (this cape is boosted)', 'Cape', 'None', 'items/capes/DatapawAmeno1.swf', 'DatapawAmeno', 'iicape', 'ba', 1, 25, 50, 24, 1, 1, 0, 0, 0, '0', true, true, false, false, false, 1, NULL, '0', NULL, '0', '', -1, 0, '', 'f4e301');
INSERT INTO "items" ("id", "class_id", "name", "description", "type", "element", "file", "link", "icon", "equipment", "level", "dps", "range", "rarity", "quantity", "stack", "cost", "coins", "diamonds", "crystal", "sell", "market", "temporary", "upgrade", "staff", "enh_id", "faction_id", "req_reputation", "req_class_id", "req_class_points", "req_quests", "quest_string_index", "quest_string_value", "meta", "color") VALUES (480, NULL, 'Alpha Pirate Armor', 'A commemorative armor reminding us of the infamous day where the pirate Captain Rhubarb seized control of that database.', 'Armor', 'None', 'pirate2_skin.swf', 'Pirate2', 'iwarmor', 'co', 1, 25, 50, 55, 1, 1, 100000, 0, 0, '0', true, false, false, false, false, 1, NULL, '0', NULL, '0', '', -1, 0, '', 'ffffff');
INSERT INTO "items" ("id", "class_id", "name", "description", "type", "element", "file", "link", "icon", "equipment", "level", "dps", "range", "rarity", "quantity", "stack", "cost", "coins", "diamonds", "crystal", "sell", "market", "temporary", "upgrade", "staff", "enh_id", "faction_id", "req_reputation", "req_class_id", "req_class_points", "req_quests", "quest_string_index", "quest_string_value", "meta", "color") VALUES (481, NULL, 'Pirate Sea', 'Pirate Sea', 'Item', 'None', 'iibag', 'iibag', 'iibag', 'None', 1, 25, 50, 60, 1, 500, 0, 0, 0, '0', true, true, false, false, false, 1, NULL, '0', NULL, '0', '', -1, 0, '', 'ffffff');
INSERT INTO "items" ("id", "class_id", "name", "description", "type", "element", "file", "link", "icon", "equipment", "level", "dps", "range", "rarity", "quantity", "stack", "cost", "coins", "diamonds", "crystal", "sell", "market", "temporary", "upgrade", "staff", "enh_id", "faction_id", "req_reputation", "req_class_id", "req_class_points", "req_quests", "quest_string_index", "quest_string_value", "meta", "color") VALUES (482, NULL, 'Black Dragonborn Naval Commander', 'A charming choice for a naval who is gifted when maintaining order in the crew is involved;yet mysterious. Be weary for not all commanders who wear black are wicked.', 'Armor', 'None', 'DragonbornNCBlackr1.swf', 'DragonbornNCBlack', 'iwarmor', 'co', 1, 25, 50, 30, 1, 1, 0, 0, 0, '0', true, true, false, false, false, 1, NULL, '0', NULL, '0', '', -1, 0, '', 'ffffff');
INSERT INTO "items" ("id", "class_id", "name", "description", "type", "element", "file", "link", "icon", "equipment", "level", "dps", "range", "rarity", "quantity", "stack", "cost", "coins", "diamonds", "crystal", "sell", "market", "temporary", "upgrade", "staff", "enh_id", "faction_id", "req_reputation", "req_class_id", "req_class_points", "req_quests", "quest_string_index", "quest_string_value", "meta", "color") VALUES (483, NULL, 'Galactic Naval Commander', 'Ye always be sayin'' how pirates be out of this world… ''n now ye can shoot for the stars ''n plunder the planets as the Galactic Naval Commander!', 'Armor', 'None', 'GalacticNC1.swf', 'GalacticNC', 'iwarmor', 'co', 1, 25, 50, 30, 1, 1, 0, 0, 0, '0', true, true, false, false, false, 1, NULL, '0', NULL, '0', '', -1, 0, '', 'ffffff');
INSERT INTO "items" ("id", "class_id", "name", "description", "type", "element", "file", "link", "icon", "equipment", "level", "dps", "range", "rarity", "quantity", "stack", "cost", "coins", "diamonds", "crystal", "sell", "market", "temporary", "upgrade", "staff", "enh_id", "faction_id", "req_reputation", "req_class_id", "req_class_points", "req_quests", "quest_string_index", "quest_string_value", "meta", "color") VALUES (484, NULL, 'Naval Commander', 'A charming choice for a naval who is gifted when maintaining order in the crew is involved;yet mysterious. Be weary for not all commanders who wear black are wicked.', 'Armor', 'None', 'Explorer.swf', 'Explorer', 'iwarmor', 'co', 1, 25, 50, 30, 1, 1, 0, 0, 0, '0', true, true, false, false, false, 1, NULL, '0', NULL, '0', '', -1, 0, '', 'ffffff');
INSERT INTO "items" ("id", "class_id", "name", "description", "type", "element", "file", "link", "icon", "equipment", "level", "dps", "range", "rarity", "quantity", "stack", "cost", "coins", "diamonds", "crystal", "sell", "market", "temporary", "upgrade", "staff", "enh_id", "faction_id", "req_reputation", "req_class_id", "req_class_points", "req_quests", "quest_string_index", "quest_string_value", "meta", "color") VALUES (485, NULL, 'Rotting Naval Commander', 'Dead men tell no tales. Except in this case;where it seems you have managed to escape Davy Jones'' Locker. Yaaar!', 'Armor', 'None', 'Davy_skin.swf', 'Davy1', 'iwarmor', 'co', 1, 25, 50, 30, 1, 1, 0, 0, 0, '0', true, true, false, false, false, 1, NULL, '0', NULL, '0', '', -1, 0, '', 'ffffff');
INSERT INTO "items" ("id", "class_id", "name", "description", "type", "element", "file", "link", "icon", "equipment", "level", "dps", "range", "rarity", "quantity", "stack", "cost", "coins", "diamonds", "crystal", "sell", "market", "temporary", "upgrade", "staff", "enh_id", "faction_id", "req_reputation", "req_class_id", "req_class_points", "req_quests", "quest_string_index", "quest_string_value", "meta", "color") VALUES (486, NULL, 'Void Naval Commander', 'Aye;even the Archfiend has his own fleet! No landlubbers allowed;Nulgath only wants the souls of the best Buccaneers in Lore. Savvy?', 'Armor', 'None', 'VoidPirate7r1.swf', 'VoidPirate7', 'iwarmor', 'co', 1, 25, 50, 30, 1, 1, 0, 0, 0, '0', true, true, false, false, false, 1, NULL, '0', NULL, '0', '', -1, 0, '', 'ffffff');
INSERT INTO "items" ("id", "class_id", "name", "description", "type", "element", "file", "link", "icon", "equipment", "level", "dps", "range", "rarity", "quantity", "stack", "cost", "coins", "diamonds", "crystal", "sell", "market", "temporary", "upgrade", "staff", "enh_id", "faction_id", "req_reputation", "req_class_id", "req_class_points", "req_quests", "quest_string_index", "quest_string_value", "meta", "color") VALUES (487, NULL, 'Platinum Naval Commander', 'As a high ranking officer at the Royal navy;your job is to hunt down treasure seeking pirates;but if you WEAR the treasure then the pirates will come to you!', 'Armor', 'None', 'PNC.swf', 'PNC', 'iwarmor', 'co', 1, 25, 50, 30, 1, 1, 0, 0, 0, '0', true, true, false, false, false, 1, NULL, '0', NULL, '0', '', -1, 0, '', 'ffffff');
INSERT INTO "items" ("id", "class_id", "name", "description", "type", "element", "file", "link", "icon", "equipment", "level", "dps", "range", "rarity", "quantity", "stack", "cost", "coins", "diamonds", "crystal", "sell", "market", "temporary", "upgrade", "staff", "enh_id", "faction_id", "req_reputation", "req_class_id", "req_class_points", "req_quests", "quest_string_index", "quest_string_value", "meta", "color") VALUES (488, NULL, 'Gold Voucher 20k', 'x', 'Item', 'None', 'iibag', 'iibag', 'iibag', 'Item', 1, 25, 50, 10, 1, 1, 20000, 0, 0, '0', true, false, false, false, false, 1, NULL, '0', NULL, '0', '', -1, 0, '', 'ffffff');
INSERT INTO "items" ("id", "class_id", "name", "description", "type", "element", "file", "link", "icon", "equipment", "level", "dps", "range", "rarity", "quantity", "stack", "cost", "coins", "diamonds", "crystal", "sell", "market", "temporary", "upgrade", "staff", "enh_id", "faction_id", "req_reputation", "req_class_id", "req_class_points", "req_quests", "quest_string_index", "quest_string_value", "meta", "color") VALUES (489, NULL, 'Double Future Gun', 'This Gun is Massive Bullet it can kill 1500 meters range of the enemy you see', 'Dagger', 'None', 'items/daggers/DatapawGunfix.swf', 'DatapawGun', 'iwdagger', 'Weapon', 1, 170, 50, 35, 1, 1, 0, 0, 0, '0', true, true, false, false, false, 1, NULL, '0', NULL, '0', '', -1, 0, '', 'cf01f9');
INSERT INTO "items" ("id", "class_id", "name", "description", "type", "element", "file", "link", "icon", "equipment", "level", "dps", "range", "rarity", "quantity", "stack", "cost", "coins", "diamonds", "crystal", "sell", "market", "temporary", "upgrade", "staff", "enh_id", "faction_id", "req_reputation", "req_class_id", "req_class_points", "req_quests", "quest_string_index", "quest_string_value", "meta", "color") VALUES (490, NULL, 'Dual black Astral', 'Dark Astral counter of Datapaw to defeat this stupid Staff use this to get him down', 'Dagger', 'None', 'items/daggers/Dualultimateblackkatana.swf', 'ultimateblackkatana', 'iwdagger', 'Weapon', 1, 25, 50, 70, 1, 1, 800, 1, 0, '0', true, true, false, false, false, 1, NULL, '0', NULL, '0', '', -1, 0, '', 'ffffff');
INSERT INTO "items" ("id", "class_id", "name", "description", "type", "element", "file", "link", "icon", "equipment", "level", "dps", "range", "rarity", "quantity", "stack", "cost", "coins", "diamonds", "crystal", "sell", "market", "temporary", "upgrade", "staff", "enh_id", "faction_id", "req_reputation", "req_class_id", "req_class_points", "req_quests", "quest_string_index", "quest_string_value", "meta", "color") VALUES (491, NULL, 'Soul King Weapon Enhancement Level 10', '', 'Enhancement', 'None', 'Enhancement.swf', 'Enhancement', 'none', 'Weapon', 10, 50, 50, 10, 1, 1, 50000, 0, 0, '0', true, true, false, false, false, 3, NULL, '0', NULL, '0', '', -1, 0, '', 'ffffff');
INSERT INTO "items" ("id", "class_id", "name", "description", "type", "element", "file", "link", "icon", "equipment", "level", "dps", "range", "rarity", "quantity", "stack", "cost", "coins", "diamonds", "crystal", "sell", "market", "temporary", "upgrade", "staff", "enh_id", "faction_id", "req_reputation", "req_class_id", "req_class_points", "req_quests", "quest_string_index", "quest_string_value", "meta", "color") VALUES (492, NULL, 'Soul King Weapon Enhancement Level 15', '', 'Enhancement', 'None', 'Enhancement.swf', 'Enhancement', 'none', 'Weapon', 15, 50, 50, 10, 1, 1, 0, 0, 0, '0', true, true, false, false, false, 4, NULL, '0', NULL, '0', '', -1, 0, '', 'ffffff');
INSERT INTO "items" ("id", "class_id", "name", "description", "type", "element", "file", "link", "icon", "equipment", "level", "dps", "range", "rarity", "quantity", "stack", "cost", "coins", "diamonds", "crystal", "sell", "market", "temporary", "upgrade", "staff", "enh_id", "faction_id", "req_reputation", "req_class_id", "req_class_points", "req_quests", "quest_string_index", "quest_string_value", "meta", "color") VALUES (493, NULL, 'Soul King Weapon Enhancement Level 20', '', 'Enhancement', 'None', 'Enhancement.swf', 'Enhancement', 'none', 'Weapon', 20, 50, 50, 10, 1, 1, 50000, 0, 0, '0', true, true, false, false, false, 5, NULL, '0', NULL, '0', '', -1, 0, '', 'ffffff');
INSERT INTO "items" ("id", "class_id", "name", "description", "type", "element", "file", "link", "icon", "equipment", "level", "dps", "range", "rarity", "quantity", "stack", "cost", "coins", "diamonds", "crystal", "sell", "market", "temporary", "upgrade", "staff", "enh_id", "faction_id", "req_reputation", "req_class_id", "req_class_points", "req_quests", "quest_string_index", "quest_string_value", "meta", "color") VALUES (494, NULL, 'Soul King Weapon Enhancement Level 25', '', 'Enhancement', 'None', 'Enhancement.swf', 'Enhancement', 'none', 'Weapon', 25, 50, 50, 10, 1, 1, 50000, 0, 0, '0', true, true, false, false, false, 6, NULL, '0', NULL, '0', '', -1, 0, '', 'ffffff');
INSERT INTO "items" ("id", "class_id", "name", "description", "type", "element", "file", "link", "icon", "equipment", "level", "dps", "range", "rarity", "quantity", "stack", "cost", "coins", "diamonds", "crystal", "sell", "market", "temporary", "upgrade", "staff", "enh_id", "faction_id", "req_reputation", "req_class_id", "req_class_points", "req_quests", "quest_string_index", "quest_string_value", "meta", "color") VALUES (495, NULL, 'Soul King Weapon Enhancement Level 30', '', 'Enhancement', 'None', 'Enhancement.swf', 'Enhancement.', 'none', 'Weapon', 30, 50, 50, 10, 1, 1, 50000, 0, 0, '0', true, true, false, false, false, 7, NULL, '0', NULL, '0', '', -1, 0, '', 'ffffff');
INSERT INTO "items" ("id", "class_id", "name", "description", "type", "element", "file", "link", "icon", "equipment", "level", "dps", "range", "rarity", "quantity", "stack", "cost", "coins", "diamonds", "crystal", "sell", "market", "temporary", "upgrade", "staff", "enh_id", "faction_id", "req_reputation", "req_class_id", "req_class_points", "req_quests", "quest_string_index", "quest_string_value", "meta", "color") VALUES (496, NULL, 'Soul King Weapon Enhancement Level 35', '', 'Enhancement', 'None', 'Enhancement.swf', 'Enhancement', 'none', 'Weapon', 35, 25, 50, 10, 1, 1, 50000, 0, 0, '0', true, true, false, false, false, 8, NULL, '0', NULL, '0', '', -1, 0, '', 'ffffff');
INSERT INTO "items" ("id", "class_id", "name", "description", "type", "element", "file", "link", "icon", "equipment", "level", "dps", "range", "rarity", "quantity", "stack", "cost", "coins", "diamonds", "crystal", "sell", "market", "temporary", "upgrade", "staff", "enh_id", "faction_id", "req_reputation", "req_class_id", "req_class_points", "req_quests", "quest_string_index", "quest_string_value", "meta", "color") VALUES (497, NULL, 'Windy Hair CC', 'This Hair Will Be Remembered by All!', 'Helm', 'None', 'items/helms/WindyHairCC.swf', 'WindyHairCC', 'iihelm', 'he', 1, 25, 50, 70, 1, 1, 0, 0, 0, '0', true, true, false, false, false, 1, NULL, '0', NULL, '0', '', -1, 0, '500000', 'ffffff');
INSERT INTO "items" ("id", "class_id", "name", "description", "type", "element", "file", "link", "icon", "equipment", "level", "dps", "range", "rarity", "quantity", "stack", "cost", "coins", "diamonds", "crystal", "sell", "market", "temporary", "upgrade", "staff", "enh_id", "faction_id", "req_reputation", "req_class_id", "req_class_points", "req_quests", "quest_string_index", "quest_string_value", "meta", "color") VALUES (498, NULL, 'Support Title', 'Special players who are dedicated to helping!', 'ServerUse', 'None', 'title::27', 'title::27', 'iCheck', 'None', 1, 25, 50, 10, 1, 1, 0, 0, 0, '0', true, false, false, false, false, 1, NULL, '0', NULL, '0', '', -1, 0, '', 'ffffff');
INSERT INTO "items" ("id", "class_id", "name", "description", "type", "element", "file", "link", "icon", "equipment", "level", "dps", "range", "rarity", "quantity", "stack", "cost", "coins", "diamonds", "crystal", "sell", "market", "temporary", "upgrade", "staff", "enh_id", "faction_id", "req_reputation", "req_class_id", "req_class_points", "req_quests", "quest_string_index", "quest_string_value", "meta", "color") VALUES (499, NULL, 'Job Manager', 'Only for Moderators/Administrators use.', 'ServerUse', 'None', 'title::2', 'title::2', 'iCheck', 'None', 1, 25, 50, 10, 1, 1, 0, 0, 0, '0', true, false, false, false, true, 1, NULL, '0', NULL, '0', '', -1, 0, '', 'ffffff');
INSERT INTO "items" ("id", "class_id", "name", "description", "type", "element", "file", "link", "icon", "equipment", "level", "dps", "range", "rarity", "quantity", "stack", "cost", "coins", "diamonds", "crystal", "sell", "market", "temporary", "upgrade", "staff", "enh_id", "faction_id", "req_reputation", "req_class_id", "req_class_points", "req_quests", "quest_string_index", "quest_string_value", "meta", "color") VALUES (500, NULL, 'Game Designers', 'Only for Moderators/Administrators use.', 'ServerUse', 'None', 'title::1', 'title::1', 'iCheck', 'None', 1, 25, 50, 10, 1, 1, 0, 0, 0, '0', true, false, false, false, true, 1, NULL, '0', NULL, '0', '', -1, 0, '', 'ffffff');
INSERT INTO "items" ("id", "class_id", "name", "description", "type", "element", "file", "link", "icon", "equipment", "level", "dps", "range", "rarity", "quantity", "stack", "cost", "coins", "diamonds", "crystal", "sell", "market", "temporary", "upgrade", "staff", "enh_id", "faction_id", "req_reputation", "req_class_id", "req_class_points", "req_quests", "quest_string_index", "quest_string_value", "meta", "color") VALUES (501, NULL, 'Soul of a Sun God', 'The Soul of a Sun God;When you Defeat All of the Monster in Jungle Sun God Will Give You The Soul of Them', 'Item', 'None', 'iibag', 'iibag', 'iibag', 'Item', 1, 25, 50, 10, 1, 1300, 0, 0, 0, '0', true, true, false, false, false, 1, NULL, '0', NULL, '0', '', -1, 0, '', 'ffffff');
INSERT INTO "items" ("id", "class_id", "name", "description", "type", "element", "file", "link", "icon", "equipment", "level", "dps", "range", "rarity", "quantity", "stack", "cost", "coins", "diamonds", "crystal", "sell", "market", "temporary", "upgrade", "staff", "enh_id", "faction_id", "req_reputation", "req_class_id", "req_class_points", "req_quests", "quest_string_index", "quest_string_value", "meta", "color") VALUES (502, NULL, 'Essence of Warlord', '', 'Item', 'None', 'iibag', 'iibag', 'iibag', 'Item', 1, 25, 50, 10, 1, 5000, 0, 0, 0, '0', true, true, false, false, false, 1, NULL, '0', NULL, '0', '', -1, 0, '', 'ffffff');
INSERT INTO "items" ("id", "class_id", "name", "description", "type", "element", "file", "link", "icon", "equipment", "level", "dps", "range", "rarity", "quantity", "stack", "cost", "coins", "diamonds", "crystal", "sell", "market", "temporary", "upgrade", "staff", "enh_id", "faction_id", "req_reputation", "req_class_id", "req_class_points", "req_quests", "quest_string_index", "quest_string_value", "meta", "color") VALUES (503, 9, 'Ancient Hero', 'Founder Package. Thank you for supporting PS Nythera.', 'Class', 'None', 'LegendArmor.swf', 'LegendArmor', 'iiclass', 'ar', 1, 25, 50, 80, 1, 1, 0, 1, 0, '0', true, true, false, true, false, 1, NULL, '0', NULL, '0', '', -1, 0, '', 'ffffff');
INSERT INTO "items" ("id", "class_id", "name", "description", "type", "element", "file", "link", "icon", "equipment", "level", "dps", "range", "rarity", "quantity", "stack", "cost", "coins", "diamonds", "crystal", "sell", "market", "temporary", "upgrade", "staff", "enh_id", "faction_id", "req_reputation", "req_class_id", "req_class_points", "req_quests", "quest_string_index", "quest_string_value", "meta", "color") VALUES (504, NULL, 'Erebus Pet', '', 'Pet', 'None', 'SoulPet.swf', 'SoulPet', 'iipet', 'pe', 1, 25, 50, 10, 1, 1, 0, 0, 0, '0', true, true, false, false, true, 1, NULL, '0', NULL, '0', '', -1, 0, '', 'ffffff');
INSERT INTO "items" ("id", "class_id", "name", "description", "type", "element", "file", "link", "icon", "equipment", "level", "dps", "range", "rarity", "quantity", "stack", "cost", "coins", "diamonds", "crystal", "sell", "market", "temporary", "upgrade", "staff", "enh_id", "faction_id", "req_reputation", "req_class_id", "req_class_points", "req_quests", "quest_string_index", "quest_string_value", "meta", "color") VALUES (505, NULL, 'Lost Devine Cloth', 'This can be Found in Yulgar and to farm in -Join Djinboss', 'Item', 'None', 'iibag', 'iibag', 'iibag', 'hi', 1, 25, 50, 10, 1, 300, 4500, 0, 0, '0', true, true, false, false, false, 1, NULL, '0', NULL, '0', '', -1, 0, '', 'ffffff');
INSERT INTO "items" ("id", "class_id", "name", "description", "type", "element", "file", "link", "icon", "equipment", "level", "dps", "range", "rarity", "quantity", "stack", "cost", "coins", "diamonds", "crystal", "sell", "market", "temporary", "upgrade", "staff", "enh_id", "faction_id", "req_reputation", "req_class_id", "req_class_points", "req_quests", "quest_string_index", "quest_string_value", "meta", "color") VALUES (506, NULL, 'Master Crowled', 'Darkness Awakening', 'Armor', 'None', 'EmoxusTemplate.swf', 'EmoxusTemplate', 'iwarmor', 'co', 1, 25, 50, 95, 1, 1, 0, 1, 0, '0', true, false, false, false, false, 1, NULL, '0', NULL, '0', '', -1, 0, '', 'ffffff');
INSERT INTO "items" ("id", "class_id", "name", "description", "type", "element", "file", "link", "icon", "equipment", "level", "dps", "range", "rarity", "quantity", "stack", "cost", "coins", "diamonds", "crystal", "sell", "market", "temporary", "upgrade", "staff", "enh_id", "faction_id", "req_reputation", "req_class_id", "req_class_points", "req_quests", "quest_string_index", "quest_string_value", "meta", "color") VALUES (507, NULL, 'Master Crowled Hair', 'Rare', 'Helm', 'None', 'items/helms/Aviatortest.swf', 'Aviatortest', 'iihelm', 'he', 1, 25, 50, 95, 1, 1, 500, 1, 0, '0', true, false, false, false, false, 1, NULL, '0', NULL, '0', '', -1, 0, '', 'ffffff');
INSERT INTO "items" ("id", "class_id", "name", "description", "type", "element", "file", "link", "icon", "equipment", "level", "dps", "range", "rarity", "quantity", "stack", "cost", "coins", "diamonds", "crystal", "sell", "market", "temporary", "upgrade", "staff", "enh_id", "faction_id", "req_reputation", "req_class_id", "req_class_points", "req_quests", "quest_string_index", "quest_string_value", "meta", "color") VALUES (508, NULL, 'Master Crowled StormFire', 'Rare', 'Sword', 'None', 'items/swords/StormFireErebus.swf', 'StormFireErebus', 'iwsword', 'Weapon', 1, 25, 50, 95, 1, 1, 0, 1, 0, '0', true, false, false, false, false, 1, NULL, '0', NULL, '0', '', -1, 0, '', 'ffffff');
INSERT INTO "items" ("id", "class_id", "name", "description", "type", "element", "file", "link", "icon", "equipment", "level", "dps", "range", "rarity", "quantity", "stack", "cost", "coins", "diamonds", "crystal", "sell", "market", "temporary", "upgrade", "staff", "enh_id", "faction_id", "req_reputation", "req_class_id", "req_class_points", "req_quests", "quest_string_index", "quest_string_value", "meta", "color") VALUES (509, NULL, 'Dual Master Crowled StormFire', 'Rare', 'Dagger', 'None', 'items/daggers/StormFireErebus.swf', 'StormFireErebus', 'iwdagger', 'Weapon', 1, 25, 50, 95, 1, 1, 0, 1, 0, '0', true, false, false, false, false, 1, NULL, '0', NULL, '0', '', -1, 0, '', 'ffffff');
INSERT INTO "items" ("id", "class_id", "name", "description", "type", "element", "file", "link", "icon", "equipment", "level", "dps", "range", "rarity", "quantity", "stack", "cost", "coins", "diamonds", "crystal", "sell", "market", "temporary", "upgrade", "staff", "enh_id", "faction_id", "req_reputation", "req_class_id", "req_class_points", "req_quests", "quest_string_index", "quest_string_value", "meta", "color") VALUES (510, NULL, 'Ichigo Blade', 'Ichigo Sword;Bleach', 'Sword', 'None', 'items/swords/Bleach2.swf', 'Bleach2', 'iwsword', 'Weapon', 1, 25, 50, 70, 1, 1, 1500, 1, 0, '0', true, false, false, false, false, 1, NULL, '0', NULL, '0', '', -1, 0, '', 'ffffff');
INSERT INTO "items" ("id", "class_id", "name", "description", "type", "element", "file", "link", "icon", "equipment", "level", "dps", "range", "rarity", "quantity", "stack", "cost", "coins", "diamonds", "crystal", "sell", "market", "temporary", "upgrade", "staff", "enh_id", "faction_id", "req_reputation", "req_class_id", "req_class_points", "req_quests", "quest_string_index", "quest_string_value", "meta", "color") VALUES (511, NULL, 'Erebus Pirate Armor', '', 'Armor', 'None', 'ArtlaSPirateArmor1.swf', 'ArtlaSPirateArmor', 'iwarmor', 'co', 1, 25, 50, 10, 1, 1, 0, 0, 0, '0', true, true, false, false, false, 1, NULL, '0', NULL, '0', '', -1, 0, '', 'ffffff');
INSERT INTO "items" ("id", "class_id", "name", "description", "type", "element", "file", "link", "icon", "equipment", "level", "dps", "range", "rarity", "quantity", "stack", "cost", "coins", "diamonds", "crystal", "sell", "market", "temporary", "upgrade", "staff", "enh_id", "faction_id", "req_reputation", "req_class_id", "req_class_points", "req_quests", "quest_string_index", "quest_string_value", "meta", "color") VALUES (512, NULL, 'Strength Potion', 'This potion increases your damage by 70% for 1 minute', 'Item', 'None', 'icb3', 'icb3', 'icb3', 'None', 1, 25, 50, 11, 1, 5, 1000, 0, 0, '0', true, true, false, false, false, 1, NULL, '0', NULL, '0', '', -1, 0, '8', 'e34f4f');
INSERT INTO "items" ("id", "class_id", "name", "description", "type", "element", "file", "link", "icon", "equipment", "level", "dps", "range", "rarity", "quantity", "stack", "cost", "coins", "diamonds", "crystal", "sell", "market", "temporary", "upgrade", "staff", "enh_id", "faction_id", "req_reputation", "req_class_id", "req_class_points", "req_quests", "quest_string_index", "quest_string_value", "meta", "color") VALUES (513, NULL, 'Soul King Token', 'this can be found in yulgar datapaw NPC', 'Item', 'None', 'iibag', 'iibag', 'iibag', 'Item', 1, 25, 50, 10, 1, 5, 0, 0, 0, '0', true, true, false, false, false, 1, NULL, '0', NULL, '0', '', -1, 0, '', 'ffffff');
INSERT INTO "items" ("id", "class_id", "name", "description", "type", "element", "file", "link", "icon", "equipment", "level", "dps", "range", "rarity", "quantity", "stack", "cost", "coins", "diamonds", "crystal", "sell", "market", "temporary", "upgrade", "staff", "enh_id", "faction_id", "req_reputation", "req_class_id", "req_class_points", "req_quests", "quest_string_index", "quest_string_value", "meta", "color") VALUES (514, NULL, 'Djin Defeated', 'this can found in djinboss', 'Item', 'None', 'iibag', 'iibag', 'iibag', 'None', 1, 25, 50, 10, 1, 1000, 0, 0, 0, '0', true, true, true, false, false, 1, NULL, '0', NULL, '0', '', -1, 0, '', 'ffffff');
INSERT INTO "items" ("id", "class_id", "name", "description", "type", "element", "file", "link", "icon", "equipment", "level", "dps", "range", "rarity", "quantity", "stack", "cost", "coins", "diamonds", "crystal", "sell", "market", "temporary", "upgrade", "staff", "enh_id", "faction_id", "req_reputation", "req_class_id", "req_class_points", "req_quests", "quest_string_index", "quest_string_value", "meta", "color") VALUES (515, 10, 'Void HighLord', 'Only the strongest;toughest;most dedicated (and insane) members of the Nulgath Nation can survive the trials required to unlock the Void Highlord Class!', 'Class', 'None', 'VoidArmor.swf', 'VoidArmor', 'iiclass', 'ar', 1, 25, 50, 19, 1, 1, 0, 1, 0, '0', true, true, false, false, false, 1, NULL, '0', NULL, '0', '', -1, 0, '', 'ffffff');
INSERT INTO "items" ("id", "class_id", "name", "description", "type", "element", "file", "link", "icon", "equipment", "level", "dps", "range", "rarity", "quantity", "stack", "cost", "coins", "diamonds", "crystal", "sell", "market", "temporary", "upgrade", "staff", "enh_id", "faction_id", "req_reputation", "req_class_id", "req_class_points", "req_quests", "quest_string_index", "quest_string_value", "meta", "color") VALUES (516, NULL, 'PvP Trophy', 'A mark signifying honorable player versus player (PvP) combat. It''s not about winning;it''s about making sure the other person loses.', 'Item', 'None', 'iibag', 'iibag', 'iibag', 'Item', 1, 25, 50, 10, 1, 500, 0, 1, 0, '0', true, true, false, false, false, 1, NULL, '0', NULL, '0', '', -1, 0, '', 'ffffff');
INSERT INTO "items" ("id", "class_id", "name", "description", "type", "element", "file", "link", "icon", "equipment", "level", "dps", "range", "rarity", "quantity", "stack", "cost", "coins", "diamonds", "crystal", "sell", "market", "temporary", "upgrade", "staff", "enh_id", "faction_id", "req_reputation", "req_class_id", "req_class_points", "req_quests", "quest_string_index", "quest_string_value", "meta", "color") VALUES (517, NULL, 'Urban Samurai Armor', 'Keeping the traditions of old doesn''t mean you can''t look fly.', 'Armor', 'None', 'UrbanSamurai.swf', 'UrbanSamurai', 'iwarmor', 'co', 1, 25, 50, 13, 1, 1, 0, 1, 0, '0', true, true, false, false, false, 1, NULL, '0', NULL, '0', '', -1, 0, '', 'ffffff');
INSERT INTO "items" ("id", "class_id", "name", "description", "type", "element", "file", "link", "icon", "equipment", "level", "dps", "range", "rarity", "quantity", "stack", "cost", "coins", "diamonds", "crystal", "sell", "market", "temporary", "upgrade", "staff", "enh_id", "faction_id", "req_reputation", "req_class_id", "req_class_points", "req_quests", "quest_string_index", "quest_string_value", "meta", "color") VALUES (518, NULL, 'Urban Samurai Hat', 'Keeping the traditions of old doesn''t mean you can''t look fly.', 'Helm', 'None', 'items/helms/UrbanSamuraiHat.swf', 'UrbanSamuraiHat', 'iihelm', 'he', 1, 25, 50, 13, 1, 1, 0, 1, 0, '0', true, true, false, false, false, 1, NULL, '0', NULL, '0', '', -1, 0, '', 'ffffff');
INSERT INTO "items" ("id", "class_id", "name", "description", "type", "element", "file", "link", "icon", "equipment", "level", "dps", "range", "rarity", "quantity", "stack", "cost", "coins", "diamonds", "crystal", "sell", "market", "temporary", "upgrade", "staff", "enh_id", "faction_id", "req_reputation", "req_class_id", "req_class_points", "req_quests", "quest_string_index", "quest_string_value", "meta", "color") VALUES (519, NULL, 'Urban Samurai Mask + Locks', 'Keeping the traditions of old doesn''t mean you can''t look fly.\r\n', 'Helm', 'None', 'items/helms/UrbanSamuraiMaskLocks.swf', 'UrbanSamuraiMaskLocks', 'iihelm', 'he', 1, 25, 50, 10, 1, 1, 0, 1, 0, '0', true, true, false, false, false, 1, NULL, '0', NULL, '0', '', -1, 0, '', 'ffffff');
INSERT INTO "items" ("id", "class_id", "name", "description", "type", "element", "file", "link", "icon", "equipment", "level", "dps", "range", "rarity", "quantity", "stack", "cost", "coins", "diamonds", "crystal", "sell", "market", "temporary", "upgrade", "staff", "enh_id", "faction_id", "req_reputation", "req_class_id", "req_class_points", "req_quests", "quest_string_index", "quest_string_value", "meta", "color") VALUES (520, NULL, 'Urban Samurai Mask', 'Keeping the traditions of old doesn''t mean you can''t look fly.', 'Helm', 'None', 'items/helms/UrbanSamuraiMask.swf', 'UrbanSamuraiMask', 'iihelm', 'he', 1, 25, 50, 13, 1, 1, 0, 1, 0, '0', true, true, false, false, false, 1, NULL, '0', NULL, '0', '', -1, 0, '', 'ffffff');
INSERT INTO "items" ("id", "class_id", "name", "description", "type", "element", "file", "link", "icon", "equipment", "level", "dps", "range", "rarity", "quantity", "stack", "cost", "coins", "diamonds", "crystal", "sell", "market", "temporary", "upgrade", "staff", "enh_id", "faction_id", "req_reputation", "req_class_id", "req_class_points", "req_quests", "quest_string_index", "quest_string_value", "meta", "color") VALUES (521, NULL, 'Ichigo', '', 'Armor', 'None', 'Ichigo.swf', 'Ichigo', 'iwarmor', 'co', 1, 25, 50, 30, 1, 1, 0, 1, 0, '0', true, false, false, false, false, 1, NULL, '0', NULL, '0', '', -1, 0, '', 'ffffff');
INSERT INTO "items" ("id", "class_id", "name", "description", "type", "element", "file", "link", "icon", "equipment", "level", "dps", "range", "rarity", "quantity", "stack", "cost", "coins", "diamonds", "crystal", "sell", "market", "temporary", "upgrade", "staff", "enh_id", "faction_id", "req_reputation", "req_class_id", "req_class_points", "req_quests", "quest_string_index", "quest_string_value", "meta", "color") VALUES (522, NULL, 'Ulquorra', 'Rare', 'Armor', 'None', 'Ulquorra.swf', 'Ulquorra', 'iwarmor', 'co', 1, 25, 50, 95, 1, 1, 0, 1, 0, '0', true, false, false, false, false, 1, NULL, '0', NULL, '0', '', -1, 0, '', 'ffffff');
INSERT INTO "items" ("id", "class_id", "name", "description", "type", "element", "file", "link", "icon", "equipment", "level", "dps", "range", "rarity", "quantity", "stack", "cost", "coins", "diamonds", "crystal", "sell", "market", "temporary", "upgrade", "staff", "enh_id", "faction_id", "req_reputation", "req_class_id", "req_class_points", "req_quests", "quest_string_index", "quest_string_value", "meta", "color") VALUES (523, NULL, 'Straw Hat', 'Rare', 'Helm', 'None', 'items/helms/StrawHat.swf', 'StrawHat', 'iihelm', 'he', 1, 25, 50, 95, 1, 1, 0, 1, 0, '0', true, false, false, false, false, 1, NULL, '0', NULL, '0', '', -1, 0, '', 'ffffff');
INSERT INTO "items" ("id", "class_id", "name", "description", "type", "element", "file", "link", "icon", "equipment", "level", "dps", "range", "rarity", "quantity", "stack", "cost", "coins", "diamonds", "crystal", "sell", "market", "temporary", "upgrade", "staff", "enh_id", "faction_id", "req_reputation", "req_class_id", "req_class_points", "req_quests", "quest_string_index", "quest_string_value", "meta", "color") VALUES (524, 11, 'Master Crowled', '', 'Class', 'None', 'EmoxusTemplate.swf', 'EmoxusTemplate', 'iiclass', 'ar', 1, 25, 50, 95, 1, 1, 0, 1, 0, '0', true, false, false, false, true, 15, NULL, '0', NULL, '0', '', -1, 0, '', 'ffffff');
INSERT INTO "items" ("id", "class_id", "name", "description", "type", "element", "file", "link", "icon", "equipment", "level", "dps", "range", "rarity", "quantity", "stack", "cost", "coins", "diamonds", "crystal", "sell", "market", "temporary", "upgrade", "staff", "enh_id", "faction_id", "req_reputation", "req_class_id", "req_class_points", "req_quests", "quest_string_index", "quest_string_value", "meta", "color") VALUES (525, NULL, 'Empyrean', '', 'Armor', 'None', 'Legion2018Calendar.swf', 'Legion2018Calendar', 'iwarmor', 'co', 1, 25, 50, 10, 1, 1, 0, 0, 0, '0', true, true, false, false, false, 1, NULL, '0', NULL, '0', '', -1, 0, '', 'ffffff');
INSERT INTO "items" ("id", "class_id", "name", "description", "type", "element", "file", "link", "icon", "equipment", "level", "dps", "range", "rarity", "quantity", "stack", "cost", "coins", "diamonds", "crystal", "sell", "market", "temporary", "upgrade", "staff", "enh_id", "faction_id", "req_reputation", "req_class_id", "req_class_points", "req_quests", "quest_string_index", "quest_string_value", "meta", "color") VALUES (526, NULL, 'Fixing Armor', '', 'Armor', 'None', 'datapaw11.swf', 'datapaw', 'iwarmor', 'co', 1, 25, 50, 10, 1, 1, 0, 0, 0, '0', true, true, false, false, false, 1, NULL, '0', NULL, '0', '', -1, 0, '', 'ffffff');
INSERT INTO "items" ("id", "class_id", "name", "description", "type", "element", "file", "link", "icon", "equipment", "level", "dps", "range", "rarity", "quantity", "stack", "cost", "coins", "diamonds", "crystal", "sell", "market", "temporary", "upgrade", "staff", "enh_id", "faction_id", "req_reputation", "req_class_id", "req_class_points", "req_quests", "quest_string_index", "quest_string_value", "meta", "color") VALUES (527, 13, 'Fallen Angel', 'Fallen Angels are the strategists of the Nation. Trained by Revontheus to wield Crystal Weaponry;they excel at fighting multiple enemies with arrows forged by Nulgath. Fallen Angels have various ways of taking their opponents down.', 'Class', 'None', 'AbyssalAngel2.swf', 'AbyssalAngel2', 'iiclass', 'ar', 1, 25, 50, 100, 1, 1, 0, 1, 0, '0', true, true, false, false, false, 1, NULL, '0', NULL, '0', '', -1, 0, '', 'ffffff');
INSERT INTO "items" ("id", "class_id", "name", "description", "type", "element", "file", "link", "icon", "equipment", "level", "dps", "range", "rarity", "quantity", "stack", "cost", "coins", "diamonds", "crystal", "sell", "market", "temporary", "upgrade", "staff", "enh_id", "faction_id", "req_reputation", "req_class_id", "req_class_points", "req_quests", "quest_string_index", "quest_string_value", "meta", "color") VALUES (528, NULL, 'Genots B', '', 'Armor', 'None', 'Workbymylittlebrotherswf2.swf', 'FemaleTemplate', 'iwarmor', 'co', 1, 25, 50, 10, 1, 1, 0, 0, 0, '0', true, true, false, false, false, 1, NULL, '0', NULL, '0', '', -1, 0, '', 'ffffff');
INSERT INTO "items" ("id", "class_id", "name", "description", "type", "element", "file", "link", "icon", "equipment", "level", "dps", "range", "rarity", "quantity", "stack", "cost", "coins", "diamonds", "crystal", "sell", "market", "temporary", "upgrade", "staff", "enh_id", "faction_id", "req_reputation", "req_class_id", "req_class_points", "req_quests", "quest_string_index", "quest_string_value", "meta", "color") VALUES (529, NULL, 'Soul King Aura', 'soul king aura just black shadow fire', 'Cape', 'None', 'items/capes/fixAura2.swf', 'Aura2', 'iicape', 'ba', 1, 25, 50, 70, 1, 1, 1230, 1, 0, '0', true, true, false, false, false, 1, NULL, '0', NULL, '0', '', -1, 0, '', 'ffffff');
INSERT INTO "items" ("id", "class_id", "name", "description", "type", "element", "file", "link", "icon", "equipment", "level", "dps", "range", "rarity", "quantity", "stack", "cost", "coins", "diamonds", "crystal", "sell", "market", "temporary", "upgrade", "staff", "enh_id", "faction_id", "req_reputation", "req_class_id", "req_class_points", "req_quests", "quest_string_index", "quest_string_value", "meta", "color") VALUES (530, NULL, 'Flaming Mask', '10x Quantity', 'Helm', 'None', 'items/helms/SmokeMaskDatapaw1.swf', 'SmokeMaskDatapaw', 'iihelm', 'he', 1, 25, 50, 70, 1, 1, 2500, 1, 0, '0', true, true, false, false, false, 1, NULL, '0', NULL, '0', '', -1, 0, '', 'ffffff');
INSERT INTO "items" ("id", "class_id", "name", "description", "type", "element", "file", "link", "icon", "equipment", "level", "dps", "range", "rarity", "quantity", "stack", "cost", "coins", "diamonds", "crystal", "sell", "market", "temporary", "upgrade", "staff", "enh_id", "faction_id", "req_reputation", "req_class_id", "req_class_points", "req_quests", "quest_string_index", "quest_string_value", "meta", "color") VALUES (531, NULL, 'Alter Miners Team', 'Alter Miners.. Finding a gold can be donated to poor players if you equipe this youre with us', 'Armor', 'None', 'AltMinersTeam2020.swf', 'AltMinersTeam2020', 'iwarmor', 'co', 1, 25, 50, 70, 1, 1, 800, 1, 0, '0', true, true, false, false, false, 1, NULL, '0', NULL, '0', '', -1, 0, '', 'ffffff');
INSERT INTO "items" ("id", "class_id", "name", "description", "type", "element", "file", "link", "icon", "equipment", "level", "dps", "range", "rarity", "quantity", "stack", "cost", "coins", "diamonds", "crystal", "sell", "market", "temporary", "upgrade", "staff", "enh_id", "faction_id", "req_reputation", "req_class_id", "req_class_points", "req_quests", "quest_string_index", "quest_string_value", "meta", "color") VALUES (532, NULL, 'Avatar of Death', 'Im in youre back while youre not dead well ill be in your back to make way to get you die WOAHAHA im your worst Nightmare', 'Cape', 'None', 'items/capes/AvatarofDeathv2.swf', 'AvatarofDeathv2', 'iicape', 'ba', 1, 25, 50, 70, 1, 1, 4000, 1, 0, '0', true, true, false, false, false, 1, NULL, '0', NULL, '0', '', -1, 0, '', 'ffffff');
INSERT INTO "items" ("id", "class_id", "name", "description", "type", "element", "file", "link", "icon", "equipment", "level", "dps", "range", "rarity", "quantity", "stack", "cost", "coins", "diamonds", "crystal", "sell", "market", "temporary", "upgrade", "staff", "enh_id", "faction_id", "req_reputation", "req_class_id", "req_class_points", "req_quests", "quest_string_index", "quest_string_value", "meta", "color") VALUES (533, NULL, 'Brutal Corn', 'Dont be frude this face an scammers face and fake friends wear this and youre frude as like me', 'Helm', 'None', 'items/helms/BrutalcornToInfinityAndBeyond.swf', 'BrutalcornToInfinityAndBeyond', 'iihelm', 'he', 1, 25, 50, 70, 1, 1, 500, 1, 0, '0', true, true, false, false, false, 1, NULL, '0', NULL, '0', '', -1, 0, '', 'ffffff');
INSERT INTO "items" ("id", "class_id", "name", "description", "type", "element", "file", "link", "icon", "equipment", "level", "dps", "range", "rarity", "quantity", "stack", "cost", "coins", "diamonds", "crystal", "sell", "market", "temporary", "upgrade", "staff", "enh_id", "faction_id", "req_reputation", "req_class_id", "req_class_points", "req_quests", "quest_string_index", "quest_string_value", "meta", "color") VALUES (534, NULL, 'Tiger Fang', 'just me and you 1v1 and you will see the power of tiger fang', 'Gauntlet', 'None', 'items/gauntlets/TigerFang.swf', 'TigerFang', 'iwclaws', 'Weapon', 1, 25, 50, 70, 1, 1, 1600, 1, 0, '0', true, true, false, false, false, 1, NULL, '0', NULL, '0', '', -1, 0, '', 'ffffff');
INSERT INTO "items" ("id", "class_id", "name", "description", "type", "element", "file", "link", "icon", "equipment", "level", "dps", "range", "rarity", "quantity", "stack", "cost", "coins", "diamonds", "crystal", "sell", "market", "temporary", "upgrade", "staff", "enh_id", "faction_id", "req_reputation", "req_class_id", "req_class_points", "req_quests", "quest_string_index", "quest_string_value", "meta", "color") VALUES (535, NULL, 'Dark Paladin Cutlass', 'Thanks for Supportings Us!', 'Sword', 'None', 'items/swords/DarkPaladinNavalSwordFixedD.swf', 'DarkPaladinNavalSwordFixed', 'iwsword', 'Weapon', 1, 25, 50, 80, 1, 1, 0, 1, 0, '0', true, false, false, false, false, 1, NULL, '0', NULL, '0', '', -1, 0, '', 'ffffff');
INSERT INTO "items" ("id", "class_id", "name", "description", "type", "element", "file", "link", "icon", "equipment", "level", "dps", "range", "rarity", "quantity", "stack", "cost", "coins", "diamonds", "crystal", "sell", "market", "temporary", "upgrade", "staff", "enh_id", "faction_id", "req_reputation", "req_class_id", "req_class_points", "req_quests", "quest_string_index", "quest_string_value", "meta", "color") VALUES (536, NULL, 'Dark Paladin BLoD', 'Thanks for Supporting Us!', 'Axe', 'None', 'items/axes/DarkNavalBLoD.swf', 'DarkNavalBLoD', 'iwaxe', 'Weapon', 1, 25, 50, 80, 1, 1, 0, 1, 0, '0', true, false, false, false, false, 1, NULL, '0', NULL, '0', '', -1, 0, '', 'ffffff');
INSERT INTO "items" ("id", "class_id", "name", "description", "type", "element", "file", "link", "icon", "equipment", "level", "dps", "range", "rarity", "quantity", "stack", "cost", "coins", "diamonds", "crystal", "sell", "market", "temporary", "upgrade", "staff", "enh_id", "faction_id", "req_reputation", "req_class_id", "req_class_points", "req_quests", "quest_string_index", "quest_string_value", "meta", "color") VALUES (537, NULL, 'Dark Caladbolg Paladin', 'Thanks for Supporting Us!', 'Sword', 'None', 'items/swords/PaladinCaladbolg.swf', 'PaladinCaladbolg', 'iwsword', 'Weapon', 1, 25, 50, 80, 1, 1, 0, 1, 0, '0', true, false, false, false, false, 1, NULL, '0', NULL, '0', '', -1, 0, '', 'ffffff');
INSERT INTO "items" ("id", "class_id", "name", "description", "type", "element", "file", "link", "icon", "equipment", "level", "dps", "range", "rarity", "quantity", "stack", "cost", "coins", "diamonds", "crystal", "sell", "market", "temporary", "upgrade", "staff", "enh_id", "faction_id", "req_reputation", "req_class_id", "req_class_points", "req_quests", "quest_string_index", "quest_string_value", "meta", "color") VALUES (538, NULL, 'Dark Paladin Naval', 'Thanks for Supporting Us!', 'Armor', 'None', 'DarkPaladinNaval33.swf', 'DarkPaladinNaval', 'iwarmor', 'co', 1, 25, 50, 80, 1, 1, 0, 1, 0, '0', true, false, false, false, false, 1, NULL, '0', NULL, '0', '', -1, 0, '', 'ffffff');
INSERT INTO "items" ("id", "class_id", "name", "description", "type", "element", "file", "link", "icon", "equipment", "level", "dps", "range", "rarity", "quantity", "stack", "cost", "coins", "diamonds", "crystal", "sell", "market", "temporary", "upgrade", "staff", "enh_id", "faction_id", "req_reputation", "req_class_id", "req_class_points", "req_quests", "quest_string_index", "quest_string_value", "meta", "color") VALUES (539, NULL, 'Dark Paladin Hat', 'Thanks for Supporting Us!', 'Helm', 'None', 'items/helms/DarkPaladinHat.swf', 'DarkPaladinHat', 'iihelm', 'he', 1, 25, 50, 80, 1, 1, 0, 1, 0, '0', true, false, false, false, false, 1, NULL, '0', NULL, '0', '', -1, 0, '', 'ffffff');
INSERT INTO "items" ("id", "class_id", "name", "description", "type", "element", "file", "link", "icon", "equipment", "level", "dps", "range", "rarity", "quantity", "stack", "cost", "coins", "diamonds", "crystal", "sell", "market", "temporary", "upgrade", "staff", "enh_id", "faction_id", "req_reputation", "req_class_id", "req_class_points", "req_quests", "quest_string_index", "quest_string_value", "meta", "color") VALUES (540, NULL, 'Dark Paladin Helm', 'Thanks for Supporting Us!', 'Helm', 'None', 'items/helms/DarkPaladinHelm.swf', 'DarkPaladinHelm', 'iihelm', 'he', 1, 25, 50, 80, 1, 1, 0, 1, 0, '0', true, false, false, false, false, 1, NULL, '0', NULL, '0', '', -1, 0, '', 'ffffff');
INSERT INTO "items" ("id", "class_id", "name", "description", "type", "element", "file", "link", "icon", "equipment", "level", "dps", "range", "rarity", "quantity", "stack", "cost", "coins", "diamonds", "crystal", "sell", "market", "temporary", "upgrade", "staff", "enh_id", "faction_id", "req_reputation", "req_class_id", "req_class_points", "req_quests", "quest_string_index", "quest_string_value", "meta", "color") VALUES (541, NULL, 'Dark Paladin Bandana', 'Thanks for Supporting Us!', 'Helm', 'None', 'items/helms/DarkPaladinNavalHelm2.swf', 'DarkPaladinNavalHelm2', 'iihelm', 'he', 1, 25, 50, 80, 1, 1, 0, 1, 0, '0', true, false, false, false, false, 1, NULL, '0', NULL, '0', '', -1, 0, '', 'ffffff');
INSERT INTO "items" ("id", "class_id", "name", "description", "type", "element", "file", "link", "icon", "equipment", "level", "dps", "range", "rarity", "quantity", "stack", "cost", "coins", "diamonds", "crystal", "sell", "market", "temporary", "upgrade", "staff", "enh_id", "faction_id", "req_reputation", "req_class_id", "req_class_points", "req_quests", "quest_string_index", "quest_string_value", "meta", "color") VALUES (542, NULL, 'Dark Paladin Bundle', 'Thanks for Supporting Us!', 'ServerUse', 'None', 'iic2', 'bundle::1::true', 'iic2', 'None', 1, 25, 50, 80, 1, 1, 0, 1, 0, '0', true, true, false, false, false, 1, NULL, '0', NULL, '0', '', -1, 0, '', 'ffffff');
INSERT INTO "items" ("id", "class_id", "name", "description", "type", "element", "file", "link", "icon", "equipment", "level", "dps", "range", "rarity", "quantity", "stack", "cost", "coins", "diamonds", "crystal", "sell", "market", "temporary", "upgrade", "staff", "enh_id", "faction_id", "req_reputation", "req_class_id", "req_class_points", "req_quests", "quest_string_index", "quest_string_value", "meta", "color") VALUES (543, NULL, 'Ultranic Titan Killed', 'Can be found in roftitan', 'Item', 'None', 'iibag', 'iibag', 'iibag', 'Item', 1, 25, 50, 10, 1, 20, 0, 0, 0, '0', true, true, true, false, false, 1, NULL, '0', NULL, '0', '', -1, 0, '', 'ffffff');
INSERT INTO "items" ("id", "class_id", "name", "description", "type", "element", "file", "link", "icon", "equipment", "level", "dps", "range", "rarity", "quantity", "stack", "cost", "coins", "diamonds", "crystal", "sell", "market", "temporary", "upgrade", "staff", "enh_id", "faction_id", "req_reputation", "req_class_id", "req_class_points", "req_quests", "quest_string_index", "quest_string_value", "meta", "color") VALUES (544, NULL, 'Golden Box Killed', 'Can be found in Roftitan', 'Item', 'None', 'iibag', 'iibag', 'iibag', 'Item', 1, 25, 50, 10, 1, 20, 0, 0, 0, '0', true, true, true, false, false, 1, NULL, '0', NULL, '0', '', -1, 0, '', 'ffffff');
INSERT INTO "items" ("id", "class_id", "name", "description", "type", "element", "file", "link", "icon", "equipment", "level", "dps", "range", "rarity", "quantity", "stack", "cost", "coins", "diamonds", "crystal", "sell", "market", "temporary", "upgrade", "staff", "enh_id", "faction_id", "req_reputation", "req_class_id", "req_class_points", "req_quests", "quest_string_index", "quest_string_value", "meta", "color") VALUES (545, NULL, 'CC Scarf', 'Just in Scarf and moving in wind', 'Cape', 'None', 'items/capes/TSScarfCC.swf', 'TSScarfCC', 'iicape', 'ba', 1, 25, 50, 70, 1, 1, 6000, 1, 0, '0', true, true, false, false, false, 1, NULL, '0', NULL, '0', '', -1, 0, '', 'ffffff');
INSERT INTO "items" ("id", "class_id", "name", "description", "type", "element", "file", "link", "icon", "equipment", "level", "dps", "range", "rarity", "quantity", "stack", "cost", "coins", "diamonds", "crystal", "sell", "market", "temporary", "upgrade", "staff", "enh_id", "faction_id", "req_reputation", "req_class_id", "req_class_points", "req_quests", "quest_string_index", "quest_string_value", "meta", "color") VALUES (546, NULL, 'Spirit Harvester', '10x quantity', 'Sword', 'None', 'items/swords/SpiritHarvesterSwordAnimated.swf', 'SpiritHarvesterSwordAnimated', 'iwsword', 'Weapon', 1, 25, 50, 70, 1, 1, 500000, 0, 0, '0', true, true, false, false, false, 1, NULL, '0', NULL, '0', '', -1, 0, '', 'ffffff');
INSERT INTO "items" ("id", "class_id", "name", "description", "type", "element", "file", "link", "icon", "equipment", "level", "dps", "range", "rarity", "quantity", "stack", "cost", "coins", "diamonds", "crystal", "sell", "market", "temporary", "upgrade", "staff", "enh_id", "faction_id", "req_reputation", "req_class_id", "req_class_points", "req_quests", "quest_string_index", "quest_string_value", "meta", "color") VALUES (547, 14, 'Dark Paladin', 'Thank your for supporting PS Nythera', 'Class', 'None', 'DarkPaladinNaval33.swf', 'DarkPaladinNaval', 'ied2', 'ar', 1, 25, 50, 80, 1, 1, 0, 1, 0, '0', true, false, false, false, false, 1, NULL, '0', NULL, '0', '', -1, 0, '', 'ffffff');
INSERT INTO "items" ("id", "class_id", "name", "description", "type", "element", "file", "link", "icon", "equipment", "level", "dps", "range", "rarity", "quantity", "stack", "cost", "coins", "diamonds", "crystal", "sell", "market", "temporary", "upgrade", "staff", "enh_id", "faction_id", "req_reputation", "req_class_id", "req_class_points", "req_quests", "quest_string_index", "quest_string_value", "meta", "color") VALUES (548, NULL, 'Dragon Slayer', '', 'Pet', 'None', 'items/pets/petest4.swf', 'BattleWyvern', 'iipet', 'pe', 1, 25, 50, 10, 1, 1, 0, 0, 0, '0', true, true, false, false, false, 1, NULL, '0', NULL, '0', '', -1, 0, '', 'ffffff');
INSERT INTO "items" ("id", "class_id", "name", "description", "type", "element", "file", "link", "icon", "equipment", "level", "dps", "range", "rarity", "quantity", "stack", "cost", "coins", "diamonds", "crystal", "sell", "market", "temporary", "upgrade", "staff", "enh_id", "faction_id", "req_reputation", "req_class_id", "req_class_points", "req_quests", "quest_string_index", "quest_string_value", "meta", "color") VALUES (549, NULL, 'Shazam Armor', '', 'Armor', 'None', 'Shazam3.swf', 'TRY', 'iwarmor', 'co', 1, 25, 50, 22, 1, 1, 2000, 1, 0, '0', true, true, false, true, false, 1, NULL, '0', NULL, '0', '', -1, 0, '', 'ffffff');
INSERT INTO "items" ("id", "class_id", "name", "description", "type", "element", "file", "link", "icon", "equipment", "level", "dps", "range", "rarity", "quantity", "stack", "cost", "coins", "diamonds", "crystal", "sell", "market", "temporary", "upgrade", "staff", "enh_id", "faction_id", "req_reputation", "req_class_id", "req_class_points", "req_quests", "quest_string_index", "quest_string_value", "meta", "color") VALUES (550, NULL, 'Frost Sword', 'test item', 'Sword', 'None', 'items/swords/AncientDragonB2.swf', 'AncientDragonB2', 'iwsword', 'Weapon', 1, 25, 50, 10, 1, 1, 0, 0, 0, '0', true, true, false, false, false, 1, NULL, '0', NULL, '0', '', -1, 0, '', 'ffffff');
