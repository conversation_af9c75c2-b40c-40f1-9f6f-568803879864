use reforged_shared::{UuidId, Value};

use super::entity::Server;

pub type ServerId = UuidId<Server>;

#[derive(Debug, <PERSON>lone, PartialEq, Eq, Default)]
pub struct ServerName(String);

impl Value<String> for ServerName {
    fn value(&self) -> String {
        self.0.clone()
    }
}

impl ServerName {
    pub fn new<T: Into<String>>(name: T) -> Self {
        Self(name.into())
    }
}

#[derive(Debu<PERSON>, <PERSON>lone, PartialEq, Eq, Default)]
pub struct ServerIp(String);

impl Value<String> for ServerIp {
    fn value(&self) -> String {
        self.0.clone()
    }
}

impl ServerIp {
    pub fn new<T: Into<String>>(ip: T) -> Self {
        Self(ip.into())
    }
}

#[derive(Debug, Clone, PartialEq, Eq, Default)]
pub struct ServerPort(i32);

impl Value<i32> for ServerPort {
    fn value(&self) -> i32 {
        self.0
    }
}

impl ServerPort {
    pub fn new(port: i32) -> Self {
        Self(port)
    }
}

#[derive(<PERSON>bu<PERSON>, <PERSON><PERSON>, <PERSON>ialEq, Eq, Default)]
pub struct ServerOnline(bool);

impl Value<bool> for ServerOnline {
    fn value(&self) -> bool {
        self.0
    }
}

impl ServerOnline {
    pub fn new(online: bool) -> Self {
        Self(online)
    }
}

#[derive(Debug, Clone, PartialEq, Eq, Default)]
pub struct ServerUpgrade(bool);

impl Value<bool> for ServerUpgrade {
    fn value(&self) -> bool {
        self.0
    }
}

impl ServerUpgrade {
    pub fn new(upgrade: bool) -> Self {
        Self(upgrade)
    }
}

#[derive(Debug, Clone, PartialEq, Eq, Default)]
pub struct ServerChat(i16);

impl Value<i16> for ServerChat {
    fn value(&self) -> i16 {
        self.0
    }
}

impl ServerChat {
    pub fn new(chat: i16) -> Self {
        Self(chat)
    }
}

#[derive(Debug, Clone, PartialEq, Eq, Default)]
pub struct ServerCount(i64);

impl Value<i64> for ServerCount {
    fn value(&self) -> i64 {
        self.0
    }
}

impl ServerCount {
    pub fn new(count: i64) -> Self {
        Self(count)
    }
}

#[derive(Debug, Clone, PartialEq, Eq, Default)]
pub struct ServerMax(i64);

impl Value<i64> for ServerMax {
    fn value(&self) -> i64 {
        self.0
    }
}

impl ServerMax {
    pub fn new(max: i64) -> Self {
        Self(max)
    }
}

#[derive(Debug, Clone, PartialEq, Eq, Default)]
pub struct ServerMotd(String);

impl Value<String> for ServerMotd {
    fn value(&self) -> String {
        self.0.clone()
    }
}

impl ServerMotd {
    pub fn new<T: Into<String>>(motd: T) -> Self {
        Self(motd.into())
    }
}

#[derive(Debug, Clone, PartialEq, Eq, Default)]
pub struct ServerMaintenance(bool);

impl Value<bool> for ServerMaintenance {
    fn value(&self) -> bool {
        self.0
    }
}

impl ServerMaintenance {
    pub fn new(maintenance: bool) -> Self {
        Self(maintenance)
    }
}
