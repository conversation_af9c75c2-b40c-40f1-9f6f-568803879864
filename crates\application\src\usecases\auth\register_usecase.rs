use std::sync::Arc;

use esrs::AggregateState;
use reforged_domain::{
    commands::user_commands::{CreateUserCommand, UserCommands},
    models::user::value_object::UserRole,
};

use crate::{
    error::ApplicationError,
    eventsource::user::{UserAggregateState, UserStoreManager},
    queries::{
        user_queries::{GetUserByEmailQuery, GetUserByUsernameQuery},
        user_query_handlers::UserQueryHandler,
    },
    services::captcha_service::Captcha,
    traits::QueryHandler,
};

pub struct RegisterUsecase {
    captcha_service: Arc<dyn Captcha>,
    user_query_handler: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
    user_store_manager: Arc<UserStoreManager>,
}

impl RegisterUsecase {
    pub fn new(
        captcha_service: Arc<dyn Captcha>,
        user_query_handler: <PERSON>r<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
        user_store_manager: Arc<UserStoreManager>,
    ) -> Self {
        Self {
            captcha_service,
            user_query_handler,
            user_store_manager,
        }
    }
}

impl RegisterUsecase {
    pub async fn execute(
        &self,
        username: String,
        email: String,
        password: String,
        gender: String,
        captcha: String,
    ) -> Result<(), ApplicationError> {
        self.captcha_service.validate(captcha).await?;

        // let user_repository = self.user_repository.clone();
        let check_user_query = GetUserByUsernameQuery {
            username: username.clone(),
        };
        let check_user = self.user_query_handler.handle(check_user_query).await;
        if check_user.is_ok() {
            return Err(ApplicationError::UserALreadyExists);
        }

        let check_user_query = GetUserByEmailQuery {
            email: email.clone(),
        };

        let check_user = self.user_query_handler.handle(check_user_query).await;

        if check_user.is_ok() {
            return Err(ApplicationError::UserALreadyExists);
        }

        let cmd = CreateUserCommand {
            id: uuid::Uuid::now_v7(),
            username,
            email,
            gender,
            password,
            role: UserRole::Player.to_string(),
        };

        let state: AggregateState<UserAggregateState> = AggregateState::with_id(cmd.id);
        _ = self
            .user_store_manager
            .handle_command(state, UserCommands::CreateUser(cmd))
            .await;

        Ok(())
    }
}
