//! `SeaORM` Entity, @generated by sea-orm-codegen 1.1.0

use sea_orm::entity::prelude::*;
use serde::Deserialize;

#[derive(<PERSON><PERSON>, Debug, PartialEq, DeriveEntityModel, Eq, Deserialize)]
#[sea_orm(table_name = "monster_drops")]
pub struct Model {
    #[sea_orm(primary_key, auto_increment = false)]
    pub id: Uuid,
    pub monster_id: Uuid,
    pub item_id: Uuid,
    pub chance: Decimal,
    pub quantity: i32,
}

#[derive(<PERSON><PERSON>, <PERSON><PERSON>, Debug, EnumIter, DeriveRelation)]
pub enum Relation {
    #[sea_orm(
        belongs_to = "super::items::Entity",
        from = "Column::ItemId",
        to = "super::items::Column::Id",
        on_update = "Cascade",
        on_delete = "Cascade"
    )]
    Items,
    #[sea_orm(
        belongs_to = "super::monsters::Entity",
        from = "Column::MonsterId",
        to = "super::monsters::Column::Id",
        on_update = "Cascade",
        on_delete = "Cascade"
    )]
    Monsters,
}

impl Related<super::items::Entity> for Entity {
    fn to() -> RelationDef {
        Relation::Items.def()
    }
}

impl Related<super::monsters::Entity> for Entity {
    fn to() -> RelationDef {
        Relation::Monsters.def()
    }
}

impl ActiveModelBehavior for ActiveModel {}
