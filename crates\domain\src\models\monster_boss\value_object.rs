use reforged_shared::{UuidId, Value};

use super::entity::MonsterBoss;

pub type MonsterBossId = UuidId<MonsterBoss>;

#[derive(Debug, PartialEq, Eq, PartialOrd, Ord, Clone, De<PERSON>ult)]

pub struct MonsterBossSpawnInterval(u32);

impl MonsterBossSpawnInterval {
    pub fn new(value: u32) -> Self {
        Self(value)
    }
}

impl Value<u32> for MonsterBossSpawnInterval {
    fn value(&self) -> u32 {
        self.0
    }
}

impl From<u32> for MonsterBossSpawnInterval {
    fn from(value: u32) -> Self {
        Self(value)
    }
}

#[derive(Debug, PartialEq, Eq, PartialOrd, Ord, Clone, Default)]

pub struct MonsterBossTimeLimit(u32);

impl MonsterBossTimeLimit {
    pub fn new(value: u32) -> Self {
        Self(value)
    }
}

impl Value<u32> for MonsterBossTimeLimit {
    fn value(&self) -> u32 {
        self.0
    }
}

#[derive(Debug, PartialEq, Eq, <PERSON><PERSON><PERSON>rd, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>)]

pub struct MonsterBossKills(u32);

impl MonsterBossKills {
    pub fn new(value: u32) -> Self {
        Self(value)
    }
}

impl Value<u32> for MonsterBossKills {
    fn value(&self) -> u32 {
        self.0
    }
}

impl From<u32> for MonsterBossKills {
    fn from(value: u32) -> Self {
        Self(value)
    }
}

#[derive(Debug, PartialEq, Eq, PartialOrd, Ord, Clone, Default)]

pub struct MonsterBossDeaths(u32);

impl MonsterBossDeaths {
    pub fn new(value: u32) -> Self {
        Self(value)
    }
}

impl Value<u32> for MonsterBossDeaths {
    fn value(&self) -> u32 {
        self.0
    }
}

impl From<u32> for MonsterBossDeaths {
    fn from(value: u32) -> Self {
        Self(value)
    }
}

#[derive(Debug, PartialEq, Eq, PartialOrd, Ord, Clone, Default)]

pub struct MonsterBossDeathTime(u32);

impl MonsterBossDeathTime {
    pub fn new(value: u32) -> Self {
        Self(value)
    }
}

impl Value<u32> for MonsterBossDeathTime {
    fn value(&self) -> u32 {
        self.0
    }
}

impl From<u32> for MonsterBossDeathTime {
    fn from(value: u32) -> Self {
        Self(value)
    }
}

#[derive(Debug, PartialEq, Eq, PartialOrd, Ord, Clone, Default)]

pub struct MonsterBossSpawnTime(u32);

impl MonsterBossSpawnTime {
    pub fn new(value: u32) -> Self {
        Self(value)
    }
}

impl Value<u32> for MonsterBossSpawnTime {
    fn value(&self) -> u32 {
        self.0
    }
}

impl From<u32> for MonsterBossSpawnTime {
    fn from(value: u32) -> Self {
        Self(value)
    }
}

#[derive(Debug, PartialEq, Eq, PartialOrd, Ord, Clone, Default)]

pub struct MonsterBossDescription(String);

impl MonsterBossDescription {
    pub fn new(name: impl Into<String>) -> Self {
        Self(name.into())
    }
}

impl Value<String> for MonsterBossDescription {
    fn value(&self) -> String {
        self.0.clone()
    }
}

impl From<String> for MonsterBossDescription {
    fn from(value: String) -> Self {
        Self(value)
    }
}
