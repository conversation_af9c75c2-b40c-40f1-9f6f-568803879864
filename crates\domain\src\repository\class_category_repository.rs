use async_trait::async_trait;

use crate::{
    error::RepositoryError,
    models::class_category::{entity::ClassCategory, value_object::ClassCategoryId},
};

#[mockall::automock]
#[async_trait]
pub trait ClassCategoryRepository: Send + Sync {
    async fn save(&self, category: &ClassCategory) -> Result<ClassCategory, RepositoryError>;
    async fn delete(&self, category: &ClassCategoryId) -> Result<(), RepositoryError>;
}

#[mockall::automock]
#[async_trait]
pub trait ClassCategoryReadRepository: Send + Sync {
    async fn find_by_id(
        &self,
        id: &ClassCategoryId,
    ) -> Result<Option<ClassCategory>, RepositoryError>;
    async fn find_by_name(&self, name: &str) -> Result<Option<ClassCategory>, RepositoryError>;
    async fn find_all(&self) -> Result<Vec<ClassCategory>, RepositoryError>;
    async fn find_paginate(
        &self,
        page: u32,
        limit: u32,
    ) -> Result<Vec<ClassCategory>, RepositoryError>;
}
