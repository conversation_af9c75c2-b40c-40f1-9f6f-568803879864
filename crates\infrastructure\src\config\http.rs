use std::net::Ipv4Addr;

use super::{Config, ConfigError};

pub struct HttpConfig {
    host: Ipv4Addr,
    port: u16,
    cors_allowed_origins: Vec<String>,
}

impl HttpConfig {
    pub fn host(&self) -> String {
        self.host.to_string()
    }

    pub fn port(&self) -> u16 {
        self.port
    }

    pub fn get_connection_string(&self) -> String {
        format!("{}:{}", self.host, self.port)
    }

    pub fn cors_allowed_origins(&self) -> Vec<String> {
        self.cors_allowed_origins.clone()
    }
}

impl Config for HttpConfig {
    fn from_env() -> Result<Self, ConfigError> {
        let host = std::env::var("HOST")
            .map_err(|_| ConfigError::EnvVarNotFound("HOST".to_string()))?
            .parse::<Ipv4Addr>()
            .map_err(|_| ConfigError::EnvVarNotValid("HOST".to_string()))?;

        let port = std::env::var("PORT")
            .map_err(|_| ConfigError::EnvVarNotFound("PORT".to_string()))?
            .parse::<u16>()
            .map_err(|_| ConfigError::EnvVarNotValid("PORT".to_string()))?;

        let cors_allowed_origins = std::env::var("CORS_ALLOWED_ORIGINS")
            .map_err(|_| ConfigError::EnvVarNotFound("CORS_ALLOWED_ORIGINS".to_string()))?
            .split(',')
            .map(|s| s.to_string())
            .collect::<Vec<String>>();

        Ok(Self {
            host,
            port,
            cors_allowed_origins,
        })
    }
}
