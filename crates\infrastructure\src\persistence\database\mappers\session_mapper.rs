use reforged_domain::models::session::{
    entity::Session,
    value_object::{
        Session<PERSON><PERSON>Ip, SessionCreatedAt, SessionExpiresAt, SessionId, SessionIsBlocked,
        SessionToken, SessionUserAgent,
    },
};
use reforged_domain::models::user::value_object::UserId;

#[derive(Debug)]
pub struct SessionDbModelMapper(super::super::models::sessions::Model);

impl SessionDbModelMapper {
    pub fn new(session: super::super::models::sessions::Model) -> Self {
        Self(session)
    }
}

impl From<SessionDbModelMapper> for Session {
    fn from(value: SessionDbModelMapper) -> Self {
        let session_model = value.0;

        let id = SessionId::new(session_model.id);

        Session::builder()
            .id(id)
            .user_id(UserId::new(session_model.user_id))
            .token(SessionToken::new(session_model.token))
            .user_agent(SessionUserAgent::new(session_model.user_agent))
            .client_ip(SessionClientIp::new(session_model.client_ip))
            .is_blocked(SessionIsBlocked::new(session_model.is_blocked))
            .created_at(SessionCreatedAt::new(session_model.created_at.and_utc()))
            .expires_at(SessionExpiresAt::new(
                session_model
                    .expires_at
                    .map(|dt| dt.and_utc())
                    .unwrap_or_default(),
            ))
            .build()
    }
}
