//! `SeaORM` Entity, @generated by sea-orm-codegen 1.1.0

use super::sea_orm_active_enums::StoreType;
use sea_orm::entity::prelude::*;
use serde::Deserialize;

#[derive(<PERSON><PERSON>, Debug, PartialEq, DeriveEntityModel, Eq, Deserialize)]
#[sea_orm(table_name = "stores")]
pub struct Model {
    #[sea_orm(primary_key, auto_increment = false)]
    pub id: Uuid,
    pub available: i16,
    pub name: String,
    pub price: Decimal,
    pub item: i32,
    pub achievement: Option<i32>,
    pub gold: i64,
    pub coins: i32,
    pub crystal: i32,
    pub diamonds: i32,
    pub upgrade: i32,
    pub role_id: Option<Uuid>,
    pub title_id: Option<Uuid>,
    pub bag_slots: i32,
    pub bank_slots: i32,
    pub house_slots: i32,
    pub r#type: Option<StoreType>,
    pub quantity: i32,
    pub img: Option<String>,
}

#[derive(<PERSON><PERSON>, <PERSON><PERSON>, Debug, EnumI<PERSON>, DeriveRelation)]
pub enum Relation {
    #[sea_orm(has_many = "super::user_purchases::Entity")]
    UserPurchases,
}

impl Related<super::user_purchases::Entity> for Entity {
    fn to() -> RelationDef {
        Relation::UserPurchases.def()
    }
}

impl ActiveModelBehavior for ActiveModel {}
