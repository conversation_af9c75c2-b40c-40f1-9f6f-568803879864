use async_trait::async_trait;
use mockall::automock;

use crate::error::RepositoryError;
use crate::models::user_quest::entity::UserQuest;
use crate::models::user_quest::value_object::UserQuestId;

#[automock]
#[async_trait]
pub trait UserQuestRepository: Send + Sync {
    async fn save(&self, entity: &UserQuest) -> Result<(), RepositoryError>;
    async fn update(&self, entity: &UserQuest) -> Result<(), RepositoryError>;
    async fn delete(&self, id: &UserQuestId) -> Result<(), RepositoryError>;
}

#[automock]
#[async_trait]
pub trait UserQuestReadRepository: Send + Sync {
    async fn find_by_id(&self, id: &UserQuestId) -> Result<Option<UserQuest>, RepositoryError>;
    async fn find_all(&self) -> Result<Vec<UserQuest>, RepositoryError>;
}
