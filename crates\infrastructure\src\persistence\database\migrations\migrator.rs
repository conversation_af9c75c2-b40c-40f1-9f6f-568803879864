use sea_orm_migration::{MigratorTrait, cli::run_cli};

use crate::migrations::{
    m20250329_081110_create_enums, m20250329_081302_create_achievements_table,
    m20250329_081330_create_factions_table, m20250329_081337_create_skills_table,
    m20250329_081344_create_classes_table, m20250329_081351_create_items_table,
    m20250329_081410_create_auras_table, m20250329_081417_create_enhancements_table,
    m20250329_081427_create_monsters_table, m20250329_081435_create_maps_table,
    m20250329_081440_create_shops_table, m20250329_081446_create_users_table,
    m20250329_081452_create_guilds_table, m20250329_081505_create_articles_table,
    m20250329_081514_create_books_table, m20250329_081520_create_hairs_table,
    m20250329_081525_create_quests_table, m20250329_081532_create_admins_table,
    m20250503_153324_create_password_reset_tokens_table,
};

use crate::seeders::{
    m20250329_081213_seed_titles, m20250329_081624_seed_roles_and_factions,
    m20250329_081731_seed_enhancements, m20250329_081805_seed_achievements,
    m20250329_081811_seed_auras, m20250329_081815_seed_aura_effects, m20250329_081823_seed_skills,
    m20250329_081829_seed_classes, m20250329_081834_seed_items, m20250329_081838_seed_hairs,
    m20250329_081844_seed_maps, m20250329_081855_seed_monsters, m20250329_081901_seed_quests,
    m20250329_081907_seed_redeem_codes, m20250329_081915_seed_servers,
    m20250329_081920_seed_settings, m20250329_081924_seed_shops,
};

pub struct Migrator;

impl MigratorTrait for Migrator {
    fn migrations() -> Vec<Box<dyn sea_orm_migration::MigrationTrait>> {
        vec![
            Box::new(m20250329_081110_create_enums::Migration),
            Box::new(m20250329_081302_create_achievements_table::Migration),
            Box::new(m20250329_081330_create_factions_table::Migration),
            Box::new(m20250329_081337_create_skills_table::Migration),
            Box::new(m20250329_081344_create_classes_table::Migration),
            Box::new(m20250329_081417_create_enhancements_table::Migration),
            Box::new(m20250329_081351_create_items_table::Migration),
            Box::new(m20250329_081410_create_auras_table::Migration),
            Box::new(m20250329_081427_create_monsters_table::Migration),
            Box::new(m20250329_081435_create_maps_table::Migration),
            Box::new(m20250329_081440_create_shops_table::Migration),
            Box::new(m20250329_081446_create_users_table::Migration),
            Box::new(m20250329_081452_create_guilds_table::Migration),
            Box::new(m20250329_081505_create_articles_table::Migration),
            Box::new(m20250329_081514_create_books_table::Migration),
            Box::new(m20250329_081520_create_hairs_table::Migration),
            Box::new(m20250329_081525_create_quests_table::Migration),
            Box::new(m20250329_081532_create_admins_table::Migration),
            Box::new(m20250503_153324_create_password_reset_tokens_table::Migration),
            Box::new(m20250329_081624_seed_roles_and_factions::Migration),
            Box::new(m20250329_081213_seed_titles::Migration),
            Box::new(m20250329_081731_seed_enhancements::Migration),
            Box::new(m20250329_081805_seed_achievements::Migration),
            Box::new(m20250329_081811_seed_auras::Migration),
            Box::new(m20250329_081815_seed_aura_effects::Migration),
            Box::new(m20250329_081823_seed_skills::Migration),
            Box::new(m20250329_081829_seed_classes::Migration),
            Box::new(m20250329_081834_seed_items::Migration),
            Box::new(m20250329_081838_seed_hairs::Migration),
            Box::new(m20250329_081844_seed_maps::Migration),
            Box::new(m20250329_081855_seed_monsters::Migration),
            Box::new(m20250329_081901_seed_quests::Migration),
            Box::new(m20250329_081907_seed_redeem_codes::Migration),
            Box::new(m20250329_081915_seed_servers::Migration),
            Box::new(m20250329_081920_seed_settings::Migration),
            Box::new(m20250329_081924_seed_shops::Migration),
        ]
    }
}

pub async fn migrate() {
    run_cli(Migrator).await;
}
