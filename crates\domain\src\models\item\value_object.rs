use super::entity::Item;
use reforged_shared::{UuidId, Value};

pub type ItemId = UuidId<Item>;

#[derive(Debug, PartialEq, Eq, PartialOrd, Ord, <PERSON>lone, Default)]

pub struct ItemName(String);

impl ItemName {
    pub fn new(name: impl Into<String>) -> Self {
        Self(name.into())
    }
}

impl Value<String> for ItemName {
    fn value(&self) -> String {
        self.0.clone()
    }
}

impl From<String> for ItemName {
    fn from(value: String) -> Self {
        Self(value)
    }
}

impl From<&str> for ItemName {
    fn from(value: &str) -> Self {
        Self(value.to_string())
    }
}

#[derive(Debug, PartialEq, Eq, PartialOrd, Ord, Clone, Default)]

pub struct ItemDescription(String);

impl ItemDescription {
    pub fn new(description: impl Into<String>) -> Self {
        Self(description.into())
    }
}

impl Value<String> for ItemDescription {
    fn value(&self) -> String {
        self.0.clone()
    }
}

impl From<String> for ItemDescription {
    fn from(value: String) -> Self {
        Self(value)
    }
}

impl From<&str> for ItemDescription {
    fn from(value: &str) -> Self {
        Self(value.to_string())
    }
}

#[derive(Debug, PartialEq, Eq, PartialOrd, Ord, Clone, Default)]

pub struct ItemType(String);

impl ItemType {
    pub fn new(item_type: impl Into<String>) -> Self {
        Self(item_type.into())
    }
}

impl Value<String> for ItemType {
    fn value(&self) -> String {
        self.0.clone()
    }
}

impl From<String> for ItemType {
    fn from(value: String) -> Self {
        Self(value)
    }
}

impl From<&str> for ItemType {
    fn from(value: &str) -> Self {
        Self(value.to_string())
    }
}

#[derive(Debug, PartialEq, Eq, PartialOrd, Ord, Clone, Default)]

pub struct Element(String);

impl Element {
    pub fn new(element: impl Into<String>) -> Self {
        Self(element.into())
    }
}

impl Value<String> for Element {
    fn value(&self) -> String {
        self.0.clone()
    }
}

impl From<String> for Element {
    fn from(value: String) -> Self {
        Self(value)
    }
}

impl From<&str> for Element {
    fn from(value: &str) -> Self {
        Self(value.to_string())
    }
}

#[derive(Debug, PartialEq, Eq, PartialOrd, Ord, Clone, Default)]

pub struct File(String);

impl File {
    pub fn new(file: impl Into<String>) -> Self {
        Self(file.into())
    }
}

impl Value<String> for File {
    fn value(&self) -> String {
        self.0.clone()
    }
}

impl From<String> for File {
    fn from(value: String) -> Self {
        Self(value)
    }
}

impl From<&str> for File {
    fn from(value: &str) -> Self {
        Self(value.to_string())
    }
}

#[derive(Debug, PartialEq, Eq, PartialOrd, Ord, Clone, Default)]

pub struct Link(String);

impl Link {
    pub fn new(link: impl Into<String>) -> Self {
        Self(link.into())
    }
}

impl Value<String> for Link {
    fn value(&self) -> String {
        self.0.clone()
    }
}

impl From<String> for Link {
    fn from(value: String) -> Self {
        Self(value)
    }
}

impl From<&str> for Link {
    fn from(value: &str) -> Self {
        Self(value.to_string())
    }
}

#[derive(Debug, PartialEq, Eq, PartialOrd, Ord, Clone, Default)]

pub struct ItemIcon(String);

impl ItemIcon {
    pub fn new(icon: impl Into<String>) -> Self {
        Self(icon.into())
    }
}

impl Value<String> for ItemIcon {
    fn value(&self) -> String {
        self.0.clone()
    }
}

impl From<String> for ItemIcon {
    fn from(value: String) -> Self {
        Self(value)
    }
}

impl From<&str> for ItemIcon {
    fn from(value: &str) -> Self {
        Self(value.to_string())
    }
}

#[derive(Debug, PartialEq, Eq, PartialOrd, Ord, Clone, Default)]

pub struct Equipment(String);

impl Equipment {
    pub fn new(equipment: impl Into<String>) -> Self {
        Self(equipment.into())
    }
}

impl Value<String> for Equipment {
    fn value(&self) -> String {
        self.0.clone()
    }
}

impl From<String> for Equipment {
    fn from(value: String) -> Self {
        Self(value)
    }
}

impl From<&str> for Equipment {
    fn from(value: &str) -> Self {
        Self(value.to_string())
    }
}

#[derive(Debug, PartialEq, Eq, PartialOrd, Ord, Clone, Default)]

pub struct Level(i16);

impl Level {
    pub fn new(value: i16) -> Self {
        Self(value)
    }
}

impl Value<i16> for Level {
    fn value(&self) -> i16 {
        self.0
    }
}

impl From<i16> for Level {
    fn from(value: i16) -> Self {
        Self(value)
    }
}

#[derive(Debug, PartialEq, Eq, PartialOrd, Ord, Clone, Default)]

pub struct Dps(i16);

impl Dps {
    pub fn new(value: i16) -> Self {
        Self(value)
    }
}

impl Value<i16> for Dps {
    fn value(&self) -> i16 {
        self.0
    }
}

impl From<i16> for Dps {
    fn from(value: i16) -> Self {
        Self(value)
    }
}

#[derive(Debug, PartialEq, Eq, PartialOrd, Ord, Clone, Default)]

pub struct ItemRange(i16);

impl ItemRange {
    pub fn new(value: i16) -> Self {
        Self(value)
    }
}

impl Value<i16> for ItemRange {
    fn value(&self) -> i16 {
        self.0
    }
}

impl From<i16> for ItemRange {
    fn from(value: i16) -> Self {
        Self(value)
    }
}

#[derive(Debug, PartialEq, Eq, PartialOrd, Ord, Clone, Default)]

pub struct Quantity(i16);

impl Quantity {
    pub fn new(value: i16) -> Self {
        Self(value)
    }
}

impl Value<i16> for Quantity {
    fn value(&self) -> i16 {
        self.0
    }
}

impl From<i16> for Quantity {
    fn from(value: i16) -> Self {
        Self(value)
    }
}

#[derive(Debug, PartialEq, Eq, PartialOrd, Ord, Clone, Default)]

pub struct Stack(i32);

impl Stack {
    pub fn new(value: i32) -> Self {
        Self(value)
    }
}

impl Value<i32> for Stack {
    fn value(&self) -> i32 {
        self.0
    }
}

impl From<i32> for Stack {
    fn from(value: i32) -> Self {
        Self(value)
    }
}

#[derive(Debug, PartialEq, Eq, PartialOrd, Ord, Clone, Default)]

pub struct Cost(i32);

impl Cost {
    pub fn new(value: i32) -> Self {
        Self(value)
    }
}

impl Value<i32> for Cost {
    fn value(&self) -> i32 {
        self.0
    }
}

impl From<i32> for Cost {
    fn from(value: i32) -> Self {
        Self(value)
    }
}

#[derive(Debug, PartialEq, Eq, PartialOrd, Ord, Clone, Default)]

pub struct Coins(i16);

impl Coins {
    pub fn new(value: i16) -> Self {
        Self(value)
    }
}

impl Value<i16> for Coins {
    fn value(&self) -> i16 {
        self.0
    }
}

impl From<i16> for Coins {
    fn from(value: i16) -> Self {
        Self(value)
    }
}

#[derive(Debug, PartialEq, Eq, PartialOrd, Ord, Clone, Default)]

pub struct Diamonds(i16);

impl Diamonds {
    pub fn new(value: i16) -> Self {
        Self(value)
    }
}

impl Value<i16> for Diamonds {
    fn value(&self) -> i16 {
        self.0
    }
}

impl From<i16> for Diamonds {
    fn from(value: i16) -> Self {
        Self(value)
    }
}

#[derive(Debug, PartialEq, Eq, PartialOrd, Ord, Clone, Default)]

pub struct Crystal(i64);

impl Crystal {
    pub fn new(value: i64) -> Self {
        Self(value)
    }
}

impl Value<i64> for Crystal {
    fn value(&self) -> i64 {
        self.0
    }
}

impl From<i64> for Crystal {
    fn from(value: i64) -> Self {
        Self(value)
    }
}

#[derive(Debug, PartialEq, Eq, PartialOrd, Ord, Clone, Default)]

pub struct Sell(bool);

impl Sell {
    pub fn new(value: bool) -> Self {
        Self(value)
    }
}

impl Value<bool> for Sell {
    fn value(&self) -> bool {
        self.0
    }
}

impl From<bool> for Sell {
    fn from(value: bool) -> Self {
        Self(value)
    }
}

#[derive(Debug, PartialEq, Eq, PartialOrd, Ord, Clone, Default)]

pub struct Market(bool);

impl Market {
    pub fn new(value: bool) -> Self {
        Self(value)
    }
}

impl Value<bool> for Market {
    fn value(&self) -> bool {
        self.0
    }
}

impl From<bool> for Market {
    fn from(value: bool) -> Self {
        Self(value)
    }
}

#[derive(Debug, PartialEq, Eq, PartialOrd, Ord, Clone, Default)]

pub struct Temporary(bool);

impl Temporary {
    pub fn new(value: bool) -> Self {
        Self(value)
    }
}

impl Value<bool> for Temporary {
    fn value(&self) -> bool {
        self.0
    }
}

impl From<bool> for Temporary {
    fn from(value: bool) -> Self {
        Self(value)
    }
}

#[derive(Debug, PartialEq, Eq, PartialOrd, Ord, Clone, Default)]

pub struct Upgrade(bool);

impl Upgrade {
    pub fn new(value: bool) -> Self {
        Self(value)
    }
}

impl Value<bool> for Upgrade {
    fn value(&self) -> bool {
        self.0
    }
}

impl From<bool> for Upgrade {
    fn from(value: bool) -> Self {
        Self(value)
    }
}

#[derive(Debug, PartialEq, Eq, PartialOrd, Ord, Clone, Default)]

pub struct Staff(bool);

impl Staff {
    pub fn new(value: bool) -> Self {
        Self(value)
    }
}

impl Value<bool> for Staff {
    fn value(&self) -> bool {
        self.0
    }
}

impl From<bool> for Staff {
    fn from(value: bool) -> Self {
        Self(value)
    }
}

#[derive(Debug, PartialEq, Eq, PartialOrd, Ord, Clone, Default)]

pub struct ReqReputation(i64);

impl ReqReputation {
    pub fn new(value: i64) -> Self {
        Self(value)
    }
}

impl Value<i64> for ReqReputation {
    fn value(&self) -> i64 {
        self.0
    }
}

impl From<i64> for ReqReputation {
    fn from(value: i64) -> Self {
        Self(value)
    }
}

#[derive(Debug, PartialEq, Eq, PartialOrd, Ord, Clone, Default)]

pub struct ReqClassPoints(i64);

impl ReqClassPoints {
    pub fn new(value: i64) -> Self {
        Self(value)
    }
}

impl Value<i64> for ReqClassPoints {
    fn value(&self) -> i64 {
        self.0
    }
}

impl From<i64> for ReqClassPoints {
    fn from(value: i64) -> Self {
        Self(value)
    }
}

#[derive(Debug, PartialEq, Eq, PartialOrd, Ord, Clone, Default)]

pub struct ReqQuests(String);

impl ReqQuests {
    pub fn new(req_quests: impl Into<String>) -> Self {
        Self(req_quests.into())
    }
}

impl Value<String> for ReqQuests {
    fn value(&self) -> String {
        self.0.clone()
    }
}

impl From<String> for ReqQuests {
    fn from(value: String) -> Self {
        Self(value)
    }
}

impl From<&str> for ReqQuests {
    fn from(value: &str) -> Self {
        Self(value.to_string())
    }
}

#[derive(Debug, PartialEq, Eq, PartialOrd, Ord, Clone, Default)]

pub struct QuestStringIndex(i16);

impl QuestStringIndex {
    pub fn new(value: i16) -> Self {
        Self(value)
    }
}

impl Value<i16> for QuestStringIndex {
    fn value(&self) -> i16 {
        self.0
    }
}

impl From<i16> for QuestStringIndex {
    fn from(value: i16) -> Self {
        Self(value)
    }
}

#[derive(Debug, PartialEq, Eq, PartialOrd, Ord, Clone, Default)]

pub struct QuestStringValue(i16);

impl QuestStringValue {
    pub fn new(value: i16) -> Self {
        Self(value)
    }
}

impl Value<i16> for QuestStringValue {
    fn value(&self) -> i16 {
        self.0
    }
}

impl From<i16> for QuestStringValue {
    fn from(value: i16) -> Self {
        Self(value)
    }
}

#[derive(Debug, PartialEq, Eq, PartialOrd, Ord, Clone, Default)]

pub struct Meta(Option<String>);

impl Meta {
    pub fn new(meta: Option<impl Into<String>>) -> Self {
        Self(meta.map(|m| m.into()))
    }
}

impl Value<Option<String>> for Meta {
    fn value(&self) -> Option<String> {
        self.0.clone()
    }
}

impl From<Option<String>> for Meta {
    fn from(value: Option<String>) -> Self {
        Self(value)
    }
}

impl From<String> for Meta {
    fn from(value: String) -> Self {
        Self(Some(value))
    }
}

impl From<&str> for Meta {
    fn from(value: &str) -> Self {
        Self(Some(value.to_string()))
    }
}

#[derive(Debug, PartialEq, Eq, PartialOrd, Ord, Clone, Default)]

pub struct Color(String);

impl Color {
    pub fn new(color: impl Into<String>) -> Self {
        Self(color.into())
    }
}

impl Value<String> for Color {
    fn value(&self) -> String {
        self.0.clone()
    }
}

impl From<String> for Color {
    fn from(value: String) -> Self {
        Self(value)
    }
}

impl From<&str> for Color {
    fn from(value: &str) -> Self {
        Self(value.to_string())
    }
}
