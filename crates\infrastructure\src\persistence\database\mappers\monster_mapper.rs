use reforged_domain::models::monster::{
    entity::Monster,
    value_object::{
        <PERSON><PERSON><PERSON>, MonsterBossMinRes, MonsterCoins, MonsterDPS, MonsterElement,
        MonsterExperience, MonsterFile, MonsterGold, MonsterHealth, MonsterHide, MonsterId,
        MonsterLevel, MonsterLinkage, MonsterMana, MonsterName, MonsterRace, MonsterReputation,
        MonsterRespawnTime, MonsterSpeed, MonsterTeamId, MonsterWorldBoss,
    },
};

#[derive(Debug)]
pub struct MonsterDbModelMapper(super::super::models::monsters::Model);

impl MonsterDbModelMapper {
    pub fn new(monster: super::super::models::monsters::Model) -> Self {
        Self(monster)
    }
}

impl From<MonsterDbModelMapper> for Monster {
    fn from(value: MonsterDbModelMapper) -> Self {
        let monster_model = value.0;

        let id = MonsterId::new(monster_model.id);

        Monster::builder()
            .id(id)
            .name(MonsterName::new(monster_model.name))
            .race(MonsterRace::new(monster_model.race))
            .file(MonsterFile::new(monster_model.file))
            .health(MonsterHealth::new(monster_model.health))
            .mana(MonsterMana::new(monster_model.mana))
            .level(MonsterLevel::new(monster_model.level))
            .gold(MonsterGold::new(monster_model.gold))
            .coins(MonsterCoins::new(monster_model.coins))
            .experience(MonsterExperience::new(monster_model.experience))
            .reputation(MonsterReputation::new(monster_model.reputation))
            .dps(MonsterDPS::new(monster_model.dps))
            .speed(MonsterSpeed::new(monster_model.speed))
            .element(MonsterElement::new(monster_model.element))
            .linkage(MonsterLinkage::new(monster_model.linkage))
            .team_id(MonsterTeamId::new(monster_model.team_id))
            .boss(MonsterBoss::new(monster_model.boss))
            .boss_min_res(MonsterBossMinRes::new(monster_model.boss_min_res))
            .respawn_time(MonsterRespawnTime::new(monster_model.respawn_time))
            .hide(MonsterHide::new(monster_model.hide))
            .world_boss(MonsterWorldBoss::new(monster_model.world_boss))
            .build()
    }
}
