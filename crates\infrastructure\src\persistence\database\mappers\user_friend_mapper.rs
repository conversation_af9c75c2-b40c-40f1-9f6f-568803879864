use reforged_domain::models::user_friend::entity::UserFriend;
use reforged_domain::models::user_friend::value_object::UserFriendId;
use reforged_domain::models::user::value_object::UserId;

#[derive(Debug)]
pub struct UserFriendDbModelMapper(super::super::models::user_friends::Model);

impl UserFriendDbModelMapper {
    pub fn new(model: super::super::models::user_friends::Model) -> Self {
        Self(model)
    }
}

impl From<UserFriendDbModelMapper> for UserFriend {
    fn from(value: UserFriendDbModelMapper) -> Self {
        let model = value.0;

        UserFriend::builder()
            .id(UserFriendId::new(model.id))
            .user_id(UserId::new(model.user_id))
            .friend_id(UserId::new(model.friend_id))
            .build()
    }
}
