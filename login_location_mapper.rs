use reforged_domain::models::login_location::entity::LoginLocation;

#[derive(Debug)]
pub struct LoginLocationDbModelMapper(super::super::models::sea_orm_active_enums::LoginLocation);

impl LoginLocationDbModelMapper {
    pub fn new(location: super::super::models::sea_orm_active_enums::LoginLocation) -> Self {
        Self(location)
    }
}

impl From<LoginLocationDbModelMapper> for LoginLocation {
    fn from(value: LoginLocationDbModelMapper) -> Self {
        match value.0 {
            super::super::models::sea_orm_active_enums::LoginLocation::Loader => LoginLocation::Loader,
            super::super::models::sea_orm_active_enums::LoginLocation::Game => LoginLocation::Game,
            super::super::models::sea_orm_active_enums::LoginLocation::Wiki => LoginLocation::Wiki,
        }
    }
}
