use std::sync::Arc;

use chrono::{DateTime, Duration, Utc};
use serde::Serialize;

use crate::{
    error::ApplicationError,
    traits::{PasetoClaimPurpose, PasetoClaims, TokenService},
};

#[derive(Serialize)]
pub struct RefreshTokenResponse {
    access_token: String,
    access_token_expires_at: DateTime<Utc>,
}

pub struct RefreshTokenUsecase {
    token_service: Arc<dyn TokenService>,
}

impl RefreshTokenUsecase {
    pub fn new(token_service: Arc<dyn TokenService>) -> Self {
        Self { token_service }
    }
}

impl RefreshTokenUsecase {
    pub async fn execute(
        &self,
        refresh_token: String,
    ) -> Result<RefreshTokenResponse, ApplicationError> {
        let token_service = self.token_service.clone();
        let claims =
            token_service.validate_token(refresh_token, PasetoClaimPurpose::RefreshToken)?;

        let claims = PasetoClaims::new(
            claims.id,
            claims.email,
            claims.role,
            claims.exp,
            PasetoClaimPurpose::AccessToken,
        );

        // TODO: add the refresh token in the invalid token cache

        let (new_access_token, new_access_token_expiry) =
            token_service.generate_token(claims, Duration::seconds(30))?;

        let response = RefreshTokenResponse {
            access_token: new_access_token,
            access_token_expires_at: new_access_token_expiry,
        };

        Ok(response)
    }
}
