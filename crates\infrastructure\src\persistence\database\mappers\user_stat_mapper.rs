use reforged_domain::models::user_stat::entity::UserStat;
use reforged_domain::models::user_stat::value_object::*;
use reforged_domain::models::user::value_object::UserId;

#[derive(Debug)]
pub struct UserStatDbModelMapper(super::super::models::user_stats::Model);

impl UserStatDbModelMapper {
    pub fn new(model: super::super::models::user_stats::Model) -> Self {
        Self(model)
    }
}

impl From<UserStatDbModelMapper> for UserStat {
    fn from(value: UserStatDbModelMapper) -> Self {
        let model = value.0;

        UserStat::builder()
            .id(UserStatId::new(model.id))
            .user_id(UserId::new(model.user_id))
            .last_area(UserStatLastArea::new(model.last_area))
            .current_server(UserStatCurrentServer::new(model.current_server))
            .house_info(UserStatHouseInfo::new(model.house_info))
            .kill_count(UserStatKillCount::new(model.kill_count.try_into().unwrap_or(0)))
            .death_count(UserStatDeathCount::new(model.death_count.try_into().unwrap_or(0)))
            .pvp_ratio(UserStatPvpRatio::new(model.pvp_ratio.map(|r| r.try_into().unwrap_or(0))))
            .build()
    }
}
