use reforged_domain::models::user_stat::entity::UserStat;
use reforged_domain::models::user_stat::value_object::UserStatId;
use reforged_domain::models::user::value_object::UserId;

#[derive(Debug)]
pub struct UserStatDbModelMapper(super::super::models::user_stats::Model);

impl UserStatDbModelMapper {
    pub fn new(model: super::super::models::user_stats::Model) -> Self {
        Self(model)
    }
}

impl From<UserStatDbModelMapper> for UserStat {
    fn from(value: UserStatDbModelMapper) -> Self {
        let model = value.0;
        
        UserStat::builder()
            .id(UserStatId::new(model.id))
            .user_id(UserId::new(model.user_id))
            .strength(model.strength.into())
            .intellect(model.intellect.into())
            .endurance(model.endurance.into())
            .dexterity(model.dexterity.into())
            .luck(model.luck.into())
            .build()
    }
}
