use crate::models::map::value_object::MapId;

use super::value_object::{MapCellFrame, MapCellId, MapCellPad};
use getset::{<PERSON><PERSON>, Set<PERSON>};

#[derive(<PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, bon::<PERSON><PERSON><PERSON>, <PERSON><PERSON>, Set<PERSON>)]
#[getset(get = "pub", set = "pub")]
pub struct MapCell {
    id: MapCellId,
    map_id: MapId,
    frame: MapCellFrame,
    pad: MapCellPad,
}

impl MapCell {
    pub fn new(id: MapCellId, map_id: MapId, frame: MapCellFrame, pad: MapCellPad) -> Self {
        Self {
            id,
            map_id,
            frame,
            pad,
        }
    }
}

#[cfg(test)]
mod tests {
    use reforged_shared::{IdTrait, Value};

    use super::*;

    #[test]
    fn test_map_cell_new() {
        let id = MapCellId::new(uuid::Uuid::now_v7());
        let map_id = MapId::new(uuid::Uuid::now_v7());
        let frame = MapCellFrame::new("frame1".to_string());
        let pad = MapCellPad::new("pad1".to_string());

        let map_cell = MapCell::new(id.clone(), map_id.clone(), frame.clone(), pad.clone());

        assert_eq!(map_cell.id().get_id(), id.get_id());
        assert_eq!(map_cell.map_id().get_id(), map_id.get_id());
        assert_eq!(map_cell.frame().value(), frame.value());
        assert_eq!(map_cell.pad().value(), pad.value());
    }

    #[test]
    fn test_map_cell_builder() {
        let id = MapCellId::new(uuid::Uuid::now_v7());
        let map_id = MapId::new(uuid::Uuid::now_v7());
        let frame = MapCellFrame::new("frame1".to_string());
        let pad = MapCellPad::new("pad1".to_string());

        let map_cell = MapCell::builder()
            .id(id.clone())
            .map_id(map_id.clone())
            .frame(frame.clone())
            .pad(pad.clone())
            .build();

        assert_eq!(map_cell.id().get_id(), id.get_id());
        assert_eq!(map_cell.map_id().get_id(), map_id.get_id());
        assert_eq!(map_cell.frame().value(), frame.value());
        assert_eq!(map_cell.pad().value(), pad.value());
    }

    #[test]
    fn test_getters_and_setters() {
        let mut map_cell = MapCell::default();

        let id = MapCellId::new(uuid::Uuid::now_v7());
        let map_id = MapId::new(uuid::Uuid::now_v7());
        let frame = MapCellFrame::new("frame1".to_string());
        let pad = MapCellPad::new("pad1".to_string());

        map_cell.set_id(id.clone());
        map_cell.set_map_id(map_id.clone());
        map_cell.set_frame(frame.clone());
        map_cell.set_pad(pad.clone());

        assert_eq!(map_cell.id().get_id(), id.get_id());
        assert_eq!(map_cell.map_id().get_id(), map_id.get_id());
        assert_eq!(map_cell.frame().value(), frame.value());
        assert_eq!(map_cell.pad().value(), pad.value());
    }
}
