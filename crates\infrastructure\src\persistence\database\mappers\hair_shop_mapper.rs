use reforged_domain::models::hair_shop::{entity::HairShop, value_object::HairShopId};

#[derive(Debug)]
pub struct HairShopDbModelMapper(super::super::models::hair_shops::Model);

impl HairShopDbModelMapper {
    pub fn new(hair_shop: super::super::models::hair_shops::Model) -> Self {
        Self(hair_shop)
    }
}

impl From<HairShopDbModelMapper> for HairShop {
    fn from(value: HairShopDbModelMapper) -> Self {
        let hair_shop_model = value.0;

        let id = HairShopId::new(hair_shop_model.id);

        HairShop::builder()
            .id(id)
            .name(hair_shop_model.name.into())
            .build()
    }
}
