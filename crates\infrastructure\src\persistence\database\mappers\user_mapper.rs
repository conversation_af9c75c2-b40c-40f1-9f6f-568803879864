use reforged_domain::models::user::entity::User;
use reforged_domain::models::user::value_object::HashedPassword;
use reforged_domain::models::user::value_object::UserId;
use reforged_domain::models::user::value_object::UserRole;

#[derive(Debug)]
pub struct UserDbModelMapper(super::super::models::users::Model);

impl UserDbModelMapper {
    pub fn new(user: super::super::models::users::Model) -> Self {
        Self(user)
    }
}

impl From<UserDbModelMapper> for User {
    fn from(value: UserDbModelMapper) -> Self {
        let user_model = value.0;

        let user_id = UserId::new(user_model.id);
        let role = UserRole::from(user_model.role_id);

        User::builder()
            .id(user_id)
            .username(user_model.username.into())
            .hashed_password(HashedPassword::new(user_model.hash, user_model.salt))
            .email(user_model.email.into())
            .role(role)
            .build()
    }
}
