use chrono::{DateTime, Utc};
use reforged_shared::{UuidId, Value};

use super::entity::DeletedUserItem;

pub type DeletedUserItemId = UuidId<DeletedUserItem>;

#[derive(Debug, PartialEq, Eq, PartialOrd, Ord, Clone, De<PERSON>ult)]

pub struct Quantity(i32);

impl Quantity {
    pub fn new(value: i32) -> Self {
        Self(value)
    }
}

impl Value<i32> for Quantity {
    fn value(&self) -> i32 {
        self.0
    }
}

impl From<i32> for Quantity {
    fn from(value: i32) -> Self {
        Self(value)
    }
}

#[derive(Debug, PartialEq, Eq, PartialOrd, Ord, Clone, Default)]

pub struct DeletedDate(DateTime<Utc>);

impl DeletedDate {
    pub fn new(date: DateTime<Utc>) -> Self {
        Self(date)
    }
}

impl Value<DateTime<Utc>> for DeletedDate {
    fn value(&self) -> DateTime<Utc> {
        self.0
    }
}

impl From<DateTime<Utc>> for DeletedDate {
    fn from(value: DateTime<Utc>) -> Self {
        Self(value)
    }
}
