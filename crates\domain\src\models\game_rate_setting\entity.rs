use super::value_object::{GameRateSettingId, GameRateSettingName, Value};
use getset::{<PERSON><PERSON>, <PERSON><PERSON>};

#[derive(<PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, bon::<PERSON><PERSON><PERSON>, <PERSON><PERSON>, Set<PERSON>)]
#[getset(get = "pub", set = "pub")]
pub struct GameRateSetting {
    id: GameRateSettingId,
    name: GameRateSettingName,
    value: Value,
}

impl GameRateSetting {
    pub fn new(id: GameRateSettingId, name: GameRateSettingName, value: Value) -> Self {
        Self { id, name, value }
    }
}

#[cfg(test)]
mod tests {
    use reforged_shared::{IdTrait, Value as SharedValue};

    use super::*;

    #[test]
    fn test_game_rate_setting_new() {
        let id = GameRateSettingId::new(uuid::Uuid::now_v7());
        let name = GameRateSettingName::new("XP_MULTIPLIER");
        let value = Value::new(2.5);

        let game_rate_setting = GameRateSetting::new(id.clone(), name.clone(), value.clone());

        assert_eq!(game_rate_setting.id().get_id(), id.get_id());
        assert_eq!(game_rate_setting.name().value(), name.value());
        assert_eq!(game_rate_setting.value().value(), value.value());
    }

    #[test]
    fn test_game_rate_setting_builder() {
        let id = GameRateSettingId::new(uuid::Uuid::now_v7());
        let name = GameRateSettingName::new("XP_MULTIPLIER");
        let value = Value::new(2.5);

        let game_rate_setting = GameRateSetting::builder()
            .id(id.clone())
            .name(name.clone())
            .value(value.clone())
            .build();

        assert_eq!(game_rate_setting.id().get_id(), id.get_id());
        assert_eq!(game_rate_setting.name().value(), name.value());
        assert_eq!(game_rate_setting.value().value(), value.value());
    }

    #[test]
    fn test_getters_and_setters() {
        let mut game_rate_setting = GameRateSetting::default();

        let name = GameRateSettingName::new("XP_MULTIPLIER");
        let value = Value::new(2.5);

        game_rate_setting.set_name(name.clone());
        game_rate_setting.set_value(value.clone());

        assert_eq!(game_rate_setting.name().value(), name.value());
        assert_eq!(game_rate_setting.value().value(), value.value());
    }
}
