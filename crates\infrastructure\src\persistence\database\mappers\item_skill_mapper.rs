use reforged_domain::models::item_skill::entity::ItemSkill;
use reforged_domain::models::item_skill::value_object::ItemSkillId;
use reforged_domain::models::item::value_object::ItemId;
use reforged_domain::models::skill::value_object::SkillId;

#[derive(Debug)]
pub struct ItemSkillDbModelMapper(super::super::models::item_skills::Model);

impl ItemSkillDbModelMapper {
    pub fn new(model: super::super::models::item_skills::Model) -> Self {
        Self(model)
    }
}

impl From<ItemSkillDbModelMapper> for ItemSkill {
    fn from(value: ItemSkillDbModelMapper) -> Self {
        let model = value.0;
        
        ItemSkill::builder()
            .id(ItemSkillId::new(model.id))
            .item_id(ItemId::new(model.item_id))
            .skill_id(SkillId::new(model.skill_id))
            .build()
    }
}
