use reforged_domain::models::server::{
    entity::Server,
    value_object::{
        ServerChat, ServerCount, ServerId, ServerIp, ServerMaintenance, ServerMax, ServerMotd,
        ServerName, ServerOnline, ServerPort, ServerUpgrade,
    },
};

#[derive(Debug)]
pub struct ServerDbModelMapper(super::super::models::servers::Model);

impl ServerDbModelMapper {
    pub fn new(server: super::super::models::servers::Model) -> Self {
        Self(server)
    }
}

impl From<ServerDbModelMapper> for Server {
    fn from(value: ServerDbModelMapper) -> Self {
        let server_model = value.0;

        let id = ServerId::new(server_model.id);

        Server::builder()
            .id(id)
            .name(ServerName::new(server_model.name))
            .ip(ServerIp::new(server_model.ip))
            .port(ServerPort::new(server_model.port))
            .online(ServerOnline::new(server_model.online))
            .upgrade(ServerUpgrade::new(server_model.upgrade))
            .chat(ServerChat::new(server_model.chat))
            .count(ServerCount::new(server_model.count))
            .max(ServerMax::new(server_model.max))
            .motd(ServerMotd::new(server_model.motd))
            .maintenance(ServerMaintenance::new(server_model.maintenance))
            .build()
    }
}
