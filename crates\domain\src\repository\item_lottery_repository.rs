use async_trait::async_trait;
use mockall::automock;

use crate::error::RepositoryError;
use crate::models::item_lottery::entity::ItemLottery;
use crate::models::item_lottery::value_object::ItemLotteryId;

#[automock]
#[async_trait]
pub trait ItemLotteryRepository: Send + Sync {
    async fn save(&self, entity: &ItemLottery) -> Result<(), RepositoryError>;
    async fn update(&self, entity: &ItemLottery) -> Result<(), RepositoryError>;
    async fn delete(&self, id: &ItemLotteryId) -> Result<(), RepositoryError>;
}

#[automock]
#[async_trait]
pub trait ItemLotteryReadRepository: Send + Sync {
    async fn find_by_id(&self, id: &ItemLotteryId) -> Result<Option<ItemLottery>, RepositoryError>;
    async fn find_all(&self) -> Result<Vec<ItemLottery>, RepositoryError>;
}
