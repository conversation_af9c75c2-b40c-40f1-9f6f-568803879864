use reforged_shared::{UuidId, Value};

use crate::models::monster_drop::entity::MonsterDrop;

pub type MonsterDropId = UuidId<MonsterDrop>;

#[derive(Debug, PartialEq, PartialOrd, <PERSON>lone, <PERSON><PERSON>ult)]

pub struct MonsterDropChance(f32);

impl MonsterDropChance {
    pub fn new(value: f32) -> Self {
        Self(value)
    }
}

impl Value<f32> for MonsterDropChance {
    fn value(&self) -> f32 {
        self.0
    }
}

impl From<f32> for MonsterDropChance {
    fn from(value: f32) -> Self {
        Self(value)
    }
}

#[derive(Debug, PartialEq, PartialOrd, <PERSON><PERSON>, <PERSON><PERSON>ult)]

pub struct MonsterDropQuantity(u32);

impl MonsterDropQuantity {
    pub fn new(value: u32) -> Self {
        Self(value)
    }
}

impl Value<u32> for MonsterDropQuantity {
    fn value(&self) -> u32 {
        self.0
    }
}

impl From<u32> for MonsterDropQuantity {
    fn from(value: u32) -> Self {
        Self(value)
    }
}
