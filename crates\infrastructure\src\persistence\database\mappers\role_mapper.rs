use reforged_domain::models::role::{
    entity::Role,
    value_object::{RoleId, RoleTag, RoleUserColor, UserRoleTag},
};

use super::role_enum_mapper::UserRoleMapper;

#[derive(Debug)]
pub struct RoleDbModelMapper(super::super::models::roles::Model);

impl RoleDbModelMapper {
    pub fn new(role: super::super::models::roles::Model) -> Self {
        Self(role)
    }
}

impl From<RoleDbModelMapper> for Role {
    fn from(value: RoleDbModelMapper) -> Self {
        let role_model = value.0;
        let user_role = UserRoleMapper::new(role_model.tag);
        let user_role_tag: UserRoleTag = user_role.into();
        let role = RoleTag::from(user_role_tag);

        let id = RoleId::new(role_model.id);

        Role::builder()
            .id(id)
            .tag(role)
            .user_color(RoleUserColor::new(role_model.user_color))
            .build()
    }
}
