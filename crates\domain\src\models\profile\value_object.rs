use std::fmt::Display;

use reforged_shared::{UuidId, Value};

use super::entity::Profile;

pub type ProfileId = UuidId<Profile>;

#[derive(Debug, PartialEq, Eq, PartialOrd, Ord, <PERSON><PERSON>, <PERSON><PERSON>ult)]
pub enum Gender {
    #[default]
    Male,
    Female,
}

impl Display for Gender {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        match self {
            Gender::Male => write!(f, "Male"),
            Gender::Female => write!(f, "Female"),
        }
    }
}

impl TryFrom<String> for Gender {
    type Error = ();

    fn try_from(value: String) -> Result<Self, Self::Error> {
        match value.to_lowercase().as_str() {
            "male" | "m" => Ok(Gender::Male),
            "female" | "f" => Ok(Gender::Female),
            _ => Err(()),
        }
    }
}

#[derive(Debug, <PERSON>ialEq, Eq, PartialOrd, Or<PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>)]
pub struct Age(u16);
#[derive(Debug, PartialEq, Eq, PartialOrd, Ord, <PERSON>lone, De<PERSON>ult)]
pub struct Country(String);
#[derive(Debug, PartialEq, Eq, PartialOrd, Ord, Clone, Default)]
pub struct Avatar(String);

impl Age {
    pub fn new(value: u16) -> Self {
        Self(value)
    }
}

impl Value<u16> for Age {
    fn value(&self) -> u16 {
        self.0
    }
}

impl From<u16> for Age {
    fn from(value: u16) -> Self {
        Self(value)
    }
}

impl From<i16> for Age {
    fn from(value: i16) -> Self {
        Self(value as u16)
    }
}

impl Country {
    pub fn new(name: impl Into<String>) -> Self {
        Self(name.into())
    }
}

impl From<String> for Country {
    fn from(value: String) -> Self {
        Self(value)
    }
}

impl<'a> From<&str> for Country {
    fn from(value: &str) -> Self {
        Self(value.to_string())
    }
}

impl Value<String> for Country {
    fn value(&self) -> String {
        self.0.clone()
    }
}

impl Avatar {
    pub fn new(name: impl Into<String>) -> Self {
        Self(name.into())
    }
}

impl Value<String> for Avatar {
    fn value(&self) -> String {
        self.0.clone()
    }
}

impl From<String> for Avatar {
    fn from(value: String) -> Self {
        Self(value)
    }
}

impl<'a> From<&str> for Avatar {
    fn from(value: &str) -> Self {
        Self(value.to_string())
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_gender_try_from() {
        let male = Gender::try_from("male".to_string()).unwrap();
        assert_eq!(male, Gender::Male);

        let female = Gender::try_from("female".to_string()).unwrap();
        assert_eq!(female, Gender::Female);

        let invalid = Gender::try_from("invalid".to_string());
        assert!(invalid.is_err());
    }

    #[test]
    fn test_gender_display() {
        let male = Gender::Male;
        let female = Gender::Female;
        let invalid = Gender::try_from("invalid".to_string());

        assert_eq!(male.to_string(), "Male");
        assert_eq!(female.to_string(), "Female");
        assert!(invalid.is_err());
    }
}
