use chrono::{DateTime, Utc};
use reforged_domain::models::{
    cms_article::{
        entity::CMSArticle,
        value_object::{CMSArticleCreatedAt, CMSArticleId},
    },
    user::value_object::UserId,
};

#[derive(Debug)]
pub struct CMSArticleDbModelMapper(super::super::models::cms_articles::Model);

impl CMSArticleDbModelMapper {
    pub fn new(cms_article: super::super::models::cms_articles::Model) -> Self {
        Self(cms_article)
    }
}

impl From<CMSArticleDbModelMapper> for CMSArticle {
    fn from(value: CMSArticleDbModelMapper) -> Self {
        let cms_article_model = value.0;

        let id = CMSArticleId::new(cms_article_model.id);

        CMSArticle::builder()
            .id(id)
            .title(cms_article_model.title.into())
            .content(cms_article_model.content.into())
            .author_id(UserId::new(cms_article_model.author_id))
            .created_at(CMSArticleCreatedAt::new(
                DateTime::<Utc>::from_naive_utc_and_offset(
                    cms_article_model.created_at.and_hms_opt(0, 0, 0).unwrap(),
                    Utc,
                ),
            ))
            .image(cms_article_model.image.into())
            .build()
    }
}
