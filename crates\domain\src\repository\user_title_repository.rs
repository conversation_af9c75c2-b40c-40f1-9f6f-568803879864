use async_trait::async_trait;
use mockall::automock;

use crate::error::RepositoryError;
use crate::models::user_title::entity::UserTitle;
use crate::models::user_title::value_object::UserTitleId;

#[automock]
#[async_trait]
pub trait UserTitleRepository: Send + Sync {
    async fn save(&self, entity: &UserTitle) -> Result<(), RepositoryError>;
    async fn update(&self, entity: &UserTitle) -> Result<(), RepositoryError>;
    async fn delete(&self, id: &UserTitleId) -> Result<(), RepositoryError>;
}

#[automock]
#[async_trait]
pub trait UserTitleReadRepository: Send + Sync {
    async fn find_by_id(&self, id: &UserTitleId) -> Result<Option<UserTitle>, RepositoryError>;
    async fn find_all(&self) -> Result<Vec<UserTitle>, RepositoryError>;
}
