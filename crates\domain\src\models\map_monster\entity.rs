use crate::models::{map::value_object::MapId, monster::value_object::MonsterId};

use super::value_object::{MapMonsterFrame, MapMonsterId};
use getset::{<PERSON><PERSON>, Set<PERSON>};

#[derive(<PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, bon::<PERSON><PERSON><PERSON>, <PERSON><PERSON>, Setters)]
#[getset(get = "pub", set = "pub")]
pub struct MapMonster {
    id: MapMonsterId,
    map_id: MapId,
    monster_id: MonsterId,
    frame: MapMonsterFrame,
}

impl MapMonster {
    pub fn new(
        id: MapMonsterId,
        map_id: MapId,
        monster_id: MonsterId,
        frame: MapMonsterFrame,
    ) -> Self {
        Self {
            id,
            map_id,
            monster_id,
            frame,
        }
    }
}

#[cfg(test)]
mod tests {
    use reforged_shared::IdTrait;

    use super::*;

    #[test]
    fn test_map_monster_new() {
        let id = MapMonsterId::new(uuid::Uuid::now_v7());
        let map_id = MapId::new(uuid::Uuid::now_v7());
        let monster_id = MonsterId::new(uuid::Uuid::now_v7());
        let frame = MapMonsterFrame::new("monster_frame".to_string());

        let map_monster = MapMonster::new(
            id.clone(),
            map_id.clone(),
            monster_id.clone(),
            frame.clone(),
        );

        assert_eq!(map_monster.id().get_id(), id.get_id());
        assert_eq!(map_monster.map_id().get_id(), map_id.get_id());
        assert_eq!(map_monster.monster_id().get_id(), monster_id.get_id());
        assert_eq!(map_monster.frame().value(), frame.value());
    }

    #[test]
    fn test_map_monster_builder() {
        let id = MapMonsterId::new(uuid::Uuid::now_v7());
        let map_id = MapId::new(uuid::Uuid::now_v7());
        let monster_id = MonsterId::new(uuid::Uuid::now_v7());
        let frame = MapMonsterFrame::new("monster_frame".to_string());

        let map_monster = MapMonster::builder()
            .id(id.clone())
            .map_id(map_id.clone())
            .monster_id(monster_id.clone())
            .frame(frame.clone())
            .build();

        assert_eq!(map_monster.id().get_id(), id.get_id());
        assert_eq!(map_monster.map_id().get_id(), map_id.get_id());
        assert_eq!(map_monster.monster_id().get_id(), monster_id.get_id());
        assert_eq!(map_monster.frame().value(), frame.value());
    }

    #[test]
    fn test_getters_and_setters() {
        let mut map_monster = MapMonster::default();

        let id = MapMonsterId::new(uuid::Uuid::now_v7());
        let map_id = MapId::new(uuid::Uuid::now_v7());
        let monster_id = MonsterId::new(uuid::Uuid::now_v7());
        let frame = MapMonsterFrame::new("monster_frame".to_string());

        map_monster.set_id(id.clone());
        map_monster.set_map_id(map_id.clone());
        map_monster.set_monster_id(monster_id.clone());
        map_monster.set_frame(frame.clone());

        assert_eq!(map_monster.id().get_id(), id.get_id());
        assert_eq!(map_monster.map_id().get_id(), map_id.get_id());
        assert_eq!(map_monster.monster_id().get_id(), monster_id.get_id());
        assert_eq!(map_monster.frame().value(), frame.value());
    }
}
