use reforged_domain::models::user_title::entity::UserTitle;
use reforged_domain::models::user_title::value_object::UserTitleId;
use reforged_domain::models::user::value_object::UserId;
use reforged_domain::models::title::value_object::TitleId;

#[derive(Debug)]
pub struct UserTitleDbModelMapper(super::super::models::user_titles::Model);

impl UserTitleDbModelMapper {
    pub fn new(model: super::super::models::user_titles::Model) -> Self {
        Self(model)
    }
}

impl From<UserTitleDbModelMapper> for UserTitle {
    fn from(value: UserTitleDbModelMapper) -> Self {
        let model = value.0;
        
        UserTitle::builder()
            .id(UserTitleId::new(model.id))
            .user_id(UserId::new(model.user_id))
            .title_id(TitleId::new(model.title_id))
            .date(model.date.and_utc().into())
            .build()
    }
}
