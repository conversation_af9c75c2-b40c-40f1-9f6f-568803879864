use crate::models::user::value_object::UserId;

use super::value_object::*;
use getset::{<PERSON><PERSON>, Set<PERSON>};

#[derive(<PERSON>bu<PERSON>, <PERSON><PERSON>, Default, bon::<PERSON><PERSON><PERSON>, <PERSON><PERSON>, Setters)]
#[getset(get = "pub", set = "pub")]
pub struct Article {
    post_id: ArticlePostId,
    author: UserId,
    subject: ArticleSubject,
    content: ArticleContent,
    image: ArticleImage,
    tags: ArticleTags,
    date: ArticleDate,
}

impl Article {
    pub fn new(
        post_id: ArticlePostId,
        author: UserId,
        subject: ArticleSubject,
        content: ArticleContent,
        image: ArticleImage,
        tags: ArticleTags,
        date: ArticleDate,
    ) -> Self {
        Self {
            post_id,
            author,
            subject,
            content,
            image,
            tags,
            date,
        }
    }
}

#[cfg(test)]
mod tests {
    use chrono::Utc;
    use reforged_shared::{IdTrait, Value};

    use super::*;

    #[test]
    fn test_article_new() {
        let post_id = ArticlePostId::new(uuid::Uuid::now_v7());
        let author = UserId::new(uuid::Uuid::now_v7());
        let subject = ArticleSubject::new("Test Subject".to_string());
        let content = ArticleContent::new("Test Content".to_string());
        let image = ArticleImage::new("test.png".to_string());
        let tags = ArticleTags::new("test,tags".to_string());
        let date = ArticleDate::new(Utc::now());

        let article = Article::new(
            post_id.clone(),
            author.clone(),
            subject.clone(),
            content.clone(),
            image.clone(),
            tags.clone(),
            date.clone(),
        );

        assert_eq!(article.post_id().get_id(), post_id.get_id());
        assert_eq!(article.author().get_id(), author.get_id());
        assert_eq!(article.subject().value(), subject.value());
        assert_eq!(article.content().value(), content.value());
        assert_eq!(article.image().value(), image.value());
        assert_eq!(article.tags().value(), tags.value());
        assert_eq!(article.date().value(), date.value());
    }

    #[test]
    fn test_article_builder() {
        let post_id = ArticlePostId::new(uuid::Uuid::now_v7());
        let author = UserId::new(uuid::Uuid::now_v7());
        let subject = ArticleSubject::new("Test Subject".to_string());
        let content = ArticleContent::new("Test Content".to_string());
        let image = ArticleImage::new("test.png".to_string());
        let tags = ArticleTags::new("test,tags".to_string());
        let date = ArticleDate::new(Utc::now());

        let article = Article::builder()
            .post_id(post_id.clone())
            .author(author.clone())
            .subject(subject.clone())
            .content(content.clone())
            .image(image.clone())
            .tags(tags.clone())
            .date(date.clone())
            .build();

        assert_eq!(article.post_id().get_id(), post_id.get_id());
        assert_eq!(article.author().get_id(), author.get_id());
        assert_eq!(article.subject().value(), subject.value());
        assert_eq!(article.content().value(), content.value());
        assert_eq!(article.image().value(), image.value());
        assert_eq!(article.tags().value(), tags.value());
        assert_eq!(article.date().value(), date.value());
    }

    #[test]
    fn test_getters_and_setters() {
        let mut article = Article::default();

        let post_id = ArticlePostId::new(uuid::Uuid::now_v7());
        let author = UserId::new(uuid::Uuid::now_v7());
        let subject = ArticleSubject::new("Test Subject".to_string());
        let content = ArticleContent::new("Test Content".to_string());
        let image = ArticleImage::new("test.png".to_string());
        let tags = ArticleTags::new("test,tags".to_string());
        let date = ArticleDate::new(Utc::now());

        article.set_post_id(post_id.clone());
        article.set_author(author.clone());
        article.set_subject(subject.clone());
        article.set_content(content.clone());
        article.set_image(image.clone());
        article.set_tags(tags.clone());
        article.set_date(date.clone());

        assert_eq!(article.post_id().get_id(), post_id.get_id());
        assert_eq!(article.author().get_id(), author.get_id());
        assert_eq!(article.subject().value(), subject.value());
        assert_eq!(article.content().value(), content.value());
        assert_eq!(article.image().value(), image.value());
        assert_eq!(article.tags().value(), tags.value());
        assert_eq!(article.date().value(), date.value());
    }
}
