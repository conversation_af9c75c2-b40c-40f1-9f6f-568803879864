use crate::models::guild::value_object::GuildId;

use super::value_object::*;
use getset::{<PERSON><PERSON>, <PERSON><PERSON>};
#[derive(<PERSON>bu<PERSON>, <PERSON><PERSON>, <PERSON><PERSON>ult, bon::<PERSON><PERSON><PERSON>, <PERSON><PERSON>, Setters)]
#[getset(get = "pub", set = "pub")]
pub struct GuildHall {
    id: GuildHallId,
    guild_id: GuildId,
    linkage: GuildHallLinkage,
    cell: GuildHallCell,
    x: GuildHallX,
    y: GuildHallY,
    interior: GuildHallInterior,
}

impl GuildHall {
    pub fn new(
        id: GuildHallId,
        guild_id: GuildId,
        linkage: GuildHallLinkage,
        cell: GuildHallCell,
        x: GuildHallX,
        y: GuildHallY,
        interior: GuildHallInterior,
    ) -> Self {
        Self {
            id,
            guild_id,
            linkage,
            cell,
            x,
            y,
            interior,
        }
    }
}

#[cfg(test)]
mod tests {
    use reforged_shared::{IdTrait, Value};

    use super::*;

    #[test]
    fn test_guild_hall_new() {
        let id = GuildHallId::new(uuid::Uuid::now_v7());
        let guild_id = GuildId::new(uuid::Uuid::now_v7());
        let linkage = GuildHallLinkage::new("guild_hall_map".to_string());
        let cell = GuildHallCell::new("hall_cell".to_string());
        let x = GuildHallX::new(10.5);
        let y = GuildHallY::new(20.3);
        let interior = GuildHallInterior::new("luxury".to_string());

        let guild_hall = GuildHall::new(
            id.clone(),
            guild_id.clone(),
            linkage.clone(),
            cell.clone(),
            x.clone(),
            y.clone(),
            interior.clone(),
        );

        assert_eq!(guild_hall.id().get_id(), id.get_id());
        assert_eq!(guild_hall.guild_id().get_id(), guild_id.get_id());
        assert_eq!(guild_hall.linkage().value(), linkage.value());
        assert_eq!(guild_hall.cell().value(), cell.value());
        assert_eq!(guild_hall.x().value(), x.value());
        assert_eq!(guild_hall.y().value(), y.value());
        assert_eq!(guild_hall.interior().value(), interior.value());
    }

    #[test]
    fn test_guild_hall_builder() {
        let id = GuildHallId::new(uuid::Uuid::now_v7());
        let guild_id = GuildId::new(uuid::Uuid::now_v7());
        let linkage = GuildHallLinkage::new("guild_hall_map".to_string());
        let cell = GuildHallCell::new("hall_cell".to_string());
        let x = GuildHallX::new(10.5);
        let y = GuildHallY::new(20.3);
        let interior = GuildHallInterior::new("luxury".to_string());

        let guild_hall = GuildHall::builder()
            .id(id.clone())
            .guild_id(guild_id.clone())
            .linkage(linkage.clone())
            .cell(cell.clone())
            .x(x.clone())
            .y(y.clone())
            .interior(interior.clone())
            .build();

        assert_eq!(guild_hall.id().get_id(), id.get_id());
        assert_eq!(guild_hall.guild_id().get_id(), guild_id.get_id());
        assert_eq!(guild_hall.linkage().value(), linkage.value());
        assert_eq!(guild_hall.cell().value(), cell.value());
        assert_eq!(guild_hall.x().value(), x.value());
        assert_eq!(guild_hall.y().value(), y.value());
        assert_eq!(guild_hall.interior().value(), interior.value());
    }

    #[test]
    fn test_getters_and_setters() {
        let mut guild_hall = GuildHall::default();

        let id = GuildHallId::new(uuid::Uuid::now_v7());
        let guild_id = GuildId::new(uuid::Uuid::now_v7());
        let linkage = GuildHallLinkage::new("guild_hall_map".to_string());
        let cell = GuildHallCell::new("hall_cell".to_string());
        let x = GuildHallX::new(10.5);
        let y = GuildHallY::new(20.3);
        let interior = GuildHallInterior::new("luxury".to_string());

        guild_hall.set_id(id.clone());
        guild_hall.set_guild_id(guild_id.clone());
        guild_hall.set_linkage(linkage.clone());
        guild_hall.set_cell(cell.clone());
        guild_hall.set_x(x.clone());
        guild_hall.set_y(y.clone());
        guild_hall.set_interior(interior.clone());

        assert_eq!(guild_hall.id().get_id(), id.get_id());
        assert_eq!(guild_hall.guild_id().get_id(), guild_id.get_id());
        assert_eq!(guild_hall.linkage().value(), linkage.value());
        assert_eq!(guild_hall.cell().value(), cell.value());
        assert_eq!(guild_hall.x().value(), x.value());
        assert_eq!(guild_hall.y().value(), y.value());
        assert_eq!(guild_hall.interior().value(), interior.value());
    }
}
