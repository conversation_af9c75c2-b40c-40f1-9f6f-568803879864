use crate::models::{item::value_object::File, profile::value_object::Gender};

use super::value_object::{HairId, HairName};
use getset::{<PERSON><PERSON>, <PERSON><PERSON>};

#[derive(<PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>ult, bon::<PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>)]
#[getset(get = "pub", set = "pub")]
pub struct Hair {
    id: HairId,
    gender: Gender,
    name: <PERSON><PERSON><PERSON>,
    file: File,
}

impl Hair {
    pub fn new(id: HairId, gender: Gender, name: <PERSON>N<PERSON>, file: File) -> Self {
        Self {
            id,
            gender,
            name,
            file,
        }
    }
}

#[cfg(test)]
mod tests {
    use reforged_shared::IdTrait;

    use super::*;

    #[test]
    fn test_hair_new() {
        let id = HairId::new(uuid::Uuid::now_v7());
        let gender = Gender::Male;
        let name = HairName::new("Cool Haircut".to_string());
        let file = File::new("hair/cool_haircut.png".to_string());

        let hair = Hair::new(id.clone(), gender.clone(), name.clone(), file.clone());

        assert_eq!(hair.id().get_id(), id.get_id());
        assert_eq!(hair.gender(), &gender);
        assert_eq!(hair.name(), &name);
        assert_eq!(hair.file(), &file);
    }

    #[test]
    fn test_hair_builder() {
        let id = HairId::new(uuid::Uuid::now_v7());
        let gender = Gender::Female;
        let name = HairName::new("Fancy Style".to_string());
        let file = File::new("hair/fancy_style.png".to_string());

        let hair = Hair::builder()
            .id(id.clone())
            .gender(gender.clone())
            .name(name.clone())
            .file(file.clone())
            .build();

        assert_eq!(hair.id().get_id(), id.get_id());
        assert_eq!(hair.gender(), &gender);
        assert_eq!(hair.name(), &name);
        assert_eq!(hair.file(), &file);
    }

    #[test]
    fn test_getters_and_setters() {
        let mut hair = Hair::default();

        let id = HairId::new(uuid::Uuid::now_v7());
        let gender = Gender::Male;
        let name = HairName::new("Default Hair".to_string());
        let file = File::new("hair/default.png".to_string());

        hair.set_id(id.clone());
        hair.set_gender(gender.clone());
        hair.set_name(name.clone());
        hair.set_file(file.clone());

        assert_eq!(hair.id().get_id(), id.get_id());
        assert_eq!(hair.gender(), &gender);
        assert_eq!(hair.name(), &name);
        assert_eq!(hair.file(), &file);
    }
}
