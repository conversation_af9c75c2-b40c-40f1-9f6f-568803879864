use reforged_domain::models::profanity_filter_setting::entity::ProfanityFilterSettings;
use reforged_domain::models::profanity_filter_setting::value_object::ProfanityFilterSettingsId;

#[derive(Debug)]
pub struct ProfanityFilterSettingDbModelMapper(super::super::models::profanity_filter_settings::Model);

impl ProfanityFilterSettingDbModelMapper {
    pub fn new(model: super::super::models::profanity_filter_settings::Model) -> Self {
        Self(model)
    }
}

impl From<ProfanityFilterSettingDbModelMapper> for ProfanityFilterSettings {
    fn from(value: ProfanityFilterSettingDbModelMapper) -> Self {
        let model = value.0;

        ProfanityFilterSettings::builder()
            .id(ProfanityFilterSettingsId::new(model.id))
            .swear(model.swear.into())
            .time_to_mute(model.time_to_mute.into())
            .build()
    }
}
