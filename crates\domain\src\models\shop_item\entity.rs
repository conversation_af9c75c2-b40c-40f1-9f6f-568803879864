use getset::{<PERSON><PERSON>, <PERSON><PERSON>};

use crate::models::{item::value_object::ItemId, shop::value_object::ShopId};

use super::value_object::*;

#[derive(<PERSON>bu<PERSON>, <PERSON><PERSON>, De<PERSON>ult, bon::<PERSON><PERSON><PERSON>, <PERSON><PERSON>, Setters)]
#[getset(get = "pub", set = "pub")]
pub struct ShopItem {
    id: ShopItemId,
    shop_id: ShopId,
    item_id: ItemId,
    quantity_remain: QuantityRemain,
}

impl ShopItem {
    pub fn new(
        id: ShopItemId,
        shop_id: ShopId,
        item_id: ItemId,
        quantity_remain: QuantityRemain,
    ) -> Self {
        Self {
            id,
            shop_id,
            item_id,
            quantity_remain,
        }
    }
}

#[cfg(test)]
mod tests {
    use reforged_shared::{IdTrait, Value};

    use super::*;

    #[test]
    fn test_shop_item_new() {
        let id = ShopItemId::new(uuid::Uuid::now_v7());
        let shop_id = ShopId::new(uuid::Uuid::now_v7());
        let item_id = ItemId::new(uuid::Uuid::now_v7());
        let quantity_remain = QuantityRemain::new(10);

        let shop_item = ShopItem::new(
            id.clone(),
            shop_id.clone(),
            item_id.clone(),
            quantity_remain.clone(),
        );

        assert_eq!(shop_item.id.get_id(), id.get_id());
        assert_eq!(shop_item.shop_id.get_id(), shop_id.get_id());
        assert_eq!(shop_item.item_id.get_id(), item_id.get_id());
        assert_eq!(shop_item.quantity_remain.value(), quantity_remain.value());
    }

    #[test]
    fn test_shop_item_builder() {
        let id = ShopItemId::new(uuid::Uuid::now_v7());
        let shop_id = ShopId::new(uuid::Uuid::now_v7());
        let item_id = ItemId::new(uuid::Uuid::now_v7());
        let quantity_remain = QuantityRemain::new(10);

        let shop_item = ShopItem::builder()
            .id(id.clone())
            .shop_id(shop_id.clone())
            .item_id(item_id.clone())
            .quantity_remain(quantity_remain.clone())
            .build();

        assert_eq!(shop_item.id.get_id(), id.get_id());
        assert_eq!(shop_item.shop_id.get_id(), shop_id.get_id());
        assert_eq!(shop_item.item_id.get_id(), item_id.get_id());
        assert_eq!(shop_item.quantity_remain.value(), quantity_remain.value());
    }

    #[test]
    fn test_getters_and_setters() {
        let mut shop_item = ShopItem::default();

        let id = ShopItemId::new(uuid::Uuid::now_v7());
        let shop_id = ShopId::new(uuid::Uuid::now_v7());
        let item_id = ItemId::new(uuid::Uuid::now_v7());
        let quantity_remain = QuantityRemain::new(10);

        shop_item.set_id(id.clone());
        shop_item.set_shop_id(shop_id.clone());
        shop_item.set_item_id(item_id.clone());
        shop_item.set_quantity_remain(quantity_remain.clone());

        assert_eq!(shop_item.id().get_id(), id.get_id());
        assert_eq!(shop_item.shop_id().get_id(), shop_id.get_id());
        assert_eq!(shop_item.item_id().get_id(), item_id.get_id());
        assert_eq!(shop_item.quantity_remain().value(), quantity_remain.value());
    }
}
