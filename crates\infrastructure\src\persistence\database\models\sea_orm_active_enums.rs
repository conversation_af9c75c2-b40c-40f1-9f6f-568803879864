//! `SeaORM` Entity, @generated by sea-orm-codegen 1.1.0

use sea_orm::entity::prelude::*;
use serde::Deserialize;

#[derive(Debug, Clone, PartialEq, Eq, EnumIter, DeriveActiveEnum, Deserialize)]
#[sea_orm(rs_type = "String", db_type = "Enum", enum_name = "gender")]
pub enum Gender {
    #[sea_orm(string_value = "M")]
    M,
    #[sea_orm(string_value = "F")]
    F,
}
#[derive(Debug, Clone, PartialEq, Eq, EnumIter, DeriveActiveEnum, Deserialize)]
#[sea_orm(rs_type = "String", db_type = "Enum", enum_name = "login_location")]
pub enum LoginLocation {
    #[sea_orm(string_value = "Loader")]
    Loader,
    #[sea_orm(string_value = "Game")]
    Game,
    #[sea_orm(string_value = "Wiki")]
    Wiki,
}
#[derive(Debug, <PERSON>lone, PartialEq, Eq, EnumIter, DeriveActiveEnum, Deserialize)]
#[sea_orm(rs_type = "String", db_type = "Enum", enum_name = "market_type")]
pub enum MarketType {
    #[sea_orm(string_value = "Auction")]
    Auction,
    #[sea_orm(string_value = "Vending")]
    Vending,
}
#[derive(Debug, Clone, PartialEq, Eq, EnumIter, DeriveActiveEnum, Deserialize)]
#[sea_orm(rs_type = "String", db_type = "Enum", enum_name = "store_type")]
pub enum StoreType {
    #[sea_orm(string_value = "Package")]
    Package,
    #[sea_orm(string_value = "VIP")]
    Vip,
    #[sea_orm(string_value = "FOUNDER")]
    Founder,
    #[sea_orm(string_value = "Coin")]
    Coin,
}
#[derive(Debug, Clone, PartialEq, Eq, EnumIter, DeriveActiveEnum, Deserialize)]
#[sea_orm(rs_type = "String", db_type = "Enum", enum_name = "user_role")]
pub enum UserRole {
    #[sea_orm(string_value = "Banned")]
    Banned,
    #[sea_orm(string_value = "Player")]
    Player,
    #[sea_orm(string_value = "Founder")]
    Founder,
    #[sea_orm(string_value = "Support")]
    Support,
    #[sea_orm(string_value = "VIP")]
    Vip,
    #[sea_orm(string_value = "Moderator")]
    Moderator,
    #[sea_orm(string_value = "Trainee")]
    Trainee,
    #[sea_orm(string_value = "Administrator")]
    Administrator,
}
