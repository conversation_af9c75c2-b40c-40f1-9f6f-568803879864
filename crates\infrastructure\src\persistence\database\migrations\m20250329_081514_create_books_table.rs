use crate::models::prelude::*;
use sea_orm::Schema;
use sea_orm_migration::prelude::*;

#[derive(DeriveMigrationName)]
pub struct Migration;

#[async_trait::async_trait]
impl MigrationTrait for Migration {
    async fn up(&self, manager: &SchemaManager) -> Result<(), DbErr> {
        let backend = manager.get_database_backend();
        let schema = Schema::new(backend);

        // MARK: books tables and junction tables
        manager
            .create_table(schema.create_table_from_entity(Books))
            .await?;
        manager
            .create_table(schema.create_table_from_entity(BookQuests))
            .await?;
        Ok(())
    }

    async fn down(&self, manager: &SchemaManager) -> Result<(), DbErr> {
        manager
            .drop_table(Table::drop().table(BookQuests).to_owned())
            .await?;

        manager
            .drop_table(Table::drop().table(Books).to_owned())
            .await
    }
}
