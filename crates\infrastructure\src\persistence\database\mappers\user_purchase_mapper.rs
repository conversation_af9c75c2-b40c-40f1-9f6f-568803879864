use reforged_domain::models::user_purchase::entity::UserPurchase;
use reforged_domain::models::user_purchase::value_object::UserPurchaseId;
use reforged_domain::models::user::value_object::UserId;
use reforged_domain::models::store::value_object::StoreId;
use chrono::{DateTime, Utc};

#[derive(Debug)]
pub struct UserPurchaseDbModelMapper(super::super::models::user_purchases::Model);

impl UserPurchaseDbModelMapper {
    pub fn new(model: super::super::models::user_purchases::Model) -> Self {
        Self(model)
    }
}

impl From<UserPurchaseDbModelMapper> for UserPurchase {
    fn from(value: UserPurchaseDbModelMapper) -> Self {
        let model = value.0;
        
        UserPurchase::builder()
            .id(UserPurchaseId::new(model.id))
            .user_id(UserId::new(model.user_id))
            .payment_id(model.payment_id.into())
            .transaction_id(model.transaction_id.into())
            .email(model.email.into())
            .hash(model.hash.into())
            .item(model.item.into())
            .item_id(StoreId::new(model.item_id))
            .price(model.price.into())
            .method(model.method.into())
            .currency(model.currency.into())
            .purchased(model.purchased.into())
            .date(DateTime::<Utc>::from_naive_utc_and_offset(model.date, Utc).into())
            .broadcast(model.broadcast.into())
            .build()
    }
}
