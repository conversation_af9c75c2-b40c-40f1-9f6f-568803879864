pub struct AuthorizeAdmin;

use actix_web::{
    dev::{Service, ServiceRequest, ServiceResponse, Transform, forward_ready},
    web::Data,
};
use futures_util::future::{LocalBoxFuture, Ready, ready};
use reforged_application::{
    error::ApplicationError,
    queries::user_queries::GetUserByIdQuery,
    traits::{PasetoClaimPurpose, QueryHandler},
};
use reforged_domain::models::user::value_object::UserRole;

use crate::{error::ApiError, state::ApiState};

impl<S, B> Transform<S, ServiceRequest> for AuthorizeAdmin
where
    S: Service<ServiceRequest, Response = ServiceResponse<B>, Error = actix_web::Error>,
    S::Future: 'static,
    B: 'static,
{
    type Response = ServiceResponse<B>;
    type Error = actix_web::Error;
    type Transform = AuthorizeAdminMiddleware<S>;
    type InitError = ();
    type Future = Ready<Result<Self::Transform, Self::InitError>>;

    fn new_transform(&self, service: S) -> Self::Future {
        ready(Ok(AuthorizeAdminMiddleware { service }))
    }
}

pub struct AuthorizeAdminMiddleware<S> {
    service: S,
}

impl<S, B> Service<ServiceRequest> for AuthorizeAdminMiddleware<S>
where
    S: Service<ServiceRequest, Response = ServiceResponse<B>, Error = actix_web::Error>,
    S::Future: 'static,
    B: 'static,
{
    type Response = ServiceResponse<B>;
    type Error = actix_web::Error;
    type Future = LocalBoxFuture<'static, Result<Self::Response, Self::Error>>;

    forward_ready!(service);

    fn call(&self, req: ServiceRequest) -> Self::Future {
        let header = req.headers();
        let token = header.get("Authorization");

        if token.is_none() {
            return Box::pin(async {
                Err(ApiError::Application(ApplicationError::TokenInvalid).into())
            });
        }

        let token = token.unwrap().to_str().unwrap_or_default();
        let token = token.split("Bearer ").collect::<Vec<&str>>();

        if token.get(1).is_none() {
            return Box::pin(async {
                Err(ApiError::Application(ApplicationError::TokenInvalid).into())
            });
        }

        let token = token.get(1).unwrap();
        let token = token.to_string();

        let fut = self.service.call(req);

        Box::pin(async move {
            let res = fut.await?;

            let state = res.request().app_data::<Data<ApiState>>();
            let state = state.unwrap();
            let token_service = state.token_service();
            let token_service = token_service.clone();
            let user_query_handler = state.user_query_handler();
            let user_query_handler = user_query_handler.clone();

            let claims = token_service
                .validate_token(token, PasetoClaimPurpose::AccessToken)
                .map_err(|e| ApiError::from(e))?;

            let user_id = claims.id;
            let query = GetUserByIdQuery::new(user_id);
            let user = user_query_handler
                .handle(query)
                .await
                .map_err(|e| ApiError::from(e))?;

            if user.is_none() {
                return Err(ApiError::Application(ApplicationError::Forbidden).into());
            }

            let user = user.unwrap();
            let role = UserRole::try_from(user.role);

            if role.is_err() {
                return Err(ApiError::Application(ApplicationError::Forbidden).into());
            }

            let role = role.unwrap();

            if role != UserRole::Administrator {
                return Err(ApiError::Application(ApplicationError::Forbidden).into());
            }

            Ok(res)
        })
    }
}
