use async_trait::async_trait;
use mockall::automock;

use crate::error::RepositoryError;
use crate::models::article::entity::Article;
use crate::models::article::value_object::ArticlePostId;

#[automock]
#[async_trait]
pub trait ArticleRepository: Send + Sync {
    async fn save(&self, entity: &Article) -> Result<(), RepositoryError>;
    async fn update(&self, entity: &Article) -> Result<(), RepositoryError>;
    async fn delete(&self, id: &ArticlePostId) -> Result<(), RepositoryError>;
}

#[automock]
#[async_trait]
pub trait ArticleReadRepository: Send + Sync {
    async fn find_by_id(&self, id: &ArticlePostId) -> Result<Option<Article>, RepositoryError>;
    async fn find_all(&self) -> Result<Vec<Article>, RepositoryError>;
}
