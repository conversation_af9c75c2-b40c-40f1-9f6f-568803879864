use reforged_shared::{UuidId, Value};

use super::entity::Faction;

pub type FactionId = UuidId<Faction>;

#[derive(Debug, PartialEq, Eq, PartialOrd, Ord, <PERSON>lone, <PERSON><PERSON>ult)]

pub struct FactionName(String);

impl FactionName {
    pub fn new(name: impl Into<String>) -> Self {
        Self(name.into())
    }
}

impl Value<String> for FactionName {
    fn value(&self) -> String {
        self.0.clone()
    }
}

impl From<String> for FactionName {
    fn from(value: String) -> Self {
        Self(value)
    }
}

impl From<&str> for FactionName {
    fn from(value: &str) -> Self {
        Self(value.to_string())
    }
}
