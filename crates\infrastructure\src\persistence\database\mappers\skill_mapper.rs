use num_traits::ToPrimitive;
use reforged_domain::models::skill::{entity::Skill, value_object::SkillId};

#[derive(Debug)]
pub struct SkillDbModelMapper(super::super::models::skills::Model);

impl SkillDbModelMapper {
    pub fn new(skill: super::super::models::skills::Model) -> Self {
        Self(skill)
    }
}

impl From<SkillDbModelMapper> for Skill {
    fn from(value: SkillDbModelMapper) -> Self {
        let skill_model = value.0;

        let id = SkillId::new(skill_model.id);

        Skill::builder()
            .id(id)
            .name(skill_model.name.into())
            .animation(skill_model.animation.into())
            .description(skill_model.description.into())
            .damage(skill_model.damage.to_f64().unwrap_or(0.).into())
            .mana(skill_model.mana.into())
            .mana_back(skill_model.mana_back.into())
            .life_steal(skill_model.life_steal.to_f64().unwrap_or(0.).into())
            .icon(skill_model.icon.into())
            .range(skill_model.range.into())
            .dsrc(skill_model.dsrc.into())
            .reference(skill_model.reference.into())
            .target(skill_model.target.into())
            .effects(skill_model.effects.into())
            .skill_type(skill_model.r#type.into())
            .cooldown(skill_model.cooldown.into())
            .hit_targets(skill_model.hit_targets.into())
            .chance(
                skill_model
                    .chance
                    .map(|c| c.to_f64().unwrap_or(0.).into())
                    .unwrap_or_default(),
            )
            .strl(skill_model.strl.into())
            .show_damage(skill_model.show_damage.into())
            .pet(skill_model.pet.into())
            .build()
    }
}
