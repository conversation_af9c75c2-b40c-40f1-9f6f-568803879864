use std::fmt::Display;

use getset::{<PERSON><PERSON>, <PERSON><PERSON>};
use reforged_shared::{UuidId, Value};

use super::entity::ClassCategory;

pub type ClassCategoryId = UuidId<ClassCategory>;

#[derive(Debug, PartialEq, PartialOrd, Clone, Default)]

pub struct ClassCategoryName(String);

impl ClassCategoryName {
    pub fn new(name: impl Into<String>) -> Self {
        Self(name.into())
    }
}

impl Value<String> for ClassCategoryName {
    fn value(&self) -> String {
        self.0.clone()
    }
}

impl From<String> for ClassCategoryName {
    fn from(value: String) -> Self {
        Self(value)
    }
}

impl From<&str> for ClassCategoryName {
    fn from(value: &str) -> Self {
        Self(value.to_string())
    }
}

#[derive(Debug, PartialEq, PartialOrd, Clone, Default)]
pub enum ClassCategoryEnum {
    #[default]
    /// M1
    Fighter,
    /// M2
    Thief,
    /// M3
    Hybrid,
    /// M4
    Armsman,
    /// C1
    Wizard,
    /// C2
    Healer,
    /// C3
    SpellBreaker,
    /// S1
    Lucky,
    Custom(String),
}

impl<'a> From<&'a str> for ClassCategoryEnum {
    fn from(value: &'a str) -> Self {
        match value {
            "Fighter" | "fighter" | "M1" => ClassCategoryEnum::Fighter,
            "Thief" | "thief" | "M2" => ClassCategoryEnum::Thief,
            "Hybrid" | "hybrid" | "M3" => ClassCategoryEnum::Hybrid,
            "Armsman" | "armsman" | "M4" => ClassCategoryEnum::Armsman,
            "Wizard" | "wizard" | "C1" => ClassCategoryEnum::Wizard,
            "Healer" | "healer" | "C2" => ClassCategoryEnum::Healer,
            "SpellBreaker" | "spellbreaker" | "C3" => ClassCategoryEnum::SpellBreaker,
            "Lucky" | "lucky" | "S1" => ClassCategoryEnum::Lucky,
            category => ClassCategoryEnum::Custom(category.to_string()),
        }
    }
}

impl TryFrom<String> for ClassCategoryEnum {
    type Error = &'static str;

    fn try_from(value: String) -> Result<Self, Self::Error> {
        match value.as_str() {
            "Fighter" | "fighter" | "M1" => Ok(ClassCategoryEnum::Fighter),
            "Thief" | "thief" | "M2" => Ok(ClassCategoryEnum::Thief),
            "Hybrid" | "hybrid" | "M3" => Ok(ClassCategoryEnum::Hybrid),
            "Armsman" | "armsman" | "M4" => Ok(ClassCategoryEnum::Armsman),
            "Wizard" | "wizard" | "C1" => Ok(ClassCategoryEnum::Wizard),
            "Healer" | "healer" | "C2" => Ok(ClassCategoryEnum::Healer),
            "SpellBreaker" | "spellbreaker" | "C3" => Ok(ClassCategoryEnum::SpellBreaker),
            "Lucky" | "lucky" | "S1" => Ok(ClassCategoryEnum::Lucky),
            _ => Err("Invalid class category"),
        }
    }
}

impl Display for ClassCategoryEnum {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        match self {
            ClassCategoryEnum::Fighter => write!(f, "M1"),
            ClassCategoryEnum::Thief => write!(f, "M2"),
            ClassCategoryEnum::Hybrid => write!(f, "M3"),
            ClassCategoryEnum::Armsman => write!(f, "M4"),
            ClassCategoryEnum::Wizard => write!(f, "C1"),
            ClassCategoryEnum::Healer => write!(f, "C2"),
            ClassCategoryEnum::SpellBreaker => write!(f, "C3"),
            ClassCategoryEnum::Lucky => write!(f, "S1"),
            ClassCategoryEnum::Custom(category) => write!(f, "{}", category),
        }
    }
}

#[derive(Debug, PartialEq, PartialOrd, Clone, Default)]

pub struct Strength(f64);

impl Strength {
    pub fn new(value: f64) -> Self {
        Self(value)
    }
}

impl Value<f64> for Strength {
    fn value(&self) -> f64 {
        self.0
    }
}

impl From<f64> for Strength {
    fn from(value: f64) -> Self {
        Self(value)
    }
}

#[derive(Debug, PartialEq, PartialOrd, Clone, Default)]

pub struct Endurance(f64);

impl Endurance {
    pub fn new(value: f64) -> Self {
        Self(value)
    }
}

impl Value<f64> for Endurance {
    fn value(&self) -> f64 {
        self.0
    }
}

impl From<f64> for Endurance {
    fn from(value: f64) -> Self {
        Self(value)
    }
}

#[derive(Debug, PartialEq, PartialOrd, Clone, Default)]

pub struct Dexterity(f64);

impl Dexterity {
    pub fn new(value: f64) -> Self {
        Self(value)
    }
}

impl Value<f64> for Dexterity {
    fn value(&self) -> f64 {
        self.0
    }
}

impl From<f64> for Dexterity {
    fn from(value: f64) -> Self {
        Self(value)
    }
}

#[derive(Debug, PartialEq, PartialOrd, Clone, Default)]

pub struct Intellect(f64);

impl Intellect {
    pub fn new(value: f64) -> Self {
        Self(value)
    }
}

impl Value<f64> for Intellect {
    fn value(&self) -> f64 {
        self.0
    }
}

impl From<f64> for Intellect {
    fn from(value: f64) -> Self {
        Self(value)
    }
}

#[derive(Debug, PartialEq, PartialOrd, Clone, Default)]

pub struct Wisdom(f64);

impl Wisdom {
    pub fn new(value: f64) -> Self {
        Self(value)
    }
}

impl Value<f64> for Wisdom {
    fn value(&self) -> f64 {
        self.0
    }
}

impl From<f64> for Wisdom {
    fn from(value: f64) -> Self {
        Self(value)
    }
}

#[derive(Debug, PartialEq, PartialOrd, Clone, Default)]

pub struct Luck(f64);

impl Luck {
    pub fn new(value: f64) -> Self {
        Self(value)
    }
}

impl Value<f64> for Luck {
    fn value(&self) -> f64 {
        self.0
    }
}

impl From<f64> for Luck {
    fn from(value: f64) -> Self {
        Self(value)
    }
}

#[derive(Debug, PartialEq, PartialOrd, Clone, Default, Getters, Setters)]
#[getset(get = "pub", set = "pub")]

pub struct ClassCategoryStats {
    strength: Strength,
    endurance: Endurance,
    dexterity: Dexterity,
    intellect: Intellect,
    wisdom: Wisdom,
    luck: Luck,
}

impl ClassCategoryStats {
    pub fn new(
        strength: Strength,
        endurance: Endurance,
        dexterity: Dexterity,
        intellect: Intellect,
        wisdom: Wisdom,
        luck: Luck,
    ) -> Self {
        Self {
            strength,
            endurance,
            dexterity,
            intellect,
            wisdom,
            luck,
        }
    }
}
