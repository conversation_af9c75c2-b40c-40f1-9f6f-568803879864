use num_traits::ToPrimitive;
use reforged_domain::models::guild::value_object::GuildId;
use reforged_domain::models::guild_hall::value_object::{
    GuildHallCell, GuildHallInterior, GuildHallLinkage, GuildHallX, GuildHallY,
};
use reforged_domain::models::guild_hall::{entity::GuildHall, value_object::GuildHallId};

#[derive(Debug)]
pub struct GuildHallDbModelMapper(super::super::models::guild_halls::Model);

impl GuildHallDbModelMapper {
    pub fn new(guild_hall: super::super::models::guild_halls::Model) -> Self {
        Self(guild_hall)
    }
}

impl From<GuildHallDbModelMapper> for GuildHall {
    fn from(value: GuildHallDbModelMapper) -> Self {
        let guild_hall_model = value.0;

        let id = GuildHallId::new(guild_hall_model.id);

        GuildHall::builder()
            .id(id)
            .guild_id(GuildId::new(guild_hall_model.guild_id))
            .linkage(GuildHallLinkage::new(guild_hall_model.linkage))
            .cell(GuildHallCell::new(guild_hall_model.cell))
            .x(GuildHallX::new(guild_hall_model.x.to_f32().unwrap_or(0.)))
            .y(GuildHallY::new(guild_hall_model.y.to_f32().unwrap_or(0.)))
            .interior(GuildHallInterior::new(guild_hall_model.interior))
            .build()
    }
}
