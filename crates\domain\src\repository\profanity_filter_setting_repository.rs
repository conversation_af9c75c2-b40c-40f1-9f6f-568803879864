use async_trait::async_trait;
use mockall::automock;

use crate::error::RepositoryError;
use crate::models::profanity_filter_setting::entity::ProfanityFilterSettings;
use crate::models::profanity_filter_setting::value_object::ProfanityFilterSettingsId;

#[automock]
#[async_trait]
pub trait ProfanityFilterSettingsRepository: Send + Sync {
    async fn save(&self, entity: &ProfanityFilterSettings) -> Result<(), RepositoryError>;
    async fn update(&self, entity: &ProfanityFilterSettings) -> Result<(), RepositoryError>;
    async fn delete(&self, id: &ProfanityFilterSettingsId) -> Result<(), RepositoryError>;
}

#[automock]
#[async_trait]
pub trait ProfanityFilterSettingsReadRepository: Send + Sync {
    async fn find_by_id(
        &self,
        id: &ProfanityFilterSettingsId,
    ) -> Result<Option<ProfanityFilterSettings>, RepositoryError>;
    async fn find_all(&self) -> Result<Vec<ProfanityFilterSettings>, RepositoryError>;
}
