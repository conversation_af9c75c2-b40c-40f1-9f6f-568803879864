use super::value_object::{ItemRarityEnum, ItemRarityId};

use getset::{<PERSON><PERSON>, <PERSON><PERSON>};

#[derive(<PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, bon::<PERSON><PERSON><PERSON>, <PERSON><PERSON>, Set<PERSON>)]
#[getset(get = "pub", set = "pub")]
pub struct ItemRarity {
    id: ItemRarityId,
    name: ItemRarityEnum,
}

impl ItemRarity {
    pub fn new(id: ItemRarityId, name: ItemRarityEnum) -> Self {
        Self { id, name }
    }
}

#[cfg(test)]
mod tests {
    use reforged_shared::IdTrait;

    use super::*;

    #[test]
    fn test_item_rarity_new() {
        let id = ItemRarityId::new(uuid::Uuid::now_v7());
        let name = ItemRarityEnum::Common;

        let rarity = ItemRarity::new(id.clone(), name.clone());

        assert_eq!(rarity.id().get_id(), id.get_id());
        assert_eq!(rarity.name(), &name);
    }

    #[test]
    fn test_item_rarity_builder() {
        let id = ItemRarityId::new(uuid::Uuid::now_v7());
        let name = ItemRarityEnum::Rare;

        let rarity = ItemRarity::builder()
            .id(id.clone())
            .name(name.clone())
            .build();

        assert_eq!(rarity.id().get_id(), id.get_id());
        assert_eq!(rarity.name(), &name);
    }

    #[test]
    fn test_getters_and_setters() {
        let mut rarity = ItemRarity::default();

        let id = ItemRarityId::new(uuid::Uuid::now_v7());
        let name = ItemRarityEnum::Common;

        rarity.set_id(id.clone());
        rarity.set_name(name.clone());

        assert_eq!(rarity.id().get_id(), id.get_id());
        assert_eq!(rarity.name(), &name);
    }
}
