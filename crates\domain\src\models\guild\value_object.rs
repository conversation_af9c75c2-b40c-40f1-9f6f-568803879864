use chrono::{DateTime, Utc};
use reforged_shared::{UuidId, Value};

use super::entity::Guild;

pub type GuildId = UuidId<Guild>;

#[derive(Debug, PartialEq, Eq, PartialOrd, Ord, Clone, Default)]

pub struct GuildName(String);

impl GuildName {
    pub fn new(name: impl Into<String>) -> Self {
        Self(name.into())
    }
}

impl Value<String> for GuildName {
    fn value(&self) -> String {
        self.0.clone()
    }
}

impl From<String> for GuildName {
    fn from(value: String) -> Self {
        Self(value)
    }
}

impl From<&str> for GuildName {
    fn from(value: &str) -> Self {
        Self(value.to_string())
    }
}

#[derive(Debug, PartialEq, Eq, PartialOrd, Ord, Clone, Default)]

pub struct GuildMessageOfTheDay(String);

impl GuildMessageOfTheDay {
    pub fn new(message: impl Into<String>) -> Self {
        Self(message.into())
    }
}

impl Value<String> for GuildMessageOfTheDay {
    fn value(&self) -> String {
        self.0.clone()
    }
}

impl From<String> for GuildMessageOfTheDay {
    fn from(value: String) -> Self {
        Self(value)
    }
}

impl From<&str> for GuildMessageOfTheDay {
    fn from(value: &str) -> Self {
        Self(value.to_string())
    }
}

#[derive(Debug, PartialEq, Eq, PartialOrd, Ord, Clone, Default)]

pub struct GuildMaxMembers(i16);

impl GuildMaxMembers {
    pub fn new(max_members: i16) -> Self {
        Self(max_members)
    }
}

impl Value<i16> for GuildMaxMembers {
    fn value(&self) -> i16 {
        self.0
    }
}

impl From<i16> for GuildMaxMembers {
    fn from(value: i16) -> Self {
        Self(value)
    }
}

#[derive(Debug, PartialEq, Eq, PartialOrd, Ord, Clone, Default)]

pub struct GuildHallSize(i16);

impl GuildHallSize {
    pub fn new(hall_size: i16) -> Self {
        Self(hall_size)
    }
}

impl Value<i16> for GuildHallSize {
    fn value(&self) -> i16 {
        self.0
    }
}

impl From<i16> for GuildHallSize {
    fn from(value: i16) -> Self {
        Self(value)
    }
}

#[derive(Debug, PartialEq, Eq, PartialOrd, Ord, Clone, Default)]

pub struct GuildLastUpdated(DateTime<Utc>);

impl GuildLastUpdated {
    pub fn new(last_updated: DateTime<Utc>) -> Self {
        Self(last_updated)
    }
}

impl Value<DateTime<Utc>> for GuildLastUpdated {
    fn value(&self) -> DateTime<Utc> {
        self.0
    }
}

impl From<DateTime<Utc>> for GuildLastUpdated {
    fn from(value: DateTime<Utc>) -> Self {
        Self(value)
    }
}

#[derive(Debug, PartialEq, Eq, PartialOrd, Ord, Clone, Default)]

pub struct GuildWins(i32);

impl GuildWins {
    pub fn new(wins: i32) -> Self {
        Self(wins)
    }
}

impl Value<i32> for GuildWins {
    fn value(&self) -> i32 {
        self.0
    }
}

impl From<i32> for GuildWins {
    fn from(value: i32) -> Self {
        Self(value)
    }
}

#[derive(Debug, PartialEq, Eq, PartialOrd, Ord, Clone, Default)]

pub struct GuildLoss(i32);

impl GuildLoss {
    pub fn new(loss: i32) -> Self {
        Self(loss)
    }
}

impl Value<i32> for GuildLoss {
    fn value(&self) -> i32 {
        self.0
    }
}

impl From<i32> for GuildLoss {
    fn from(value: i32) -> Self {
        Self(value)
    }
}

#[derive(Debug, PartialEq, Eq, PartialOrd, Ord, Clone, Default)]

pub struct GuildTotalKills(i32);

impl GuildTotalKills {
    pub fn new(total_kills: i32) -> Self {
        Self(total_kills)
    }
}

impl Value<i32> for GuildTotalKills {
    fn value(&self) -> i32 {
        self.0
    }
}

impl From<i32> for GuildTotalKills {
    fn from(value: i32) -> Self {
        Self(value)
    }
}

#[derive(Debug, PartialEq, Eq, PartialOrd, Ord, Clone, Default)]

pub struct GuildLevel(i32);

impl GuildLevel {
    pub fn new(level: i32) -> Self {
        Self(level)
    }
}

impl Value<i32> for GuildLevel {
    fn value(&self) -> i32 {
        self.0
    }
}

impl From<i32> for GuildLevel {
    fn from(value: i32) -> Self {
        Self(value)
    }
}

#[derive(Debug, PartialEq, Eq, PartialOrd, Ord, Clone, Default)]

pub struct GuildExperience(i64);

impl GuildExperience {
    pub fn new(experience: i64) -> Self {
        Self(experience)
    }
}

impl Value<i64> for GuildExperience {
    fn value(&self) -> i64 {
        self.0
    }
}

impl From<i64> for GuildExperience {
    fn from(value: i64) -> Self {
        Self(value)
    }
}

#[derive(Debug, PartialEq, Eq, PartialOrd, Ord, Clone, Default)]

pub struct GuildColor(String);

impl GuildColor {
    pub fn new(color: impl Into<String>) -> Self {
        Self(color.into())
    }
}

impl Value<String> for GuildColor {
    fn value(&self) -> String {
        self.0.clone()
    }
}

impl From<String> for GuildColor {
    fn from(value: String) -> Self {
        Self(value)
    }
}

impl From<&str> for GuildColor {
    fn from(value: &str) -> Self {
        Self(value.to_string())
    }
}

#[derive(Debug, PartialEq, Eq, PartialOrd, Ord, Clone, Default)]

pub struct GuildColorId(Option<i32>);

impl GuildColorId {
    pub fn new(color_id: Option<i32>) -> Self {
        Self(color_id)
    }
}

impl Value<Option<i32>> for GuildColorId {
    fn value(&self) -> Option<i32> {
        self.0
    }
}

impl From<Option<i32>> for GuildColorId {
    fn from(value: Option<i32>) -> Self {
        Self(value)
    }
}
