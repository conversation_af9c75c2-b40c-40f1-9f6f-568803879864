use crate::models;
use sea_orm::{ActiveModelTrait, IntoActiveModel};
use sea_orm_migration::prelude::*;

#[derive(DeriveMigrationName)]
pub struct Migration;

#[async_trait::async_trait]
impl MigrationTrait for Migration {
    async fn up(&self, manager: &SchemaManager) -> Result<(), DbErr> {
        let db = manager.get_connection();

        let factions = include_str!("../json/factions.json");
        let factions: Vec<models::factions::Model> = serde_json::from_str(factions).unwrap();

        for model in factions {
            let faction = model.into_active_model();
            faction.insert(db).await?;
        }

        let access = include_str!("../json/access.json");
        let access: Vec<models::roles::Model> = serde_json::from_str(access).unwrap();

        for model in access {
            let role = model.into_active_model();
            role.insert(db).await?;
        }

        Ok(())
    }

    async fn down(&self, _manager: &SchemaManager) -> Result<(), DbErr> {
        Ok(())
    }
}
