use reforged_shared::{UuidId, Value};

use super::entity::GuildHall;

pub type GuildHallId = UuidId<GuildHall>;

#[derive(Debug, PartialEq, PartialOrd, Clone, Default)]

pub struct GuildHallLinkage(String);

impl GuildHallLinkage {
    pub fn new(linkage: String) -> Self {
        Self(linkage)
    }
}

impl Value<String> for GuildHallLinkage {
    fn value(&self) -> String {
        self.0.clone()
    }
}

impl From<String> for GuildHallLinkage {
    fn from(value: String) -> Self {
        Self(value)
    }
}

#[derive(Debug, PartialEq, PartialOrd, Clone, Default)]

pub struct GuildHallCell(String);

impl GuildHallCell {
    pub fn new(cell: String) -> Self {
        Self(cell)
    }
}

impl Value<String> for GuildHallCell {
    fn value(&self) -> String {
        self.0.clone()
    }
}

impl From<String> for GuildHallCell {
    fn from(value: String) -> Self {
        Self(value)
    }
}

#[derive(Debug, PartialEq, PartialOrd, <PERSON><PERSON>, Default)]

pub struct GuildHallX(f32);

impl GuildHallX {
    pub fn new(x: f32) -> Self {
        Self(x)
    }
}

impl Value<f32> for GuildHallX {
    fn value(&self) -> f32 {
        self.0
    }
}

impl From<f32> for GuildHallX {
    fn from(value: f32) -> Self {
        Self(value)
    }
}

#[derive(Debug, PartialEq, PartialOrd, Clone, Default)]

pub struct GuildHallY(f32);

impl GuildHallY {
    pub fn new(y: f32) -> Self {
        Self(y)
    }
}

impl Value<f32> for GuildHallY {
    fn value(&self) -> f32 {
        self.0
    }
}

impl From<f32> for GuildHallY {
    fn from(value: f32) -> Self {
        Self(value)
    }
}

#[derive(Debug, PartialEq, PartialOrd, Clone, Default)]

pub struct GuildHallInterior(String);

impl GuildHallInterior {
    pub fn new(interior: String) -> Self {
        Self(interior)
    }
}

impl Value<String> for GuildHallInterior {
    fn value(&self) -> String {
        self.0.clone()
    }
}

impl From<String> for GuildHallInterior {
    fn from(value: String) -> Self {
        Self(value)
    }
}
