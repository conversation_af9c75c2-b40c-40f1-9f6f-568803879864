# Shell commands used.

### RabbitMQ on docker/podman
```nushell
# PODMAN
podman run -d --hostname reforged-rabbit-broker --name reforged-broker -e RABBITMQ_DEFAULT_USER=user -e RABBITMQ_DEFAULT_PASS=secret -p 5672:5672 -p 8080:15672 rabbitmq:3-management

# DOCKER
docker run -d --hostname reforged-rabbit-broker --name reforged-broker -e RABBITMQ_DEFAULT_USER=user -e RABBITMQ_DEFAULT_PASS=secret -p 5672:5672 -p 8080:15672 rabbitmq:3-management
```

### Migration
```nushell
# generating migration file using sea-orm-cli
sea-orm-cli migrate generate -d crates\infrastructure\src\persistence\database\migrations\ --universal-time create_enums

# running migration
cargo run --bin migrator --help
```