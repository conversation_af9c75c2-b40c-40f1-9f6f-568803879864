use reforged_shared::{UuidId, Value};

use super::entity::Achievement;

pub type AchievementId = UuidId<Achievement>;

#[derive(Debug, PartialEq, Eq, PartialOrd, Ord, Clone, Default)]
pub struct AchievementName(String);

impl AchievementName {
    pub fn new(name: impl Into<String>) -> Self {
        Self(name.into())
    }
}

impl Value<String> for AchievementName {
    fn value(&self) -> String {
        self.0.clone()
    }
}

impl From<String> for AchievementName {
    fn from(value: String) -> Self {
        Self(value)
    }
}

impl From<&str> for AchievementName {
    fn from(value: &str) -> Self {
        Self(value.to_string())
    }
}

#[derive(Debug, PartialEq, Eq, PartialOrd, Ord, Clone, Default)]
pub struct AchievementDescription(String);

impl AchievementDescription {
    pub fn new(description: impl Into<String>) -> Self {
        Self(description.into())
    }
}

impl Value<String> for AchievementDescription {
    fn value(&self) -> String {
        self.0.clone()
    }
}

impl From<String> for AchievementDescription {
    fn from(value: String) -> Self {
        Self(value)
    }
}

impl From<&str> for AchievementDescription {
    fn from(value: &str) -> Self {
        Self(value.to_string())
    }
}

#[derive(Debug, PartialEq, Eq, PartialOrd, Ord, Clone, Default)]
pub struct AchievementFile(String);

impl AchievementFile {
    pub fn new(file: impl Into<String>) -> Self {
        Self(file.into())
    }
}

impl Value<String> for AchievementFile {
    fn value(&self) -> String {
        self.0.clone()
    }
}

impl From<String> for AchievementFile {
    fn from(value: String) -> Self {
        Self(value)
    }
}

impl From<&str> for AchievementFile {
    fn from(value: &str) -> Self {
        Self(value.to_string())
    }
}

#[derive(Debug, PartialEq, Eq, PartialOrd, Ord, Clone, Default)]
pub struct AchievementCategory(String);

impl AchievementCategory {
    pub fn new(category: impl Into<String>) -> Self {
        Self(category.into())
    }
}

impl Value<String> for AchievementCategory {
    fn value(&self) -> String {
        self.0.clone()
    }
}

impl From<String> for AchievementCategory {
    fn from(value: String) -> Self {
        Self(value)
    }
}

impl From<&str> for AchievementCategory {
    fn from(value: &str) -> Self {
        Self(value.to_string())
    }
}

#[derive(Debug, PartialEq, Eq, PartialOrd, Ord, Clone, Default)]
pub struct AchievementShow(bool);

impl AchievementShow {
    pub fn new(show: bool) -> Self {
        Self(show)
    }
}

impl Value<bool> for AchievementShow {
    fn value(&self) -> bool {
        self.0
    }
}

impl From<bool> for AchievementShow {
    fn from(value: bool) -> Self {
        Self(value)
    }
}
