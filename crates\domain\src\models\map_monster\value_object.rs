use reforged_shared::UuidId;

use super::entity::MapMonster;

pub type MapMonsterId = UuidId<MapMonster>;

#[derive(Debug, PartialEq, Eq, PartialOrd, Ord, <PERSON>lone, De<PERSON>ult)]

pub struct MapMonsterFrame(String);

impl MapMonsterFrame {
    pub fn new(name: impl Into<String>) -> Self {
        Self(name.into())
    }
}

impl MapMonsterFrame {
    pub fn value(&self) -> &String {
        &self.0
    }
}

impl From<&str> for MapMonsterFrame {
    fn from(value: &str) -> Self {
        Self(value.to_string())
    }
}

impl From<String> for MapMonsterFrame {
    fn from(value: String) -> Self {
        Self(value)
    }
}
