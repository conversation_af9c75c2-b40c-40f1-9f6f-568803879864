use reforged_domain::models::item_lottery::entity::ItemLottery;
use reforged_domain::models::item_lottery::value_object::ItemLotteryId;
use reforged_domain::models::item::value_object::ItemId;
use num_traits::ToPrimitive;

#[derive(Debug)]
pub struct ItemLotteryDbModelMapper(super::super::models::item_lotteries::Model);

impl ItemLotteryDbModelMapper {
    pub fn new(model: super::super::models::item_lotteries::Model) -> Self {
        Self(model)
    }
}

impl From<ItemLotteryDbModelMapper> for ItemLottery {
    fn from(value: ItemLotteryDbModelMapper) -> Self {
        let model = value.0;

        ItemLottery::builder()
            .id(ItemLotteryId::new(model.id))
            .item_id(ItemId::new(model.item_id))
            .reward_id(ItemId::new(model.reward_id))
            .quantity(model.quantity.into())
            .chance(model.chance.to_f64().unwrap_or(0.0).into())
            .build()
    }
}
