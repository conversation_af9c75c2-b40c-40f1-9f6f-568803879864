use crate::models::item::value_object::ItemId;
use crate::models::item_lottery::value_object::*;
use getset::{<PERSON><PERSON>, Set<PERSON>};

#[derive(<PERSON>bu<PERSON>, <PERSON><PERSON>, De<PERSON>ult, bon::<PERSON><PERSON><PERSON>, <PERSON><PERSON>, Set<PERSON>)]
#[getset(get = "pub", set = "pub")]
pub struct ItemLottery {
    id: ItemLotteryId,
    pub item_id: ItemId,
    pub reward_id: ItemId,
    pub quantity: ItemLotteryQuantity,
    pub chance: ItemLotteryChance,
}

impl ItemLottery {
    pub fn new(
        id: ItemLotteryId,
        item_id: ItemId,
        reward_id: ItemId,
        quantity: ItemLotteryQuantity,
        chance: ItemLotteryChance,
    ) -> Self {
        Self {
            id,
            item_id,
            reward_id,
            quantity,
            chance,
        }
    }
}

#[cfg(test)]
mod tests {
    use reforged_shared::{IdTrait, Value};

    use super::*;

    #[test]
    fn test_item_lottery_new() {
        let id = ItemLotteryId::new(uuid::Uuid::now_v7());
        let item_id = ItemId::new(uuid::Uuid::now_v7());
        let reward_id = ItemId::new(uuid::Uuid::now_v7());
        let quantity = ItemLotteryQuantity::new(5);
        let chance = ItemLotteryChance::new(75.5);

        let lottery = ItemLottery::new(
            id.clone(),
            item_id.clone(),
            reward_id.clone(),
            quantity.clone(),
            chance.clone(),
        );

        assert_eq!(lottery.id.get_id(), id.get_id());
        assert_eq!(lottery.item_id.get_id(), item_id.get_id());
        assert_eq!(lottery.reward_id.get_id(), reward_id.get_id());
        assert_eq!(lottery.quantity.value(), quantity.value());
        assert_eq!(lottery.chance.value(), chance.value());
    }

    #[test]
    fn test_item_lottery_builder() {
        let id = ItemLotteryId::new(uuid::Uuid::now_v7());
        let item_id = ItemId::new(uuid::Uuid::now_v7());
        let reward_id = ItemId::new(uuid::Uuid::now_v7());
        let quantity = ItemLotteryQuantity::new(5);
        let chance = ItemLotteryChance::new(75.5);

        let lottery = ItemLottery::builder()
            .id(id.clone())
            .item_id(item_id.clone())
            .reward_id(reward_id.clone())
            .quantity(quantity.clone())
            .chance(chance.clone())
            .build();

        assert_eq!(lottery.id.get_id(), id.get_id());
        assert_eq!(lottery.item_id.get_id(), item_id.get_id());
        assert_eq!(lottery.reward_id.get_id(), reward_id.get_id());
        assert_eq!(lottery.quantity.value(), quantity.value());
        assert_eq!(lottery.chance.value(), chance.value());
    }

    #[test]
    fn test_getters_and_setters() {
        let mut lottery = ItemLottery::default();

        let id = ItemLotteryId::new(uuid::Uuid::now_v7());
        let item_id = ItemId::new(uuid::Uuid::now_v7());
        let reward_id = ItemId::new(uuid::Uuid::now_v7());
        let quantity = ItemLotteryQuantity::new(5);
        let chance = ItemLotteryChance::new(75.5);

        lottery.set_id(id.clone());
        lottery.set_item_id(item_id.clone());
        lottery.set_reward_id(reward_id.clone());
        lottery.set_quantity(quantity.clone());
        lottery.set_chance(chance.clone());

        assert_eq!(lottery.id.get_id(), id.get_id());
        assert_eq!(lottery.item_id.get_id(), item_id.get_id());
        assert_eq!(lottery.reward_id.get_id(), reward_id.get_id());
        assert_eq!(lottery.quantity.value(), quantity.value());
        assert_eq!(lottery.chance.value(), chance.value());
    }
}
