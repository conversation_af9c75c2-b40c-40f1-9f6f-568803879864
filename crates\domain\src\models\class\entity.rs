use super::value_object::{
    ClassDescription, ClassId, ClassName, ManaRegenerationMethods, StatsDescription,
};
use crate::models::class_category::value_object::ClassCategoryEnum;

use getset::{<PERSON><PERSON>, Set<PERSON>};

#[derive(<PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, bon::<PERSON><PERSON><PERSON>, <PERSON><PERSON>, Set<PERSON>)]
#[getset(get = "pub", set = "pub")]
pub struct Class {
    id: ClassId,
    name: ClassName,
    category: ClassCategoryEnum,
    description: ClassDescription,
    mana_regeneration_methods: ManaRegenerationMethods,
    stats_description: StatsDescription,
}

impl Class {
    pub fn new(
        id: ClassId,
        name: ClassName,
        category: ClassCategoryEnum,
        description: ClassDescription,
        mana_regeneration_methods: ManaRegenerationMethods,
        stats_description: StatsDescription,
    ) -> Self {
        Self {
            id,
            name,
            category,
            description,
            mana_regeneration_methods,
            stats_description,
        }
    }
}

#[cfg(test)]
mod tests {
    use reforged_shared::{IdTrait, Value};

    use super::*;

    #[test]
    fn test_class_new() {
        let id = ClassId::new(uuid::Uuid::now_v7());
        let name = ClassName::new("Warrior");
        let category = ClassCategoryEnum::Fighter;
        let description = ClassDescription::new("A powerful melee fighter");
        let mana_regeneration_methods = ManaRegenerationMethods::new("Meditation");
        let stats_description = StatsDescription::new("High strength and endurance");

        let class = Class::new(
            id.clone(),
            name.clone(),
            category.clone(),
            description.clone(),
            mana_regeneration_methods.clone(),
            stats_description.clone(),
        );

        assert_eq!(class.id().get_id(), id.get_id());
        assert_eq!(class.name().value(), name.value());
        assert_eq!(class.category(), &category);
        assert_eq!(class.description().value(), description.value());
        assert_eq!(
            class.mana_regeneration_methods().value(),
            mana_regeneration_methods.value()
        );
        assert_eq!(class.stats_description().value(), stats_description.value());
    }

    #[test]
    fn test_class_builder() {
        let id = ClassId::new(uuid::Uuid::now_v7());
        let name = ClassName::new("Warrior");
        let category = ClassCategoryEnum::Fighter;
        let description = ClassDescription::new("A powerful melee fighter");
        let mana_regeneration_methods = ManaRegenerationMethods::new("Meditation");
        let stats_description = StatsDescription::new("High strength and endurance");

        let class = Class::builder()
            .id(id.clone())
            .name(name.clone())
            .category(category.clone())
            .description(description.clone())
            .mana_regeneration_methods(mana_regeneration_methods.clone())
            .stats_description(stats_description.clone())
            .build();

        assert_eq!(class.id().get_id(), id.get_id());
        assert_eq!(class.name().value(), name.value());
        assert_eq!(class.category(), &category);
        assert_eq!(class.description().value(), description.value());
        assert_eq!(
            class.mana_regeneration_methods().value(),
            mana_regeneration_methods.value()
        );
        assert_eq!(class.stats_description().value(), stats_description.value());
    }

    #[test]
    fn test_getters_and_setters() {
        let mut class = Class::default();

        let id = ClassId::new(uuid::Uuid::now_v7());
        let name = ClassName::new("Warrior");
        let category = ClassCategoryEnum::Fighter;
        let description = ClassDescription::new("A powerful melee fighter");
        let mana_regeneration_methods = ManaRegenerationMethods::new("Meditation");
        let stats_description = StatsDescription::new("High strength and endurance");

        class.set_id(id.clone());
        class.set_name(name.clone());
        class.set_category(category.clone());
        class.set_description(description.clone());
        class.set_mana_regeneration_methods(mana_regeneration_methods.clone());
        class.set_stats_description(stats_description.clone());

        assert_eq!(class.id().get_id(), id.get_id());
        assert_eq!(class.name().value(), name.value());
        assert_eq!(class.category(), &category);
        assert_eq!(class.description().value(), description.value());
        assert_eq!(
            class.mana_regeneration_methods().value(),
            mana_regeneration_methods.value()
        );
        assert_eq!(class.stats_description().value(), stats_description.value());
    }
}
