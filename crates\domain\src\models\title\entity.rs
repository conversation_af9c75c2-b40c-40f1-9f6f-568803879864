use super::value_object::*;
use crate::models::role::value_object::RoleId;
use getset::{<PERSON><PERSON>, <PERSON><PERSON>};

#[derive(Debug, <PERSON><PERSON>, Default, bon::<PERSON><PERSON><PERSON>, <PERSON><PERSON>, Setters)]
#[getset(get = "pub", set = "pub")]
pub struct Title {
    id: TitleId,
    name: TitleName,
    description: TitleDescription,
    color: TitleColor,
    strength: TitleStrength,
    intellect: TitleIntellect,
    endurance: TitleEndurance,
    dexterity: TitleDexterity,
    wisdom: TitleWisdom,
    luck: TitleLuck,
    role_id: RoleId,
}

impl Title {
    #[allow(clippy::too_many_arguments)]
    pub fn new(
        id: TitleId,
        name: TitleName,
        description: TitleDescription,
        color: TitleColor,
        strength: TitleStrength,
        intellect: TitleIntellect,
        endurance: TitleEndurance,
        dexterity: TitleDexterity,
        wisdom: TitleWisdom,
        luck: TitleLuck,
        role_id: RoleId,
    ) -> Self {
        Self {
            id,
            name,
            description,
            color,
            strength,
            intellect,
            endurance,
            dexterity,
            wisdom,
            luck,
            role_id,
        }
    }
}

#[cfg(test)]
mod tests {
    use reforged_shared::{IdTrait, Value};

    use super::*;

    #[test]
    fn test_title_new() {
        let id = TitleId::new(uuid::Uuid::now_v7());
        let name = TitleName::new("Test Title");
        let description = TitleDescription::new("Test Description");
        let color = TitleColor::new("#FF0000");
        let strength = TitleStrength::new(10);
        let intellect = TitleIntellect::new(20);
        let endurance = TitleEndurance::new(30);
        let dexterity = TitleDexterity::new(40);
        let wisdom = TitleWisdom::new(50);
        let luck = TitleLuck::new(60);
        let role_id = RoleId::new(uuid::Uuid::now_v7());

        let title = Title::new(
            id.clone(),
            name.clone(),
            description.clone(),
            color.clone(),
            strength.clone(),
            intellect.clone(),
            endurance.clone(),
            dexterity.clone(),
            wisdom.clone(),
            luck.clone(),
            role_id.clone(),
        );

        assert_eq!(title.id.get_id(), id.get_id());
        assert_eq!(title.name.value(), name.value());
        assert_eq!(title.description.value(), description.value());
        assert_eq!(title.color.value(), color.value());
        assert_eq!(title.strength.value(), strength.value());
        assert_eq!(title.intellect.value(), intellect.value());
        assert_eq!(title.endurance.value(), endurance.value());
        assert_eq!(title.dexterity.value(), dexterity.value());
        assert_eq!(title.wisdom.value(), wisdom.value());
        assert_eq!(title.luck.value(), luck.value());
        assert_eq!(title.role_id.get_id(), role_id.get_id());
    }

    #[test]
    fn test_title_builder() {
        let id = TitleId::new(uuid::Uuid::now_v7());
        let name = TitleName::new("Test Title");
        let description = TitleDescription::new("Test Description");
        let color = TitleColor::new("#FF0000");
        let strength = TitleStrength::new(10);
        let intellect = TitleIntellect::new(20);
        let endurance = TitleEndurance::new(30);
        let dexterity = TitleDexterity::new(40);
        let wisdom = TitleWisdom::new(50);
        let luck = TitleLuck::new(60);
        let role_id = RoleId::new(uuid::Uuid::now_v7());

        let title = Title::builder()
            .id(id.clone())
            .name(name.clone())
            .description(description.clone())
            .color(color.clone())
            .strength(strength.clone())
            .intellect(intellect.clone())
            .endurance(endurance.clone())
            .dexterity(dexterity.clone())
            .wisdom(wisdom.clone())
            .luck(luck.clone())
            .role_id(role_id.clone())
            .build();

        assert_eq!(title.id.get_id(), id.get_id());
        assert_eq!(title.name.value(), name.value());
        assert_eq!(title.description.value(), description.value());
        assert_eq!(title.color.value(), color.value());
        assert_eq!(title.strength.value(), strength.value());
        assert_eq!(title.intellect.value(), intellect.value());
        assert_eq!(title.endurance.value(), endurance.value());
        assert_eq!(title.dexterity.value(), dexterity.value());
        assert_eq!(title.wisdom.value(), wisdom.value());
        assert_eq!(title.luck.value(), luck.value());
        assert_eq!(title.role_id.get_id(), role_id.get_id());
    }

    #[test]
    fn test_getters_and_setters() {
        let mut title = Title::default();

        let id = TitleId::new(uuid::Uuid::now_v7());
        let name = TitleName::new("Test Title");
        let description = TitleDescription::new("Test Description");
        let color = TitleColor::new("#FF0000");
        let strength = TitleStrength::new(10);
        let intellect = TitleIntellect::new(20);
        let endurance = TitleEndurance::new(30);
        let dexterity = TitleDexterity::new(40);
        let wisdom = TitleWisdom::new(50);
        let luck = TitleLuck::new(60);
        let role_id = RoleId::new(uuid::Uuid::now_v7());

        title.set_id(id.clone());
        title.set_name(name.clone());
        title.set_description(description.clone());
        title.set_color(color.clone());
        title.set_strength(strength.clone());
        title.set_intellect(intellect.clone());
        title.set_endurance(endurance.clone());
        title.set_dexterity(dexterity.clone());
        title.set_wisdom(wisdom.clone());
        title.set_luck(luck.clone());
        title.set_role_id(role_id.clone());

        assert_eq!(title.id().get_id(), id.get_id());
        assert_eq!(title.name().value(), name.value());
        assert_eq!(title.description().value(), description.value());
        assert_eq!(title.color().value(), color.value());
        assert_eq!(title.strength().value(), strength.value());
        assert_eq!(title.intellect().value(), intellect.value());
        assert_eq!(title.endurance().value(), endurance.value());
        assert_eq!(title.dexterity().value(), dexterity.value());
        assert_eq!(title.wisdom().value(), wisdom.value());
        assert_eq!(title.luck().value(), luck.value());
        assert_eq!(title.role_id().get_id(), role_id.get_id());
    }
}
