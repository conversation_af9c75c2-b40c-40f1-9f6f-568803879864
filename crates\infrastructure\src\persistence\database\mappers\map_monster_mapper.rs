use reforged_domain::models::map_monster::entity::MapMonster;
use reforged_domain::models::map_monster::value_object::{MapMonsterId, MapMonsterFrame};
use reforged_domain::models::map::value_object::MapId;
use reforged_domain::models::monster::value_object::MonsterId;

#[derive(Debug)]
pub struct MapMonsterDbModelMapper(super::super::models::map_monsters::Model);

impl MapMonsterDbModelMapper {
    pub fn new(model: super::super::models::map_monsters::Model) -> Self {
        Self(model)
    }
}

impl From<MapMonsterDbModelMapper> for MapMonster {
    fn from(value: MapMonsterDbModelMapper) -> Self {
        let model = value.0;

        MapMonster::builder()
            .id(MapMonsterId::new(model.id))
            .map_id(MapId::new(model.map_id))
            .monster_id(MonsterId::new(model.monster_id))
            .frame(MapMonsterFrame::new(model.frame))
            .build()
    }
}
