//! `SeaORM` Entity, @generated by sea-orm-codegen 1.1.0

use sea_orm::entity::prelude::*;
use serde::Deserialize;

#[derive(<PERSON><PERSON>, Debug, PartialEq, DeriveEntityModel, Eq, Deserialize)]
#[sea_orm(table_name = "aura_effects")]
pub struct Model {
    #[sea_orm(primary_key, auto_increment = false)]
    pub id: Uuid,
    pub aura_id: Uuid,
    pub stat: String,
    pub value: Decimal,
    pub r#type: String,
}

#[derive(Co<PERSON>, <PERSON><PERSON>, Debug, EnumIter, DeriveRelation)]
pub enum Relation {
    #[sea_orm(
        belongs_to = "super::auras::Entity",
        from = "Column::AuraId",
        to = "super::auras::Column::Id",
        on_update = "Cascade",
        on_delete = "Cascade"
    )]
    Auras,
}

impl Related<super::auras::Entity> for Entity {
    fn to() -> RelationDef {
        Relation::Auras.def()
    }
}

impl ActiveModelBehavior for ActiveModel {}
