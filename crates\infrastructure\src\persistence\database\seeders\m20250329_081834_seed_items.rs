use crate::models;
use sea_orm::{ActiveModelTrait, IntoActiveModel};
use sea_orm_migration::prelude::*;

#[derive(DeriveMigrationName)]
pub struct Migration;

#[async_trait::async_trait]
impl MigrationTrait for Migration {
    async fn up(&self, manager: &SchemaManager) -> Result<(), DbErr> {
        let db = manager.get_connection();

        let items_rarities = include_str!("../json/items_rarities.json");
        let item_rarities: Vec<models::item_rarities::Model> =
            serde_json::from_str(items_rarities).unwrap();

        for model in item_rarities {
            let item_rarity = model.into_active_model();
            item_rarity.insert(db).await?;
        }

        let items = include_str!("../json/items.json");
        let items: Vec<models::items::Model> = serde_json::from_str(items).unwrap();

        for model in items {
            let item = model.into_active_model();
            item.insert(db).await?;
        }

        let item_requirements = include_str!("../json/items_requirements.json");
        let item_requirements: Vec<models::item_requirements::Model> =
            serde_json::from_str(item_requirements).unwrap();

        for model in item_requirements {
            let item_requirement = model.into_active_model();
            item_requirement.insert(db).await?;
        }

        let item_bundles = include_str!("../json/items_bundles.json");
        let item_bundles: Vec<models::item_bundles::Model> =
            serde_json::from_str(item_bundles).unwrap();

        for model in item_bundles {
            let item_bundle = model.into_active_model();
            item_bundle.insert(db).await?;
        }

        let item_effect = include_str!("../json/items_effects.json");
        let item_effect: Vec<models::item_effects::Model> =
            serde_json::from_str(item_effect).unwrap();

        for model in item_effect {
            let item_effect = model.into_active_model();
            item_effect.insert(db).await?;
        }

        let item_skills = include_str!("../json/items_skills.json");
        let item_skills: Vec<models::item_skills::Model> =
            serde_json::from_str(item_skills).unwrap();

        for model in item_skills {
            let item_skill = model.into_active_model();
            item_skill.insert(db).await?;
        }

        let wheel_rewards = include_str!("../json/wheels_rewards.json");
        let wheel_rewards: Vec<models::wheel_rewards::Model> =
            serde_json::from_str(wheel_rewards).unwrap();

        for model in wheel_rewards {
            let wheel_reward = model.into_active_model();
            wheel_reward.insert(db).await?;
        }

        Ok(())
    }

    async fn down(&self, _manager: &SchemaManager) -> Result<(), DbErr> {
        Ok(())
    }
}
