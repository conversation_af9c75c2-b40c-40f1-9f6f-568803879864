use std::time::Duration;

use sea_orm::{ConnectOptions, Database, DatabaseConnection, DbErr};

pub async fn create_pool(connection: &str) -> Result<DatabaseConnection, DbErr> {
    let mut options = ConnectOptions::new(connection.to_string());

    options
        .min_connections(5)
        .max_connections(100)
        .connect_timeout(Duration::from_secs(8))
        .acquire_timeout(Duration::from_secs(8))
        .idle_timeout(Duration::from_secs(8))
        .max_lifetime(Duration::from_secs(8));

    let pool = Database::connect(options).await?;
    Ok(pool)
}
