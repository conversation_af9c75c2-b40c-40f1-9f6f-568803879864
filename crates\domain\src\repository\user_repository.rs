use crate::{
    error::RepositoryError,
    models::{
        profile::{entity::Profile, value_object::Gender},
        user::{entity::User, value_object::UserId},
    },
};
use async_trait::async_trait;

#[mockall::automock]
#[async_trait]
pub trait UserRepository: Send + Sync {
    async fn save(&self, user: &User, gender: &Gender) -> Result<(), RepositoryError>;
    async fn update(&self, user: &User) -> Result<(), RepositoryError>;
    async fn delete(&self, id: &UserId) -> Result<(), RepositoryError>;
}

#[mockall::automock]
#[async_trait]
pub trait UserReadRepository: Send + Sync {
    async fn get_latest_id(&self) -> Result<UserId, RepositoryError>;
    async fn find_by_id(
        &self,
        id: &UserId,
    ) -> Result<Option<(User, Option<Profile>)>, RepositoryError>;
    async fn find_by_username(
        &self,
        username: &str,
    ) -> Result<Option<(User, Option<Profile>)>, RepositoryError>;
    async fn find_by_email(
        &self,
        email: &str,
    ) -> Result<Option<(User, Option<Profile>)>, RepositoryError>;
}
