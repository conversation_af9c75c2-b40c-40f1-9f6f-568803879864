use crate::{
    error::ApplicationError,
    queries::{
        class_category_queries::ListClassCategoryPaginatedQuery,
        class_category_query_handlers::{ClassCategory<PERSON><PERSON>y<PERSON><PERSON><PERSON>, ClassCategoryResponse},
    },
    traits::Query<PERSON><PERSON><PERSON>,
};

pub struct GetClassCategoriesUsecase {
    class_category_query_handler: ClassCategoryQueryHandler,
}

impl GetClassCategoriesUsecase {
    pub fn new(class_category_query_handler: ClassCategoryQueryHandler) -> Self {
        Self {
            class_category_query_handler,
        }
    }
}

impl GetClassCategoriesUsecase {
    pub async fn execute(
        &self,
        page: u32,
        limit: u32,
    ) -> Result<Vec<ClassCategoryResponse>, ApplicationError> {
        let query = ListClassCategoryPaginatedQuery { page, limit };
        let categories = self.class_category_query_handler.handle(query).await?;

        Ok(categories)
    }
}
