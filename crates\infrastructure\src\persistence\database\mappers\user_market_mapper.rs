use reforged_domain::models::user_market::entity::UserMarket;
use reforged_domain::models::user_market::value_object::{UserMarketId, MarketType};
use reforged_domain::models::user::value_object::UserId;
use reforged_domain::models::item::value_object::ItemId;
use reforged_domain::models::enhancement::value_object::EnhancementId;
use chrono::{DateTime, Utc};

#[derive(Debug)]
pub struct UserMarketDbModelMapper(super::super::models::user_markets::Model);

impl UserMarketDbModelMapper {
    pub fn new(model: super::super::models::user_markets::Model) -> Self {
        Self(model)
    }
}

impl From<UserMarketDbModelMapper> for UserMarket {
    fn from(value: UserMarketDbModelMapper) -> Self {
        let model = value.0;
        
        let market_type = match model.market_type.as_str() {
            "Vending" => MarketType::Vending,
            _ => MarketType::Auction,
        };
        
        UserMarket::builder()
            .id(UserMarketId::new(model.id))
            .user_id(UserId::new(model.user_id))
            .item_id(ItemId::new(model.item_id))
            .enh_id(EnhancementId::new(model.enh_id))
            .datetime(DateTime::<Utc>::from_naive_utc_and_offset(model.datetime, Utc).into())
            .maybe_buyer_id(model.buyer_id.map(UserId::new))
            .coins(model.coins.into())
            .gold(model.gold.into())
            .quantity(model.quantity.into())
            .status(model.status.into())
            .market_type(market_type)
            .build()
    }
}
