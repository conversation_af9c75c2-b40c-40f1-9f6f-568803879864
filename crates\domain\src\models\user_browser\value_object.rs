use reforged_shared::{UuidId, Value};

use super::entity::UserBrowser;

pub type UserBrowserId = UuidId<UserBrowser>;

#[derive(Debug, PartialEq, Eq, PartialOrd, Ord, Clone, Default)]
pub struct Referer(String);

impl Referer {
    pub fn new(value: impl Into<String>) -> Self {
        Self(value.into())
    }
}

impl Value<String> for Referer {
    fn value(&self) -> String {
        self.0.clone()
    }
}

impl From<String> for Referer {
    fn from(value: String) -> Self {
        Self(value)
    }
}

impl From<&str> for Referer {
    fn from(value: &str) -> Self {
        Self(value.to_string())
    }
}

#[derive(Debug, PartialEq, Eq, PartialOrd, Ord, Clone, Default)]

pub struct Engine(String);

impl Engine {
    pub fn new(value: impl Into<String>) -> Self {
        Self(value.into())
    }
}

impl Value<String> for Engine {
    fn value(&self) -> String {
        self.0.clone()
    }
}

impl From<String> for Engine {
    fn from(value: String) -> Self {
        Self(value)
    }
}

impl From<&str> for Engine {
    fn from(value: &str) -> Self {
        Self(value.to_string())
    }
}

#[derive(Debug, PartialEq, Eq, PartialOrd, Ord, Clone, Default)]

pub struct Platform(String);

impl Platform {
    pub fn new(value: impl Into<String>) -> Self {
        Self(value.into())
    }
}

impl Value<String> for Platform {
    fn value(&self) -> String {
        self.0.clone()
    }
}

impl From<String> for Platform {
    fn from(value: String) -> Self {
        Self(value)
    }
}

impl From<&str> for Platform {
    fn from(value: &str) -> Self {
        Self(value.to_string())
    }
}

#[derive(Debug, PartialEq, Eq, PartialOrd, Ord, Clone, Default)]

pub struct Browser(String);

impl Browser {
    pub fn new(value: impl Into<String>) -> Self {
        Self(value.into())
    }
}

impl Value<String> for Browser {
    fn value(&self) -> String {
        self.0.clone()
    }
}

impl From<String> for Browser {
    fn from(value: String) -> Self {
        Self(value)
    }
}

impl From<&str> for Browser {
    fn from(value: &str) -> Self {
        Self(value.to_string())
    }
}
