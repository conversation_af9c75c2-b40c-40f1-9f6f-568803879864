[package]
name = "reforged-infrastructure"
version = "0.1.0"
edition = "2024"

[dependencies]
actix.workspace = true
actix-broker.workspace = true
argon2.workspace = true
async-trait.workspace = true
cf-turnstile.workspace = true
chrono.workspace = true
dotenvy.workspace = true
esrs.workspace = true
futures.workspace = true
futures-util.workspace = true
lapin.workspace = true
mail-send.workspace = true
num-traits.workspace = true
pasetors.workspace = true
reforged-application = { path = "../application" }
reforged-domain = { path = "../domain" }
reforged-shared = { path = "../shared" }
rustls.workspace = true

sea-orm.workspace = true
sea-orm-migration.workspace = true
serde.workspace = true
serde_json.workspace = true
thiserror.workspace = true
time.workspace = true
tokio.workspace = true
tokio-rustls.workspace = true
tracing.workspace = true
tracing-log.workspace = true
tracing-subscriber.workspace = true
uuid.workspace = true
