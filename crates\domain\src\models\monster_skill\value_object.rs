use reforged_shared::{UuidId, Value};

use super::entity::MonsterSkill;

pub type MonsterSkillId = UuidId<MonsterSkill>;

#[derive(Debug, PartialEq, Eq, <PERSON>ial<PERSON>rd, Or<PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>)]

pub struct MonsterSkills(i32);

impl MonsterSkills {
    pub fn new(skills: i32) -> Self {
        Self(skills)
    }
}

impl Value<i32> for MonsterSkills {
    fn value(&self) -> i32 {
        self.0
    }
}

impl From<i32> for MonsterSkills {
    fn from(skills: i32) -> Self {
        Self(skills)
    }
}
