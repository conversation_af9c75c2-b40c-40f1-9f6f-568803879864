INSERT INTO "classes" ("id", "name", "category", "description", "mana_regeneration_methods", "stats_description") VALUES (1, 'Nytherian', 'C1', 'Nytherian are a basic class for new players.', 'Nytherian gain mana when they strike an enemy in combat (more effective on crits)', 'Light');
INSERT INTO "classes" ("id", "name", "category", "description", "mana_regeneration_methods", "stats_description") VALUES (2, 'Beta Berserker', 'M1', 'Recommended enhancement: Fighter. Special Berserker class available only to characters who played in the Beta version!', 'Berserkers gain mana from all hit landed in combat, and especially on crits. The amount depends on damage relative to their "own HP" total.', 'Berserker Class');
INSERT INTO "classes" ("id", "name", "category", "description", "mana_regeneration_methods", "stats_description") VALUES (3, 'Bloondermon', 'S1', 'Bloondermon created for every player who was brave to pass the /Jungle quests', '<PERSON><PERSON> is restored instantly.', 'Farmers!');
INSERT INTO "classes" ("id", "name", "category", "description", "mana_regeneration_methods", "stats_description") VALUES (4, 'Alpha Pirate', 'S1', 'Pirates are opportunistic rogues and treasure hunters of the sea. Combat might not always go their way but they will do the best they can to make the most of any combat situation they find themselves in. You never really know what move a pirate will make next, and just as often, they don\''t know either.', 'Alpha Pirate gain mana from all hits landed in combat, and especially on crits. The amount depends on damage relative to *their own* HP total.', 'Lucky');
INSERT INTO "classes" ("id", "name", "category", "description", "mana_regeneration_methods", "stats_description") VALUES (5, 'Warrior', 'M1', 'gain mana from all hits landed in combat, and especially on crits. The amount depends on damage relative to *their own* HP total.', 'Warrior gain mana from all hits landed in combat, and especially on crits. The amount depends on damage relative to *their own* HP total.', 'Fighter');
INSERT INTO "classes" ("id", "name", "category", "description", "mana_regeneration_methods", "stats_description") VALUES (6, 'Mage', 'C1', 'Magic courses through your veins. Your arcane powers enable you to cast powerful spells.', 'Mages gain mana from all hits landed in combat, and especially on crits. The amount depends on damage relative to *their own* HP total.', 'Wizard');
INSERT INTO "classes" ("id", "name", "category", "description", "mana_regeneration_methods", "stats_description") VALUES (7, 'Healer', 'C2', 'Healers are servants of good who use their powers to aid the sick, weak, and injured. Their powerful healing magic is often the difference between a groups victory or doom.', 'Healer gain mana from all hits landed in combat, and especially on crits. The amount depends on damage relative to *their own* HP total.', 'Healers');
INSERT INTO "classes" ("id", "name", "category", "description", "mana_regeneration_methods", "stats_description") VALUES (8, 'Rogue', 'M2', 'Rogues follow their own set of rules. With a combination of speed, cunning and poisons, you take your enemies by surprise.', 'Rogue gain mana from all hits landed in combat, and especially on crits. The amount depends on damage relative to *their own* HP total.', 'Thief');
INSERT INTO "classes" ("id", "name", "category", "description", "mana_regeneration_methods", "stats_description") VALUES (9, 'Ancient Hero', 'M1', 'You are truly one of Lore''s greatest heroes! Because of your support and efforts in battle, the world will be saved!', 'Strike an enemy in combat (more effective on crits)', 'Lucky');
INSERT INTO "classes" ("id", "name", "category", "description", "mana_regeneration_methods", "stats_description") VALUES (10, 'Void HighLord', 'M1', 'Only the strongest, toughest, most dedicated (and insane) members of the Nulgath Nation can survive the trials.', 'Void Highlords gain mana from all hits landed in combat, and especially on crits. The amount depends on damage relative to "their own" HP total.', 'Fighter');
INSERT INTO "classes" ("id", "name", "category", "description", "mana_regeneration_methods", "stats_description") VALUES (11, 'Master Crowled', 'S1', 'Recommended enhancement: Wizard. You have honed your psychic abilities to work in unison with your weapons to create the ultimate psionic warrior! Thanks for your Membership support and enjoy.', 'Strike an enemy in combat (more effective on crits)
Are struck by an enemy in combat', 'La Ñema Pela');
INSERT INTO "classes" ("id", "name", "category", "description", "mana_regeneration_methods", "stats_description") VALUES (12, 'Soul King', 'M1', 'Wait me...', 'Soul King gain mana when they strike an enemy in combat (more effective on crits)', 'Lucky');
INSERT INTO "classes" ("id", "name", "category", "description", "mana_regeneration_methods", "stats_description") VALUES (13, 'Fallen Angel', 'M1', 'Fallen Angels are the strategists of the Nation. Trained by Revontheus to wield Crystal Weaponry, they excel at fighting multiple enemies with arrows forged by Nulgath. Fallen Angels have various ways of taking their opponents down.', 'Spellbreakers gain mana when they strike an enemy in combat (more effective on crits)', 'Lucky');
INSERT INTO "classes" ("id", "name", "category", "description", "mana_regeneration_methods", "stats_description") VALUES (14, 'Dark Paladin', 'M1', 'Dark Paladin are the dishonest and evil protectors of… Pure Evil and WICKEDNESS! Heh, but really… You used to be a Paladin and now you\''re the undead minion of a seemingly invincible armored lich.', 'Dark Paladin gain mana from all hits landed in combat, and especially on crits. The amount depends on damage relative to *their own* HP total.', 'Wizard');
INSERT INTO "classes" ("id", "name", "category", "description", "mana_regeneration_methods", "stats_description") VALUES (15, 'Pinkomancer', 'S1', 'As the natural enemy to Paladins, Pinkomancers are powerful but adorably twisted magic users who command undead buddies!', 'Strike an enemy in combat (more effective on crits).
Are struck by an enemy in combat.', 'Strike an enemy in combat (more effective on crits).
Are struck by an enemy in combat.');
INSERT INTO "classes" ("id", "name", "category", "description", "mana_regeneration_methods", "stats_description") VALUES (16, 'Legion Revenant', 'M1', 'Steeped in darkness, Legion Revenants derive their power from the essence responsible for the Legion''s un-death. The Legion flows through them, and they are the Legion.', 'Legion Revenants regain mana from all hits landed in combat, more if it''s a critical strike. The amount of mana regained is based on the amount of damage done relative to their own HP total.', 'Wizard');
INSERT INTO "classes" ("id", "name", "category", "description", "mana_regeneration_methods", "stats_description") VALUES (17, 'Dark Lord', 'M1', 'God Gamer is made out of all your favorite game consoles. You''d be a hit at parties. I hope you have enough controllers for everyone.', 'Dark Lord regain mana from all hits landed in combat, more if it''s a critical strike. The amount of mana regained is based on the amount of damage done relative to their own HP total.', 'Lucky');
INSERT INTO "classes" ("id", "name", "category", "description", "mana_regeneration_methods", "stats_description") VALUES (18, 'Master of Moglins', 'M1', 'You have earned your place as a Master of Moglins and can call on them to assist you and your allies in battle!', 'Master of Moglins gain mana from all hits landed in combat, and especially on critical hits. The amount depends on damage relative to *their own* HP total.', 'Lucky');
