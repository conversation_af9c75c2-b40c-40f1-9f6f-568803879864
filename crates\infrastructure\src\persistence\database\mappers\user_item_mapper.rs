use num_traits::ToPrimitive;
use reforged_domain::models::enhancement::value_object::EnhancementId;
use reforged_domain::models::item::value_object::ItemId;
use reforged_domain::models::user::value_object::UserId;
use reforged_domain::models::user_item::value_object::{
    UserItemBank, UserItemBind, UserItemEquipped,
};
use reforged_domain::models::user_item::{
    entity::UserItem,
    value_object::{UserItemDate, UserItemId, UserItemQuantity},
};

#[derive(Debug)]
pub struct UserItemDbModelMapper(super::super::models::user_items::Model);

impl UserItemDbModelMapper {
    pub fn new(user_item: super::super::models::user_items::Model) -> Self {
        Self(user_item)
    }
}

impl From<UserItemDbModelMapper> for UserItem {
    fn from(value: UserItemDbModelMapper) -> Self {
        let user_item_model = value.0;

        let id = UserItemId::new(user_item_model.id);

        UserItem::builder()
            .id(id)
            .user_id(UserId::new(user_item_model.user_id))
            .item_id(ItemId::new(user_item_model.item_id))
            .enh_id(EnhancementId::new(user_item_model.enh_id))
            .quantity(UserItemQuantity::new(
                user_item_model.quantity.to_u64().unwrap_or(0),
            ))
            .date_purchased(UserItemDate::new(user_item_model.date_purchased.and_utc()))
            .equipped(UserItemEquipped::from(user_item_model.equipped))
            .bank(UserItemBank::from(user_item_model.bank))
            .bind(UserItemBind::from(user_item_model.bind))
            .build()
    }
}
