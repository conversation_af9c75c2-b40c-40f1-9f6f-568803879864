use crate::models::user::value_object::UserId;
use crate::models::user_quest::value_object::*;
use getset::{<PERSON><PERSON>, Set<PERSON>};

#[derive(Debug, <PERSON><PERSON>, Default, bon::Build<PERSON>, Get<PERSON>, Setters)]
#[getset(get = "pub", set = "pub")]
pub struct UserQuest {
    id: UserQuestId,
    pub user_id: UserId,
    pub quests1: UserQuestQuests1,
    pub quests2: UserQuestQuests2,
    pub quests3: UserQuestQuests3,
    pub quests4: UserQuestQuests4,
    pub quests5: UserQuestQuests5,
    pub daily_quests0: UserQuestDailyQuests0,
    pub daily_quests1: UserQuestDailyQuests1,
    pub daily_quests2: UserQuestDailyQuests2,
    pub monthly_quests0: UserQuestMonthlyQuests0,
    pub daily_ads: UserQuestDailyAds,
}

impl UserQuest {
    pub fn new(
        id: UserQuestId,
        user_id: UserId,
        quests1: UserQuestQuests1,
        quests2: UserQuestQuests2,
        quests3: UserQuestQuests3,
        quests4: UserQuestQuests4,
        quests5: UserQuestQuests5,
        daily_quests0: UserQuestDailyQuests0,
        daily_quests1: UserQuestDailyQuests1,
        daily_quests2: UserQuestDailyQuests2,
        monthly_quests0: UserQuestMonthlyQuests0,
        daily_ads: UserQuestDailyAds,
    ) -> Self {
        Self {
            id,
            user_id,
            quests1,
            quests2,
            quests3,
            quests4,
            quests5,
            daily_quests0,
            daily_quests1,
            daily_quests2,
            monthly_quests0,
            daily_ads,
        }
    }
}

#[cfg(test)]
mod tests {
    use reforged_shared::{IdTrait, Value};

    use super::*;

    #[test]
    fn test_user_quest_new() {
        let id = UserQuestId::new(uuid::Uuid::now_v7());
        let user_id = UserId::new(uuid::Uuid::now_v7());
        let quests1 = UserQuestQuests1::new("test1");
        let quests2 = UserQuestQuests2::new("test2");
        let quests3 = UserQuestQuests3::new("test3");
        let quests4 = UserQuestQuests4::new("test4");
        let quests5 = UserQuestQuests5::new("test5");
        let daily_quests0 = UserQuestDailyQuests0::new(0);
        let daily_quests1 = UserQuestDailyQuests1::new(0);
        let daily_quests2 = UserQuestDailyQuests2::new(0);
        let monthly_quests0 = UserQuestMonthlyQuests0::new(0);
        let daily_ads = UserQuestDailyAds::new(0);

        let user_quest = UserQuest::new(
            id.clone(),
            user_id.clone(),
            quests1.clone(),
            quests2.clone(),
            quests3.clone(),
            quests4.clone(),
            quests5.clone(),
            daily_quests0.clone(),
            daily_quests1.clone(),
            daily_quests2.clone(),
            monthly_quests0.clone(),
            daily_ads.clone(),
        );

        assert_eq!(user_quest.id().get_id(), id.get_id());
        assert_eq!(user_quest.user_id.get_id(), user_id.get_id());
        assert_eq!(user_quest.quests1.value(), quests1.value());
        assert_eq!(user_quest.quests2.value(), quests2.value());
        assert_eq!(user_quest.quests3.value(), quests3.value());
        assert_eq!(user_quest.quests4.value(), quests4.value());
        assert_eq!(user_quest.quests5.value(), quests5.value());
        assert_eq!(user_quest.daily_quests0.value(), daily_quests0.value());
        assert_eq!(user_quest.daily_quests1.value(), daily_quests1.value());
        assert_eq!(user_quest.daily_quests2.value(), daily_quests2.value());
        assert_eq!(user_quest.monthly_quests0.value(), monthly_quests0.value());
        assert_eq!(user_quest.daily_ads.value(), daily_ads.value());
    }

    #[test]
    fn test_user_quest_builder() {
        let id = UserQuestId::new(uuid::Uuid::now_v7());
        let user_id = UserId::new(uuid::Uuid::now_v7());
        let quests1 = UserQuestQuests1::new("test1");
        let quests2 = UserQuestQuests2::new("test2");
        let quests3 = UserQuestQuests3::new("test3");
        let quests4 = UserQuestQuests4::new("test4");
        let quests5 = UserQuestQuests5::new("test5");
        let daily_quests0 = UserQuestDailyQuests0::new(0);
        let daily_quests1 = UserQuestDailyQuests1::new(0);
        let daily_quests2 = UserQuestDailyQuests2::new(0);
        let monthly_quests0 = UserQuestMonthlyQuests0::new(0);
        let daily_ads = UserQuestDailyAds::new(0);

        let user_quest = UserQuest::builder()
            .id(id.clone())
            .user_id(user_id.clone())
            .quests1(quests1.clone())
            .quests2(quests2.clone())
            .quests3(quests3.clone())
            .quests4(quests4.clone())
            .quests5(quests5.clone())
            .daily_quests0(daily_quests0.clone())
            .daily_quests1(daily_quests1.clone())
            .daily_quests2(daily_quests2.clone())
            .monthly_quests0(monthly_quests0.clone())
            .daily_ads(daily_ads.clone())
            .build();

        assert_eq!(user_quest.id().get_id(), id.get_id());
        assert_eq!(user_quest.user_id.get_id(), user_id.get_id());
        assert_eq!(user_quest.quests1.value(), quests1.value());
        assert_eq!(user_quest.quests2.value(), quests2.value());
        assert_eq!(user_quest.quests3.value(), quests3.value());
        assert_eq!(user_quest.quests4.value(), quests4.value());
        assert_eq!(user_quest.quests5.value(), quests5.value());
        assert_eq!(user_quest.daily_quests0.value(), daily_quests0.value());
        assert_eq!(user_quest.daily_quests1.value(), daily_quests1.value());
        assert_eq!(user_quest.daily_quests2.value(), daily_quests2.value());
        assert_eq!(user_quest.monthly_quests0.value(), monthly_quests0.value());
        assert_eq!(user_quest.daily_ads.value(), daily_ads.value());
    }

    #[test]
    fn test_getters_and_setters() {
        let mut user_quest = UserQuest::default();

        let id = UserQuestId::new(uuid::Uuid::now_v7());
        let user_id = UserId::new(uuid::Uuid::now_v7());
        let quests1 = UserQuestQuests1::new("test1");
        let quests2 = UserQuestQuests2::new("test2");
        let quests3 = UserQuestQuests3::new("test3");
        let quests4 = UserQuestQuests4::new("test4");
        let quests5 = UserQuestQuests5::new("test5");
        let daily_quests0 = UserQuestDailyQuests0::new(0);
        let daily_quests1 = UserQuestDailyQuests1::new(0);
        let daily_quests2 = UserQuestDailyQuests2::new(0);
        let monthly_quests0 = UserQuestMonthlyQuests0::new(0);
        let daily_ads = UserQuestDailyAds::new(0);

        user_quest.set_id(id.clone());
        user_quest.set_user_id(user_id.clone());
        user_quest.set_quests1(quests1.clone());
        user_quest.set_quests2(quests2.clone());
        user_quest.set_quests3(quests3.clone());
        user_quest.set_quests4(quests4.clone());
        user_quest.set_quests5(quests5.clone());
        user_quest.set_daily_quests0(daily_quests0.clone());
        user_quest.set_daily_quests1(daily_quests1.clone());
        user_quest.set_daily_quests2(daily_quests2.clone());
        user_quest.set_monthly_quests0(monthly_quests0.clone());
        user_quest.set_daily_ads(daily_ads.clone());

        assert_eq!(user_quest.id().get_id(), id.get_id());
        assert_eq!(user_quest.user_id().get_id(), user_id.get_id());
        assert_eq!(user_quest.quests1().value(), quests1.value());
        assert_eq!(user_quest.quests2().value(), quests2.value());
        assert_eq!(user_quest.quests3().value(), quests3.value());
        assert_eq!(user_quest.quests4().value(), quests4.value());
        assert_eq!(user_quest.quests5().value(), quests5.value());
        assert_eq!(user_quest.daily_quests0().value(), daily_quests0.value());
        assert_eq!(user_quest.daily_quests1().value(), daily_quests1.value());
        assert_eq!(user_quest.daily_quests2().value(), daily_quests2.value());
        assert_eq!(
            user_quest.monthly_quests0().value(),
            monthly_quests0.value()
        );
        assert_eq!(user_quest.daily_ads().value(), daily_ads.value());
    }
}
