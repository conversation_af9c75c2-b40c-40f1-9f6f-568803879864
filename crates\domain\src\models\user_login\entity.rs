use getset::{<PERSON><PERSON>, <PERSON><PERSON>};

use crate::models::user::value_object::UserId;

use super::value_object::{Address, Location, LoginDate, Status, UserLoginId};

#[derive(<PERSON>bu<PERSON>, <PERSON><PERSON>, Default, bon::<PERSON><PERSON><PERSON>, <PERSON><PERSON>, Setters)]
#[getset(get = "pub", set = "pub")]
pub struct UserLogin {
    id: UserLoginId,
    user_id: UserId,
    location: Location,
    status: Status,
    address: Address,
    date: LoginDate,
}

impl UserLogin {
    pub fn new(
        id: UserLoginId,
        user_id: UserId,
        location: Location,
        status: Status,
        address: Address,
        date: LoginDate,
    ) -> Self {
        Self {
            id,
            user_id,
            location,
            status,
            address,
            date,
        }
    }
}

#[cfg(test)]
mod tests {
    use chrono::Utc;
    use reforged_shared::{IdTrait, Value};

    use super::*;

    #[test]
    fn test_user_login_new() {
        let id = UserLoginId::new(uuid::Uuid::now_v7());
        let user_id = UserId::new(uuid::Uuid::now_v7());
        let location = Location::new("Game");
        let status = Status::new("Success");
        let address = Address::new("***********");
        let date = LoginDate::new(Utc::now());

        let user_login = UserLogin::new(
            id.clone(),
            user_id.clone(),
            location.clone(),
            status.clone(),
            address.clone(),
            date.clone(),
        );

        assert_eq!(user_login.id().get_id(), id.get_id());
        assert_eq!(user_login.user_id().get_id(), user_id.get_id());
        assert_eq!(user_login.location().value(), "Game");
        assert_eq!(user_login.status().value(), "Success");
        assert_eq!(user_login.address().value(), "***********");
    }

    #[test]
    fn test_user_login_builder() {
        let id = UserLoginId::new(uuid::Uuid::now_v7());
        let user_id = UserId::new(uuid::Uuid::now_v7());

        let user_login = UserLogin::builder()
            .id(id.clone())
            .user_id(user_id.clone())
            .location(Location::new("Loader"))
            .status(Status::new("Failed"))
            .address(Address::new("********"))
            .date(LoginDate::new(Utc::now()))
            .build();

        assert_eq!(user_login.id().get_id(), id.get_id());
        assert_eq!(user_login.user_id().get_id(), user_id.get_id());
        assert_eq!(user_login.location().value(), "Loader");
        assert_eq!(user_login.status().value(), "Failed");
        assert_eq!(user_login.address().value(), "********");
    }
}