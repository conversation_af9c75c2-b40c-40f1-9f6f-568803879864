use crate::models::guild::value_object::GuildId;
use crate::models::user_exp::value_object::{Experience, Level};
use getset::{<PERSON><PERSON>, Set<PERSON>};

use super::value_object::GuildLevelId;

#[derive(<PERSON>bu<PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, bon::<PERSON><PERSON><PERSON>, <PERSON><PERSON>, Setters)]
#[getset(get = "pub", set = "pub")]
pub struct GuildLevel {
    id: GuildLevelId,
    guild_id: GuildId,
    level: Level,
    exp: Experience,
}

impl GuildLevel {
    pub fn new(id: GuildLevelId, guild_id: GuildId, level: Level, exp: Experience) -> Self {
        Self {
            id,
            guild_id,
            level,
            exp,
        }
    }
}

#[cfg(test)]
mod tests {
    use reforged_shared::{IdTrait, Value};

    use super::*;

    #[test]
    fn test_guild_level_new() {
        let id = GuildLevelId::new(uuid::Uuid::now_v7());
        let guild_id = GuildId::new(uuid::Uuid::now_v7());
        let level = Level::new(5);
        let exp = Experience::new(1000);

        let guild_level = GuildLevel::new(id.clone(), guild_id.clone(), level.clone(), exp.clone());

        assert_eq!(guild_level.id().get_id(), id.get_id());
        assert_eq!(guild_level.guild_id().get_id(), guild_id.get_id());
        assert_eq!(guild_level.level().value(), level.value());
        assert_eq!(guild_level.exp().value(), exp.value());
    }

    #[test]
    fn test_guild_level_builder() {
        let id = GuildLevelId::new(uuid::Uuid::now_v7());
        let guild_id = GuildId::new(uuid::Uuid::now_v7());
        let level = Level::new(5);
        let exp = Experience::new(1000);

        let guild_level = GuildLevel::builder()
            .id(id.clone())
            .guild_id(guild_id.clone())
            .level(level.clone())
            .exp(exp.clone())
            .build();

        assert_eq!(guild_level.guild_id().get_id(), guild_id.get_id());
        assert_eq!(guild_level.level().value(), level.value());
        assert_eq!(guild_level.exp().value(), exp.value());
    }

    #[test]
    fn test_getters_and_setters() {
        let mut guild_level = GuildLevel::default();

        let id = GuildLevelId::new(uuid::Uuid::now_v7());
        let guild_id = GuildId::new(uuid::Uuid::now_v7());
        let level = Level::new(5);
        let exp = Experience::new(1000);

        guild_level.set_id(id.clone());
        guild_level.set_guild_id(guild_id.clone());
        guild_level.set_level(level.clone());
        guild_level.set_exp(exp.clone());

        assert_eq!(guild_level.id().get_id(), id.get_id());
        assert_eq!(guild_level.guild_id().get_id(), guild_id.get_id());
        assert_eq!(guild_level.level().value(), level.value());
        assert_eq!(guild_level.exp().value(), exp.value());
    }
}
