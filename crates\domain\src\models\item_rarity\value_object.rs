use reforged_shared::{UuidId, Value};
use std::fmt;

use super::entity::ItemRarity;

pub type ItemRarityId = UuidId<ItemRarity>;

#[derive(Debug, <PERSON><PERSON>, De<PERSON>ult, PartialEq, Eq)]
pub enum ItemRarityEnum {
    Unknown,
    #[default]
    Common,
    Weird,
    Awesome,
    OnePercentDrop,
    FivePercentDrop,
    BossDrop,
    Secret,
    Junk,
    Impossible,
    Artifact,
    LimitedTimeDrop,
    Dumb,
    Crazy,
    Expensive,
    Rare,
    Epic,
    ImportItem,
    SeasonalItem,
    SeasonalRare,
    EventItem,
    EventRare,
    LimitedRare,
    CollectorsRare,
    PromotionalItem,
    UltraRare,
    SuperMegaUltraRare,
    LegendaryItem,
}

impl fmt::Display for ItemRarityEnum {
    fn fmt(&self, f: &mut fmt::Formatter<'_>) -> fmt::Result {
        let name = match self {
            ItemRarityEnum::Unknown => "Unknown",
            ItemRarityEnum::Common => "Common",
            ItemRarityEnum::Weird => "Weird",
            ItemRarityEnum::Awesome => "Awesome",
            ItemRarityEnum::OnePercentDrop => "1% Drop",
            ItemRarityEnum::FivePercentDrop => "5% Drop",
            ItemRarityEnum::BossDrop => "Boss Drop",
            ItemRarityEnum::Secret => "Secret",
            ItemRarityEnum::Junk => "Junk",
            ItemRarityEnum::Impossible => "Impossible",
            ItemRarityEnum::Artifact => "Artifact",
            ItemRarityEnum::LimitedTimeDrop => "Limited Time Drop",
            ItemRarityEnum::Dumb => "Dumb",
            ItemRarityEnum::Crazy => "Crazy",
            ItemRarityEnum::Expensive => "Expensive",
            ItemRarityEnum::Rare => "Rare",
            ItemRarityEnum::Epic => "Epic",
            ItemRarityEnum::ImportItem => "Import Item",
            ItemRarityEnum::SeasonalItem => "Seasonal Item",
            ItemRarityEnum::SeasonalRare => "Seasonal Rare",
            ItemRarityEnum::EventItem => "Event Item",
            ItemRarityEnum::EventRare => "Event Rare",
            ItemRarityEnum::LimitedRare => "Limited Rare",
            ItemRarityEnum::CollectorsRare => "Collector's Rare",
            ItemRarityEnum::PromotionalItem => "Promotional Item",
            ItemRarityEnum::UltraRare => "Ultra Rare",
            ItemRarityEnum::SuperMegaUltraRare => "Super Mega Ultra Rare",
            ItemRarityEnum::LegendaryItem => "Legendary Item",
        };
        write!(f, "{}", name)
    }
}

impl From<&str> for ItemRarityEnum {
    fn from(value: &str) -> Self {
        match value {
            "Unknown" => ItemRarityEnum::Unknown,
            "Common" => ItemRarityEnum::Common,
            "Weird" => ItemRarityEnum::Weird,
            "Awesome" => ItemRarityEnum::Awesome,
            "1% Drop" => ItemRarityEnum::OnePercentDrop,
            "5% Drop" => ItemRarityEnum::FivePercentDrop,
            "Boss Drop" => ItemRarityEnum::BossDrop,
            "Secret" => ItemRarityEnum::Secret,
            "Junk" => ItemRarityEnum::Junk,
            "Impossible" => ItemRarityEnum::Impossible,
            "Artifact" => ItemRarityEnum::Artifact,
            "Limited Time Drop" => ItemRarityEnum::LimitedTimeDrop,
            "Dumb" => ItemRarityEnum::Dumb,
            "Crazy" => ItemRarityEnum::Crazy,
            "Expensive" => ItemRarityEnum::Expensive,
            "Rare" => ItemRarityEnum::Rare,
            "Epic" => ItemRarityEnum::Epic,
            "Import Item" => ItemRarityEnum::ImportItem,
            "Seasonal Item" => ItemRarityEnum::SeasonalItem,
            "Seasonal Rare" => ItemRarityEnum::SeasonalRare,
            "Event Item" => ItemRarityEnum::EventItem,
            "Event Rare" => ItemRarityEnum::EventRare,
            "Limited Rare" => ItemRarityEnum::LimitedRare,
            "Collector's Rare" => ItemRarityEnum::CollectorsRare,
            "Promotional Item" => ItemRarityEnum::PromotionalItem,
            "Ultra Rare" => ItemRarityEnum::UltraRare,
            "Super Mega Ultra Rare" => ItemRarityEnum::SuperMegaUltraRare,
            "Legendary Item" => ItemRarityEnum::LegendaryItem,
            _ => panic!("Invalid item rarity name: {}", value), // Or return a default/Unknown variant
        }
    }
}

impl Value<String> for ItemRarityEnum {
    fn value(&self) -> String {
        self.to_string()
    }
}
