//! `SeaORM` Entity, @generated by sea-orm-codegen 1.1.0

use sea_orm::entity::prelude::*;
use serde::Deserialize;

#[derive(<PERSON><PERSON>, Debug, PartialEq, DeriveEntityModel, Eq, Deserialize)]
#[sea_orm(table_name = "achievements")]
pub struct Model {
    #[sea_orm(primary_key, auto_increment = false)]
    pub id: Uuid,
    pub name: String,
    #[sea_orm(column_type = "Text")]
    pub description: String,
    pub file: String,
    pub category: String,
    pub show: bool,
}

#[derive(<PERSON><PERSON>, <PERSON><PERSON>, Debug, EnumIter, DeriveRelation)]
pub enum Relation {
    #[sea_orm(has_many = "super::user_achievements::Entity")]
    UserAchievements,
    #[sea_orm(has_many = "super::user_livedrops::Entity")]
    UserLivedrops,
}

impl Related<super::user_achievements::Entity> for Entity {
    fn to() -> RelationDef {
        Relation::UserAchievements.def()
    }
}

impl Related<super::user_livedrops::Entity> for Entity {
    fn to() -> RelationDef {
        Relation::UserLivedrops.def()
    }
}

impl ActiveModelBehavior for ActiveModel {}
