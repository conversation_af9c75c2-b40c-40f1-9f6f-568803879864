use serde::{Deserialize, Serialize};
use validator::<PERSON><PERSON><PERSON>;

#[derive(Debug, Serialize, Deserialize, Validate)]
pub struct CreateClassCategoryDTO {
    #[validate(length(
        min = 1,
        max = 255,
        message = "Name must be between 1 and 255 characters"
    ))]
    pub name: String,
    #[validate(length(
        min = 1,
        max = 255,
        message = "Category must be between 1 and 255 characters"
    ))]
    pub category: String,
    #[validate(range(
        min = 0.,
        max = 100_000.,
        message = "Strength must be between 0 and 100,000"
    ))]
    pub strength: f64,
    #[validate(range(
        min = 0.,
        max = 100_000.,
        message = "Endurance must be between 0 and 100,000"
    ))]
    pub endurance: f64,
    #[validate(range(
        min = 0.,
        max = 100_000.,
        message = "Dexterity must be between 0 and 100,000"
    ))]
    pub dexterity: f64,
    #[validate(range(
        min = 0.,
        max = 100_000.,
        message = "Intellect must be between 0 and 100,000"
    ))]
    pub intellect: f64,
    #[validate(range(
        min = 0.,
        max = 100_000.,
        message = "Wisdom must be between 0 and 100,000"
    ))]
    pub wisdom: f64,
    #[validate(range(
        min = 0.,
        max = 100_000.,
        message = "Luck must be between 0 and 100,000"
    ))]
    pub luck: f64,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct GetClassCategoriesQueryParams {
    pub page: Option<u32>,
    pub limit: Option<u32>,
}
