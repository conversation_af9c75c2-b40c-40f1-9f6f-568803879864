use chrono::{DateTime, Utc};
use reforged_shared::{UuidId, Value};

use super::entity::CMSArticle;

pub type CMSArticleId = UuidId<CMSArticle>;

#[derive(Debug, PartialEq, Eq, PartialOrd, Ord, Clone, Default)]

pub struct CMSArticleTitle(String);

impl CMSArticleTitle {
    pub fn new(title: impl Into<String>) -> Self {
        Self(title.into())
    }
}

impl Value<String> for CMSArticleTitle {
    fn value(&self) -> String {
        self.0.clone()
    }
}

impl From<String> for CMSArticleTitle {
    fn from(value: String) -> Self {
        Self(value)
    }
}

impl From<&str> for CMSArticleTitle {
    fn from(value: &str) -> Self {
        Self(value.to_string())
    }
}

#[derive(Debug, PartialEq, Eq, PartialOrd, Ord, <PERSON>lone, Default)]

pub struct CMSArticleContent(String);

impl CMSArticleContent {
    pub fn new(content: impl Into<String>) -> Self {
        Self(content.into())
    }
}

impl Value<String> for CMSArticleContent {
    fn value(&self) -> String {
        self.0.clone()
    }
}

impl From<String> for CMSArticleContent {
    fn from(value: String) -> Self {
        Self(value)
    }
}

impl From<&str> for CMSArticleContent {
    fn from(value: &str) -> Self {
        Self(value.to_string())
    }
}

#[derive(Debug, PartialEq, Eq, PartialOrd, Ord, Clone, Default)]

pub struct CMSArticleCreatedAt(DateTime<Utc>);

impl CMSArticleCreatedAt {
    pub fn new(created_at: DateTime<Utc>) -> Self {
        Self(created_at)
    }
}

impl Value<DateTime<Utc>> for CMSArticleCreatedAt {
    fn value(&self) -> DateTime<Utc> {
        self.0
    }
}

impl From<DateTime<Utc>> for CMSArticleCreatedAt {
    fn from(value: DateTime<Utc>) -> Self {
        Self(value)
    }
}

#[derive(Debug, PartialEq, Eq, PartialOrd, Ord, Clone, Default)]

pub struct CMSArticleImage(Option<String>);

impl CMSArticleImage {
    pub fn new(image: Option<impl Into<String>>) -> Self {
        Self(image.map(|i| i.into()))
    }
}

impl Value<Option<String>> for CMSArticleImage {
    fn value(&self) -> Option<String> {
        self.0.clone()
    }
}

impl From<Option<String>> for CMSArticleImage {
    fn from(value: Option<String>) -> Self {
        Self(value)
    }
}

impl From<Option<&str>> for CMSArticleImage {
    fn from(value: Option<&str>) -> Self {
        Self(value.map(|s| s.to_string()))
    }
}
