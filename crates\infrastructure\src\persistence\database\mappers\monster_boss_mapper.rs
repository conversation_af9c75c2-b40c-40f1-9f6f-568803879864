use reforged_domain::models::monster_boss::entity::MonsterB<PERSON>;
use reforged_domain::models::monster_boss::value_object::*;
use reforged_domain::models::monster::value_object::MonsterId;
use reforged_domain::models::map::value_object::MapId;

#[derive(Debug)]
pub struct MonsterBossDbModelMapper(super::super::models::monster_bosses::Model);

impl MonsterBossDbModelMapper {
    pub fn new(model: super::super::models::monster_bosses::Model) -> Self {
        Self(model)
    }
}

impl From<MonsterBossDbModelMapper> for MonsterBoss {
    fn from(value: MonsterBossDbModelMapper) -> Self {
        let model = value.0;

        MonsterBoss::builder()
            .id(MonsterBossId::new(model.id))
            .monster_id(MonsterId::new(model.monster_id))
            .map_id(MapId::new(model.map_id))
            .spawn_interval(MonsterBossSpawnInterval::new(model.spawn_interval.try_into().unwrap_or(0)))
            .time_limit(MonsterBossTimeLimit::new(model.time_limit.try_into().unwrap_or(0)))
            .kills(MonsterBossKills::new(model.kills.unwrap_or(0).try_into().unwrap_or(0)))
            .deaths(MonsterBossDeaths::new(model.deaths.unwrap_or(0).try_into().unwrap_or(0)))
            .death_time(MonsterBossDeathTime::new(model.death_time.and_utc().timestamp().try_into().unwrap_or(0)))
            .spawn_time(MonsterBossSpawnTime::new(model.spawn_time.and_utc().timestamp().try_into().unwrap_or(0)))
            .description(MonsterBossDescription::new(model.description.unwrap_or_default()))
            .build()
    }
}
