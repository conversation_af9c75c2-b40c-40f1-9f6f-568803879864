use reforged_domain::models::monster_boss::entity::<PERSON><PERSON><PERSON>;
use reforged_domain::models::monster_boss::value_object::MonsterBossId;
use reforged_domain::models::monster::value_object::MonsterId;

#[derive(Debug)]
pub struct MonsterBossDbModelMapper(super::super::models::monster_bosses::Model);

impl MonsterBossDbModelMapper {
    pub fn new(model: super::super::models::monster_bosses::Model) -> Self {
        Self(model)
    }
}

impl From<MonsterBossDbModelMapper> for MonsterBoss {
    fn from(value: MonsterBossDbModelMapper) -> Self {
        let model = value.0;
        
        MonsterBoss::builder()
            .id(MonsterBossId::new(model.id))
            .monster_id(MonsterId::new(model.monster_id))
            .hp(model.hp.into())
            .mp(model.mp.into())
            .build()
    }
}
