use reforged_domain::models::war::{
    entity::War,
    value_object::{WarId, WarMaxPoints, WarName, WarPoints},
};

#[derive(Debug)]
pub struct WarDbModelMapper(super::super::models::wars::Model);

impl WarDbModelMapper {
    pub fn new(war: super::super::models::wars::Model) -> Self {
        Self(war)
    }
}

impl From<WarDbModelMapper> for War {
    fn from(value: WarDbModelMapper) -> Self {
        let war_model = value.0;

        let id = WarId::new(war_model.id);

        War::builder()
            .id(id)
            .name(WarName::new(war_model.name))
            .points(WarPoints::new(war_model.points))
            .max_points(WarMaxPoints::new(war_model.max_points))
            .build()
    }
}
