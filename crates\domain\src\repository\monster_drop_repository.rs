use async_trait::async_trait;
use mockall::automock;

use crate::error::RepositoryError;
use crate::models::monster_drop::entity::MonsterDrop;
use crate::models::monster_drop::value_object::MonsterDropId;

#[automock]
#[async_trait]
pub trait MonsterDropRepository: Send + Sync {
    async fn save(&self, entity: &MonsterDrop) -> Result<(), RepositoryError>;
    async fn update(&self, entity: &MonsterDrop) -> Result<(), RepositoryError>;
    async fn delete(&self, id: &MonsterDropId) -> Result<(), RepositoryError>;
}

#[automock]
#[async_trait]
pub trait MonsterDropReadRepository: Send + Sync {
    async fn find_by_id(&self, id: &MonsterDropId) -> Result<Option<MonsterDrop>, RepositoryError>;
    async fn find_all(&self) -> Result<Vec<MonsterDrop>, RepositoryError>;
}
