use reforged_shared::{UuidId, Value};

use super::entity::HairShop;

pub type HairShopId = UuidId<HairShop>;

#[derive(Debug, PartialEq, Eq, PartialOrd, Ord, <PERSON><PERSON>, <PERSON><PERSON><PERSON>)]

pub struct HairShopName(String);

impl HairShopName {
    pub fn new(name: impl Into<String>) -> Self {
        Self(name.into())
    }
}

impl Value<String> for HairShopName {
    fn value(&self) -> String {
        self.0.clone()
    }
}

impl From<String> for HairShopName {
    fn from(value: String) -> Self {
        Self(value)
    }
}

impl From<&str> for HairShopName {
    fn from(value: &str) -> Self {
        Self(value.to_string())
    }
}
