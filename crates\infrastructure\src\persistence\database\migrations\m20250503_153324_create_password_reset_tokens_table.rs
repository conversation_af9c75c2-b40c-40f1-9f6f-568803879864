use sea_orm::Schema;
use sea_orm_migration::prelude::*;

use crate::models::prelude::PasswordResetTokens;

#[derive(DeriveMigrationName)]
pub struct Migration;

#[async_trait::async_trait]
impl MigrationTrait for Migration {
    async fn up(&self, manager: &SchemaManager) -> Result<(), DbErr> {
        let backend = manager.get_database_backend();
        let schema = Schema::new(backend);

        manager
            .create_table(schema.create_table_from_entity(PasswordResetTokens))
            .await?;

        Ok(())
    }

    async fn down(&self, manager: &SchemaManager) -> Result<(), DbErr> {
        manager
            .drop_table(Table::drop().table(PasswordResetTokens).to_owned())
            .await?;
        Ok(())
    }
}
