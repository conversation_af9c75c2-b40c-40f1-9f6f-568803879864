use super::entity::GameRateSetting;
use reforged_shared::{UuidId, Value as SharedValue};

pub type GameRateSettingId = UuidId<GameRateSetting>;

#[derive(Debug, PartialEq, PartialOrd, <PERSON>lone, Default)]
pub struct GameRateSettingName(String);

impl GameRateSettingName {
    pub fn new(name: impl Into<String>) -> Self {
        Self(name.into())
    }
}

impl SharedValue<String> for GameRateSettingName {
    fn value(&self) -> String {
        self.0.clone()
    }
}

impl From<String> for GameRateSettingName {
    fn from(value: String) -> Self {
        Self(value)
    }
}

impl From<&str> for GameRateSettingName {
    fn from(value: &str) -> Self {
        Self(value.to_string())
    }
}

#[derive(Debug, PartialEq, PartialOrd, Clone, Default)]

pub struct Value(f64);

impl Value {
    pub fn new(value: f64) -> Self {
        Self(value)
    }
}

impl SharedValue<f64> for Value {
    fn value(&self) -> f64 {
        self.0
    }
}

impl From<f64> for Value {
    fn from(value: f64) -> Self {
        Self(value)
    }
}
