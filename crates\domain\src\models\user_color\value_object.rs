use reforged_shared::{UuidId, Value};

use super::entity::UserColor;

pub type UserColorId = UuidId<UserColor>;

#[derive(Debug, PartialEq, Eq, PartialOrd, Ord, Clone, Default)]
pub struct ColorChat(String);

impl ColorChat {
    pub fn new(color: impl Into<String>) -> Self {
        Self(color.into())
    }
}

impl Value<String> for ColorChat {
    fn value(&self) -> String {
        self.0.clone()
    }
}

impl From<String> for ColorChat {
    fn from(value: String) -> Self {
        Self(value)
    }
}

impl From<&str> for ColorChat {
    fn from(value: &str) -> Self {
        Self(value.to_string())
    }
}

#[derive(Debug, PartialEq, Eq, PartialOrd, Ord, Clone, Default)]

pub struct ColorName(String);

impl ColorName {
    pub fn new(color: impl Into<String>) -> Self {
        Self(color.into())
    }
}

impl Value<String> for ColorName {
    fn value(&self) -> String {
        self.0.clone()
    }
}

impl From<String> for ColorName {
    fn from(value: String) -> Self {
        Self(value)
    }
}

impl From<&str> for ColorName {
    fn from(value: &str) -> Self {
        Self(value.to_string())
    }
}

#[derive(Debug, PartialEq, Eq, PartialOrd, Ord, Clone, Default)]

pub struct ColorHair(String);

impl ColorHair {
    pub fn new(color: impl Into<String>) -> Self {
        Self(color.into())
    }
}

impl Value<String> for ColorHair {
    fn value(&self) -> String {
        self.0.clone()
    }
}

impl From<String> for ColorHair {
    fn from(value: String) -> Self {
        Self(value)
    }
}

impl From<&str> for ColorHair {
    fn from(value: &str) -> Self {
        Self(value.to_string())
    }
}

#[derive(Debug, PartialEq, Eq, PartialOrd, Ord, Clone, Default)]

pub struct ColorSkin(String);

impl ColorSkin {
    pub fn new(color: impl Into<String>) -> Self {
        Self(color.into())
    }
}

impl Value<String> for ColorSkin {
    fn value(&self) -> String {
        self.0.clone()
    }
}

impl From<String> for ColorSkin {
    fn from(value: String) -> Self {
        Self(value)
    }
}

impl From<&str> for ColorSkin {
    fn from(value: &str) -> Self {
        Self(value.to_string())
    }
}

#[derive(Debug, PartialEq, Eq, PartialOrd, Ord, Clone, Default)]

pub struct ColorEye(String);

impl ColorEye {
    pub fn new(color: impl Into<String>) -> Self {
        Self(color.into())
    }
}

impl Value<String> for ColorEye {
    fn value(&self) -> String {
        self.0.clone()
    }
}

impl From<String> for ColorEye {
    fn from(value: String) -> Self {
        Self(value)
    }
}

impl From<&str> for ColorEye {
    fn from(value: &str) -> Self {
        Self(value.to_string())
    }
}

#[derive(Debug, PartialEq, Eq, PartialOrd, Ord, Clone, Default)]

pub struct ColorBase(String);

impl ColorBase {
    pub fn new(color: impl Into<String>) -> Self {
        Self(color.into())
    }
}

impl Value<String> for ColorBase {
    fn value(&self) -> String {
        self.0.clone()
    }
}

impl From<String> for ColorBase {
    fn from(value: String) -> Self {
        Self(value)
    }
}

impl From<&str> for ColorBase {
    fn from(value: &str) -> Self {
        Self(value.to_string())
    }
}

#[derive(Debug, PartialEq, Eq, PartialOrd, Ord, Clone, Default)]

pub struct ColorTrim(String);

impl ColorTrim {
    pub fn new(color: impl Into<String>) -> Self {
        Self(color.into())
    }
}

impl Value<String> for ColorTrim {
    fn value(&self) -> String {
        self.0.clone()
    }
}

impl From<String> for ColorTrim {
    fn from(value: String) -> Self {
        Self(value)
    }
}

impl From<&str> for ColorTrim {
    fn from(value: &str) -> Self {
        Self(value.to_string())
    }
}

#[derive(Debug, PartialEq, Eq, PartialOrd, Ord, Clone, Default)]

pub struct ColorAccessory(String);

impl ColorAccessory {
    pub fn new(color: impl Into<String>) -> Self {
        Self(color.into())
    }
}

impl Value<String> for ColorAccessory {
    fn value(&self) -> String {
        self.0.clone()
    }
}

impl From<String> for ColorAccessory {
    fn from(value: String) -> Self {
        Self(value)
    }
}

impl From<&str> for ColorAccessory {
    fn from(value: &str) -> Self {
        Self(value.to_string())
    }
}
