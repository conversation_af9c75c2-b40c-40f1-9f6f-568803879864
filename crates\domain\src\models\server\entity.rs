use super::value_object::*;
use getset::{<PERSON><PERSON>, <PERSON><PERSON>};

#[derive(<PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, bon::<PERSON><PERSON><PERSON>, <PERSON><PERSON>, Set<PERSON>)]
#[getset(get = "pub", set = "pub")]
pub struct Server {
    id: ServerId,
    name: ServerName,
    ip: ServerIp,
    port: ServerPort,
    online: ServerOnline,
    upgrade: ServerUpgrade,
    chat: ServerChat,
    count: ServerCount,
    max: ServerMax,
    motd: ServerMotd,
    maintenance: ServerMaintenance,
}

impl Server {
    pub fn new(
        id: ServerId,
        name: ServerName,
        ip: ServerIp,
        port: ServerPort,
        online: ServerOnline,
        upgrade: ServerUpgrade,
        chat: ServerChat,
        count: ServerCount,
        max: ServerMax,
        motd: ServerMotd,
        maintenance: ServerMaintenance,
    ) -> Self {
        Self {
            id,
            name,
            ip,
            port,
            online,
            upgrade,
            chat,
            count,
            max,
            motd,
            maintenance,
        }
    }
}

#[cfg(test)]
mod tests {
    use reforged_shared::{IdTrait, Value};

    use super::*;

    #[test]
    fn test_server_new() {
        let id = ServerId::new(uuid::Uuid::now_v7());
        let name = ServerName::new("Main Server");
        let ip = ServerIp::new("127.0.0.1");
        let port = ServerPort::new(8080);
        let online = ServerOnline::new(true);
        let upgrade = ServerUpgrade::new(false);
        let chat = ServerChat::new(1);
        let count = ServerCount::new(150);
        let max = ServerMax::new(500);
        let motd = ServerMotd::new("Welcome to Reforged!");
        let maintenance = ServerMaintenance::new(false);

        let server = Server::new(
            id.clone(),
            name.clone(),
            ip.clone(),
            port.clone(),
            online.clone(),
            upgrade.clone(),
            chat.clone(),
            count.clone(),
            max.clone(),
            motd.clone(),
            maintenance.clone(),
        );

        assert_eq!(server.id().get_id(), id.get_id());
        assert_eq!(server.name().value(), name.value());
        assert_eq!(server.ip().value(), ip.value());
        assert_eq!(server.port().value(), port.value());
        assert_eq!(server.online().value(), online.value());
        assert_eq!(server.upgrade().value(), upgrade.value());
        assert_eq!(server.chat().value(), chat.value());
        assert_eq!(server.count().value(), count.value());
        assert_eq!(server.max().value(), max.value());
        assert_eq!(server.motd().value(), motd.value());
        assert_eq!(server.maintenance().value(), maintenance.value());
    }

    #[test]
    fn test_server_builder() {
        let id = ServerId::new(uuid::Uuid::now_v7());
        let name = ServerName::new("Main Server");
        let ip = ServerIp::new("127.0.0.1");
        let port = ServerPort::new(8080);
        let online = ServerOnline::new(true);
        let upgrade = ServerUpgrade::new(false);
        let chat = ServerChat::new(1);
        let count = ServerCount::new(150);
        let max = ServerMax::new(500);
        let motd = ServerMotd::new("Welcome to Reforged!");
        let maintenance = ServerMaintenance::new(false);

        let server = Server::builder()
            .id(id.clone())
            .name(name.clone())
            .ip(ip.clone())
            .port(port.clone())
            .online(online.clone())
            .upgrade(upgrade.clone())
            .chat(chat.clone())
            .count(count.clone())
            .max(max.clone())
            .motd(motd.clone())
            .maintenance(maintenance.clone())
            .build();

        assert_eq!(server.id().get_id(), id.get_id());
        assert_eq!(server.name().value(), name.value());
        assert_eq!(server.ip().value(), ip.value());
        assert_eq!(server.port().value(), port.value());
        assert_eq!(server.online().value(), online.value());
        assert_eq!(server.upgrade().value(), upgrade.value());
        assert_eq!(server.chat().value(), chat.value());
        assert_eq!(server.count().value(), count.value());
        assert_eq!(server.max().value(), max.value());
        assert_eq!(server.motd().value(), motd.value());
        assert_eq!(server.maintenance().value(), maintenance.value());
    }

    #[test]
    fn test_getters_and_setters() {
        let mut server = Server::default();

        let id = ServerId::new(uuid::Uuid::now_v7());
        let name = ServerName::new("Main Server");
        let ip = ServerIp::new("127.0.0.1");
        let port = ServerPort::new(8080);
        let online = ServerOnline::new(true);
        let upgrade = ServerUpgrade::new(false);
        let chat = ServerChat::new(1);
        let count = ServerCount::new(150);
        let max = ServerMax::new(500);
        let motd = ServerMotd::new("Welcome to Reforged!");
        let maintenance = ServerMaintenance::new(false);

        server.set_id(id.clone());
        server.set_name(name.clone());
        server.set_ip(ip.clone());
        server.set_port(port.clone());
        server.set_online(online.clone());
        server.set_upgrade(upgrade.clone());
        server.set_chat(chat.clone());
        server.set_count(count.clone());
        server.set_max(max.clone());
        server.set_motd(motd.clone());
        server.set_maintenance(maintenance.clone());

        assert_eq!(server.id().get_id(), id.get_id());
        assert_eq!(server.name().value(), name.value());
        assert_eq!(server.ip().value(), ip.value());
        assert_eq!(server.port().value(), port.value());
        assert_eq!(server.online().value(), online.value());
        assert_eq!(server.upgrade().value(), upgrade.value());
        assert_eq!(server.chat().value(), chat.value());
        assert_eq!(server.count().value(), count.value());
        assert_eq!(server.max().value(), max.value());
        assert_eq!(server.motd().value(), motd.value());
        assert_eq!(server.maintenance().value(), maintenance.value());
    }
}
