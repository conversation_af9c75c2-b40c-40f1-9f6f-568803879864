use chrono::{DateTime, Utc};
use reforged_shared::{UuidId, Value};

use super::entity::RedeemCode;

pub type RedeemCodeId = UuidId<RedeemCode>;

#[derive(Debug, PartialEq, Eq, PartialOrd, Ord, Clone, Default)]

pub struct RedeemCodeCode(String);

impl RedeemCodeCode {
    pub fn new(code: impl Into<String>) -> Self {
        Self(code.into())
    }
}

impl Value<String> for RedeemCodeCode {
    fn value(&self) -> String {
        self.0.clone()
    }
}

impl From<String> for RedeemCodeCode {
    fn from(code: String) -> Self {
        Self(code)
    }
}

#[derive(Debug, PartialEq, Eq, PartialOrd, Ord, Clone, Default)]

pub struct RedeemCodeCoins(i32);

impl RedeemCodeCoins {
    pub fn new(coins: i32) -> Self {
        Self(coins)
    }
}

impl Value<i32> for RedeemCodeCoins {
    fn value(&self) -> i32 {
        self.0
    }
}

impl From<i32> for RedeemCodeCoins {
    fn from(coins: i32) -> Self {
        Self(coins)
    }
}

#[derive(Debug, PartialEq, Eq, PartialOrd, Ord, Clone, Default)]

pub struct RedeemCodeGold(i32);

impl RedeemCodeGold {
    pub fn new(gold: i32) -> Self {
        Self(gold)
    }
}

impl Value<i32> for RedeemCodeGold {
    fn value(&self) -> i32 {
        self.0
    }
}

impl From<i32> for RedeemCodeGold {
    fn from(gold: i32) -> Self {
        Self(gold)
    }
}

#[derive(Debug, PartialEq, Eq, PartialOrd, Ord, Clone, Default)]

pub struct RedeemCodeExp(i32);

impl RedeemCodeExp {
    pub fn new(exp: i32) -> Self {
        Self(exp)
    }
}

impl Value<i32> for RedeemCodeExp {
    fn value(&self) -> i32 {
        self.0
    }
}

impl From<i32> for RedeemCodeExp {
    fn from(exp: i32) -> Self {
        Self(exp)
    }
}

#[derive(Debug, PartialEq, Eq, PartialOrd, Ord, Clone, Default)]

pub struct RedeemCodeClassPoints(i32);

impl RedeemCodeClassPoints {
    pub fn new(class_points: i32) -> Self {
        Self(class_points)
    }
}

impl Value<i32> for RedeemCodeClassPoints {
    fn value(&self) -> i32 {
        self.0
    }
}

impl From<i32> for RedeemCodeClassPoints {
    fn from(class_points: i32) -> Self {
        Self(class_points)
    }
}

#[derive(Debug, PartialEq, Eq, PartialOrd, Ord, Clone, Default)]

pub struct RedeemCodeUpgradeDays(i32);

impl RedeemCodeUpgradeDays {
    pub fn new(upgrade_days: i32) -> Self {
        Self(upgrade_days)
    }
}

impl Value<i32> for RedeemCodeUpgradeDays {
    fn value(&self) -> i32 {
        self.0
    }
}

impl From<i32> for RedeemCodeUpgradeDays {
    fn from(upgrade_days: i32) -> Self {
        Self(upgrade_days)
    }
}

#[derive(Debug, PartialEq, Eq, PartialOrd, Ord, Clone, Default)]

pub struct RedeemCodeDateExpiry(DateTime<Utc>);

impl RedeemCodeDateExpiry {
    pub fn new(date_expiry: DateTime<Utc>) -> Self {
        Self(date_expiry)
    }
}

impl Value<DateTime<Utc>> for RedeemCodeDateExpiry {
    fn value(&self) -> DateTime<Utc> {
        self.0
    }
}

impl From<DateTime<Utc>> for RedeemCodeDateExpiry {
    fn from(date_expiry: DateTime<Utc>) -> Self {
        Self(date_expiry)
    }
}

#[derive(Debug, PartialEq, Eq, PartialOrd, Ord, Clone, Default)]

pub struct RedeemCodeLimit(i32);

impl RedeemCodeLimit {
    pub fn new(limit: i32) -> Self {
        Self(limit)
    }
}

impl Value<i32> for RedeemCodeLimit {
    fn value(&self) -> i32 {
        self.0
    }
}

impl From<i32> for RedeemCodeLimit {
    fn from(limit: i32) -> Self {
        Self(limit)
    }
}
