use crate::models::prelude::*;
use sea_orm::Schema;
use sea_orm_migration::{
    DbErr, MigrationTrait, async_trait::async_trait, prelude::*, sea_orm::DeriveMigrationName,
};

#[derive(DeriveMigrationName)]
pub struct Migration;

#[async_trait]
impl MigrationTrait for Migration {
    async fn up(&self, manager: &SchemaManager) -> Result<(), DbErr> {
        let backend = manager.get_database_backend();
        let schema = Schema::new(backend);

        // MARK: BASE TABLES
        manager
            .create_table(schema.create_table_from_entity(Guilds))
            .await?;
        manager
            .create_table(schema.create_table_from_entity(UserGuilds))
            .await?;
        manager
            .create_table(schema.create_table_from_entity(GuildHalls))
            .await?;

        manager
            .create_table(schema.create_table_from_entity(GuildHallBuildings))
            .await?;
        manager
            .create_table(schema.create_table_from_entity(GuildHallConnections))
            .await?;
        manager
            .create_table(schema.create_table_from_entity(GuildLevels))
            .await?;
        manager
            .create_table(schema.create_table_from_entity(GuildItems))
            .await?;

        Ok(())
    }

    async fn down(&self, manager: &SchemaManager) -> Result<(), DbErr> {
        manager
            .drop_table(Table::drop().table(UserGuilds).to_owned())
            .await?;
        manager
            .drop_table(Table::drop().table(GuildItems).to_owned())
            .await?;
        manager
            .drop_table(Table::drop().table(GuildLevels).to_owned())
            .await?;
        manager
            .drop_table(Table::drop().table(GuildHalls).to_owned())
            .await?;
        manager
            .drop_table(Table::drop().table(GuildHallConnections).to_owned())
            .await?;
        manager
            .drop_table(Table::drop().table(GuildHallBuildings).to_owned())
            .await?;
        manager
            .drop_table(Table::drop().table(Guilds).to_owned())
            .await?;

        Ok(())
    }
}
