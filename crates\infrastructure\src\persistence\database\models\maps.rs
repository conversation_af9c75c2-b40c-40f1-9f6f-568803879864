//! `SeaORM` Entity, @generated by sea-orm-codegen 1.1.0

use sea_orm::entity::prelude::*;
use serde::Deserialize;

#[derive(<PERSON><PERSON>, Debug, PartialEq, DeriveEntityModel, Eq, Deserialize)]
#[sea_orm(table_name = "maps")]
pub struct Model {
    #[sea_orm(primary_key, auto_increment = false)]
    pub id: Uuid,
    pub name: String,
    pub file: String,
    pub max_players: i16,
    pub req_level: i16,
    pub upgrade: bool,
    pub staff: bool,
    pub pvp: bool,
    pub world_boss: bool,
}

#[derive(Co<PERSON>, Clone, Debug, EnumIter, DeriveRelation)]
pub enum Relation {
    #[sea_orm(has_many = "super::map_cells::Entity")]
    MapCells,
    #[sea_orm(has_many = "super::map_items::Entity")]
    MapItems,
    #[sea_orm(has_many = "super::map_monsters::Entity")]
    MapMonsters,
    #[sea_orm(has_many = "super::monster_bosses::Entity")]
    MonsterBosses,
    #[sea_orm(has_many = "super::quest_locations::Entity")]
    QuestLocations,
    #[sea_orm(has_many = "super::shop_locations::Entity")]
    ShopLocations,
}

impl Related<super::map_cells::Entity> for Entity {
    fn to() -> RelationDef {
        Relation::MapCells.def()
    }
}

impl Related<super::map_items::Entity> for Entity {
    fn to() -> RelationDef {
        Relation::MapItems.def()
    }
}

impl Related<super::map_monsters::Entity> for Entity {
    fn to() -> RelationDef {
        Relation::MapMonsters.def()
    }
}

impl Related<super::monster_bosses::Entity> for Entity {
    fn to() -> RelationDef {
        Relation::MonsterBosses.def()
    }
}

impl Related<super::quest_locations::Entity> for Entity {
    fn to() -> RelationDef {
        Relation::QuestLocations.def()
    }
}

impl Related<super::shop_locations::Entity> for Entity {
    fn to() -> RelationDef {
        Relation::ShopLocations.def()
    }
}

impl ActiveModelBehavior for ActiveModel {}
