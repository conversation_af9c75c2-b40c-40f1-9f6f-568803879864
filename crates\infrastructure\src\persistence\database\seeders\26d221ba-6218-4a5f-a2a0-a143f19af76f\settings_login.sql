INSERT INTO "game_settings" ("name", "value", "location") VALUES ('isEU', 'false', 'Loader');
INSERT INTO "game_settings" ("name", "value", "location") VALUES ('isWeb', 'false', 'Loader');
INSERT INTO "game_settings" ("name", "value", "location") VALUES ('sAppVersion', '0.1', 'Loader');
INSERT INTO "game_settings" ("name", "value", "location") VALUES ('sFBC', 'FBC-2011-03-08.swf', 'Game');
INSERT INTO "game_settings" ("name", "value", "location") VALUES ('sItemPreview', 'ItemPreview04.swf', 'Wiki');
INSERT INTO "game_settings" ("name", "value", "location") VALUES ('sLaunch', 'LauncherAlphaVWR10.swf', 'Loader');
INSERT INTO "game_settings" ("name", "value", "location") VALUES ('sMap', 'interface/WorldMap22-7.swf', 'Game');
INSERT INTO "game_settings" ("name", "value", "location") VALUES ('sMapPreview', 'MapPreview01.swf', 'Wiki');
INSERT INTO "game_settings" ("name", "value", "location") VALUES ('sProfile', 'Character.swf', 'Wiki');
INSERT INTO "game_settings" ("name", "value", "location") VALUES ('sVersion', '0.1', 'Loader');
INSERT INTO "game_settings" ("name", "value", "location") VALUES ('sWTSandbox', 'false', 'Game');
INSERT INTO "game_settings" ("name", "value", "location") VALUES ('gMenu', 'dynamic-gameMenu-17Jan22.swf', 'Game');
INSERT INTO "game_settings" ("name", "value", "location") VALUES ('sBook', 'news/spiderbook3.swf', 'Game');
INSERT INTO "game_settings" ("name", "value", "location") VALUES ('sBG', 'Generic2.swf', 'Loader');
INSERT INTO "game_settings" ("name", "value", "location") VALUES ('sTitle', 'DeVoid', 'Loader');
INSERT INTO "game_settings" ("name", "value", "location") VALUES ('sNews', 'news/Book-200619_r2.swf', 'Game');
INSERT INTO "game_settings" ("name", "value", "location") VALUES ('sAssets', 'Assets_20240227.swf', 'Game');
INSERT INTO "game_settings" ("name", "value", "location") VALUES ('sLoader', 'Loader3.swf', 'Loader');
INSERT INTO "game_settings" ("name", "value", "location") VALUES ('sFile', 'Game3089-Latest.swf', 'Loader');
