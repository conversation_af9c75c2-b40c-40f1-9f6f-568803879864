use chrono::{DateTime, Utc};
use reforged_shared::{UuidId, Value};

use super::entity::Session;

pub type SessionId = UuidId<Session>;

#[derive(Debug, Clone, PartialEq, Eq, Default)]
pub struct SessionToken(String);

impl Value<String> for SessionToken {
    fn value(&self) -> String {
        self.0.clone()
    }
}

impl SessionToken {
    pub fn new<T: Into<String>>(token: T) -> Self {
        Self(token.into())
    }
}

#[derive(Debug, <PERSON>lone, PartialEq, Eq, Default)]
pub struct SessionUserAgent(String);

impl Value<String> for SessionUserAgent {
    fn value(&self) -> String {
        self.0.clone()
    }
}

impl SessionUserAgent {
    pub fn new<T: Into<String>>(user_agent: T) -> Self {
        Self(user_agent.into())
    }
}

#[derive(Debug, Clone, PartialEq, Eq, Default)]
pub struct SessionClientIp(String);

impl Value<String> for SessionClientIp {
    fn value(&self) -> String {
        self.0.clone()
    }
}

impl SessionClientIp {
    pub fn new<T: Into<String>>(client_ip: T) -> Self {
        Self(client_ip.into())
    }
}

#[derive(Debug, Clone, PartialEq, Eq, Default)]
pub struct SessionIsBlocked(bool);

impl Value<bool> for SessionIsBlocked {
    fn value(&self) -> bool {
        self.0
    }
}

impl SessionIsBlocked {
    pub fn new(is_blocked: bool) -> Self {
        Self(is_blocked)
    }
}

#[derive(Debug, Clone, PartialEq, Default)]
pub struct SessionCreatedAt(DateTime<Utc>);

impl Value<DateTime<Utc>> for SessionCreatedAt {
    fn value(&self) -> DateTime<Utc> {
        self.0
    }
}

impl SessionCreatedAt {
    pub fn new(created_at: DateTime<Utc>) -> Self {
        Self(created_at)
    }
}

#[derive(Debug, Clone, PartialEq, Default)]
pub struct SessionExpiresAt(DateTime<Utc>);

impl Value<DateTime<Utc>> for SessionExpiresAt {
    fn value(&self) -> DateTime<Utc> {
        self.0
    }
}

impl SessionExpiresAt {
    pub fn new(expires_at: DateTime<Utc>) -> Self {
        Self(expires_at)
    }
}
