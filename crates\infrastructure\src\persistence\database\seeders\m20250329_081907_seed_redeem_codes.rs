use crate::models;
use sea_orm::{ActiveModelTrait, IntoActiveModel};
use sea_orm_migration::prelude::*;

#[derive(DeriveMigrationName)]
pub struct Migration;

#[async_trait::async_trait]
impl MigrationTrait for Migration {
    async fn up(&self, manager: &SchemaManager) -> Result<(), DbErr> {
        let db = manager.get_connection();

        let redeem_codes = include_str!("../json/redeem_codes.json");
        let redeem_codes: Vec<models::redeem_codes::Model> =
            serde_json::from_str(redeem_codes).unwrap();

        for model in redeem_codes {
            let redeem_code = model.into_active_model();
            redeem_code.insert(db).await?;
        }

        Ok(())
    }

    async fn down(&self, _manager: &SchemaManager) -> Result<(), DbErr> {
        Ok(())
    }
}
