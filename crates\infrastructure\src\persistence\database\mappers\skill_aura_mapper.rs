use reforged_domain::models::skill_aura::entity::Skill<PERSON>ura;
use reforged_domain::models::skill_aura::value_object::SkillAuraId;
use reforged_domain::models::skill::value_object::SkillId;
use reforged_domain::models::aura::value_object::AuraId;

#[derive(Debug)]
pub struct SkillAuraDbModelMapper(super::super::models::skill_auras::Model);

impl SkillAuraDbModelMapper {
    pub fn new(model: super::super::models::skill_auras::Model) -> Self {
        Self(model)
    }
}

impl From<SkillAuraDbModelMapper> for SkillAura {
    fn from(value: SkillAuraDbModelMapper) -> Self {
        let model = value.0;
        
        SkillAura::builder()
            .id(SkillAuraId::new(model.id))
            .skill_id(SkillId::new(model.skill_id))
            .aura_id(AuraId::new(model.aura_id))
            .build()
    }
}
