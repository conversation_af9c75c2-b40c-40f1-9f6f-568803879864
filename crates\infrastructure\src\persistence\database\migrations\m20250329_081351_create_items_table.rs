use crate::models::prelude::*;
use sea_orm::Schema;
use sea_orm_migration::prelude::*;

#[derive(DeriveMigrationName)]
pub struct Migration;

#[async_trait::async_trait]
impl MigrationTrait for Migration {
    async fn up(&self, manager: &SchemaManager) -> Result<(), DbErr> {
        let backend = manager.get_database_backend();
        let schema = Schema::new(backend);
        manager
            .create_table(schema.create_table_from_entity(ItemRarities))
            .await?;
        // MARK: items tables and junction tables
        manager
            .create_table(schema.create_table_from_entity(Items))
            .await?;

        // MARK: Wheels tables
        manager
            .create_table(schema.create_table_from_entity(WheelRewards))
            .await?;

        manager
            .create_table(schema.create_table_from_entity(ItemBundles))
            .await?;
        manager
            .create_table(schema.create_table_from_entity(ItemEffects))
            .await?;
        manager
            .create_table(schema.create_table_from_entity(ItemLotteries))
            .await?;

        manager
            .create_table(schema.create_table_from_entity(ItemRequirements))
            .await?;

        manager
            .create_table(schema.create_table_from_entity(ItemSkills))
            .await?;

        Ok(())
    }

    async fn down(&self, manager: &SchemaManager) -> Result<(), DbErr> {
        manager
            .drop_table(Table::drop().table(WheelRewards).to_owned())
            .await?;
        manager
            .drop_table(Table::drop().table(ItemBundles).to_owned())
            .await?;
        manager
            .drop_table(Table::drop().table(ItemEffects).to_owned())
            .await?;
        manager
            .drop_table(Table::drop().table(ItemLotteries).to_owned())
            .await?;
        manager
            .drop_table(Table::drop().table(ItemRarities).to_owned())
            .await?;
        manager
            .drop_table(Table::drop().table(ItemRequirements).to_owned())
            .await?;

        manager
            .drop_table(Table::drop().table(Items).to_owned())
            .await?;

        manager
            .drop_table(Table::drop().table(ItemSkills).to_owned())
            .await
    }
}
