use chrono::{DateTime, Utc};
use serde::{Deserialize, Serialize};

#[derive(Debug, Serial<PERSON>, Deserialize, Clone, bon::Builder)]
pub struct UserCreated {
    pub id: uuid::Uuid,
    pub username: String,
    pub email: String,
    pub role: String,
    pub gender: String,
    pub password: String,
}

#[derive(Debug, Serialize, Deserialize, Clone, bon::Builder)]
pub struct ResetPasswordRequested {
    pub id: uuid::Uuid,      // This is the ID of the reset token itself
    pub user_id: uuid::Uuid, // This is the ID of the user requesting the reset
    pub email: String,
    pub reset_token: String,
    pub created_at: DateTime<Utc>,
    pub expires_at: DateTime<Utc>,
}

#[derive(Debug, Serialize, Deserialize, Clone, bon::Builder)]
pub struct UserPasswordChanged {
    pub id: uuid::Uuid,
    pub email: String,
    pub new_password: String,
    pub password_changed_at: DateTime<Utc>,
    pub reset_token_id: uuid::Uuid,
}

#[derive(Debug, Serial<PERSON>, Deserialize, <PERSON><PERSON>)]
pub enum UserEvents {
    UserCreated(UserCreated),
    ResetPasswordRequested(ResetPasswordRequested),
    UserPasswordChanged(UserPasswordChanged),
}
