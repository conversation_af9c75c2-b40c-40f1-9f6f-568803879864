use crate::models::war::value_object::*;
use getset::{<PERSON><PERSON>, <PERSON><PERSON>};

#[derive(<PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, bon::<PERSON><PERSON><PERSON>, <PERSON><PERSON>, Set<PERSON>)]
#[getset(get = "pub", set = "pub")]
pub struct War {
    pub id: WarId,
    pub name: WarName,
    pub points: WarPoints,
    pub max_points: WarMaxPoints,
}

impl War {
    pub fn new(id: WarId, name: WarN<PERSON>, points: WarPoints, max_points: WarMaxPoints) -> Self {
        Self {
            id,
            name,
            points,
            max_points,
        }
    }
}

#[cfg(test)]
mod tests {
    use reforged_shared::{IdTrait, Value};
    use uuid::Uuid;

    use super::*;

    #[test]
    fn test_war_new() {
        let id = WarId::new(Uuid::now_v7());
        let name = WarName::new("First War");
        let points = WarPoints::new(1500);
        let max_points = WarMaxPoints::new(5000);

        let war = War::new(id.clone(), name.clone(), points.clone(), max_points.clone());

        assert_eq!(war.id().get_id(), id.get_id());
        assert_eq!(war.name().value(), name.value());
        assert_eq!(war.points().value(), points.value());
        assert_eq!(war.max_points().value(), max_points.value());
    }

    #[test]
    fn test_war_builder() {
        let id = WarId::new(Uuid::now_v7());
        let name = WarName::new("First War");
        let points = WarPoints::new(1500);
        let max_points = WarMaxPoints::new(5000);

        let war = War::builder()
            .id(id.clone())
            .name(name.clone())
            .points(points.clone())
            .max_points(max_points.clone())
            .build();

        assert_eq!(war.id().get_id(), id.get_id());
        assert_eq!(war.name().value(), name.value());
        assert_eq!(war.points().value(), points.value());
        assert_eq!(war.max_points().value(), max_points.value());
    }

    #[test]
    fn test_getters_and_setters() {
        let mut war = War::default();

        let id = WarId::new(Uuid::now_v7());
        let name = WarName::new("First War");
        let points = WarPoints::new(1500);
        let max_points = WarMaxPoints::new(5000);

        war.set_id(id.clone());
        war.set_name(name.clone());
        war.set_points(points.clone());
        war.set_max_points(max_points.clone());

        assert_eq!(war.id().get_id(), id.get_id());
        assert_eq!(war.name().value(), name.value());
        assert_eq!(war.points().value(), points.value());
        assert_eq!(war.max_points().value(), max_points.value());
    }
}
