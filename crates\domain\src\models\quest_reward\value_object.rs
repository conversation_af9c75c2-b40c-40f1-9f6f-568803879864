use reforged_shared::{UuidId, Value};

use super::entity::QuestR<PERSON><PERSON>;

pub type QuestRewardId = UuidId<QuestReward>;

#[derive(Debug, PartialEq, PartialOrd, <PERSON><PERSON>, <PERSON><PERSON>ult)]

pub struct QuestRewardQuantity(i32);

impl QuestRewardQuantity {
    pub fn new(quantity: i32) -> Self {
        Self(quantity)
    }
}

impl Value<i32> for QuestRewardQuantity {
    fn value(&self) -> i32 {
        self.0
    }
}

impl From<i32> for QuestRewardQuantity {
    fn from(value: i32) -> Self {
        Self(value)
    }
}

#[derive(Debug, PartialEq, PartialOrd, <PERSON>lone, De<PERSON>ult)]

pub struct QuestRewardRate(f64);

impl QuestRewardRate {
    pub fn new(rate: f64) -> Self {
        Self(rate)
    }
}

impl Value<f64> for QuestRewardRate {
    fn value(&self) -> f64 {
        self.0
    }
}

impl From<f64> for QuestRewardRate {
    fn from(value: f64) -> Self {
        Self(value)
    }
}

#[derive(Debug, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>)]

pub struct QuestRewardType(String);

impl QuestRewardType {
    pub fn new(reward_type: impl Into<String>) -> Self {
        Self(reward_type.into())
    }
}

impl Value<String> for QuestRewardType {
    fn value(&self) -> String {
        self.0.clone()
    }
}

impl From<String> for QuestRewardType {
    fn from(value: String) -> Self {
        Self(value)
    }
}

impl From<&str> for QuestRewardType {
    fn from(value: &str) -> Self {
        Self(value.to_string())
    }
}
