//! `SeaORM` Entity, @generated by sea-orm-codegen 1.1.0

use sea_orm::entity::prelude::*;
use serde::Deserialize;

#[derive(<PERSON><PERSON>, Debug, PartialEq, DeriveEntityModel, Eq, Deserialize)]
#[sea_orm(table_name = "guild_hall_connections")]
pub struct Model {
    #[sea_orm(primary_key, auto_increment = false)]
    pub id: Uuid,
    pub hall_id: Uuid,
    pub pad: String,
    pub cell: String,
    pub pad_position: String,
}

#[derive(Co<PERSON>, <PERSON><PERSON>, Debug, EnumIter, DeriveRelation)]
pub enum Relation {
    #[sea_orm(
        belongs_to = "super::guild_halls::Entity",
        from = "Column::HallId",
        to = "super::guild_halls::Column::Id",
        on_update = "Cascade",
        on_delete = "Cascade"
    )]
    GuildHalls,
}

impl Related<super::guild_halls::Entity> for Entity {
    fn to() -> RelationDef {
        Relation::GuildHalls.def()
    }
}

impl ActiveModelBehavior for ActiveModel {}
