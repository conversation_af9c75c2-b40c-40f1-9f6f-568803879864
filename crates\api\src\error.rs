use std::fmt::Display;

use actix_web::HttpResponse;
use actix_web::error::Error as ActixError;
use actix_web::{ResponseError, http::StatusCode};
use reforged_application::{error::ApplicationError, services::captcha_service::CaptchaError};
use serde::Serialize;
use serde_json::json;
use validator::ValidationErrors;

#[derive(Debug, Serialize)]
pub struct BaseApiError {
    error: String,
    code: u16,
}

#[derive(Debug)]
pub enum ApiError {
    Application(ApplicationError),
    JsonRequest(ActixError),
    Validation(ValidationErrors),
}

impl Display for ApiError {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        match self {
            ApiError::Application(err) => write!(f, "{}", err),
            ApiError::JsonRequest(err) => write!(f, "{}", err),
            ApiError::Validation(err) => write!(f, "{}", err),
        }
    }
}

impl ResponseError for ApiError {
    fn status_code(&self) -> actix_web::http::StatusCode {
        match self {
            ApiError::JsonRequest(_) => StatusCode::BAD_REQUEST,
            ApiError::Validation(_) => StatusCode::BAD_REQUEST,
            ApiError::Application(err) => match err {
                ApplicationError::InternalError(_) => StatusCode::INTERNAL_SERVER_ERROR,
                ApplicationError::Unauthorized => StatusCode::UNAUTHORIZED,
                ApplicationError::TokenInvalid => StatusCode::UNAUTHORIZED,
                ApplicationError::TokenExpired => StatusCode::UNAUTHORIZED,
                ApplicationError::Forbidden => StatusCode::FORBIDDEN,
                _ => StatusCode::BAD_REQUEST,
            },
        }
    }

    fn error_response(&self) -> actix_web::HttpResponse<actix_web::body::BoxBody> {
        let cause = match self {
            ApiError::Application(_) => "APPLICATION_ERROR",
            ApiError::JsonRequest(_) => "JSON_ERROR",
            ApiError::Validation(_) => "VALIDATION_ERROR",
        };

        let json_error = json!({
            "cause": cause,
            "error": self.to_string(),
        });

        HttpResponse::build(self.status_code()).json(json_error)
    }
}

impl From<ApplicationError> for ApiError {
    fn from(error: ApplicationError) -> Self {
        Self::Application(error)
    }
}

impl From<CaptchaError> for ApiError {
    fn from(value: CaptchaError) -> Self {
        match value {
            CaptchaError::InternalError => ApiError::Application(ApplicationError::InternalError(
                "Failed to validate captcha".to_string(),
            )),
            CaptchaError::InvalidCaptcha => ApiError::Application(ApplicationError::InvalidCaptcha),
        }
    }
}

impl From<ActixError> for ApiError {
    fn from(value: ActixError) -> Self {
        ApiError::JsonRequest(value)
    }
}

impl From<ValidationErrors> for ApiError {
    fn from(value: ValidationErrors) -> Self {
        ApiError::Validation(value)
    }
}
