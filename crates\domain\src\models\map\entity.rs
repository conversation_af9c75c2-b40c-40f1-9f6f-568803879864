use crate::models::map::value_object::*;
use getset::{<PERSON><PERSON>, <PERSON><PERSON>};

#[derive(<PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, bon::<PERSON><PERSON><PERSON>, <PERSON><PERSON>, Set<PERSON>)]
#[getset(get = "pub", set = "pub")]
pub struct Map {
    pub id: MapId,
    pub name: MapName,
    pub file: MapFile,
    pub max_players: MapMaxPlayers,
    pub req_level: MapReqLevel,
    pub upgrade: MapUpgrade,
    pub staff: MapStaff,
    pub pvp: MapPvp,
    pub world_boss: MapWorldBoss,
}

impl Map {
    pub fn new(
        id: MapId,
        name: MapName,
        file: MapFile,
        max_players: MapMaxPlayers,
        req_level: MapReqLevel,
        upgrade: MapUpgrade,
        staff: MapStaff,
        pvp: MapPvp,
        world_boss: MapWorldBoss,
    ) -> Self {
        Self {
            id,
            name,
            file,
            max_players,
            req_level,
            upgrade,
            staff,
            pvp,
            world_boss,
        }
    }
}

#[cfg(test)]
mod tests {
    use reforged_shared::{Id<PERSON><PERSON><PERSON>, Value};

    use super::*;

    #[test]
    fn test_map_new() {
        let id = MapId::new(uuid::Uuid::now_v7());
        let name = MapName::new("Test Map".to_string());
        let file = MapFile::new("maps/test_map.json".to_string());
        let max_players = MapMaxPlayers::new(25);
        let req_level = MapReqLevel::new(10);
        let upgrade = MapUpgrade::new(true);
        let staff = MapStaff::new(false);
        let pvp = MapPvp::new(true);
        let world_boss = MapWorldBoss::new(false);

        let map = Map::new(
            id.clone(),
            name.clone(),
            file.clone(),
            max_players.clone(),
            req_level.clone(),
            upgrade.clone(),
            staff.clone(),
            pvp.clone(),
            world_boss.clone(),
        );

        assert_eq!(map.id.get_id(), id.get_id());
        assert_eq!(map.name.value(), name.value());
        assert_eq!(map.file.value(), file.value());
        assert_eq!(map.max_players.value(), max_players.value());
        assert_eq!(map.req_level.value(), req_level.value());
        assert_eq!(map.upgrade.value(), upgrade.value());
        assert_eq!(map.staff.value(), staff.value());
        assert_eq!(map.pvp.value(), pvp.value());
        assert_eq!(map.world_boss.value(), world_boss.value());
    }

    #[test]
    fn test_map_builder() {
        let id = MapId::new(uuid::Uuid::now_v7());
        let name = MapName::new("Test Map".to_string());
        let file = MapFile::new("maps/test_map.json".to_string());
        let max_players = MapMaxPlayers::new(25);
        let req_level = MapReqLevel::new(10);
        let upgrade = MapUpgrade::new(true);
        let staff = MapStaff::new(false);
        let pvp = MapPvp::new(true);
        let world_boss = MapWorldBoss::new(false);

        let map = Map::builder()
            .id(id.clone())
            .name(name.clone())
            .file(file.clone())
            .max_players(max_players.clone())
            .req_level(req_level.clone())
            .upgrade(upgrade.clone())
            .staff(staff.clone())
            .pvp(pvp.clone())
            .world_boss(world_boss.clone())
            .build();

        assert_eq!(map.id.get_id(), id.get_id());
        assert_eq!(map.name.value(), name.value());
        assert_eq!(map.file.value(), file.value());
        assert_eq!(map.max_players.value(), max_players.value());
        assert_eq!(map.req_level.value(), req_level.value());
        assert_eq!(map.upgrade.value(), upgrade.value());
        assert_eq!(map.staff.value(), staff.value());
        assert_eq!(map.pvp.value(), pvp.value());
        assert_eq!(map.world_boss.value(), world_boss.value());
    }

    #[test]
    fn test_getters_and_setters() {
        let mut map = Map::default();

        let id = MapId::new(uuid::Uuid::now_v7());
        let name = MapName::new("Test Map".to_string());
        let file = MapFile::new("maps/test_map.json".to_string());
        let max_players = MapMaxPlayers::new(25);
        let req_level = MapReqLevel::new(10);
        let upgrade = MapUpgrade::new(true);
        let staff = MapStaff::new(false);
        let pvp = MapPvp::new(true);
        let world_boss = MapWorldBoss::new(false);

        map.set_id(id.clone());
        map.set_name(name.clone());
        map.set_file(file.clone());
        map.set_max_players(max_players.clone());
        map.set_req_level(req_level.clone());
        map.set_upgrade(upgrade.clone());
        map.set_staff(staff.clone());
        map.set_pvp(pvp.clone());
        map.set_world_boss(world_boss.clone());

        assert_eq!(map.id().get_id(), id.get_id());
        assert_eq!(map.name().value(), name.value());
        assert_eq!(map.file().value(), file.value());
        assert_eq!(map.max_players().value(), max_players.value());
        assert_eq!(map.req_level().value(), req_level.value());
        assert_eq!(map.upgrade().value(), upgrade.value());
        assert_eq!(map.staff().value(), staff.value());
        assert_eq!(map.pvp().value(), pvp.value());
        assert_eq!(map.world_boss().value(), world_boss.value());
    }
}
