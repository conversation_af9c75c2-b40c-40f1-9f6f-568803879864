use async_trait::async_trait;
use mockall::automock;

use crate::error::RepositoryError;
use crate::models::game_setting::entity::GameSetting;
use crate::models::game_setting::value_object::GameSettingId;

#[automock]
#[async_trait]
pub trait GameSettingRepository: Send + Sync {
    async fn save(&self, entity: &GameSetting) -> Result<(), RepositoryError>;
    async fn update(&self, entity: &GameSetting) -> Result<(), RepositoryError>;
    async fn delete(&self, id: &GameSettingId) -> Result<(), RepositoryError>;
}

#[automock]
#[async_trait]
pub trait GameSettingReadRepository: Send + Sync {
    async fn find_by_id(&self, id: &GameSettingId) -> Result<Option<GameSetting>, RepositoryError>;
    async fn find_all(&self) -> Result<Vec<GameSetting>, RepositoryError>;
}
