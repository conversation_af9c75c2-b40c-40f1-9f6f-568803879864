use crate::models::prelude::*;
use sea_orm::Schema;
use sea_orm_migration::prelude::*;

#[derive(DeriveMigrationName)]
pub struct Migration;

#[async_trait::async_trait]
impl MigrationTrait for Migration {
    async fn up(&self, manager: &SchemaManager) -> Result<(), DbErr> {
        let backend = manager.get_database_backend();
        let schema = Schema::new(backend);

        // MARK: Users tables and junction tables
        manager
            .create_table(schema.create_table_from_entity(Users))
            .await?;

        manager
            .create_table(schema.create_table_from_entity(Profiles))
            .await?;

        manager
            .create_table(schema.create_table_from_entity(RedeemCodes))
            .await?;

        manager
            .create_table(schema.create_table_from_entity(Sessions))
            .await?;

        manager
            .create_table(schema.create_table_from_entity(UserAchievements))
            .await?;
        manager
            .create_table(schema.create_table_from_entity(UserBoosts))
            .await?;
        manager
            .create_table(schema.create_table_from_entity(UserBrowsers))
            .await?;
        manager
            .create_table(schema.create_table_from_entity(UserColors))
            .await?;
        manager
            .create_table(schema.create_table_from_entity(UserCurrencies))
            .await?;
        manager
            .create_table(schema.create_table_from_entity(UserExps))
            .await?;
        manager
            .create_table(schema.create_table_from_entity(UserFactions))
            .await?;
        manager
            .create_table(schema.create_table_from_entity(UserFriends))
            .await?;
        manager
            .create_table(schema.create_table_from_entity(DeletedUserItems))
            .await?;
        manager
            .create_table(schema.create_table_from_entity(UserItems))
            .await?;
        manager
            .create_table(schema.create_table_from_entity(UserLivedrops))
            .await?;
        manager
            .create_table(schema.create_table_from_entity(UserLogins))
            .await?;
        manager
            .create_table(schema.create_table_from_entity(UserMarkets))
            .await?;
        manager
            .create_table(schema.create_table_from_entity(UserPurchases))
            .await?;
        manager
            .create_table(schema.create_table_from_entity(UserRedeems))
            .await?;
        manager
            .create_table(schema.create_table_from_entity(UserReports))
            .await?;
        manager
            .create_table(schema.create_table_from_entity(UserSlots))
            .await?;
        manager
            .create_table(schema.create_table_from_entity(UserStats))
            .await?;
        manager
            .create_table(schema.create_table_from_entity(UserTitles))
            .await?;

        Ok(())
    }

    async fn down(&self, manager: &SchemaManager) -> Result<(), DbErr> {
        manager
            .drop_table(Table::drop().table(UserTitles).to_owned())
            .await?;
        manager
            .drop_table(Table::drop().table(UserStats).to_owned())
            .await?;
        manager
            .drop_table(Table::drop().table(UserSlots).to_owned())
            .await?;
        manager
            .drop_table(Table::drop().table(UserReports).to_owned())
            .await?;
        manager
            .drop_table(Table::drop().table(UserReports).to_owned())
            .await?;
        manager
            .drop_table(Table::drop().table(UserRedeems).to_owned())
            .await?;
        manager
            .drop_table(Table::drop().table(UserPurchases).to_owned())
            .await?;
        manager
            .drop_table(Table::drop().table(UserMarkets).to_owned())
            .await?;

        manager
            .drop_table(Table::drop().table(UserAchievements).to_owned())
            .await?;
        manager
            .drop_table(Table::drop().table(UserBoosts).to_owned())
            .await?;
        manager
            .drop_table(Table::drop().table(UserBrowsers).to_owned())
            .await?;
        manager
            .drop_table(Table::drop().table(UserColors).to_owned())
            .await?;
        manager
            .drop_table(Table::drop().table(UserCurrencies).to_owned())
            .await?;
        manager
            .drop_table(Table::drop().table(UserExps).to_owned())
            .await?;
        manager
            .drop_table(Table::drop().table(UserFactions).to_owned())
            .await?;
        manager
            .drop_table(Table::drop().table(UserFriends).to_owned())
            .await?;
        manager
            .drop_table(Table::drop().table(DeletedUserItems).to_owned())
            .await?;
        manager
            .drop_table(Table::drop().table(UserItems).to_owned())
            .await?;
        manager
            .drop_table(Table::drop().table(UserLivedrops).to_owned())
            .await?;
        manager
            .drop_table(Table::drop().table(UserLogins).to_owned())
            .await?;
        manager
            .drop_table(Table::drop().table(RedeemCodes).to_owned())
            .await?;
        manager
            .drop_table(Table::drop().table(Sessions).to_owned())
            .await?;
        manager
            .drop_table(Table::drop().table(Profiles).to_owned())
            .await?;
        manager
            .drop_table(Table::drop().table(Users).to_owned())
            .await
    }
}
