"F:\Dev\reforged> cargo c                                                                        101 05/27/25 16:05:41 PM
warning: F:\Dev\reforged\Cargo.toml: unused manifest key: profile.dev.package.tokio.unstable-features
warning: F:\Dev\reforged\Cargo.toml: unused manifest key: profile.release.package.tokio.unstable-features
    Checking reforged-infrastructure v0.1.0 (F:\Dev\reforged\crates\infrastructure)
error[E0583]: file not found for module `login_location_mapper`
  --> crates\infrastructure\src\persistence\database\mappers\mod.rs:31:1
   |
31 | pub mod login_location_mapper;
   | ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
   |
   = help: to create the module `login_location_mapper`, create file "crates\infrastructure\src\persistence\database\mappers\login_location_mapper.rs" or "crates\infrastructure\src\persistence\database\mappers\login_location_mapper\mod.rs"
   = note: if there is a `mod login_location_mapper` elsewhere in the crate already, import it with `use crate::...` instead

error[E0583]: file not found for module `market_type_mapper`
  --> crates\infrastructure\src\persistence\database\mappers\mod.rs:33:1
   |
33 | pub mod market_type_mapper;
   | ^^^^^^^^^^^^^^^^^^^^^^^^^^^
   |
   = help: to create the module `market_type_mapper`, create file "crates\infrastructure\src\persistence\database\mappers\market_type_mapper.rs" or "crates\infrastructure\src\persistence\database\mappers\market_type_mapper\mod.rs"
   = note: if there is a `mod market_type_mapper` elsewhere in the crate already, import it with `use crate::...` instead

error[E0583]: file not found for module `store_type_mapper`
  --> crates\infrastructure\src\persistence\database\mappers\mod.rs:44:1
   |
44 | pub mod store_type_mapper;
   | ^^^^^^^^^^^^^^^^^^^^^^^^^^
   |
   = help: to create the module `store_type_mapper`, create file "crates\infrastructure\src\persistence\database\mappers\store_type_mapper.rs" or "crates\infrastructure\src\persistence\database\mappers\store_type_mapper\mod.rs"
   = note: if there is a `mod store_type_mapper` elsewhere in the crate already, import it with `use crate::...` instead

error[E0583]: file not found for module `user_role_mapper`
  --> crates\infrastructure\src\persistence\database\mappers\mod.rs:59:1
   |
59 | pub mod user_role_mapper;
   | ^^^^^^^^^^^^^^^^^^^^^^^^^
   |
   = help: to create the module `user_role_mapper`, create file "crates\infrastructure\src\persistence\database\mappers\user_role_mapper.rs" or "crates\infrastructure\src\persistence\database\mappers\user_role_mapper\mod.rs"
   = note: if there is a `mod user_role_mapper` elsewhere in the crate already, import it with `use crate::...` instead

error[E0432]: unresolved imports `reforged_domain::models::user_currency::entity::UserCurrency`, `reforged_domain::models::user_currency::entity::UserCurrencyId`
 --> crates\infrastructure\src\persistence\database\mappers\user_currency_mapper.rs:1:54
  |
1 | use reforged_domain::models::user_currency::entity::{UserCurrency, UserCurrencyId};
  |                                                      ^^^^^^^^^^^^  ^^^^^^^^^^^^^^ no `UserCurrencyId` in `models::user_currency::entity`
  |                                                      |
  |                                                      no `UserCurrency` in `models::user_currency::entity`

error[E0432]: unresolved imports `reforged_domain::models::user_exp::entity::UserExp`, `reforged_domain::models::user_exp::entity::UserExpId`
 --> crates\infrastructure\src\persistence\database\mappers\user_exp_mapper.rs:1:49
  |
1 | use reforged_domain::models::user_exp::entity::{UserExp, UserExpId};
  |                                                 ^^^^^^^  ^^^^^^^^^
  |                                                 |        |
  |                                                 |        no `UserExpId` in `models::user_exp::entity`
  |                                                 |        help: a similar name exists in the module: `UserId`
  |                                                 no `UserExp` in `models::user_exp::entity`

error[E0432]: unresolved imports `reforged_domain::models::user_friend::entity::UserFriend`, `reforged_domain::models::user_friend::entity::UserFriendId`
 --> crates\infrastructure\src\persistence\database\mappers\user_friend_mapper.rs:1:52
  |
1 | use reforged_domain::models::user_friend::entity::{UserFriend, UserFriendId};
  |                                                    ^^^^^^^^^^  ^^^^^^^^^^^^ no `UserFriendId` in `models::user_friend::entity`
  |                                                    |
  |                                                    no `UserFriend` in `models::user_friend::entity`

error[E0432]: unresolved imports `reforged_domain::models::user_livedrop::entity::UserLivedrop`, `reforged_domain::models::user_livedrop::entity::UserLivedropId`
 --> crates\infrastructure\src\persistence\database\mappers\user_livedrop_mapper.rs:1:54
  |
1 | use reforged_domain::models::user_livedrop::entity::{UserLivedrop, UserLivedropId};
  |                                                      ^^^^^^^^^^^^  ^^^^^^^^^^^^^^ no `UserLivedropId` in `models::user_livedrop::entity`
  |                                                      |
  |                                                      no `UserLivedrop` in `models::user_livedrop::entity`

error[E0432]: unresolved imports `reforged_domain::models::user_login::entity::UserLogin`, `reforged_domain::models::user_login::entity::UserLoginId`
 --> crates\infrastructure\src\persistence\database\mappers\user_login_mapper.rs:1:51
  |
1 | use reforged_domain::models::user_login::entity::{UserLogin, UserLoginId};
  |                                                   ^^^^^^^^^  ^^^^^^^^^^^ no `UserLoginId` in `models::user_login::entity`
  |                                                   |
  |                                                   no `UserLogin` in `models::user_login::entity`

error[E0603]: type alias `UserFactionId` is private
 --> crates\infrastructure\src\persistence\database\mappers\user_faction_mapper.rs:1:66
  |
1 | use reforged_domain::models::user_faction::entity::{UserFaction, UserFactionId};
  |                                                                  ^^^^^^^^^^^^^ private type alias
  |
note: the type alias `UserFactionId` is defined here
 --> F:\Dev\reforged\crates\domain\src\models\user_faction\entity.rs:1:39
  |
1 | use super::value_object::{Reputation, UserFactionId};
  |                                       ^^^^^^^^^^^^^
help: import `UserFactionId` directly
  |
1 | use reforged_domain::models::user_faction::entity::{UserFaction, reforged_domain::models::user_faction::value_object::UserFactionId};
  |                                                                  +++++++++++++++++++++++++++++++++++++++++++++++++++++

error[E0603]: type alias `UserReportId` is private
 --> crates\infrastructure\src\persistence\database\mappers\user_report_mapper.rs:1:64
  |
1 | use reforged_domain::models::user_report::entity::{UserReport, UserReportId};
  |                                                                ^^^^^^^^^^^^ private type alias
  |
note: the type alias `UserReportId` is defined here
 --> F:\Dev\reforged\crates\domain\src\models\user_report\entity.rs:1:5
  |
1 | use super::value_object::*;
  |     ^^^^^^^^^^^^^^^^^^^
help: import `UserReportId` directly
  |
1 | use reforged_domain::models::user_report::entity::{UserReport, reforged_domain::models::user_report::value_object::UserReportId};
  |                                                                ++++++++++++++++++++++++++++++++++++++++++++++++++++

error[E0603]: type alias `UserSlotId` is private
 --> crates\infrastructure\src\persistence\database\mappers\user_slot_mapper.rs:1:60
  |
1 | use reforged_domain::models::user_slot::entity::{UserSlot, UserSlotId};
  |                                                            ^^^^^^^^^^ private type alias
  |
note: the type alias `UserSlotId` is defined here
 --> F:\Dev\reforged\crates\domain\src\models\user_slot\entity.rs:1:5
  |
1 | use super::value_object::*;
  |     ^^^^^^^^^^^^^^^^^^^
help: import `UserSlotId` directly
  |
1 | use reforged_domain::models::user_slot::entity::{UserSlot, reforged_domain::models::user_slot::value_object::UserSlotId};
  |                                                            ++++++++++++++++++++++++++++++++++++++++++++++++++

error[E0603]: type alias `UserTitleId` is private
 --> crates\infrastructure\src\persistence\database\mappers\user_title_mapper.rs:1:62
  |
1 | use reforged_domain::models::user_title::entity::{UserTitle, UserTitleId};
  |                                                              ^^^^^^^^^^^ private type alias
  |
note: the type alias `UserTitleId` is defined here
 --> F:\Dev\reforged\crates\domain\src\models\user_title\entity.rs:1:5
  |
1 | use super::value_object::*;
  |     ^^^^^^^^^^^^^^^^^^^
help: import `UserTitleId` directly
  |
1 | use reforged_domain::models::user_title::entity::{UserTitle, reforged_domain::models::user_title::value_object::UserTitleId};
  |                                                              +++++++++++++++++++++++++++++++++++++++++++++++++++

error[E0277]: the member `Unset<exp_boost_expire>` was not set, but this method requires it to be set
  --> crates\infrastructure\src\persistence\database\mappers\user_boost_mapper.rs:24:14
   |
24 |             .build()
   |              ^^^^^ the member `Unset<exp_boost_expire>` was not set, but this method requires it to be set
   |
   = help: the trait `bon::builder_state::IsSet` is not implemented for `Unset<exp_boost_expire>`
   = help: the trait `bon::builder_state::IsSet` is implemented for `bon::__::Set<Name>`
   = note: required for `SetGoldBoostExpire<SetRepBoostExpire<...>>` to implement `reforged_domain::models::user_boost::entity::user_boost_builder::IsComplete`
note: required by a bound in `UserBoostBuilder::<S>::build`
  --> F:\Dev\reforged\crates\domain\src\models\user_boost\entity.rs:8:33
   |
8  | #[derive(Debug, Clone, Default, bon::Builder, Getters, Setters)]
   |                                 ^^^^^^^^^^^^ required by this bound in `UserBoostBuilder::<S>::build`
9  | #[getset(get = "pub", set = "pub")]
10 | pub struct UserBoost {
   |            --------- required by a bound in this associated function
   = note: the full name for the type has been written to 'F:\Dev\reforged\target\debug\deps\reforged_infrastructure-3d67f9a3d93351b9.long-type-7302570507983766086.txt'
   = note: consider using `--verbose` to print the full type name to the console
   = note: this error originates in the derive macro `bon::Builder` (in Nightly builds, run with -Z macro-backtrace for more info)

error[E0277]: the member `Unset<upgrade_expire>` was not set, but this method requires it to be set
  --> crates\infrastructure\src\persistence\database\mappers\user_boost_mapper.rs:24:14
   |
24 |             .build()
   |              ^^^^^ the member `Unset<upgrade_expire>` was not set, but this method requires it to be set
   |
   = help: the trait `bon::builder_state::IsSet` is not implemented for `Unset<upgrade_expire>`
   = help: the trait `bon::builder_state::IsSet` is implemented for `bon::__::Set<Name>`
   = note: required for `SetGoldBoostExpire<SetRepBoostExpire<...>>` to implement `reforged_domain::models::user_boost::entity::user_boost_builder::IsComplete`
note: required by a bound in `UserBoostBuilder::<S>::build`
  --> F:\Dev\reforged\crates\domain\src\models\user_boost\entity.rs:8:33
   |
8  | #[derive(Debug, Clone, Default, bon::Builder, Getters, Setters)]
   |                                 ^^^^^^^^^^^^ required by this bound in `UserBoostBuilder::<S>::build`
9  | #[getset(get = "pub", set = "pub")]
10 | pub struct UserBoost {
   |            --------- required by a bound in this associated function
   = note: the full name for the type has been written to 'F:\Dev\reforged\target\debug\deps\reforged_infrastructure-3d67f9a3d93351b9.long-type-10993200875298603773.txt'
   = note: consider using `--verbose` to print the full type name to the console
   = note: this error originates in the derive macro `bon::Builder` (in Nightly builds, run with -Z macro-backtrace for more info)

error[E0277]: the member `Unset<upgrade_days>` was not set, but this method requires it to be set
  --> crates\infrastructure\src\persistence\database\mappers\user_boost_mapper.rs:24:14
   |
24 |             .build()
   |              ^^^^^ the member `Unset<upgrade_days>` was not set, but this method requires it to be set
   |
   = help: the trait `bon::builder_state::IsSet` is not implemented for `Unset<upgrade_days>`
   = help: the trait `bon::builder_state::IsSet` is implemented for `bon::__::Set<Name>`
   = note: required for `SetGoldBoostExpire<SetRepBoostExpire<...>>` to implement `reforged_domain::models::user_boost::entity::user_boost_builder::IsComplete`
note: required by a bound in `UserBoostBuilder::<S>::build`
  --> F:\Dev\reforged\crates\domain\src\models\user_boost\entity.rs:8:33
   |
8  | #[derive(Debug, Clone, Default, bon::Builder, Getters, Setters)]
   |                                 ^^^^^^^^^^^^ required by this bound in `UserBoostBuilder::<S>::build`
9  | #[getset(get = "pub", set = "pub")]
10 | pub struct UserBoost {
   |            --------- required by a bound in this associated function
   = note: the full name for the type has been written to 'F:\Dev\reforged\target\debug\deps\reforged_infrastructure-3d67f9a3d93351b9.long-type-13804234588912308005.txt'
   = note: consider using `--verbose` to print the full type name to the console
   = note: this error originates in the derive macro `bon::Builder` (in Nightly builds, run with -Z macro-backtrace for more info)

error[E0277]: the member `Unset<upgraded>` was not set, but this method requires it to be set
  --> crates\infrastructure\src\persistence\database\mappers\user_boost_mapper.rs:24:14
   |
24 |             .build()
   |              ^^^^^ the member `Unset<upgraded>` was not set, but this method requires it to be set
   |
   = help: the trait `bon::builder_state::IsSet` is not implemented for `Unset<upgraded>`
   = help: the trait `bon::builder_state::IsSet` is implemented for `bon::__::Set<Name>`
   = note: required for `SetGoldBoostExpire<SetRepBoostExpire<...>>` to implement `reforged_domain::models::user_boost::entity::user_boost_builder::IsComplete`
note: required by a bound in `UserBoostBuilder::<S>::build`
  --> F:\Dev\reforged\crates\domain\src\models\user_boost\entity.rs:8:33
   |
8  | #[derive(Debug, Clone, Default, bon::Builder, Getters, Setters)]
   |                                 ^^^^^^^^^^^^ required by this bound in `UserBoostBuilder::<S>::build`
9  | #[getset(get = "pub", set = "pub")]
10 | pub struct UserBoost {
   |            --------- required by a bound in this associated function
   = note: the full name for the type has been written to 'F:\Dev\reforged\target\debug\deps\reforged_infrastructure-3d67f9a3d93351b9.long-type-12344529988374424962.txt'
   = note: consider using `--verbose` to print the full type name to the console
   = note: this error originates in the derive macro `bon::Builder` (in Nightly builds, run with -Z macro-backtrace for more info)

error[E0277]: the member `Unset<color_chat>` was not set, but this method requires it to be set
  --> crates\infrastructure\src\persistence\database\mappers\user_color_mapper.rs:23:14
   |
23 |             .build()
   |              ^^^^^ the member `Unset<color_chat>` was not set, but this method requires it to be set
   |
   = help: the trait `bon::builder_state::IsSet` is not implemented for `Unset<color_chat>`
   = help: the trait `bon::builder_state::IsSet` is implemented for `bon::__::Set<Name>`
   = note: required for `SetColorName<SetColorEye<SetUserId<SetId>>>` to implement `reforged_domain::models::user_color::entity::user_color_builder::IsComplete`
note: required by a bound in `UserColorBuilder::<S>::build`
  --> F:\Dev\reforged\crates\domain\src\models\user_color\entity.rs:5:33
   |
5  | #[derive(Debug, Clone, Default, bon::Builder, Getters, Setters)]
   |                                 ^^^^^^^^^^^^ required by this bound in `UserColorBuilder::<S>::build`
6  | #[getset(get = "pub", set = "pub")]
7  | pub struct UserColor {
   |            --------- required by a bound in this associated function
   = note: the full name for the type has been written to 'F:\Dev\reforged\target\debug\deps\reforged_infrastructure-3d67f9a3d93351b9.long-type-7397483601727775536.txt'
   = note: consider using `--verbose` to print the full type name to the console
   = note: this error originates in the derive macro `bon::Builder` (in Nightly builds, run with -Z macro-backtrace for more info)

error[E0277]: the member `Unset<color_hair>` was not set, but this method requires it to be set
  --> crates\infrastructure\src\persistence\database\mappers\user_color_mapper.rs:23:14
   |
23 |             .build()
   |              ^^^^^ the member `Unset<color_hair>` was not set, but this method requires it to be set
   |
   = help: the trait `bon::builder_state::IsSet` is not implemented for `Unset<color_hair>`
   = help: the trait `bon::builder_state::IsSet` is implemented for `bon::__::Set<Name>`
   = note: required for `SetColorName<SetColorEye<SetUserId<SetId>>>` to implement `reforged_domain::models::user_color::entity::user_color_builder::IsComplete`
note: required by a bound in `UserColorBuilder::<S>::build`
  --> F:\Dev\reforged\crates\domain\src\models\user_color\entity.rs:5:33
   |
5  | #[derive(Debug, Clone, Default, bon::Builder, Getters, Setters)]
   |                                 ^^^^^^^^^^^^ required by this bound in `UserColorBuilder::<S>::build`
6  | #[getset(get = "pub", set = "pub")]
7  | pub struct UserColor {
   |            --------- required by a bound in this associated function
   = note: the full name for the type has been written to 'F:\Dev\reforged\target\debug\deps\reforged_infrastructure-3d67f9a3d93351b9.long-type-1431908910946951335.txt'
   = note: consider using `--verbose` to print the full type name to the console
   = note: this error originates in the derive macro `bon::Builder` (in Nightly builds, run with -Z macro-backtrace for more info)

error[E0277]: the member `Unset<color_skin>` was not set, but this method requires it to be set
  --> crates\infrastructure\src\persistence\database\mappers\user_color_mapper.rs:23:14
   |
23 |             .build()
   |              ^^^^^ the member `Unset<color_skin>` was not set, but this method requires it to be set
   |
   = help: the trait `bon::builder_state::IsSet` is not implemented for `Unset<color_skin>`
   = help: the trait `bon::builder_state::IsSet` is implemented for `bon::__::Set<Name>`
   = note: required for `SetColorName<SetColorEye<SetUserId<SetId>>>` to implement `reforged_domain::models::user_color::entity::user_color_builder::IsComplete`
note: required by a bound in `UserColorBuilder::<S>::build`
  --> F:\Dev\reforged\crates\domain\src\models\user_color\entity.rs:5:33
   |
5  | #[derive(Debug, Clone, Default, bon::Builder, Getters, Setters)]
   |                                 ^^^^^^^^^^^^ required by this bound in `UserColorBuilder::<S>::build`
6  | #[getset(get = "pub", set = "pub")]
7  | pub struct UserColor {
   |            --------- required by a bound in this associated function
   = note: the full name for the type has been written to 'F:\Dev\reforged\target\debug\deps\reforged_infrastructure-3d67f9a3d93351b9.long-type-7246851040943481460.txt'
   = note: consider using `--verbose` to print the full type name to the console
   = note: this error originates in the derive macro `bon::Builder` (in Nightly builds, run with -Z macro-backtrace for more info)

error[E0277]: the member `Unset<color_base>` was not set, but this method requires it to be set
  --> crates\infrastructure\src\persistence\database\mappers\user_color_mapper.rs:23:14
   |
23 |             .build()
   |              ^^^^^ the member `Unset<color_base>` was not set, but this method requires it to be set
   |
   = help: the trait `bon::builder_state::IsSet` is not implemented for `Unset<color_base>`
   = help: the trait `bon::builder_state::IsSet` is implemented for `bon::__::Set<Name>`
   = note: required for `SetColorName<SetColorEye<SetUserId<SetId>>>` to implement `reforged_domain::models::user_color::entity::user_color_builder::IsComplete`
note: required by a bound in `UserColorBuilder::<S>::build`
  --> F:\Dev\reforged\crates\domain\src\models\user_color\entity.rs:5:33
   |
5  | #[derive(Debug, Clone, Default, bon::Builder, Getters, Setters)]
   |                                 ^^^^^^^^^^^^ required by this bound in `UserColorBuilder::<S>::build`
6  | #[getset(get = "pub", set = "pub")]
7  | pub struct UserColor {
   |            --------- required by a bound in this associated function
   = note: the full name for the type has been written to 'F:\Dev\reforged\target\debug\deps\reforged_infrastructure-3d67f9a3d93351b9.long-type-5065817842209934928.txt'
   = note: consider using `--verbose` to print the full type name to the console
   = note: this error originates in the derive macro `bon::Builder` (in Nightly builds, run with -Z macro-backtrace for more info)

error[E0277]: the member `Unset<color_trim>` was not set, but this method requires it to be set
  --> crates\infrastructure\src\persistence\database\mappers\user_color_mapper.rs:23:14
   |
23 |             .build()
   |              ^^^^^ the member `Unset<color_trim>` was not set, but this method requires it to be set
   |
   = help: the trait `bon::builder_state::IsSet` is not implemented for `Unset<color_trim>`
   = help: the trait `bon::builder_state::IsSet` is implemented for `bon::__::Set<Name>`
   = note: required for `SetColorName<SetColorEye<SetUserId<SetId>>>` to implement `reforged_domain::models::user_color::entity::user_color_builder::IsComplete`
note: required by a bound in `UserColorBuilder::<S>::build`
  --> F:\Dev\reforged\crates\domain\src\models\user_color\entity.rs:5:33
   |
5  | #[derive(Debug, Clone, Default, bon::Builder, Getters, Setters)]
   |                                 ^^^^^^^^^^^^ required by this bound in `UserColorBuilder::<S>::build`
6  | #[getset(get = "pub", set = "pub")]
7  | pub struct UserColor {
   |            --------- required by a bound in this associated function
   = note: the full name for the type has been written to 'F:\Dev\reforged\target\debug\deps\reforged_infrastructure-3d67f9a3d93351b9.long-type-1789711860938928683.txt'
   = note: consider using `--verbose` to print the full type name to the console
   = note: this error originates in the derive macro `bon::Builder` (in Nightly builds, run with -Z macro-backtrace for more info)

error[E0277]: the member `Unset<color_accessory>` was not set, but this method requires it to be set
  --> crates\infrastructure\src\persistence\database\mappers\user_color_mapper.rs:23:14
   |
23 |             .build()
   |              ^^^^^ the member `Unset<color_accessory>` was not set, but this method requires it to be set
   |
   = help: the trait `bon::builder_state::IsSet` is not implemented for `Unset<color_accessory>`
   = help: the trait `bon::builder_state::IsSet` is implemented for `bon::__::Set<Name>`
   = note: required for `SetColorName<SetColorEye<SetUserId<SetId>>>` to implement `reforged_domain::models::user_color::entity::user_color_builder::IsComplete`
note: required by a bound in `UserColorBuilder::<S>::build`
  --> F:\Dev\reforged\crates\domain\src\models\user_color\entity.rs:5:33
   |
5  | #[derive(Debug, Clone, Default, bon::Builder, Getters, Setters)]
   |                                 ^^^^^^^^^^^^ required by this bound in `UserColorBuilder::<S>::build`
6  | #[getset(get = "pub", set = "pub")]
7  | pub struct UserColor {
   |            --------- required by a bound in this associated function
   = note: the full name for the type has been written to 'F:\Dev\reforged\target\debug\deps\reforged_infrastructure-3d67f9a3d93351b9.long-type-1031661762068481617.txt'
   = note: consider using `--verbose` to print the full type name to the console
   = note: this error originates in the derive macro `bon::Builder` (in Nightly builds, run with -Z macro-backtrace for more info)

error[E0609]: no field `currency_type` on type `user_currencies::Model`
  --> crates\infrastructure\src\persistence\database\mappers\user_currency_mapper.rs:20:34
   |
20 |             .currency_type(model.currency_type.into())
   |                                  ^^^^^^^^^^^^^ unknown field
   |
   = note: available fields are: `id`, `user_id`, `gold`, `coins`, `diamonds`, `crystal`

error[E0609]: no field `amount` on type `user_currencies::Model`
  --> crates\infrastructure\src\persistence\database\mappers\user_currency_mapper.rs:21:27
   |
21 |             .amount(model.amount.into())
   |                           ^^^^^^ unknown field
   |
   = note: available fields are: `id`, `user_id`, `gold`, `coins`, `diamonds`, `crystal`

error[E0609]: no field `exp_type` on type `user_exps::Model`
  --> crates\infrastructure\src\persistence\database\mappers\user_exp_mapper.rs:20:29
   |
20 |             .exp_type(model.exp_type.into())
   |                             ^^^^^^^^ unknown field
   |
   = note: available fields are: `id`, `user_id`, `level`, `exp`

error[E0609]: no field `amount` on type `user_exps::Model`
  --> crates\infrastructure\src\persistence\database\mappers\user_exp_mapper.rs:21:27
   |
21 |             .amount(model.amount.into())
   |                           ^^^^^^ unknown field
   |
   = note: available fields are: `id`, `user_id`, `level`, `exp`

error[E0609]: no field `status` on type `user_friends::Model`
  --> crates\infrastructure\src\persistence\database\mappers\user_friend_mapper.rs:21:27
   |
21 |             .status(model.status.into())
   |                           ^^^^^^ unknown field
   |
   = note: available fields are: `id`, `user_id`, `friend_id`

error[E0609]: no field `created_at` on type `user_friends::Model`
  --> crates\infrastructure\src\persistence\database\mappers\user_friend_mapper.rs:22:31
   |
22 |             .created_at(model.created_at.into())
   |                               ^^^^^^^^^^ unknown field
   |
   = note: available fields are: `id`, `user_id`, `friend_id`

error[E0308]: mismatched types
  --> crates\infrastructure\src\persistence\database\mappers\user_livedrop_mapper.rs:19:34
   |
19 |             .user_id(UserId::new(model.user_id))
   |                      ----------- ^^^^^^^^^^^^^ expected `Uuid`, found `Option<Uuid>`
   |                      |
   |                      arguments to this function are incorrect
   |
   = note: expected struct `sea_orm::prelude::Uuid`
                found enum `std::option::Option<sea_orm::prelude::Uuid>`
note: associated function defined here
  --> F:\Dev\reforged\crates\shared\src\types\id.rs:70:12
   |
70 |     pub fn new(id: uuid::Uuid) -> Self {
   |            ^^^
help: consider using `Option::expect` to unwrap the `std::option::Option<sea_orm::prelude::Uuid>` value, panicking if the value is an `Option::None`
   |
19 |             .user_id(UserId::new(model.user_id.expect("REASON")))
   |                                               +++++++++++++++++

error[E0609]: no field `claimed_at` on type `user_livedrops::Model`
  --> crates\infrastructure\src\persistence\database\mappers\user_livedrop_mapper.rs:22:31
   |
22 |             .claimed_at(model.claimed_at.into())
   |                               ^^^^^^^^^^ unknown field
   |
   = note: available fields are: `id`, `user_id`, `item_id`, `quantity`, `sent` ... and 12 others

error[E0609]: no field `ip_address` on type `user_logins::Model`
  --> crates\infrastructure\src\persistence\database\mappers\user_login_mapper.rs:20:31
   |
20 |             .ip_address(model.ip_address.into())
   |                               ^^^^^^^^^^ unknown field
   |
help: a field with a similar name exists
   |
20 -             .ip_address(model.ip_address.into())
20 +             .ip_address(model.address.into())
   |

error[E0609]: no field `device_info` on type `user_logins::Model`
  --> crates\infrastructure\src\persistence\database\mappers\user_login_mapper.rs:22:32
   |
22 |             .device_info(model.device_info.into())
   |                                ^^^^^^^^^^^ unknown field
   |
   = note: available fields are: `id`, `user_id`, `location`, `status`, `address`, `date`

error[E0609]: no field `login_time` on type `user_logins::Model`
  --> crates\infrastructure\src\persistence\database\mappers\user_login_mapper.rs:23:31
   |
23 |             .login_time(model.login_time.into())
   |                               ^^^^^^^^^^ unknown field
   |
   = note: available fields are: `id`, `user_id`, `location`, `status`, `address`, `date`

error[E0599]: no method named `reporter_id` found for struct `UserReportBuilder` in the current scope
  --> crates\infrastructure\src\persistence\database\mappers\user_report_mapper.rs:19:14
   |
17 | /         UserReport::builder()
18 | |             .id(UserReportId::new(model.id))
19 | |             .reporter_id(UserId::new(model.reporter_id))
   | |             -^^^^^^^^^^^ method not found in `UserReportBuilder<SetId>`
   | |_____________|
   |

error[E0609]: no field `reporter_id` on type `user_reports::Model`
  --> crates\infrastructure\src\persistence\database\mappers\user_report_mapper.rs:19:44
   |
19 |             .reporter_id(UserId::new(model.reporter_id))
   |                                            ^^^^^^^^^^^ unknown field
   |
   = note: available fields are: `id`, `user_id`, `target_name`, `category`, `description`, `date_submitted`

error[E0609]: no field `reported_id` on type `user_reports::Model`
  --> crates\infrastructure\src\persistence\database\mappers\user_report_mapper.rs:20:44
   |
20 |             .reported_id(UserId::new(model.reported_id))
   |                                            ^^^^^^^^^^^ unknown field
   |
   = note: available fields are: `id`, `user_id`, `target_name`, `category`, `description`, `date_submitted`

error[E0609]: no field `reason` on type `user_reports::Model`
  --> crates\infrastructure\src\persistence\database\mappers\user_report_mapper.rs:21:27
   |
21 |             .reason(model.reason.into())
   |                           ^^^^^^ unknown field
   |
   = note: available fields are: `id`, `user_id`, `target_name`, `category`, `description`, `date_submitted`

error[E0609]: no field `status` on type `user_reports::Model`
  --> crates\infrastructure\src\persistence\database\mappers\user_report_mapper.rs:22:27
   |
22 |             .status(model.status.into())
   |                           ^^^^^^ unknown field
   |
   = note: available fields are: `id`, `user_id`, `target_name`, `category`, `description`, `date_submitted`

error[E0609]: no field `created_at` on type `user_reports::Model`
  --> crates\infrastructure\src\persistence\database\mappers\user_report_mapper.rs:23:31
   |
23 |             .created_at(model.created_at.into())
   |                               ^^^^^^^^^^ unknown field
   |
   = note: available fields are: `id`, `user_id`, `target_name`, `category`, `description`, `date_submitted`

error[E0599]: no method named `slot_type` found for struct `UserSlotBuilder` in the current scope
  --> crates\infrastructure\src\persistence\database\mappers\user_slot_mapper.rs:20:14
   |
17 | /         UserSlot::builder()
18 | |             .id(UserSlotId::new(model.id))
19 | |             .user_id(UserId::new(model.user_id))
20 | |             .slot_type(model.slot_type.into())
   | |             -^^^^^^^^^ method not found in `UserSlotBuilder<SetUserId<SetId>>`
   | |_____________|
   |

error[E0609]: no field `slot_type` on type `user_slots::Model`
  --> crates\infrastructure\src\persistence\database\mappers\user_slot_mapper.rs:20:30
   |
20 |             .slot_type(model.slot_type.into())
   |                              ^^^^^^^^^ unknown field
   |
   = note: available fields are: `id`, `user_id`, `slots_bag`, `slots_bank`, `slots_house`, `slots_auction`

error[E0609]: no field `item_id` on type `user_slots::Model`
  --> crates\infrastructure\src\persistence\database\mappers\user_slot_mapper.rs:21:28
   |
21 |             .item_id(model.item_id.map(|id| id.into()))
   |                            ^^^^^^^ unknown field
   |
   = note: available fields are: `id`, `user_id`, `slots_bag`, `slots_bank`, `slots_house`, `slots_auction`

error[E0599]: no method named `date_acquired` found for struct `UserTitleBuilder` in the current scope
  --> crates\infrastructure\src\persistence\database\mappers\user_title_mapper.rs:22:14
   |
18 | /         UserTitle::builder()
19 | |             .id(UserTitleId::new(model.id))
20 | |             .user_id(UserId::new(model.user_id))
21 | |             .title_id(TitleId::new(model.title_id))
22 | |             .date_acquired(model.date_acquired.into())
   | |             -^^^^^^^^^^^^^ method not found in `UserTitleBuilder<SetTitleId<SetUserId<SetId>>>`
   | |_____________|
   |

error[E0609]: no field `date_acquired` on type `user_titles::Model`
  --> crates\infrastructure\src\persistence\database\mappers\user_title_mapper.rs:22:34
   |
22 |             .date_acquired(model.date_acquired.into())
   |                                  ^^^^^^^^^^^^^ unknown field
   |
   = note: available fields are: `id`, `user_id`, `title_id`, `date`

Some errors have detailed explanations: E0277, E0308, E0432, E0583, E0599, E0603, E0609.
For more information about an error, try `rustc --explain E0277`.
error: could not compile `reforged-infrastructure` (lib) due to 45 previous errors"
