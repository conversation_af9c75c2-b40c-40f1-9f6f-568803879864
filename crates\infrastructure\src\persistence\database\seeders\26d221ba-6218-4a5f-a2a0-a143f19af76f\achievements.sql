INSERT INTO "achievements" ("id", "name", "description", "file", "category", "show") VALUES (1, 'Founder', 'Awarded to those who became an upgraded Legend within the first month of Nythera''s release.\r\n\r\n', 'founder.jpg', '\', true);
INSERT INTO "achievements" ("id", "name", "description", "file", "category", "show") VALUES (2, 'Beta Character', 'Awarded to those who created an account during our Beta release.', 'beta-tester.jpg', '\', true);
INSERT INTO "achievements" ("id", "name", "description", "file", "category", "show") VALUES (5, 'Honorable Hero', 'Player who set out to save Forest from grave danger from villains!', 'forestcomplete.jpg', '\', true);
INSERT INTO "achievements" ("id", "name", "description", "file", "category", "show") VALUES (6, 'Doodle', 'really a map that has no meaning at all', 'Doodle.png', '\', true);
INSERT INTO "achievements" ("id", "name", "description", "file", "category", "show") VALUES (7, 'Pinky!', 'You are pretty Pinky!', 'Lovely.png', '\', true);
INSERT INTO "achievements" ("id", "name", "description", "file", "category", "show") VALUES (8, 'Member', 'Those who buy the Membership Pack.', 'member.jpg', '\', true);
INSERT INTO "achievements" ("id", "name", "description", "file", "category", "show") VALUES (9, 'Sepulchure''s Shadow', 'Embrace the Darkness! Awarded for purchasing the Shadow Sepuchure Package. Thank you', 'ShadowSepulchurer.png', '\', true);
INSERT INTO "achievements" ("id", "name", "description", "file", "category", "show") VALUES (10, 'Hollowborn', 'Even in death;a paladin never loses their faith and sense of justice. If these two things are strong enough;one may rise as a Hollowborn. A light that shines even in the darkest days.', 'Hollowborn1.png', '\', true);
INSERT INTO "achievements" ("id", "name", "description", "file", "category", "show") VALUES (11, 'Hollowborn ShadowRaptor', 'Unlock exclusive armor sets;weapons;other accessories;and character page badge in your new upgrade package.', 'HollowbornSR.png', '\', true);
