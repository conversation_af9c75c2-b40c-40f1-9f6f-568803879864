use reforged_domain::models::enhancement_pattern::{
    entity::EnhancementPattern,
    value_object::{
        Description, Dexterity, Endurance, EnhancementPatternId, EnhancementPatternName,
        Intelligence, Luck, Strength, Wisdom,
    },
};

#[derive(Debug)]
pub struct EnhancementPatternDbModelMapper(super::super::models::enhancement_patterns::Model);

impl EnhancementPatternDbModelMapper {
    pub fn new(enhancement_pattern: super::super::models::enhancement_patterns::Model) -> Self {
        Self(enhancement_pattern)
    }
}

impl From<EnhancementPatternDbModelMapper> for EnhancementPattern {
    fn from(value: EnhancementPatternDbModelMapper) -> Self {
        let enhancement_pattern_model = value.0;

        let id = EnhancementPatternId::new(enhancement_pattern_model.id);

        EnhancementPattern::builder()
            .id(id)
            .name(EnhancementPatternName::new(enhancement_pattern_model.name))
            .description(Description::new(enhancement_pattern_model.desc))
            .wisdom(Wisdom::new(enhancement_pattern_model.wisdom))
            .strength(Strength::new(enhancement_pattern_model.strength))
            .luck(Luck::new(enhancement_pattern_model.luck))
            .dexterity(Dexterity::new(enhancement_pattern_model.dexterity))
            .endurance(Endurance::new(enhancement_pattern_model.endurance))
            .intelligence(Intelligence::new(enhancement_pattern_model.intelligence))
            .build()
    }
}
