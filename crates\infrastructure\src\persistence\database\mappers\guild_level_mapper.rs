use num_traits::ToPrimitive;
use reforged_domain::models::{
    guild::value_object::GuildId,
    guild_level::{entity::GuildLevel, value_object::GuildLevelId},
    user_exp::value_object::{Experience, Level},
};

#[derive(Debug)]
pub struct GuildLevelDbModelMapper(super::super::models::guild_levels::Model);

impl GuildLevelDbModelMapper {
    pub fn new(guild_level: super::super::models::guild_levels::Model) -> Self {
        Self(guild_level)
    }
}

impl From<GuildLevelDbModelMapper> for GuildLevel {
    fn from(value: GuildLevelDbModelMapper) -> Self {
        let guild_level_model = value.0;

        let id = GuildLevelId::new(guild_level_model.id);

        GuildLevel::builder()
            .id(id)
            .guild_id(GuildId::new(guild_level_model.guild_id))
            .level(Level::new(guild_level_model.level.to_u16().unwrap_or(0)))
            .exp(Experience::new(guild_level_model.exp.to_u32().unwrap_or(0)))
            .build()
    }
}
