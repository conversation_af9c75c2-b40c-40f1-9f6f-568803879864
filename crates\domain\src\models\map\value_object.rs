use reforged_shared::{UuidId, Value};

use super::entity::Map;

pub type MapId = UuidId<Map>;

#[derive(Debug, PartialEq, Eq, PartialOrd, Ord, Clone, Default)]

pub struct MapName(String);

impl MapName {
    pub fn new(name: impl Into<String>) -> Self {
        Self(name.into())
    }
}

impl Value<String> for MapName {
    fn value(&self) -> String {
        self.0.clone()
    }
}

impl From<String> for MapName {
    fn from(value: String) -> Self {
        Self(value)
    }
}

impl From<&str> for MapName {
    fn from(value: &str) -> Self {
        Self(value.to_string())
    }
}

#[derive(Debug, PartialEq, Eq, PartialOrd, Ord, Clone, Default)]

pub struct MapFile(String);

impl MapFile {
    pub fn new(file: impl Into<String>) -> Self {
        Self(file.into())
    }
}

impl Value<String> for MapFile {
    fn value(&self) -> String {
        self.0.clone()
    }
}

impl From<String> for MapFile {
    fn from(value: String) -> Self {
        Self(value)
    }
}

impl From<&str> for MapFile {
    fn from(value: &str) -> Self {
        Self(value.to_string())
    }
}

#[derive(Debug, PartialEq, Eq, PartialOrd, Ord, Clone, Default)]

pub struct MapMaxPlayers(i16);

impl MapMaxPlayers {
    pub fn new(max_players: i16) -> Self {
        Self(max_players)
    }
}

impl Value<i16> for MapMaxPlayers {
    fn value(&self) -> i16 {
        self.0
    }
}

impl From<i16> for MapMaxPlayers {
    fn from(value: i16) -> Self {
        Self(value)
    }
}

#[derive(Debug, PartialEq, Eq, PartialOrd, Ord, Clone, Default)]

pub struct MapReqLevel(i16);

impl MapReqLevel {
    pub fn new(req_level: i16) -> Self {
        Self(req_level)
    }
}

impl Value<i16> for MapReqLevel {
    fn value(&self) -> i16 {
        self.0
    }
}

impl From<i16> for MapReqLevel {
    fn from(value: i16) -> Self {
        Self(value)
    }
}

#[derive(Debug, PartialEq, Eq, PartialOrd, Ord, Clone, Default)]

pub struct MapUpgrade(bool);

impl MapUpgrade {
    pub fn new(upgrade: bool) -> Self {
        Self(upgrade)
    }
}

impl Value<bool> for MapUpgrade {
    fn value(&self) -> bool {
        self.0
    }
}

impl From<bool> for MapUpgrade {
    fn from(value: bool) -> Self {
        Self(value)
    }
}

#[derive(Debug, PartialEq, Eq, PartialOrd, Ord, Clone, Default)]

pub struct MapStaff(bool);

impl MapStaff {
    pub fn new(staff: bool) -> Self {
        Self(staff)
    }
}

impl Value<bool> for MapStaff {
    fn value(&self) -> bool {
        self.0
    }
}

impl From<bool> for MapStaff {
    fn from(value: bool) -> Self {
        Self(value)
    }
}

#[derive(Debug, PartialEq, Eq, PartialOrd, Ord, Clone, Default)]

pub struct MapPvp(bool);

impl MapPvp {
    pub fn new(pvp: bool) -> Self {
        Self(pvp)
    }
}

impl Value<bool> for MapPvp {
    fn value(&self) -> bool {
        self.0
    }
}

impl From<bool> for MapPvp {
    fn from(value: bool) -> Self {
        Self(value)
    }
}

#[derive(Debug, PartialEq, Eq, PartialOrd, Ord, Clone, Default)]

pub struct MapWorldBoss(bool);

impl MapWorldBoss {
    pub fn new(world_boss: bool) -> Self {
        Self(world_boss)
    }
}

impl Value<bool> for MapWorldBoss {
    fn value(&self) -> bool {
        self.0
    }
}

impl From<bool> for MapWorldBoss {
    fn from(value: bool) -> Self {
        Self(value)
    }
}
