use reforged_domain::models::store_type::entity::StoreType;

#[derive(Debug)]
pub struct StoreTypeDbModelMapper(super::super::models::sea_orm_active_enums::StoreType);

impl StoreTypeDbModelMapper {
    pub fn new(store_type: super::super::models::sea_orm_active_enums::StoreType) -> Self {
        Self(store_type)
    }
}

impl From<StoreTypeDbModelMapper> for StoreType {
    fn from(value: StoreTypeDbModelMapper) -> Self {
        match value.0 {
            super::super::models::sea_orm_active_enums::StoreType::Package => StoreType::Package,
            super::super::models::sea_orm_active_enums::StoreType::Vip => StoreType::Vip,
            super::super::models::sea_orm_active_enums::StoreType::Founder => StoreType::Founder,
            super::super::models::sea_orm_active_enums::StoreType::Coin => StoreType::Coin,
        }
    }
}
