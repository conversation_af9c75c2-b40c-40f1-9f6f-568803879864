use crate::models::item::value_object::ItemId;
use crate::models::wheel_reward::value_object::*;
use getset::{<PERSON><PERSON>, <PERSON><PERSON>};

#[derive(<PERSON>bu<PERSON>, <PERSON><PERSON>, <PERSON><PERSON>ult, bon::<PERSON><PERSON><PERSON>, <PERSON><PERSON>, Set<PERSON>)]
#[getset(get = "pub", set = "pub")]
pub struct WheelReward {
    id: WheelRewardId,
    pub item_id: ItemId,
    pub chance: WheelRewardChance,
}

impl WheelReward {
    pub fn new(id: WheelRewardId, item_id: ItemId, chance: WheelRewardChance) -> Self {
        Self {
            id,
            item_id,
            chance,
        }
    }
}

#[cfg(test)]
mod tests {
    use reforged_shared::{IdTrait, Value};

    use super::*;

    #[test]
    fn test_wheel_reward_new() {
        let id = WheelRewardId::new(uuid::Uuid::now_v7());
        let item_id = ItemId::new(uuid::Uuid::now_v7());
        let chance = WheelRewardChance::new(0.25);

        let wheel_reward = WheelReward::new(id.clone(), item_id.clone(), chance.clone());

        assert_eq!(wheel_reward.id().get_id(), id.get_id());
        assert_eq!(wheel_reward.item_id().get_id(), item_id.get_id());
        assert_eq!(wheel_reward.chance().value(), chance.value());
    }

    #[test]
    fn test_wheel_reward_builder() {
        let id = WheelRewardId::new(uuid::Uuid::now_v7());
        let item_id = ItemId::new(uuid::Uuid::now_v7());
        let chance = WheelRewardChance::new(0.25);

        let wheel_reward = WheelReward::builder()
            .id(id.clone())
            .item_id(item_id.clone())
            .chance(chance.clone())
            .build();

        assert_eq!(wheel_reward.id().get_id(), id.get_id());
        assert_eq!(wheel_reward.item_id().get_id(), item_id.get_id());
        assert_eq!(wheel_reward.chance().value(), chance.value());
    }

    #[test]
    fn test_getters_and_setters() {
        let mut wheel_reward = WheelReward::default();

        let id = WheelRewardId::new(uuid::Uuid::now_v7());
        let item_id = ItemId::new(uuid::Uuid::now_v7());
        let chance = WheelRewardChance::new(0.25);

        wheel_reward.set_id(id.clone());
        wheel_reward.set_item_id(item_id.clone());
        wheel_reward.set_chance(chance.clone());

        assert_eq!(wheel_reward.id().get_id(), id.get_id());
        assert_eq!(wheel_reward.item_id().get_id(), item_id.get_id());
        assert_eq!(wheel_reward.chance().value(), chance.value());
    }
}
