use reforged_domain::models::guild::{entity::Guild, value_object::GuildId};
use reforged_domain::models::user::value_object::UserId;

#[derive(Debug)]
pub struct GuildDbModelMapper(super::super::models::guilds::Model);

impl GuildDbModelMapper {
    pub fn new(guild: super::super::models::guilds::Model) -> Self {
        Self(guild)
    }
}

impl From<GuildDbModelMapper> for Guild {
    fn from(value: GuildDbModelMapper) -> Self {
        let guild_model = value.0;

        let id = GuildId::new(guild_model.id);

        Guild::builder()
            .id(id)
            .name(guild_model.name.into())
            .message_of_the_day(guild_model.message_of_the_day.into())
            .max_members(guild_model.max_members.into())
            .hall_size(guild_model.hall_size.into())
            .last_updated(guild_model.last_updated.and_utc().into())
            .wins(guild_model.wins.into())
            .loss(guild_model.loss.into())
            .total_kills(guild_model.total_kills.into())
            .level(guild_model.level.into())
            .experience(guild_model.experience.into())
            .guild_color(guild_model.guild_color.into())
            .staff_g(UserId::new(guild_model.staff_g))
            .color(guild_model.color.into())
            .build()
    }
}
