use reforged_domain::models::user_guild::entity::UserGuild;
use reforged_domain::models::user_guild::value_object::UserGuildId;
use reforged_domain::models::user::value_object::UserId;
use reforged_domain::models::guild::value_object::GuildId;
use chrono::{DateTime, Utc};

#[derive(Debug)]
pub struct UserGuildDbModelMapper(super::super::models::user_guilds::Model);

impl UserGuildDbModelMapper {
    pub fn new(model: super::super::models::user_guilds::Model) -> Self {
        Self(model)
    }
}

impl From<UserGuildDbModelMapper> for UserGuild {
    fn from(value: UserGuildDbModelMapper) -> Self {
        let model = value.0;
        
        UserGuild::builder()
            .id(UserGuildId::new(model.id))
            .user_id(UserId::new(model.user_id))
            .guild_id(GuildId::new(model.guild_id))
            .rank(model.rank.into())
            .joined_at(DateTime::<Utc>::from_naive_utc_and_offset(model.joined_at, Utc).into())
            .build()
    }
}
