//! `SeaORM` Entity, @generated by sea-orm-codegen 1.1.0

use sea_orm::entity::prelude::*;
use serde::Deserialize;

#[derive(<PERSON><PERSON>, Debug, PartialEq, DeriveEntityModel, Eq, Deserialize)]
#[sea_orm(table_name = "enhancement_patterns")]
pub struct Model {
    #[sea_orm(primary_key, auto_increment = false)]
    pub id: Uuid,
    pub name: String,
    pub desc: String,
    pub wisdom: i16,
    pub strength: i16,
    pub luck: i16,
    pub dexterity: i16,
    pub endurance: i16,
    pub intelligence: i16,
}

#[derive(Co<PERSON>, <PERSON>lone, Debug, EnumIter, DeriveRelation)]
pub enum Relation {
    #[sea_orm(has_many = "super::enhancements::Entity")]
    Enhancements,
}

impl Related<super::enhancements::Entity> for Entity {
    fn to() -> RelationDef {
        Relation::Enhancements.def()
    }
}

impl ActiveModelBehavior for ActiveModel {}
