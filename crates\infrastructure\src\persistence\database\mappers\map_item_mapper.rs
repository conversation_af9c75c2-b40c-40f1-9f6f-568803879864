use reforged_domain::models::map_item::entity::MapItem;
use reforged_domain::models::map_item::value_object::MapItemId;
use reforged_domain::models::map::value_object::MapId;
use reforged_domain::models::item::value_object::ItemId;

#[derive(Debug)]
pub struct MapItemDbModelMapper(super::super::models::map_items::Model);

impl MapItemDbModelMapper {
    pub fn new(model: super::super::models::map_items::Model) -> Self {
        Self(model)
    }
}

impl From<MapItemDbModelMapper> for MapItem {
    fn from(value: MapItemDbModelMapper) -> Self {
        let model = value.0;
        
        MapItem::builder()
            .id(MapItemId::new(model.id))
            .map_id(MapId::new(model.map_id))
            .item_id(ItemId::new(model.item_id))
            .x(model.x.into())
            .y(model.y.into())
            .build()
    }
}
