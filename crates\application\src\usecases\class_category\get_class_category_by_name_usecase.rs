use crate::{
    error::ApplicationError,
    queries::{
        class_category_queries::GetClassCategoryByNameQuery,
        class_category_query_handlers::{ClassCategory<PERSON>uery<PERSON>and<PERSON>, ClassCategoryResponse},
    },
    traits::Query<PERSON><PERSON><PERSON>,
};

pub struct GetClassCategoryByNameUsecase {
    class_category_query_handler: ClassCategoryQueryHandler,
}

impl GetClassCategoryByNameUsecase {
    pub fn new(class_category_query_handler: ClassCategoryQueryHandler) -> Self {
        Self {
            class_category_query_handler,
        }
    }
}

impl GetClassCategoryByNameUsecase {
    pub async fn execute(&self, name: String) -> Result<ClassCategoryResponse, ApplicationError> {
        let query = GetClassCategoryByNameQuery {
            name: name.to_owned(),
        };
        let class_category = self
            .class_category_query_handler
            .handle(query)
            .await?
            .ok_or(ApplicationError::EntityNotFound(name.to_string()))?;

        Ok(class_category)
    }
}
