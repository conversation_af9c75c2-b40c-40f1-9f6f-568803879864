use std::sync::Arc;

use mail_send::{SmtpClient, mail_builder::MessageBuilder};
use reforged_application::{error::ApplicationError, services::email_service::EmailService};
use sea_orm_migration::async_trait::async_trait;
use tokio::{net::TcpStream, sync::Mutex};
use tokio_rustls::client::TlsStream;

pub struct SMTPEmailService {
    smtp_client: Arc<Mutex<SmtpClient<TlsStream<TcpStream>>>>,
}

impl SMTPEmailService {
    pub fn new(smtp_client: SmtpClient<TlsStream<TcpStream>>) -> Self {
        let smtp_client = Arc::new(Mutex::new(smtp_client));
        Self { smtp_client }
    }
}

#[async_trait]
impl EmailService for SMTPEmailService {
    async fn send_email(
        &self,
        to: String,
        subject: String,
        body: String,
    ) -> Result<(), ApplicationError> {
        let message = MessageBuilder::new()
            .from("<EMAIL>")
            .to(to)
            .subject(subject)
            .html_body(body);

        let mut smtp_client = self.smtp_client.lock().await;
        if let Err(e) = smtp_client.send(message).await {
            tracing::error!("Failed to send email: {}", e);
            return Err(ApplicationError::InternalError(
                "Failed to send email".to_string(),
            ));
        }

        Ok(())
    }
}
