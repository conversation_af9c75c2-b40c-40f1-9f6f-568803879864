use chrono::{DateTime, Utc};
use reforged_shared::{UuidId, Value};

use super::entity::UserPurchase;

pub type UserPurchaseId = UuidId<UserPurchase>;

#[derive(Debug, PartialEq, Eq, PartialOrd, Ord, Clone, Default)]
pub struct PaymentId(String);

impl PaymentId {
    pub fn new(payment_id: impl Into<String>) -> Self {
        Self(payment_id.into())
    }
}

impl Value<String> for PaymentId {
    fn value(&self) -> String {
        self.0.clone()
    }
}

impl From<String> for PaymentId {
    fn from(value: String) -> Self {
        Self(value)
    }
}

impl From<&str> for PaymentId {
    fn from(value: &str) -> Self {
        Self(value.to_string())
    }
}

#[derive(Debug, PartialEq, Eq, PartialOrd, Ord, Clone, Default)]
pub struct TransactionId(Option<String>);

impl TransactionId {
    pub fn new(transaction_id: Option<String>) -> Self {
        Self(transaction_id)
    }
}

impl Value<Option<String>> for TransactionId {
    fn value(&self) -> Option<String> {
        self.0.clone()
    }
}

impl From<Option<String>> for TransactionId {
    fn from(value: Option<String>) -> Self {
        Self(value)
    }
}

#[derive(Debug, PartialEq, Eq, PartialOrd, Ord, Clone, Default)]
pub struct PurchaseEmail(Option<String>);

impl PurchaseEmail {
    pub fn new(email: Option<String>) -> Self {
        Self(email)
    }
}

impl Value<Option<String>> for PurchaseEmail {
    fn value(&self) -> Option<String> {
        self.0.clone()
    }
}

impl From<Option<String>> for PurchaseEmail {
    fn from(value: Option<String>) -> Self {
        Self(value)
    }
}

#[derive(Debug, PartialEq, Eq, PartialOrd, Ord, Clone, Default)]
pub struct Hash(String);

impl Hash {
    pub fn new(hash: impl Into<String>) -> Self {
        Self(hash.into())
    }
}

impl Value<String> for Hash {
    fn value(&self) -> String {
        self.0.clone()
    }
}

impl From<String> for Hash {
    fn from(value: String) -> Self {
        Self(value)
    }
}

impl From<&str> for Hash {
    fn from(value: &str) -> Self {
        Self(value.to_string())
    }
}

#[derive(Debug, PartialEq, Eq, PartialOrd, Ord, Clone, Default)]
pub struct PurchaseItem(String);

impl PurchaseItem {
    pub fn new(item: impl Into<String>) -> Self {
        Self(item.into())
    }
}

impl Value<String> for PurchaseItem {
    fn value(&self) -> String {
        self.0.clone()
    }
}

impl From<String> for PurchaseItem {
    fn from(value: String) -> Self {
        Self(value)
    }
}

impl From<&str> for PurchaseItem {
    fn from(value: &str) -> Self {
        Self(value.to_string())
    }
}

#[derive(Debug, PartialEq, Eq, PartialOrd, Ord, Clone, Default)]
pub struct PurchasePrice(String);

impl PurchasePrice {
    pub fn new(price: impl Into<String>) -> Self {
        Self(price.into())
    }
}

impl Value<String> for PurchasePrice {
    fn value(&self) -> String {
        self.0.clone()
    }
}

impl From<String> for PurchasePrice {
    fn from(value: String) -> Self {
        Self(value)
    }
}

impl From<&str> for PurchasePrice {
    fn from(value: &str) -> Self {
        Self(value.to_string())
    }
}

#[derive(Debug, PartialEq, Eq, PartialOrd, Ord, Clone, Default)]
pub struct Method(String);

impl Method {
    pub fn new(method: impl Into<String>) -> Self {
        Self(method.into())
    }
}

impl Value<String> for Method {
    fn value(&self) -> String {
        self.0.clone()
    }
}

impl From<String> for Method {
    fn from(value: String) -> Self {
        Self(value)
    }
}

impl From<&str> for Method {
    fn from(value: &str) -> Self {
        Self(value.to_string())
    }
}

#[derive(Debug, PartialEq, Eq, PartialOrd, Ord, Clone, Default)]
pub struct Currency(String);

impl Currency {
    pub fn new(currency: impl Into<String>) -> Self {
        Self(currency.into())
    }
}

impl Value<String> for Currency {
    fn value(&self) -> String {
        self.0.clone()
    }
}

impl From<String> for Currency {
    fn from(value: String) -> Self {
        Self(value)
    }
}

impl From<&str> for Currency {
    fn from(value: &str) -> Self {
        Self(value.to_string())
    }
}

#[derive(Debug, PartialEq, Eq, PartialOrd, Ord, Clone, Default)]
pub struct Purchased(i16);

impl Purchased {
    pub fn new(value: i16) -> Self {
        Self(value)
    }
}

impl Value<i16> for Purchased {
    fn value(&self) -> i16 {
        self.0
    }
}

impl From<i16> for Purchased {
    fn from(value: i16) -> Self {
        Self(value)
    }
}

#[derive(Debug, PartialEq, Eq, PartialOrd, Ord, Clone, Default)]
pub struct PurchaseDate(DateTime<Utc>);

impl PurchaseDate {
    pub fn new(date: DateTime<Utc>) -> Self {
        Self(date)
    }
}

impl Value<DateTime<Utc>> for PurchaseDate {
    fn value(&self) -> DateTime<Utc> {
        self.0
    }
}

impl From<DateTime<Utc>> for PurchaseDate {
    fn from(date: DateTime<Utc>) -> Self {
        Self(date)
    }
}

#[derive(Debug, PartialEq, Eq, PartialOrd, Ord, Clone, Default)]
pub struct Broadcast(bool);

impl Broadcast {
    pub fn new(value: bool) -> Self {
        Self(value)
    }
}

impl Value<bool> for Broadcast {
    fn value(&self) -> bool {
        self.0
    }
}

impl From<bool> for Broadcast {
    fn from(value: bool) -> Self {
        Self(value)
    }
}