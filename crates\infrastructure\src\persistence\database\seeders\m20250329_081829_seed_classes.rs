use crate::models;
use sea_orm::{ActiveModelTrait, IntoActiveModel};
use sea_orm_migration::prelude::*;

#[derive(DeriveMigrationName)]
pub struct Migration;

#[async_trait::async_trait]
impl MigrationTrait for Migration {
    async fn up(&self, manager: &SchemaManager) -> Result<(), DbErr> {
        let db = manager.get_connection();

        let class_categories = include_str!("../json/classes_categories.json");
        let class_categories: Vec<models::class_categories::Model> =
            serde_json::from_str(class_categories).unwrap();

        for model in class_categories {
            let class_category = model.into_active_model();
            class_category.insert(db).await?;
        }

        let classes = include_str!("../json/classes.json");
        let classes: Vec<models::classes::Model> = serde_json::from_str(classes).unwrap();

        for model in classes {
            let class = model.into_active_model();
            class.insert(db).await?;
        }

        let class_skills = include_str!("../json/classes_skills.json");
        let class_skills: Vec<models::class_skills::Model> =
            serde_json::from_str(class_skills).unwrap();

        for model in class_skills {
            let class_skill = model.into_active_model();
            class_skill.insert(db).await?;
        }

        Ok(())
    }

    async fn down(&self, _manager: &SchemaManager) -> Result<(), DbErr> {
        Ok(())
    }
}
