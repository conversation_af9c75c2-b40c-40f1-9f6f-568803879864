use reforged_domain::models::item_rarity::{
    entity::ItemRarity,
    value_object::{ItemRarityEnum, ItemRarityId},
};

#[derive(Debug)]
pub struct ItemRarityDbModelMapper(super::super::models::item_rarities::Model);

impl ItemRarityDbModelMapper {
    pub fn new(item_rarity: super::super::models::item_rarities::Model) -> Self {
        Self(item_rarity)
    }
}

impl From<ItemRarityDbModelMapper> for ItemRarity {
    fn from(value: ItemRarityDbModelMapper) -> Self {
        let item_rarity_model = value.0;
        let name = ItemRarityEnum::from(item_rarity_model.name.as_str());

        let id = ItemRarityId::new(item_rarity_model.id);

        ItemRarity::builder().id(id).name(name).build()
    }
}
