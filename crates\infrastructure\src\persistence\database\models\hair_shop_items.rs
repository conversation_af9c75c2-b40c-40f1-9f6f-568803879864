//! `SeaORM` Entity, @generated by sea-orm-codegen 1.1.0

use super::sea_orm_active_enums::Gender;
use sea_orm::entity::prelude::*;
use serde::Deserialize;

#[derive(<PERSON>lone, Debug, PartialEq, DeriveEntityModel, Eq, Deserialize)]
#[sea_orm(table_name = "hair_shop_items")]
pub struct Model {
    #[sea_orm(primary_key, auto_increment = false)]
    pub id: Uuid,
    pub gender: Gender,
    pub shop_id: Uuid,
    pub hair_id: Uuid,
}

#[derive(<PERSON><PERSON>, <PERSON><PERSON>, Debug, EnumIter, DeriveRelation)]
pub enum Relation {
    #[sea_orm(
        belongs_to = "super::hair_shops::Entity",
        from = "Column::ShopId",
        to = "super::hair_shops::Column::Id",
        on_update = "Cascade",
        on_delete = "Cascade"
    )]
    HairShops,
    #[sea_orm(
        belongs_to = "super::hairs::Entity",
        from = "Column::HairId",
        to = "super::hairs::Column::Id",
        on_update = "Cascade",
        on_delete = "Cascade"
    )]
    Hairs,
}

impl Related<super::hair_shops::Entity> for Entity {
    fn to() -> RelationDef {
        Relation::HairShops.def()
    }
}

impl Related<super::hairs::Entity> for Entity {
    fn to() -> RelationDef {
        Relation::Hairs.def()
    }
}

impl ActiveModelBehavior for ActiveModel {}
