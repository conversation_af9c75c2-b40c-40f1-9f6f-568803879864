use crate::models;
use sea_orm::{ActiveModelTrait, IntoActiveModel};
use sea_orm_migration::prelude::*;

#[derive(DeriveMigrationName)]
pub struct Migration;

#[async_trait::async_trait]
impl MigrationTrait for Migration {
    async fn up(&self, manager: &SchemaManager) -> Result<(), DbErr> {
        let db = manager.get_connection();

        let game_settings = include_str!("../json/settings_login.json");
        let game_settings: Vec<models::game_settings::Model> =
            serde_json::from_str(game_settings).unwrap();

        for model in game_settings {
            let game_setting = model.into_active_model();
            game_setting.insert(db).await?;
        }

        let game_rates_settings = include_str!("../json/settings_rates.json");
        let game_rates_settings: Vec<models::game_rates_settings::Model> =
            serde_json::from_str(game_rates_settings).unwrap();

        for model in game_rates_settings {
            let game_rates_setting = model.into_active_model();
            game_rates_setting.insert(db).await?;
        }

        Ok(())
    }

    async fn down(&self, _manager: &SchemaManager) -> Result<(), DbErr> {
        Ok(())
    }
}
