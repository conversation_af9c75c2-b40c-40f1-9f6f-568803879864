use super::value_object::{Reputation, UserFactionId};
use crate::models::{faction::value_object::FactionId, user::value_object::UserId};
use getset::{<PERSON><PERSON>, Set<PERSON>};

#[derive(<PERSON>bu<PERSON>, <PERSON><PERSON>, <PERSON><PERSON>ult, bon::<PERSON><PERSON><PERSON>, <PERSON><PERSON>, Setters)]
#[getset(get = "pub", set = "pub")]
pub struct UserFaction {
    id: UserFactionId,
    user_id: UserId,
    faction_id: FactionId,
    reputation: Reputation,
}

impl UserFaction {
    pub fn new(
        id: UserFactionId,
        user_id: UserId,
        faction_id: FactionId,
        reputation: Reputation,
    ) -> Self {
        Self {
            id,
            user_id,
            faction_id,
            reputation,
        }
    }
}

#[cfg(test)]
mod tests {
    use reforged_shared::{IdTrait, Value};

    use super::*;

    #[test]
    fn test_user_faction_new() {
        let id = UserFactionId::new(uuid::Uuid::now_v7());
        let user_id = UserId::new(uuid::Uuid::now_v7());
        let faction_id = FactionId::new(uuid::Uuid::now_v7());
        let reputation = Reputation::new(1000);

        let user_faction = UserFaction::new(
            id.clone(),
            user_id.clone(),
            faction_id.clone(),
            reputation.clone(),
        );

        assert_eq!(user_faction.id.get_id(), id.get_id());
        assert_eq!(user_faction.user_id.get_id(), user_id.get_id());
        assert_eq!(user_faction.faction_id.get_id(), faction_id.get_id());
        assert_eq!(user_faction.reputation.value(), reputation.value());
    }

    #[test]
    fn test_user_faction_builder() {
        let id = UserFactionId::new(uuid::Uuid::now_v7());
        let user_id = UserId::new(uuid::Uuid::now_v7());
        let faction_id = FactionId::new(uuid::Uuid::now_v7());
        let reputation = Reputation::new(1000);

        let user_faction = UserFaction::builder()
            .id(id.clone())
            .user_id(user_id.clone())
            .faction_id(faction_id.clone())
            .reputation(reputation.clone())
            .build();

        assert_eq!(user_faction.id.get_id(), id.get_id());
        assert_eq!(user_faction.user_id.get_id(), user_id.get_id());
        assert_eq!(user_faction.faction_id.get_id(), faction_id.get_id());
        assert_eq!(user_faction.reputation.value(), reputation.value());
    }

    #[test]
    fn test_getters_and_setters() {
        let mut user_faction = UserFaction::default();

        let id = UserFactionId::new(uuid::Uuid::now_v7());
        let user_id = UserId::new(uuid::Uuid::now_v7());
        let faction_id = FactionId::new(uuid::Uuid::now_v7());
        let reputation = Reputation::new(1000);

        user_faction.set_id(id.clone());
        user_faction.set_user_id(user_id.clone());
        user_faction.set_faction_id(faction_id.clone());
        user_faction.set_reputation(reputation.clone());

        assert_eq!(user_faction.id().get_id(), id.get_id());
        assert_eq!(user_faction.user_id().get_id(), user_id.get_id());
        assert_eq!(user_faction.faction_id().get_id(), faction_id.get_id());
        assert_eq!(user_faction.reputation().value(), reputation.value());
    }
}
