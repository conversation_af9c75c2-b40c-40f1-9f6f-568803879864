use async_trait::async_trait;
use mockall::automock;

use crate::error::RepositoryError;
use crate::models::quest_reward::entity::QuestReward;
use crate::models::quest_reward::value_object::QuestRewardId;

#[automock]
#[async_trait]
pub trait QuestRewardRepository: Send + Sync {
    async fn save(&self, entity: &QuestReward) -> Result<(), RepositoryError>;
    async fn update(&self, entity: &QuestReward) -> Result<(), RepositoryError>;
    async fn delete(&self, id: &QuestRewardId) -> Result<(), RepositoryError>;
}

#[automock]
#[async_trait]
pub trait QuestRewardReadRepository: Send + Sync {
    async fn find_by_id(&self, id: &QuestRewardId) -> Result<Option<QuestReward>, RepositoryError>;
    async fn find_all(&self) -> Result<Vec<QuestReward>, RepositoryError>;
}
